import{_ as pe,r as v,c as $,o as ce,$ as de,b as T,m as _,d as l,g as s,a8 as me,M as w,az as fe,ax as ve,J as _e,v as u,F as C,t as ye,af as ge,e as b,bh as be,j as he,p as S,q as ke,s as je,be as Ve,bf as Ee,aD as we,aE as Ce,aF as xe,k as Oe,R as $e,S as Se,E as i,ah as De}from"./entry-DxFfH4M0.js";/* empty css                   *//* empty css                *//* empty css                 *//* empty css                        *//* empty css               *//* empty css                  *//* empty css                       *//* empty css                 *//* empty css                *//* empty css                        *//* empty css                    */const Pe={class:"project-manager"},Ue={class:"card-header"},Te={class:"path-input"},Re={class:"dialog-footer"},Fe={class:"dialog-header"},Ie={class:"project-output"},Me={__name:"项目",setup(Be){const h=v(null),k=$(()=>h.value?.system==="Windows"),R=$(()=>h.value?.system==="Darwin"),q=$(()=>h.value?.system!=="Windows"&&h.value?.system!=="Darwin"),m={project:window.pywebview.api.inject_project,system:window.pywebview.api},F=v([]),D=v(!1),j=v(null),c=v({visible:!1,isEdit:!1}),r=v({name:"",type:"py",path:"",start_params:"",check_method:"process",check_param:null}),I=$(()=>{const{path:t,type:e,start_params:n}=r.value;if(!t)return"";let o="";if(e==="exe")o=`"${t}"`;else if(e==="py")o=`${k.value?"python":"python3"} "${t}"`;else if(e==="sh")o=`bash "${t}"`;else if(e==="app")o=`open "${t}"`;else if(e==="bin")if(k.value)o=`"${t}"`;else{const p=t.substring(0,t.lastIndexOf("/")+1),f=t.substring(t.lastIndexOf("/")+1);p?o=`cd "${p}" && ./${f}`:o=`./${t}`}return n&&(o+=` ${n}`),o}),z={name:[{required:!0,message:"请输入项目名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],type:[{required:!0,message:"请选择项目类型",trigger:"change"}],path:[{required:!0,message:"请选择项目路径",trigger:"change"}],check_method:[{required:!0,message:"请选择检查方式",trigger:"change"}],check_param:[{required:!0,message:"请输入端口号",trigger:"change",type:"number"}]},P=v(!1),A=async()=>{try{const t=await m.system.get_platform_info(),e=typeof t=="string"?JSON.parse(t):t;e&&e.status==="success"&&(h.value=e.data,k.value&&r.value.type===""?r.value.type="exe":(R.value&&r.value.type===""||q.value&&r.value.type==="")&&(r.value.type="py"))}catch(t){console.error("获取平台信息失败:",t),h.value={system:"Unknown",release:""}}},V=async()=>{D.value=!0;try{const t=await m.project.get_project_list(),e=typeof t=="string"?JSON.parse(t):t;e.status==="success"?F.value=Object.entries(e.data).map(([n,o])=>({name:n,...o,loading:!1})):i.error(e.message||"加载失败")}catch(t){console.error("加载项目列表失败:",t),i.error("加载项目列表失败")}finally{D.value=!1}},J=async()=>{try{const t=await m.system.select_file_path(),e=typeof t=="string"?JSON.parse(t):t;e&&e.status==="success"&&(r.value.path=e.data[0])}catch(t){console.error("选择路径失败:",t),i.error("选择路径失败")}},N=()=>{c.value.isEdit=!1,c.value.visible=!0},L=t=>{c.value.isEdit=!0,Object.assign(r.value,{name:t.name,type:t.type,path:t.path,start_params:t.start_params||"",check_method:t.check_method,check_param:t.check_param}),c.value.visible=!0},W=()=>{j.value&&j.value.resetFields(),r.value={name:"",type:k.value?"exe":"py",path:"",start_params:"",check_method:"process",check_param:null}},X=async()=>{j.value&&await j.value.validate(async t=>{if(t){P.value=!0;try{const{name:e,...n}=r.value,o={...n,check_param:n.check_method==="port"?Number(n.check_param):null},p=c.value.isEdit?await m.project.update_project(e,o):await m.project.add_project({project_name:e,project_type:o.type,project_path:o.path,start_params:o.start_params,check_method:o.check_method,check_param:o.check_param}),f=typeof p=="string"?JSON.parse(p):p;f.status==="success"?(i.success(c.value.isEdit?"更新成功":"添加成功"),c.value.visible=!1,await V()):i.error(f.message||"操作失败")}catch(e){console.error("提交项目失败:",e),i.error("提交失败")}finally{P.value=!1}}})},G=async t=>{try{await De.confirm(`确定要删除项目 "${t.name}" 吗？`,"警告",{type:"warning",confirmButtonText:"确定",cancelButtonText:"取消"});const e=await m.project.remove_project(t.name);e.status==="success"?(i.success("删除成功"),await V()):i.error(e.message||"删除失败")}catch(e){e!=="cancel"&&(console.error("删除项目失败:",e),i.error("删除失败"))}},H=async t=>{t.loading=!0;try{const e=t.status==="running"?"stop_project":"start_project",n=await m.project[e](t.name),o=typeof n=="string"?JSON.parse(n):n;o.status==="success"?(i.success(t.status==="running"?"已停止":"已启动"),await V()):i.error(o.message||"操作失败")}catch(e){console.error("操作项目失败:",e),i.error("操作失败")}finally{t.loading=!1}},d=v(new Map),K=(t,e)=>{try{const n=atob(e),o=new TextDecoder("utf-8").decode(new Uint8Array([...n].map(f=>f.charCodeAt(0))));d.value.has(t)||d.value.set(t,{visible:!1,content:""});const p=d.value.get(t);p.content+=o}catch(n){console.error("Error processing project output:",n)}},Q=async t=>{d.value.has(t.name)||d.value.set(t.name,{visible:!1,content:""});const e=d.value.get(t.name);e.visible=!0;try{await m.project.get_project_output(t.name)}catch(n){console.error("获取项目输出失败:",n),i.error("获取项目输出失败")}},Y=t=>{if(d.value.has(t)){const e=d.value.get(t);e.content=""}},Z=t=>{if(d.value.has(t)){const e=d.value.get(t);e.visible=!1}};window.receiveProjectOutput=K;let x=null;const ee=()=>{x=setInterval(V,5e3)},te=()=>{x&&(clearInterval(x),x=null)},ae=t=>({exe:"可执行文件(EXE)",py:"Python项目",sh:"Shell脚本",app:"macOS应用",bin:"二进制可执行文件"})[t]||t,le=t=>({exe:"success",py:"info",sh:"warning",app:"danger",bin:"primary"})[t]||"info";return ce(()=>{A(),V(),ee()}),de(()=>{te()}),(t,e)=>{const n=ye,o=ve,p=_e,f=fe,se=be,O=je,y=ke,E=Ee,oe=Ve,M=Ce,ne=we,re=xe,ue=he,B=Oe,ie=ge;return _(),T("div",Pe,[l(se,{class:"project-list"},{header:s(()=>[b("div",Ue,[e[10]||(e[10]=b("span",null,"项目列表",-1)),l(n,{type:"primary",onClick:N},{default:s(()=>e[9]||(e[9]=[u("添加项目")])),_:1})])]),default:s(()=>[me((_(),w(f,{data:F.value,style:{width:"100%"}},{default:s(()=>[l(o,{prop:"name",label:"项目名称"}),l(o,{prop:"path",label:"项目路径","show-overflow-tooltip":""}),l(o,{prop:"type",label:"类型"},{default:s(({row:a})=>[l(p,{type:le(a.type)},{default:s(()=>[u(C(ae(a.type)),1)]),_:2},1032,["type"])]),_:1}),l(o,{prop:"status",label:"状态"},{default:s(({row:a})=>[l(p,{type:a.status==="running"?"success":"info"},{default:s(()=>[u(C(a.status==="running"?"运行中":"已停止"),1)]),_:2},1032,["type"])]),_:1}),l(o,{prop:"last_run_time",label:"最后运行时间","show-overflow-tooltip":""},{default:s(({row:a})=>[u(C(a.last_run_time||"从未运行"),1)]),_:1}),l(o,{prop:"run_count",label:"运行次数",width:"100"}),l(o,{label:"操作",width:"330"},{default:s(({row:a})=>[l(n,{type:a.status==="running"?"danger":"success",size:"small",onClick:g=>H(a),loading:a.loading},{default:s(()=>[u(C(a.status==="running"?"停止":"启动"),1)]),_:2},1032,["type","onClick","loading"]),l(n,{type:"primary",size:"small",onClick:g=>L(a),disabled:a.status==="running"},{default:s(()=>e[11]||(e[11]=[u(" 编辑 ")])),_:2},1032,["onClick","disabled"]),l(n,{type:"danger",size:"small",onClick:g=>G(a),disabled:a.status==="running"},{default:s(()=>e[12]||(e[12]=[u(" 删除 ")])),_:2},1032,["onClick","disabled"]),l(n,{type:"info",size:"small",onClick:g=>Q(a),disabled:a.status!=="running"},{default:s(()=>e[13]||(e[13]=[u(" 输出 ")])),_:2},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])),[[ie,D.value]])]),_:1}),l(B,{modelValue:c.value.visible,"onUpdate:modelValue":e[8]||(e[8]=a=>c.value.visible=a),title:c.value.isEdit?"编辑项目":"添加项目",width:"600px",onClose:W},{footer:s(()=>[b("span",Re,[l(n,{onClick:e[7]||(e[7]=a=>c.value.visible=!1)},{default:s(()=>e[20]||(e[20]=[u("取消")])),_:1}),l(n,{type:"primary",onClick:X,loading:P.value},{default:s(()=>e[21]||(e[21]=[u(" 确定 ")])),_:1},8,["loading"])])]),default:s(()=>[l(ue,{ref_key:"projectFormRef",ref:j,model:r.value,rules:z,"label-width":"100px"},{default:s(()=>[l(y,{label:"项目名称",prop:"name"},{default:s(()=>[l(O,{modelValue:r.value.name,"onUpdate:modelValue":e[0]||(e[0]=a=>r.value.name=a),placeholder:"请输入项目名称"},null,8,["modelValue"])]),_:1}),l(y,{label:"项目类型",prop:"type"},{default:s(()=>[l(oe,{modelValue:r.value.type,"onUpdate:modelValue":e[1]||(e[1]=a=>r.value.type=a)},{default:s(()=>[k.value?(_(),w(E,{key:0,label:"exe"},{default:s(()=>e[14]||(e[14]=[u("可执行文件(EXE)")])),_:1})):S("",!0),l(E,{label:"py"},{default:s(()=>e[15]||(e[15]=[u("Python项目")])),_:1}),k.value?S("",!0):(_(),w(E,{key:1,label:"sh"},{default:s(()=>e[16]||(e[16]=[u("Shell脚本")])),_:1})),R.value?(_(),w(E,{key:2,label:"app"},{default:s(()=>e[17]||(e[17]=[u("macOS应用")])),_:1})):S("",!0),l(E,{label:"bin"},{default:s(()=>e[18]||(e[18]=[u("二进制可执行文件")])),_:1})]),_:1},8,["modelValue"])]),_:1}),l(y,{label:"项目路径",prop:"path"},{default:s(()=>[b("div",Te,[l(O,{modelValue:r.value.path,"onUpdate:modelValue":e[2]||(e[2]=a=>r.value.path=a),placeholder:"请选择项目文件或目录",readonly:""},null,8,["modelValue"]),l(n,{onClick:J},{default:s(()=>e[19]||(e[19]=[u("选择")])),_:1})])]),_:1}),l(y,{label:"启动参数"},{default:s(()=>[l(O,{modelValue:r.value.start_params,"onUpdate:modelValue":e[3]||(e[3]=a=>r.value.start_params=a),placeholder:"可选，如: --port 3000 --config config.json"},null,8,["modelValue"])]),_:1}),l(y,{label:"检查方式",prop:"check_method"},{default:s(()=>[l(ne,{modelValue:r.value.check_method,"onUpdate:modelValue":e[4]||(e[4]=a=>r.value.check_method=a)},{default:s(()=>[l(M,{label:"进程检查",value:"process"}),l(M,{label:"端口检查",value:"port"})]),_:1},8,["modelValue"])]),_:1}),r.value.check_method==="port"?(_(),w(y,{key:0,label:"检查参数",prop:"check_param"},{default:s(()=>[l(re,{modelValue:r.value.check_param,"onUpdate:modelValue":e[5]||(e[5]=a=>r.value.check_param=a),min:1,max:65535,placeholder:"请输入端口号"},null,8,["modelValue"])]),_:1})):S("",!0),l(y,{label:"启动命令"},{default:s(()=>[l(O,{modelValue:I.value,"onUpdate:modelValue":e[6]||(e[6]=a=>I.value=a),type:"textarea",rows:2,readonly:"",placeholder:"根据配置自动生成的启动命令"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),(_(!0),T($e,null,Se(Array.from(d.value),([a,g])=>(_(),T("div",{key:a},[l(B,{modelValue:g.visible,"onUpdate:modelValue":U=>g.visible=U,title:`${a} 输出`,width:"80%","destroy-on-close":"",onClosed:U=>Z(a)},{default:s(()=>[b("div",Fe,[l(n,{type:"primary",size:"small",onClick:U=>Y(a)},{default:s(()=>e[22]||(e[22]=[u(" 清除输出 ")])),_:2},1032,["onClick"])]),b("div",Ie,[b("pre",null,C(g.content),1)])]),_:2},1032,["modelValue","onUpdate:modelValue","title","onClosed"])]))),128))])}}},Ye=pe(Me,[["__scopeId","data-v-0f831493"]]);export{Ye as default};
