const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./index-CyRisoZP.js","./entry-DxFfH4M0.js","./css/main.css-Dp-yYx0W.css","./aiProviders-Zwr_VLaf.js"])))=>i.map(i=>d[i]);
import{_ as ke,a as _e,r as m,c as x,Y as P,a7 as G,o as be,b as h,m as v,e as l,d as M,p as T,R as H,S as Y,h as O,n as J,F as B,cx as xe,M as Me,a8 as Ae,at as Se,x as Q,g as A,t as Ie,v as U,k as Te}from"./entry-DxFfH4M0.js";import{useAIRolesStore as De}from"./aiRoles-Cy8pLKqW.js";import{useAIProvidersStore as Ee}from"./aiProviders-Zwr_VLaf.js";import{U as X,M as Ve}from"./UniversalSelector-WSkEUteJ.js";import{n as Fe}from"./index-browser-OxPLOBIU.js";/* empty css                 */import"./purify-es-DhD2mIk-.js";/* empty css                 */const Ne={class:"chat-layout"},Be={class:"sidebar-header"},Re={key:0,xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},je={key:1,xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},Pe={class:"chat-list"},Oe=["onClick"],Ue={key:0,class:"chat-item-icon"},$e={key:1,class:"chat-item-title"},He=["onClick"],Je={class:"sidebar-footer"},qe={class:"main-chat-panel"},ze={class:"chat-panel-header"},Le={class:"chat-title"},Ke={class:"chat-controls"},We={class:"control-group model-selector"},Ze={class:"control-group role-selector"},Ge={key:0,xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},Ye={key:1,xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},Qe={key:0,class:"typing-indicator"},Xe={class:"chat-input-area"},et={class:"main-input-wrapper"},tt=["onKeydown","disabled"],ot=["disabled"],st={key:0,xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},lt={key:1,xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},at={class:"dialog-footer"},nt={class:"dialog-footer"},rt={__name:"chat",setup(it){const ee=_e(),D=De(),te=Ee(),_=m(!1),S=m(""),w=m(new Set),c=m([]),i=m(""),j=m(null),y=m(!1),C=m(!0),f=m([]),E=m(!1),k=m(null),V=m(!1),u=m(""),g=x(()=>te.modelOptions.map(e=>({id:e.uniqueId,name:e.label,providerId:e.providerId,providerName:e.providerName,uniqueId:e.uniqueId,config:e.config}))),q=x(()=>g.value.map(t=>({value:t.id,label:t.name,description:t.providerName?`提供商: ${t.providerName}`:void 0,provider:t.providerName}))),z=x(()=>W.value.map(t=>({value:t.id,label:t.name||t.id,description:t.description}))),oe=x(()=>{const t=c.value.find(e=>e.id===i.value);return t?t.messages:[]}),I=x(()=>w.value.has(i.value)),se=x(()=>ee.chat||{fontSize:14,fontFamily:"微软雅黑, sans-serif",codeBlockTheme:"auto",chatBackground:"",messageMaxWidth:75,userMessageColor:"#f0f7ff",aiMessageColor:"#ffffff",showTimestamp:!1,codeHighlightStyle:"tomorrow",customCSS:""}),le=()=>{const t=document.documentElement.classList.contains("dark"),e=se.value.codeBlockTheme||"auto";let o="tomorrow";e==="auto"?o=t?"tomorrow-night":"tomorrow":e==="dark"?o="tomorrow-night":e==="light"&&(o="tomorrow"),console.log("Applying code highlight theme:",o),document.documentElement.style.setProperty("--code-bg-color",o==="tomorrow-night"?"#1e1e1e":"#f6f8fa"),document.documentElement.style.setProperty("--code-text-color",o==="tomorrow-night"?"#e0e0e0":"#24292e"),document.documentElement.style.setProperty("--code-border-color",o==="tomorrow-night"?"#333":"#e8e8e8"),document.documentElement.style.setProperty("--code-header-bg",o==="tomorrow-night"?"#2d2d2d":"#f6f8fa")},ae=()=>{_.value=!_.value},ne=async(t,e)=>{console.log("Model changed:",t,e);const o=c.value.find(r=>r.id===i.value);o&&(o.model_id=t,await N(o))},re=async(t,e)=>{console.log("Roles changed:",t,e),f.value=t;const o=c.value.find(r=>r.id===i.value);if(o)if(o.roles=t,await N(o),t.length>0){const r=t.map(s=>{const n=W.value.find(a=>a.id===s);return n&&n.name||s}).join(", ");console.log(`已选择角色: ${r}`)}else console.log("已清除所有角色设定")},$=()=>{P(()=>{j.value&&(j.value.scrollTop=j.value.scrollHeight)})},F=t=>{console.error(typeof t=="string"?t:t.message||"Operation failed")},ie=async()=>{try{const t=await window.pywebview.api.model_controller.get_all_chats(),e=typeof t=="string"?JSON.parse(t):t;if(e&&e.status==="success"&&Array.isArray(e.data))if(c.value=e.data,c.value.length>0){const o=c.value[0];i.value=o.id,o.model_id&&(u.value=o.model_id)}else await R();else throw new Error(e?.message||"Failed to load chats")}catch(t){console.error("Failed to load chats:",t),F(t.message||"Failed to load chats"),await R()}},R=async()=>{if(!u.value){console.warn("Please select a model first");return}const t=Fe(),e={id:t,title:`New Chat ${c.value.length+1}`,name:`New Chat ${c.value.length+1}`,messages:[],model_id:u.value,roles:f.value||[],last_updated:Date.now()/1e3};try{await N(e),c.value.push(e),i.value=t}catch(o){console.error("Failed to create chat:",o),F("Failed to create new chat")}},N=async t=>{try{return!t.title&&t.name?t.title=t.name:t.title||(t.title=`New Chat ${Date.now()}`),await window.pywebview.api.model_controller.save_chat(t.id,t),!0}catch(e){throw console.error("Failed to save chat:",e),e}},de=t=>{if(i.value===t)return;i.value=t;const e=c.value.find(o=>o.id===t);if(e){if(e.model_id){const o=e.model_id;if(console.log("切换聊天，模型ID:",o),console.log("可用模型数量:",g.value.length),g.value.length>0&&console.log("第一个可用模型:",g.value[0]),g.value.find(s=>s.id===o))u.value=o,console.log("使用新格式模型ID:",o);else{console.log("尝试转换旧格式模型ID:",o);const s=g.value.find(n=>n.uniqueId&&n.uniqueId.endsWith(":"+o));s?(u.value=s.id,e.model_id=s.id,console.log("转换成功:",o,"->",s.id)):(console.log("找不到匹配模型，使用默认模型"),g.value.length>0?(u.value=g.value[0].id,e.model_id=g.value[0].id,console.log("使用默认模型:",g.value[0].id)):console.warn("没有可用模型"))}}e.roles&&Array.isArray(e.roles)?f.value=e.roles:f.value=[]}},ce=async t=>{k.value=c.value.find(e=>e.id===t),E.value=!0},ue=()=>{E.value=!1,k.value=null},ve=async()=>{try{if(!k.value)return;const t=await window.pywebview.api.model_controller.delete_chat(k.value.id),e=typeof t=="string"?JSON.parse(t):t;if(e.status!=="success")throw new Error(e.message||"Delete failed");const o=c.value.findIndex(r=>r.id===k.value.id);if(o===-1)return;c.value.splice(o,1),i.value===k.value.id&&(i.value=c.value[0]?.id||""),console.log("Delete successful"),E.value=!1,k.value=null}catch(t){console.error("Failed to delete chat:",t),E.value=!1,k.value=null}},he=()=>{V.value=!0},ge=()=>{V.value=!1},pe=async()=>{try{const t=await window.pywebview.api.model_controller.clear_all_chats(),e=typeof t=="string"?JSON.parse(t):t;if(!e||e.status!=="success")throw new Error(e?.message||"Failed to clear chats");c.value=[],i.value="",console.log("Cleared all chats"),V.value=!1,await R()}catch(t){console.error("Failed to clear chats:",t),F("Failed to clear chats, please try again"),V.value=!1}},me=()=>{C.value=!C.value,localStorage.setItem("chat_memory_enabled",C.value),console.log(C.value?"Memory mode enabled":"Memory mode disabled")},L=async()=>{if(!S.value.trim()||I.value)return;const t=c.value.find(s=>s.id===i.value);if(!t)return;t.model_id=u.value;const e=[];if(f.value&&f.value.length>0)for(const s of f.value){const n=D.roles.find(a=>a.id===s);n&&n.prompt&&e.push(n.prompt)}const o={role:"user",content:S.value};let r=[];if(C.value){const s=e.length>0?[{role:"system",content:e.join(`

`)}]:[],n=t.messages.filter(a=>a.role!=="system").map(a=>({role:a.role,content:a.content}));r=[...s,...n,o]}else e.length>0&&r.push({role:"system",content:e.join(`

`)}),r.push(o);t.messages.push(o),t.last_updated=Date.now()/1e3,S.value="",w.value.add(i.value),y.value=!0,P(()=>{$()});try{await N(t),await we(i.value,o.content);const n={stream:!0,...Z(u.value)};console.log("发送消息到后端，模型ID:",u.value),console.log("模型配置:",n),window.pywebview.api.model_controller.chat(i.value,u.value,r,n)}catch(s){w.value.delete(i.value),y.value=!1,console.error("Failed to send message:",s),F(s.message||"Failed to send message")}},fe=async()=>{if(!(!i.value||!w.value.has(i.value)))try{const t=await window.pywebview.api.model_controller.stop_chat(i.value),e=typeof t=="string"?JSON.parse(t):t;if(e.status==="success")w.value.delete(i.value),y.value=!1,console.log("Generation stopped");else throw new Error(e.message||"Stop failed")}catch(t){console.error("Failed to stop chat:",t),F(t.message||"Failed to stop chat")}finally{w.value.delete(i.value),y.value=!1}},we=async(t,e)=>{try{const o=await window.pywebview.api.model_controller.get_chat(t),r=typeof o=="string"?JSON.parse(o):o;if(r.status==="success"){const s=r.data;if(s.messages.filter(a=>a.role==="user").length<=1&&/^New Chat/.test(s.title)){const a=e.slice(0,20)+(e.length>20?"...":"");await ye(t,a)}}}catch(o){console.error("Failed to auto-update title:",o)}},ye=async(t,e)=>{try{const o=await window.pywebview.api.model_controller.get_chat(t),r=typeof o=="string"?JSON.parse(o):o;if(r.status==="success"){const s=r.data;s.title=e,s.name=e;const n=await window.pywebview.api.model_controller.save_chat(t,s);if((typeof n=="string"?JSON.parse(n):n).status==="success"){const d=c.value.find(p=>p.id===t);return d&&(d.title=e,d.name=e),!0}}return!1}catch(o){return console.error("Failed to update chat title:",o),!1}};window.receiveChunk=t=>{try{const e=atob(t),o=new TextDecoder("utf-8").decode(new Uint8Array([...e].map(d=>d.charCodeAt(0)))),r=JSON.parse(o),{chat_id:s,content:n,reasoning:a}=r;if(a&&console.log("Received reasoning content:",a),s===i.value){const d=c.value.find(b=>b.id===s);if(!d)return;d.messages||(d.messages=[]);const p=d.messages[d.messages.length-1];if(!p||p.role!=="assistant"){const b={role:"assistant",content:n||"",reasoningStartTime:a?Date.now():null};a&&(b.reasoning=a,b.reasoningCollapsed=!1,b.reasoningTime="Thinking..."),d.messages.push(b)}else if(n&&(p.content+=n),a){p.reasoningStartTime||(p.reasoningStartTime=Date.now()),p.reasoning||(p.reasoning="");const b=a.replace(/\\n/g,`
`);p.reasoning+=b,"reasoningCollapsed"in p||(p.reasoningCollapsed=!1,p.reasoningTime="Thinking...")}P(()=>{$()}),y.value=!1}}catch(e){console.error("Failed to process message chunk:",e)}},window.onMessageComplete=t=>{if(t===i.value){w.value.delete(t),y.value=!1;const e=c.value.find(o=>o.id===t);if(e&&e.messages.length>0){const o=e.messages[e.messages.length-1];if(o&&o.reasoning&&o.reasoningStartTime){const s=((Date.now()-o.reasoningStartTime)/1e3).toFixed(1);o.reasoningTime=`for ${s}s`,delete o.reasoningStartTime}N(e).catch(r=>{console.error("Failed to save chat:",r)})}}},window.receiveChatError=t=>{try{const e=atob(t),o=new TextDecoder("utf-8").decode(new Uint8Array([...e].map(a=>a.charCodeAt(0)))),r=JSON.parse(o),{chat_id:s,error_message:n}=r;if(console.log("接收到聊天错误消息:",s,n),G(async()=>{const{ElMessage:a}=await import("./index-CyRisoZP.js");return{ElMessage:a}},__vite__mapDeps([0,1,2]),import.meta.url).then(({ElMessage:a})=>{a.error({message:`AI回复失败: ${n}`,duration:5e3})}),s===i.value){w.value.delete(s),y.value=!1;const a=c.value.find(d=>d.id===s);if(a&&a.messages.length>0){const d=a.messages[a.messages.length-1];d&&d.role==="assistant"&&!d.content.trim()&&(d.content=`[Error: ${n}]`,d.isError=!0,N(a).catch(p=>{console.error("Failed to save chat:",p)}))}}}catch(e){console.error("Failed to process error message:",e)}},be(async()=>{le(),console.log("Chat.vue: 开始初始化聊天界面...");try{try{const{useAIProvidersStore:e}=await G(async()=>{const{useAIProvidersStore:r}=await import("./aiProviders-Zwr_VLaf.js");return{useAIProvidersStore:r}},__vite__mapDeps([3,1,2]),import.meta.url),o=e();o.initialized?console.log("Chat.vue: AI提供商配置已初始化，跳过加载"):(console.log("Chat.vue: 开始加载AI提供商配置..."),await o.loadProviders(!0),console.log("Chat.vue: AI提供商配置加载完成:",o.providers.length,"个提供商"))}catch(e){console.error("Chat.vue: 加载AI提供商配置失败:",e)}if(console.log("Chat.vue: 可用模型数量:",g.value.length),g.value.length===0)console.warn("Chat.vue: 没有可用的模型，请检查AI提供商配置");else if(!u.value&&g.value.length>0){const e=g.value[0];u.value=e.id,console.log("Chat.vue: 默认选择模型:",u.value)}try{D.roles.length?console.log("Chat.vue: AI角色已加载，跳过加载"):(console.log("Chat.vue: 开始加载AI角色..."),await D.loadRoles(),console.log("Chat.vue: AI角色加载完成:",D.roles.length,"个角色"))}catch(e){console.error("Chat.vue: 加载AI角色失败:",e)}try{console.log("Chat.vue: 开始加载聊天历史..."),await ie(),console.log("Chat.vue: 聊天历史加载完成:",c.value.length,"个聊天")}catch(e){console.error("Chat.vue: 加载聊天历史失败:",e),await R()}}catch(e){console.error("Chat.vue: 初始化错误:",e)}const t=localStorage.getItem("chat_memory_enabled");t!==null&&(C.value=t==="true"),console.log("Chat interface initialized")});const K=x(()=>c.value.find(t=>t.id===i.value)),W=x(()=>D.roles.filter(t=>t.isEnabled!==!1)),Z=t=>{try{const e=g.value.find(o=>o.id===t);return e&&e.config?(console.log("获取到模型配置:",e.config),e.config):(console.log("未找到模型配置，使用默认配置"),{temperature:.8,max_tokens:8192,top_p:.8,frequency_penalty:0,presence_penalty:0,stream:!0})}catch(e){return console.error("获取模型配置失败:",e),{temperature:.8,max_tokens:8192,top_p:.8,frequency_penalty:0,presence_penalty:0,stream:!0}}},Ce=async t=>{if(I.value||t.role!=="user")return;const e=c.value.find(n=>n.id===i.value);if(!e)return;e.model_id=u.value;const o=[];if(f.value&&f.value.length>0)for(const n of f.value){const a=D.roles.find(d=>d.id===n);a&&a.prompt&&o.push(a.prompt)}const r={role:"user",content:t.content};let s=[];if(C.value){const n=o.length>0?[{role:"system",content:o.join(`

`)}]:[],a=e.messages.filter(d=>d.role!=="system").map(d=>({role:d.role,content:d.content}));s=[...n,...a]}else o.length>0&&s.push({role:"system",content:o.join(`

`)}),s.push(r);w.value.add(i.value),y.value=!0,P(()=>{$()});try{const a={stream:!0,...Z(u.value)};window.pywebview.api.model_controller.chat(i.value,u.value,s,a)}catch(n){w.value.delete(i.value),y.value=!1,console.error("Failed to resend message:",n),F(n.message||"Failed to resend message")}};return(t,e)=>{const o=Ie,r=Te;return v(),h("div",Ne,[l("aside",{class:J(["sidebar",{"sidebar-collapsed":_.value}])},[l("div",Be,[_.value?T("",!0):(v(),h("button",{key:0,class:"btn new-chat-btn",onClick:R},e[7]||(e[7]=[l("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[l("line",{x1:"12",y1:"5",x2:"12",y2:"19"}),l("line",{x1:"5",y1:"12",x2:"19",y2:"12"})],-1),l("span",null,"新建聊天",-1)]))),l("button",{class:"btn toggle-sidebar-btn",onClick:ae},[_.value?(v(),h("svg",Re,e[8]||(e[8]=[l("polyline",{points:"9 18 15 12 9 6"},null,-1)]))):(v(),h("svg",je,e[9]||(e[9]=[l("polyline",{points:"15 18 9 12 15 6"},null,-1)])))])]),l("nav",Pe,[(v(!0),h(H,null,Y(c.value,s=>(v(),h("a",{key:s.id,href:"#",class:J(["chat-list-item",{active:s.id===i.value}]),onClick:O(n=>de(s.id),["prevent"])},[_.value?(v(),h("span",Ue,e[10]||(e[10]=[l("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[l("path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"})],-1)]))):T("",!0),_.value?T("",!0):(v(),h("span",$e,B(s.title||"Untitled Chat"),1)),_.value?T("",!0):(v(),h("button",{key:2,class:"btn delete-chat-btn",onClick:O(n=>ce(s.id),["stop","prevent"])},e[11]||(e[11]=[l("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[l("path",{d:"M3 6h18"}),l("path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6"}),l("path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"})],-1)]),8,He))],10,Oe))),128))]),l("div",Je,[_.value?T("",!0):(v(),h("button",{key:0,class:"btn clear-all-btn",onClick:he},e[12]||(e[12]=[xe('<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-v-634987e9><path d="M3 6h18" data-v-634987e9></path><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6" data-v-634987e9></path><path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" data-v-634987e9></path><line x1="10" y1="11" x2="10" y2="17" data-v-634987e9></line><line x1="14" y1="11" x2="14" y2="17" data-v-634987e9></line></svg><span data-v-634987e9>清空所有聊天</span>',2)])))])],2),l("main",qe,[l("header",ze,[l("h2",Le,B(K.value?K.value.title:"新建聊天"),1),l("div",Ke,[l("div",We,[e[13]||(e[13]=l("label",null,"模型:",-1)),M(X,{modelValue:u.value,"onUpdate:modelValue":e[0]||(e[0]=s=>u.value=s),options:q.value,placeholder:"请选择模型","header-title":"选择AI模型",searchable:q.value.length>8,"max-height":"300px",onChange:ne},null,8,["modelValue","options","searchable"])]),l("div",Ze,[e[14]||(e[14]=l("label",null,"角色:",-1)),M(X,{modelValue:f.value,"onUpdate:modelValue":e[1]||(e[1]=s=>f.value=s),options:z.value,multiple:!0,placeholder:"默认角色","header-title":"选择AI角色",searchable:z.value.length>6,"max-height":"280px",onChange:re},null,8,["modelValue","options","searchable"])]),l("button",{class:J(["btn memory-toggle-btn",{active:C.value}]),onClick:me},[C.value?(v(),h("svg",Ge,e[15]||(e[15]=[l("path",{d:"M9.5 2A2.5 2.5 0 0 1 12 4.5V7H6V4.5A2.5 2.5 0 0 1 8.5 2h1Z"},null,-1),l("path",{d:"M15 10.5A2.5 2.5 0 0 1 12.5 13H12v1.5a2.5 2.5 0 0 1-5 0V13h-.5A2.5 2.5 0 0 1 4 10.5v-1A2.5 2.5 0 0 1 6.5 7H7V4.5A2.5 2.5 0 0 1 9.5 2h5A2.5 2.5 0 0 1 17 4.5V7h.5A2.5 2.5 0 0 1 20 9.5v1a2.5 2.5 0 0 1-2.5 2.5H15Z"},null,-1)]))):(v(),h("svg",Ye,e[16]||(e[16]=[l("polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2"},null,-1)]))),l("span",null,B(C.value?"记忆模式":"单次对话"),1)],2)])]),l("div",{class:"message-display-area",ref_key:"messagesContainer",ref:j},[(v(!0),h(H,null,Y(oe.value,s=>(v(),h(H,null,[s.role==="user"||s.role==="assistant"?(v(),Me(Ve,{key:s.id||s.timestamp,content:s.content,isUser:s.role==="user",isError:s.isError,timestamp:s.timestamp||Date.now(),selectedModel:u.value,reasoning:s.reasoning,reasoningTime:s.reasoningTime,onResend:n=>Ce(s)},null,8,["content","isUser","isError","timestamp","selectedModel","reasoning","reasoningTime","onResend"])):s.role==="system"?(v(),h("div",{key:"system-"+(s.id||s.timestamp),class:"system-message"}," 系统提示: "+B(s.content),1)):T("",!0)],64))),256)),y.value?(v(),h("div",Qe," AI 思考中... ")):T("",!0)],512),l("footer",Xe,[e[19]||(e[19]=l("div",{class:"input-toolbar"},[l("button",{class:"btn tool-btn",title:"Upload File (Coming Soon)",disabled:""},[l("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[l("path",{d:"M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"})])])],-1)),l("div",et,[Ae(l("textarea",{"onUpdate:modelValue":e[2]||(e[2]=s=>S.value=s),placeholder:"发送消息... (Ctrl+Enter 换行)",onKeydown:[Q(O(L,["exact","prevent"]),["enter"]),e[3]||(e[3]=Q(O(s=>S.value+=`
`,["ctrl","exact"]),["enter"]))],disabled:I.value},null,40,tt),[[Se,S.value]]),l("button",{class:"btn send-btn",onClick:e[4]||(e[4]=s=>I.value?fe():L()),disabled:!S.value.trim()&&!I.value},[I.value?(v(),h("svg",lt,e[18]||(e[18]=[l("rect",{x:"3",y:"3",width:"18",height:"18",rx:"2",ry:"2"},null,-1)]))):(v(),h("svg",st,e[17]||(e[17]=[l("path",{d:"m22 2-11 11-4-4-7 7 4 4 7-7 11-11z"},null,-1)]))),l("span",null,B(I.value?"停止":"发送"),1)],8,ot)]),e[20]||(e[20]=l("div",{class:"input-footer"},[l("span",null,"AI 可能会出错，请核实重要信息。")],-1))])]),M(r,{modelValue:E.value,"onUpdate:modelValue":e[5]||(e[5]=s=>E.value=s),title:"删除聊天",width:"400px","show-close":!1,center:""},{footer:A(()=>[l("span",at,[M(o,{onClick:ue},{default:A(()=>e[21]||(e[21]=[U("取消")])),_:1}),M(o,{type:"danger",onClick:ve},{default:A(()=>e[22]||(e[22]=[U("删除")])),_:1})])]),default:A(()=>[l("span",null,'确定要删除 "'+B(k.value?k.value.title:"这个聊天")+'" 吗？',1)]),_:1},8,["modelValue"]),M(r,{modelValue:V.value,"onUpdate:modelValue":e[6]||(e[6]=s=>V.value=s),title:"清空所有聊天",width:"400px","show-close":!1,center:""},{footer:A(()=>[l("span",nt,[M(o,{onClick:ge},{default:A(()=>e[23]||(e[23]=[U("取消")])),_:1}),M(o,{type:"danger",onClick:pe},{default:A(()=>e[24]||(e[24]=[U("清空所有")])),_:1})])]),default:A(()=>[e[25]||(e[25]=l("span",null,"确定要删除所有聊天记录吗？此操作无法撤销。",-1))]),_:1},8,["modelValue"])])}}},ft=ke(rt,[["__scopeId","data-v-634987e9"]]);export{ft as default};
