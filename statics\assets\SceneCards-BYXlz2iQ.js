import{_ as It,b,m as p,e as s,d as l,g as n,C as g,b8 as Rl,B as Pt,F as y,au as Le,h as P,t as Et,R as z,S as B,M as $,v as f,J as Mt,r as v,bt as Ve,c as K,o as Ge,E as d,$ as _t,w as yt,aR as Al,aE as Bl,aD as Jl,aO as Nl,a1 as Ul,p as ae,a9 as ht,bj as Yl,aS as wt,ad as Xl,ae as ql,aY as jl,a$ as Hl,ab as Wl,bn as Kl,ca as Zl,cb as Gl,aH as bt,bi as Ql,n as ye,W as Ct,cc as eo,cd as to,ce as lo,aw as oo,bo as no,aN as kt,q as so,s as ao,x as Ie,j as io,X as ro,ba as uo,y as co,bk as xt,k as vo,aK as po,V as mo,bm as fo,c4 as St,aA as go,aW as _o,a4 as yo,ah as he,Y as Pe}from"./entry-DxFfH4M0.js";/* empty css                      *//* empty css               *//* empty css                  *//* empty css                 *//* empty css                 *//* empty css                    *//* empty css                  *//* empty css                        *//* empty css                *//* empty css                   */import{u as ho}from"./book-mHAKpUpK.js";import{d as wo}from"./vuedraggable-umd-CerWkR59.js";const bo={class:"scene-card"},Co={class:"card-header"},ko={class:"card-icon"},xo={class:"card-title"},So={class:"card-actions"},Vo={class:"card-content"},Io={class:"card-description"},Po={class:"card-tags"},Eo={__name:"Card",props:{title:{type:String,default:"未命名场景"},description:{type:String,default:""},tags:{type:Array,default:()=>[]}},emits:["delete"],setup(Ee){return(ie,re)=>{const ee=Pt,Me=Et,De=Mt;return p(),b("div",bo,[s("div",Co,[s("div",ko,[l(ee,null,{default:n(()=>[l(g(Rl))]),_:1})]),s("div",xo,y(Ee.title),1),s("div",So,[l(Me,{type:"danger",size:"small",circle:"",onClick:re[0]||(re[0]=P(Z=>ie.$emit("delete"),["stop"])),class:"delete-btn"},{default:n(()=>[l(ee,null,{default:n(()=>[l(g(Le))]),_:1})]),_:1})])]),s("div",Vo,[s("div",Io,y(Ee.description),1),s("div",Po,[(p(!0),b(z,null,B(Ee.tags,Z=>(p(),$(De,{key:Z,size:"small",effect:"light",class:"card-tag"},{default:n(()=>[f(y(Z),1)]),_:2},1024))),128))])])])}}},Vt=It(Eo,[["__scopeId","data-v-b425cdbb"]]),Mo={class:"scene-cards-container"},Do={class:"action-bar"},To={class:"pool-option"},zo={class:"pool-info"},$o={class:"pool-option"},Fo={class:"pool-name"},Oo={class:"pool-info"},Lo={class:"dropdown-item-content"},Ro={class:"dropdown-item-content"},Ao={class:"dropdown-item-content"},Bo={class:"dropdown-item-content"},Jo={class:"dropdown-item-content"},No=["id"],Uo=["onMousedown","onDblclick","onMouseover","onMouseleave"],Yo={class:"canvas-controls"},Xo={class:"zoom-display"},qo={class:"custom-dialog-header"},jo={class:"custom-dialog-body"},Ho={class:"custom-dialog-footer"},Wo={class:"draw-result"},Ko={class:"draw-content"},Zo={class:"scenes-section"},Go={class:"drawn-cards"},Qo={class:"scene-number"},en={key:0,class:"scene-connector"},tn={class:"combination-hint"},ln={class:"inspiration-section"},on={class:"inspiration-input"},nn={class:"inspiration-meta"},sn={class:"inspiration-rating"},an={class:"inspiration-actions"},rn={class:"dialog-header-content"},un=["id"],dn={class:"history-count"},cn={class:"history-container"},vn={class:"history-content"},pn={class:"history-list"},mn={class:"history-list-content"},fn=["onClick"],gn={class:"history-header"},_n={class:"header-main"},yn={class:"history-meta-info"},hn={class:"history-time"},wn={class:"related-pools-tooltip"},bn={class:"history-brief"},Cn={class:"history-meta"},kn={class:"history-tags"},xn={class:"history-detail-panel"},Sn={class:"history-scenes"},Vn={class:"scenes-title"},In={class:"scenes-list"},Pn={class:"scene-number"},En={class:"scene-title"},Mn={key:0,class:"scene-description"},Dn={class:"scene-tags"},Tn={key:1,class:"scene-connector"},zn={class:"history-inspiration"},$n={class:"inspiration-header"},Fn={class:"inspiration-title"},On={class:"inspiration-content"},Ln={key:1,class:"empty-detail"},Rn={class:"history-footer"},An={class:"history-pagination"},Bn={class:"dialog-footer"},Jn={class:"dialog-footer"},Nn={class:"dialog-footer"},Un={class:"manage-pools-container"},Yn={class:"pools-toolbar"},Xn={class:"search-wrapper"},qn={class:"pools-actions"},jn={class:"pool-item-content"},Hn={class:"pool-item-name"},Wn={class:"pool-item-info"},Kn={class:"update-time"},Zn={class:"pool-item-actions"},Gn={class:"dialog-footer"},Qn={class:"dialog-footer"},es=5,ts={__name:"SceneCards",setup(Ee){const ie=ho(),re=v(null),ee=v(null),Me=Ve({x:0,y:0}),De=Ve({x:0,y:0}),Z=v(!1),M=v(1),Dt=t=>{let e=t;typeof t=="string"&&(e=h.value.find(a=>a.id===t)),e&&(M.value=Math.max(M.value,...h.value.map(a=>a.zIndex||0)),M.value++,e.zIndex=M.value,V())},c=v({pools:[],currentPoolId:null}),m=K(()=>c.value.currentPoolId==="all"?Qe.value:c.value.pools.find(t=>t.id===c.value.currentPoolId)||null),Qe=K(()=>({id:"all",name:"全部场景",scenes:c.value.pools.reduce((e,a)=>{const u=JSON.parse(JSON.stringify(a.scenes||[]));return u.forEach(i=>{i.sourcePool={id:a.id,name:a.name}}),[...e,...u]},[]),isVirtual:!0,createTime:Date.now(),updateTime:Date.now()})),h=K({get:()=>m.value?.scenes||[],set:t=>{m.value&&!m.value.isVirtual&&(m.value.scenes=t,V())}}),we=v(2),L=v("");Ge(async()=>{try{await ie.loadBooks(),ie.bookList?.length>0&&(L.value=ie.bookList[0].id,await Re())}catch(t){console.error("初始化失败:",t),d.error("初始化失败，请刷新页面重试")}});const S=v(null),et=K(()=>c.value.pools.reduce((e,a)=>{if(Array.isArray(a.inspirations)){const u=a.inspirations.map(i=>i.isFromAllPool?{...i,poolName:i.mainPoolName||a.name,poolId:i.mainPoolId||a.id,fromAllPool:!0,relatedPools:i.relatedPools||[]}:{...i,poolName:a.name,poolId:a.id,fromAllPool:!1,relatedPools:[]});e.push(...u)}return e},[]).sort((e,a)=>a.timestamp-e.timestamp)),Re=async()=>{if(L.value)try{const t=await window.pywebview.api.book_controller.get_scene_events(L.value),e=typeof t=="string"?JSON.parse(t):t;if(e.status==="success"){const a=e.data||{};c.value={pools:Array.isArray(a.pools)?a.pools:[],currentPoolId:a.currentPoolId||null},M.value=1,c.value.pools.forEach(u=>{Array.isArray(u.scenes)&&u.scenes.forEach(i=>{i.zIndex?M.value=Math.max(M.value,i.zIndex):(i.zIndex=M.value,M.value++)})}),c.value.pools.length||tt("默认卡池"),!c.value.currentPoolId&&c.value.pools.length>0&&(c.value.currentPoolId=c.value.pools[0].id)}else throw new Error(e.message||"加载失败")}catch(t){console.error("加载场景失败:",t),d.error(`加载场景失败: ${t.message}`),c.value={pools:[],currentPoolId:null},M.value=1}},V=async()=>{Array.isArray(c.value.pools)||(c.value.pools=[]);try{const t=await window.pywebview.api.book_controller.save_scene_events(L.value,JSON.parse(JSON.stringify(c.value))),e=typeof t=="string"?JSON.parse(t):t;if(e.status==="success")return!0;throw new Error(e.message||"保存失败")}catch(t){throw console.error("保存场景数据失败:",t),t}},tt=t=>{Array.isArray(c.value.pools)||(c.value.pools=[]);const e={id:Date.now().toString(),name:t,createTime:Date.now(),updateTime:Date.now(),scenes:[],inspirations:[]};c.value.pools.push(e),c.value.currentPoolId=e.id,V()},lt=t=>{if(!ee.value||!re.value)return;Math.sqrt(Math.pow(t.clientX-De.x,2)+Math.pow(t.clientY-De.y,2))>5&&(Z.value=!0);const a=re.value.getBoundingClientRect(),u=re.value.scrollTop;ee.value.position={left:t.clientX-a.left-Me.x,top:t.clientY-a.top-Me.y+u}},ot=()=>{ee.value&&Z.value&&V(),ee.value=null,Z.value=!1,document.removeEventListener("mousemove",lt),document.removeEventListener("mouseup",ot)};_t(()=>{document.removeEventListener("mousemove",lt),document.removeEventListener("mouseup",ot)});const Tt=v(["战斗","对话","探索","追逐","相遇","告别","冲突","和解","发现","选择"]),ue=v(!1),Te=v(!1),de=v(!1),_=Ve({id:"",title:"",description:"",tags:[]}),ce=v([]),F=v({content:"",rating:80,isUsed:!1}),ze=v(!1),zt=()=>{if(m.value?.isVirtual){d.warning('不能在"全部场景"卡池中创建场景');return}de.value=!1,_.id="",_.title="",_.description="",_.tags=[],ue.value=!0,setTimeout(()=>{const t=document.querySelector(".custom-dialog input");t&&t.focus()},0)},$t=(t,e)=>{if(!Z.value){if(m.value?.isVirtual&&e.sourcePool){c.value.currentPoolId=e.sourcePool.id,d.success(`已切换到场景所在卡池：${e.sourcePool.name}`),setTimeout(()=>{const a=h.value.find(u=>u.id===e.id);a&&(de.value=!0,_.id=a.id,_.title=a.title,_.description=a.description,_.tags=[...a.tags],ue.value=!0,setTimeout(()=>{const u=document.querySelector(".custom-dialog input");u&&u.focus()},0))},0);return}de.value=!0,_.id=e.id,_.title=e.title,_.description=e.description,_.tags=[...e.tags],ue.value=!0,setTimeout(()=>{const a=document.querySelector(".custom-dialog input");a&&a.focus()},0)}},Ft=t=>{he.confirm("确定要删除这个场景吗？","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",appendTo:D.value||document.body}).then(async()=>{const e=h.value.findIndex(a=>a.id===t.id);if(e!==-1){h.value.splice(e,1);try{await V(),d.success("场景已删除")}catch{d.error("删除失败，请重试")}}}).catch(()=>{})},nt=async()=>{if(!_.title.trim()){d.warning("请输入场景标题");return}if(de.value){const t=h.value.findIndex(e=>e.id===_.id);if(t!==-1){const e=h.value[t];e.title=_.title,e.description=_.description,e.tags=_.tags}}else{const t={id:Date.now().toString(),title:_.title,description:_.description,tags:_.tags,x:Math.random()*500,y:Math.random()*300,zIndex:M.value+1};h.value.push(t),M.value++}try{await V(),ue.value=!1,d.success(de.value?"场景已更新":"场景已创建")}catch{d.error("保存失败，请重试")}},Ae=t=>new Date(t).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}),Ot=async()=>{if(!F.value.content.trim())return;const t={timestamp:Date.now(),scenes:ce.value,content:F.value.content,rating:F.value.rating,isUsed:F.value.isUsed};if(m.value?.isVirtual){const e=ce.value.reduce((i,r)=>(r.sourcePool&&(i[r.sourcePool.id]||(i[r.sourcePool.id]={poolName:r.sourcePool.name,scenes:[]}),i[r.sourcePool.id].scenes.push(r)),i),{}),a=Object.values(e).map(i=>({name:i.poolName,sceneCount:i.scenes.length})),u=ce.value[0];if(u?.sourcePool){const i=c.value.pools.find(r=>r.id===u.sourcePool.id);if(i){const r={...t,isFromAllPool:!0,relatedPools:a,mainPoolId:i.id,mainPoolName:i.name};Array.isArray(i.inspirations)||(i.inspirations=[]),i.inspirations.unshift(r),i.updateTime=Date.now()}}}else{if(!m.value)return;Array.isArray(m.value.inspirations)||(m.value.inspirations=[]),m.value.inspirations.unshift(t),m.value.updateTime=Date.now()}await V()&&(d.success("灵感已保存"),Te.value=!1,F.value={content:"",rating:80,isUsed:!1})},Lt=()=>{if(h.value.length<we.value){d.warning(`需要至少${we.value}个场景才能进行随机抽取`);return}const t=[...h.value].sort(()=>.5-Math.random());ce.value=t.slice(0,we.value),F.value={content:"",rating:80,isUsed:!1},Te.value=!0},st=v(null),Rt=async()=>{try{const t=JSON.stringify(c.value,null,2),e=await window.pywebview.api.book_controller.export_scenes(t),a=typeof e=="string"?JSON.parse(e):e;a.status==="success"?d.success("导出成功"):d.error(`导出失败: ${a.message}`)}catch(t){d.error(`导出出错: ${t.message}`)}},At=()=>{st.value.click()},Bt=async t=>{const e=t.target.files[0];if(!e)return;const a=new FileReader;a.onload=async u=>{try{const i=JSON.parse(u.target.result);if(!Array.isArray(i.pools))throw new Error("无效的场景数据格式");he.confirm("导入将覆盖当前场景数据，是否继续？","导入确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",appendTo:D.value||document.body}).then(async()=>{let r=1;i.pools.forEach(x=>{Array.isArray(x.scenes)&&x.scenes.forEach(k=>{k.zIndex?r=Math.max(r,k.zIndex+1):k.zIndex=r++})}),c.value=i,M.value=r;try{await V(),d.success("场景数据导入成功")}catch{d.error("保存失败，请重试"),await Re()}}).catch(()=>{})}catch(i){d.error(`导入失败：${i.message}`)}t.target.value=""},a.readAsText(e)},te=Ve({name:""}),be=v(!1),Jt=t=>{t.stopPropagation();const e=document.querySelector(".pool-select input");e&&e.blur(),setTimeout(()=>{te.name="",be.value=!0,setTimeout(()=>{const a=document.querySelector(".el-dialog input");a&&a.focus()},0)},0)},$e=()=>{if(!te.name.trim()){d.warning("请输入卡池名称");return}tt(te.name.trim()),be.value=!1,d.success("卡池创建成功")},Nt=async()=>{if(c.value={pools:[],currentPoolId:null},!!L.value)try{await Re()}catch(t){console.error("加载场景失败:",t),d.error(`加载场景失败: ${t.message}`)}},ve=v(1),Fe=v(5),at=K(()=>et.value.length),it=K(()=>{const t=(ve.value-1)*Fe.value,e=t+Fe.value;return et.value.slice(t,e)}),Ut=t=>{Fe.value=t,ve.value=1,S.value=null},Yt=t=>{ve.value=t,S.value=null},Xt=t=>{S.value=S.value===t?null:t};yt(ze,t=>{t||(S.value=null,ve.value=1)}),yt([L,()=>c.value.currentPoolId],()=>{S.value=null,ve.value=1});const rt=t=>t>=90?"danger":t>=80?"success":t>=60?"warning":"info",ut=t=>{he.confirm(`确定要删除卡池"${t.name}"吗？此操作将永久删除该卡池及其所有场景和灵感记录。`,"删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning",confirmButtonClass:"el-button--danger",appendTo:D.value||document.body}).then(()=>{qt(t)}).catch(()=>{})},qt=t=>{const e=c.value.pools.findIndex(a=>a.id===t.id);e!==-1&&(c.value.pools.splice(e,1),t.id===c.value.currentPoolId&&(c.value.currentPoolId=c.value.pools[0]?.id||null),V().then(()=>{d.success("卡池删除成功")}))},jt=t=>{he.confirm("确定要删除这条灵感记录吗？此操作不可恢复。","删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning",confirmButtonClass:"el-button--danger",appendTo:D.value||document.body}).then(()=>{Ht(t)}).catch(()=>{})},Ht=t=>{const e=c.value.pools.find(a=>a.id===t.poolId);if(e&&Array.isArray(e.inspirations)){const a=e.inspirations.findIndex(u=>u.timestamp===t.timestamp);a!==-1&&(e.inspirations.splice(a,1),e.updateTime=Date.now(),V().then(()=>{d.success("灵感记录已删除"),S.value===t&&(S.value=null)}).catch(()=>{d.error("删除失败，请重试")}))}},Wt=()=>{te.name=""},Kt=()=>{_.id="",_.title="",_.description="",_.tags=[]},Be=()=>{ue.value=!1,Kt()},Zt=t=>{t.target===t.currentTarget&&Be()},Gt=async t=>{try{t==="exportAll"?await Rt():t==="exportPool"&&await Qt()}catch(e){d.error(`导出失败: ${e.message}`)}},Qt=async()=>{if(!m.value||m.value.isVirtual){d.warning("请选择一个有效的卡池");return}try{const t={id:m.value.id,name:m.value.name,scenes:m.value.scenes,createTime:m.value.createTime,updateTime:m.value.updateTime};await window.pywebview.api.copy_to_clipboard(JSON.stringify(t,null,2)),d.success("卡池数据已复制到剪贴板")}catch(t){console.error("导出卡池失败:",t),d.error("导出失败，请重试")}},el=t=>{t==="importFile"?At():t==="importJson"?tl():t==="mergeJson"&&ol()},pe=v(!1),q=v({jsonContent:""}),tl=()=>{q.value.jsonContent="",pe.value=!0},Je=async()=>{if(!q.value.jsonContent.trim()){d.warning("请输入要导入的JSON数据");return}try{const t=JSON.parse(q.value.jsonContent);if(!t.scenes||!Array.isArray(t.scenes))throw new Error("无效的场景数据格式");const e=q.value.jsonContent;pe.value=!1;try{await he.confirm("导入将覆盖当前卡池的场景数据，是否继续？","导入确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",appendTo:D.value||document.body}),m.value&&!m.value.isVirtual&&(m.value.scenes=t.scenes,m.value.updateTime=Date.now(),await V(),d.success("场景数据导入成功"))}catch{q.value.jsonContent=e,pe.value=!0}}catch(t){console.error("导入JSON失败:",t),d.error(`导入失败：${t.message}`)}},ll=()=>{q.value.jsonContent=""},me=v(!1),j=v({jsonContent:""}),ol=()=>{j.value.jsonContent="",me.value=!0},Ne=async()=>{if(!j.value.jsonContent.trim()){d.warning("请输入要融合的JSON数据");return}try{const t=JSON.parse(j.value.jsonContent);if(!t.scenes||!Array.isArray(t.scenes))throw new Error("无效的场景数据格式");const e=j.value.jsonContent;me.value=!1;try{if(await he.confirm("导入将添加新场景到当前卡池，可能会根据ID去除重复场景。是否继续？","融合确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",appendTo:D.value||document.body}),m.value&&!m.value.isVirtual){Array.isArray(m.value.scenes)||(m.value.scenes=[]);const a=m.value.scenes.map(i=>i.id),u=t.scenes.filter(i=>!a.includes(i.id));u.length>0?(u.forEach(i=>{i.position||(i.position={left:Math.random()*560,top:Math.random()*320}),M.value++,i.zIndex=M.value}),m.value.scenes=[...m.value.scenes,...u],m.value.updateTime=Date.now(),await V(),d.success(`成功融合 ${u.length} 个新场景到当前卡池`)):d.info("没有新的场景需要融合，所有场景ID已存在")}}catch{j.value.jsonContent=e,me.value=!0}}catch(t){console.error("融合JSON失败:",t),d.error(`融合失败：${t.message}`)}},nl=()=>{j.value.jsonContent=""},sl=K(()=>[...c.value.pools]),fe=v(!1),al=t=>{t.stopPropagation(),t.preventDefault();const e=document.querySelector(".el-select__popper");e&&(e.classList.add("el-popper--hidden"),e.classList.remove("el-popper--visible"));const a=document.querySelector(".pool-select .el-select");if(a){a.classList.remove("is-focus");const u=a.querySelector("input");u&&u.blur()}setTimeout(()=>{fe.value=!0},50)},il=()=>{document.body.style.overflow="hidden",Pe(()=>{oe.value&&oe.value.setScrollTop(0)})},rl=()=>{document.body.style.overflow="",le.value=""},ul=async()=>{try{await V(),d.success("卡池顺序已保存")}catch(t){d.error("保存顺序失败，请重试"),console.error("保存卡池顺序失败:",t)}},dl=t=>{c.value.currentPoolId=t.id,fe.value=!1,d.success(`已切换到卡池：${t.name}`)},Ce=v(!1),N=Ve({id:"",name:""}),dt=v(null),cl=t=>{dt.value=t,N.id=t.id,N.name=t.name,Ce.value=!0,Pe(()=>{const e=document.querySelector(".rename-pool-dialog .el-input__inner");e&&(e.focus(),e.select())})},ct=async()=>{if(!N.name.trim()){d.warning("请输入卡池名称");return}const t=c.value.pools.find(e=>e.id===N.id);if(t){t.name=N.name.trim(),t.updateTime=Date.now();try{await V(),d.success("卡池已重命名"),Ce.value=!1}catch(e){d.error("重命名失败，请重试"),console.error("重命名卡池失败:",e)}}},vl=()=>{dt.value=null,N.id="",N.name=""},le=v(""),oe=v(null),pl=async t=>{if(t>0){const e=c.value.pools.splice(t,1)[0];c.value.pools.unshift(e);try{await V(),d.success("卡池已置顶")}catch(a){d.error("操作失败，请重试"),console.error("卡池置顶失败:",a)}}},ml=()=>{if(oe.value){let i=function(x){const k=x-u,R=Math.min(k/a,1),Q=1-Math.pow(1-R,3);t.scrollTop=e*(1-Q),R<1&&requestAnimationFrame(i)};const t=oe.value.$el.querySelector(".el-scrollbar__wrap");if(!t)return;const e=t.scrollTop;if(e===0)return;const a=Math.min(Math.max(e*.5,300),800),u=performance.now();requestAnimationFrame(i);const r=document.querySelector(".scroll-top-btn");r&&(r.classList.add("is-scrolling"),setTimeout(()=>{r.classList.remove("is-scrolling")},300))}},fl=K(()=>{let t=[...c.value.pools];if(le.value.trim()){const e=le.value.toLowerCase().trim();t=t.filter(a=>a.name.toLowerCase().includes(e))}return t}),gl=()=>{Pe(()=>{oe.value&&oe.value.setScrollTop(0)})},_l=t=>{if(!le.value.trim())return!1;const e=le.value.toLowerCase().trim();return t.name.toLowerCase().includes(e)},D=v(null),ke=v(null),U=v(0),Y=v(0),C=v(1),ge=v(!1),Ue=v(0),Ye=v(0),ne=v(!0),xe=v(null),T=v(null),Xe=v(null),qe=v(!1),yl=v(0),je=v({x:0,y:0}),hl=K(()=>({transform:`translate(${U.value}px, ${Y.value}px) scale(${C.value})`}));Ge(()=>{h.value&&h.value.length>0&&(h.value=h.value.map($l),V()),ne.value=!0,setTimeout(()=>{ne.value=!1},1e3)});const wl=t=>{t.button===2&&(ge.value=!0,Ue.value=t.clientX-U.value,Ye.value=t.clientY-Y.value,ne.value=!0,ke.value&&(ke.value.style.cursor="grabbing"),t.preventDefault())},bl=t=>{ge.value&&(U.value=t.clientX-Ue.value,Y.value=t.clientY-Ye.value,t.preventDefault())},Cl=t=>{ge.value&&(ge.value=!1,ke.value&&(ke.value.style.cursor="default"),setTimeout(()=>{ne.value=!1},1e3))},kl=t=>{t.preventDefault();const e=D.value.getBoundingClientRect(),a=t.clientX-e.left,u=t.clientY-e.top,i=(a-U.value)/C.value,r=(u-Y.value)/C.value,x=.1;t.deltaY<0?C.value=Math.min(2,C.value+x):C.value=Math.max(.3,C.value-x);const k=(a-U.value)/C.value,R=(u-Y.value)/C.value;U.value+=(k-i)*C.value,Y.value+=(R-r)*C.value,ne.value=!0,setTimeout(()=>{ne.value=!1},1e3)},xl=()=>{C.value=Math.min(2,C.value+.1)},Sl=()=>{C.value=Math.max(.3,C.value-.1)},Vl=()=>{if(!h.value.length){d.info("没有场景卡片需要排版");return}if(m.value?.isVirtual){d.warning('不能在"全部场景"卡池中进行自动排版');return}const t=D.value;if(!t)return;const e=t.getBoundingClientRect(),a=e.width,u=e.height,i=240,r=280,x=20,k=16,R=a/C.value-x*2,Q=u/C.value-x*2,O=Math.floor((R+k)/(i+k)),E=Math.max(1,Math.min(O,h.value.length)),J=Math.ceil(h.value.length/E),_e=E*i+(E-1)*k,Se=J*r+(J-1)*k,Ke=(R-_e)/2+x,H=(Q-Se)/2+x;h.value.forEach((I,A)=>{const W=Math.floor(A/E),se=A%E;I.x=Ke+se*(i+k),I.y=H+W*(r+k)}),V().then(()=>{d.success(`已自动排版 ${h.value.length} 个场景卡片`)}).catch(()=>{d.error("自动排版失败，请重试")}),Pe(()=>{if(h.value.length>0){let I=1/0,A=1/0,W=-1/0,se=-1/0;h.value.forEach(mt=>{const ft=mt.x||0,gt=mt.y||0,Ol=i,Ll=r;I=Math.min(I,ft),A=Math.min(A,gt),W=Math.max(W,ft+Ol),se=Math.max(se,gt+Ll)});const Ze=(I+W)/2,o=(A+se)/2,w=e.width/2,X=e.height/2;U.value=w-Ze*C.value,Y.value=X-o*C.value}})},Il=()=>{U.value=0,Y.value=0,C.value=1,D.value&&Pe(()=>{const e=D.value.getBoundingClientRect();if(h.value.length>0){let a=1/0,u=1/0,i=-1/0,r=-1/0;h.value.forEach(O=>{const E=O.x||0,J=O.y||0,_e=240,Se=280;a=Math.min(a,E),u=Math.min(u,J),i=Math.max(i,E+_e),r=Math.max(r,J+Se)});const x=(a+i)/2,k=(u+r)/2,R=e.width/2,Q=e.height/2;U.value=R-x,Y.value=Q-k}})},Pl=(t,e)=>{xe.value=t,e.stopPropagation()},El=()=>{xe.value=null},Ml=(t,e)=>{e.button!==0||ge.value||(yl.value=Date.now(),je.value={x:e.clientX,y:e.clientY},qe.value=!1,xe.value=t,Xe.value&&(clearTimeout(Xe.value),Xe.value=null),document.addEventListener("mousemove",He),document.addEventListener("mouseup",We))},He=t=>{if(qe.value)return;const e=Math.abs(t.clientX-je.value.x),a=Math.abs(t.clientY-je.value.y);Math.sqrt(e*e+a*a)>es&&!T.value&&Dl(t),T.value&&vt(t)},We=t=>{document.removeEventListener("mousemove",He),document.removeEventListener("mouseup",We),T.value&&pt()},Dl=t=>{const e=xe.value;if(!e)return;T.value=e;const a=document.getElementById(`scene-card-${e}`);a&&a.classList.add("dragging");const u=h.value.find(r=>r.id===e),i=document.getElementById(`scene-card-wrapper-${e}`);if(u&&i){const r=i.getBoundingClientRect(),x=t.clientX-r.left,k=t.clientY-r.top;Ue.value=t.clientX,Ye.value=t.clientY,u.startX=u.x!==void 0?u.x:u.position?.left||0,u.startY=u.y!==void 0?u.y:u.position?.top||0,u.mouseOffsetX=x,u.mouseOffsetY=k}},Tl=(t,e)=>{if(qe.value=!0,T.value){const a=document.getElementById(`scene-card-${T.value}`);a&&a.classList.remove("dragging"),T.value=null}document.removeEventListener("mousemove",He),document.removeEventListener("mouseup",We),$t(e,t)},vt=t=>{if(T.value){const e=h.value.find(a=>a.id===T.value);if(e){const a=D.value.getBoundingClientRect(),u=(t.clientX-a.left-U.value)/C.value,i=(t.clientY-a.top-Y.value)/C.value;e.x=u-e.mouseOffsetX/C.value,e.y=i-e.mouseOffsetY/C.value}}},pt=()=>{if(T.value){const t=document.getElementById(`scene-card-${T.value}`);t&&t.classList.remove("dragging"),Dt(T.value),V(),T.value=null,document.removeEventListener("mousemove",vt),document.removeEventListener("mouseup",pt)}},zl=t=>{const e=t.x!==void 0?t.x:t.position?.left||0,a=t.y!==void 0?t.y:t.position?.top||0;return{left:`${e}px`,top:`${a}px`,zIndex:t.zIndex||0}},$l=t=>(t.position?(t.x=t.position.left||0,t.y=t.position.top||0):(typeof t.x>"u"||typeof t.y>"u")&&(t.x=Math.random()*500,t.y=Math.random()*300),t.zIndex||(t.zIndex=1),t),Oe=v(!1),Fl=()=>{if(Oe.value)document.exitFullscreen?document.exitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.webkitExitFullscreen?document.webkitExitFullscreen():document.msExitFullscreen&&document.msExitFullscreen();else{const t=D.value;t.requestFullscreen?t.requestFullscreen():t.mozRequestFullScreen?t.mozRequestFullScreen():t.webkitRequestFullscreen?t.webkitRequestFullscreen():t.msRequestFullscreen&&t.msRequestFullscreen()}};Ge(()=>{document.addEventListener("fullscreenchange",G),document.addEventListener("webkitfullscreenchange",G),document.addEventListener("mozfullscreenchange",G),document.addEventListener("MSFullscreenChange",G)}),_t(()=>{document.removeEventListener("fullscreenchange",G),document.removeEventListener("webkitfullscreenchange",G),document.removeEventListener("mozfullscreenchange",G),document.removeEventListener("MSFullscreenChange",G)});const G=()=>{Oe.value=!!document.fullscreenElement||!!document.webkitFullscreenElement||!!document.mozFullScreenElement||!!document.msFullscreenElement};return(t,e)=>{const a=Bl,u=Jl,i=Pt,r=Et,x=Nl,k=ql,R=Xl,Q=Wl,O=ao,E=so,J=io,_e=Al("Right"),Se=uo,Ke=co,H=vo,I=Mt,A=po,W=mo,se=fo,Ze=go;return p(),b("div",Mo,[s("div",Do,[l(u,{modelValue:L.value,"onUpdate:modelValue":e[0]||(e[0]=o=>L.value=o),class:"book-select",size:"large",placeholder:"请选择书籍",onChange:Nt},{default:n(()=>[(p(!0),b(z,null,B(g(ie).bookList,o=>(p(),$(a,{key:o.id,label:o.title,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),l(u,{modelValue:c.value.currentPoolId,"onUpdate:modelValue":e[1]||(e[1]=o=>c.value.currentPoolId=o),class:"pool-select",size:"large",placeholder:"请选择场景卡池",disabled:!L.value},{prefix:n(()=>[l(r,{class:"create-pool-btn",link:"",onClick:P(Jt,["stop"]),disabled:!L.value},{default:n(()=>[l(i,null,{default:n(()=>[l(g(ht))]),_:1})]),_:1},8,["disabled"])]),default:n(()=>[l(a,{key:"all",label:"全部场景",value:"all"},{default:n(()=>[s("div",To,[e[31]||(e[31]=s("span",{class:"pool-name"},"全部场景",-1)),s("span",zo,y(Qe.value.scenes.length)+"个场景 ",1)])]),_:1}),l(x,{"content-position":"center"},{default:n(()=>[e[32]||(e[32]=f(" 其他卡池 ")),l(r,{class:"manage-pools-btn",link:"",onClick:P(al,["stop"]),disabled:!L.value},{default:n(()=>[l(i,null,{default:n(()=>[l(g(Ul))]),_:1})]),_:1},8,["disabled"])]),_:1}),(p(!0),b(z,null,B(sl.value,o=>(p(),$(a,{key:o.id,label:o.name,value:o.id},{default:n(()=>[s("div",$o,[s("span",Fo,y(o.name),1),s("span",Oo,y(o.scenes.length)+"个场景 | "+y(Ae(o.updateTime)),1),c.value.pools.length>1?(p(),$(r,{key:0,class:"delete-pool-btn",type:"danger",link:"",onClick:P(w=>ut(o),["stop"])},{default:n(()=>[l(i,null,{default:n(()=>[l(g(Le))]),_:1})]),_:2},1032,["onClick"])):ae("",!0)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled"]),l(r,{type:"primary",onClick:zt,size:"large",disabled:!m.value},{default:n(()=>[l(i,null,{default:n(()=>[l(g(ht))]),_:1}),e[33]||(e[33]=f(" 创建场景 "))]),_:1},8,["disabled"]),l(u,{modelValue:we.value,"onUpdate:modelValue":e[2]||(e[2]=o=>we.value=o),class:"draw-count-select",size:"large",disabled:h.value.length<2},{default:n(()=>[(p(!0),b(z,null,B(Math.min(5,h.value.length),o=>(p(),$(a,{key:o,label:`抽取${o}个场景`,value:o},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"]),l(r,{onClick:Lt,size:"large",disabled:h.value.length<2},{default:n(()=>e[34]||(e[34]=[f(" 随机抽取 ")])),_:1},8,["disabled"]),l(Q,{onCommand:Gt,trigger:"click"},{dropdown:n(()=>[l(R,null,{default:n(()=>[l(k,{command:"exportAll"},{default:n(()=>[s("div",Lo,[l(i,null,{default:n(()=>[l(g(jl))]),_:1}),e[36]||(e[36]=s("span",null,"导出全部场景",-1))])]),_:1}),l(k,{command:"exportPool",disabled:!m.value||m.value.isVirtual},{default:n(()=>[s("div",Ro,[l(i,null,{default:n(()=>[l(g(Hl))]),_:1}),e[37]||(e[37]=s("span",null,"复制当前卡池",-1))])]),_:1},8,["disabled"])]),_:1})]),default:n(()=>[l(r,{size:"large",disabled:h.value.length===0},{default:n(()=>[l(i,null,{default:n(()=>[l(g(Yl))]),_:1}),e[35]||(e[35]=f(" 导出场景 ")),l(i,{class:"el-icon--right"},{default:n(()=>[l(g(wt))]),_:1})]),_:1},8,["disabled"])]),_:1}),l(Q,{onCommand:el,trigger:"click"},{dropdown:n(()=>[l(R,null,{default:n(()=>[l(k,{command:"importFile"},{default:n(()=>[s("div",Ao,[l(i,null,{default:n(()=>[l(g(Zl))]),_:1}),e[39]||(e[39]=s("span",null,"导入全部场景",-1))])]),_:1}),l(k,{command:"importJson",disabled:!m.value||m.value.isVirtual},{default:n(()=>[s("div",Bo,[l(i,null,{default:n(()=>[l(g(Gl))]),_:1}),e[40]||(e[40]=s("span",null,"导入到当前卡池",-1))])]),_:1},8,["disabled"]),l(k,{command:"mergeJson",disabled:!m.value||m.value.isVirtual},{default:n(()=>[s("div",Jo,[l(i,null,{default:n(()=>[l(g(bt))]),_:1}),e[41]||(e[41]=s("span",null,"融合到当前卡池",-1))])]),_:1},8,["disabled"])]),_:1})]),default:n(()=>[l(r,{size:"large"},{default:n(()=>[l(i,null,{default:n(()=>[l(g(Kl))]),_:1}),e[38]||(e[38]=f(" 导入场景 ")),l(i,{class:"el-icon--right"},{default:n(()=>[l(g(wt))]),_:1})]),_:1})]),_:1}),s("input",{type:"file",ref_key:"fileInput",ref:st,style:{display:"none"},accept:".json",onChange:Bt},null,544),l(r,{onClick:e[3]||(e[3]=o=>ze.value=!0),size:"large",disabled:!L.value},{default:n(()=>[l(i,null,{default:n(()=>[l(g(Ql))]),_:1}),e[42]||(e[42]=f(" 灵感历史 "))]),_:1},8,["disabled"])]),s("div",{class:"cards-grid",onMousedown:wl,onMouseup:Cl,onMousemove:bl,onContextmenu:e[4]||(e[4]=P(()=>{},["prevent"])),onWheel:kl,ref_key:"cardsGridRef",ref:D},[s("div",{class:ye(["drag-hint",{visible:ne.value}])},e[43]||(e[43]=[f(" 按住 "),s("span",{class:"key-hint"},"右键",-1),f(" 拖动画布 | 使用 "),s("span",{class:"key-hint"},"滚轮",-1),f(" 缩放 ")]),2),s("div",{class:ye(["infinite-canvas",{dragging:ge.value}]),style:Ct(hl.value),ref_key:"infiniteCanvasRef",ref:ke},[(p(!0),b(z,null,B(h.value,o=>(p(),b("div",{key:o.id,class:"scene-card-wrapper",id:`scene-card-wrapper-${o.id}`,style:Ct(zl(o))},[s("div",{class:"scene-card-interaction-area",onMousedown:P(w=>Ml(o.id,w),["stop"]),onDblclick:P(w=>Tl(o,w),["stop"]),onMouseover:P(w=>Pl(o.id,w),["stop"]),onMouseleave:P(w=>El(o.id),["stop"])},[l(Vt,{title:o.title,description:o.description,tags:o.tags,onDelete:w=>Ft(o),class:ye({active:xe.value===o.id,dragging:T.value===o.id}),id:`scene-card-${o.id}`},null,8,["title","description","tags","onDelete","class","id"])],40,Uo)],12,No))),128))],6),s("div",Yo,[s("div",{class:"control-btn",onClick:xl},[l(i,null,{default:n(()=>[l(g(eo))]),_:1})]),s("div",Xo,y(Math.round(C.value*10))+"%",1),s("div",{class:"control-btn",onClick:Sl},[l(i,null,{default:n(()=>[l(g(to))]),_:1})]),s("div",{class:"control-btn",onClick:Vl,title:"自动排版"},[l(i,null,{default:n(()=>[l(g(lo))]),_:1})]),s("div",{class:"control-btn",onClick:Il},[l(i,null,{default:n(()=>[l(g(oo))]),_:1})]),s("div",{class:"control-btn",onClick:Fl},[Oe.value?(p(),$(i,{key:1},{default:n(()=>[l(g(kt))]),_:1})):(p(),$(i,{key:0},{default:n(()=>[l(g(no))]),_:1}))])])],544),(p(),$(ro,{to:Oe.value?D.value:"body"},[ue.value?(p(),b("div",{key:0,class:"custom-dialog-overlay",onClick:Zt},[s("div",{class:"custom-dialog custom-dialog-medium",onClick:e[8]||(e[8]=P(()=>{},["stop"]))},[s("div",qo,[s("h3",null,y(de.value?"编辑场景":"创建场景"),1),s("button",{class:"custom-dialog-close",onClick:Be},"×")]),s("div",jo,[l(J,{model:_,"label-width":"100px",class:"scene-form"},{default:n(()=>[l(E,{label:"场景标题"},{default:n(()=>[l(O,{modelValue:_.title,"onUpdate:modelValue":e[5]||(e[5]=o=>_.title=o),placeholder:"请输入场景标题",ref:"sceneTitleInput",onKeyup:Ie(P(nt,["prevent"]),["enter"]),autofocus:"",class:"scene-title-input"},null,8,["modelValue","onKeyup"])]),_:1}),l(E,{label:"场景描述"},{default:n(()=>[l(O,{modelValue:_.description,"onUpdate:modelValue":e[6]||(e[6]=o=>_.description=o),type:"textarea",rows:6,resize:"none",placeholder:"请描述场景内容",class:"scene-description-input"},null,8,["modelValue"])]),_:1}),l(E,{label:"场景标签"},{default:n(()=>[l(u,{modelValue:_.tags,"onUpdate:modelValue":e[7]||(e[7]=o=>_.tags=o),multiple:"",filterable:"","allow-create":"","default-first-option":"",placeholder:"请选择或创建标签",class:"scene-tags-select"},{default:n(()=>[(p(!0),b(z,null,B(Tt.value,o=>(p(),$(a,{key:o,label:o,value:o},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),s("div",Ho,[l(r,{onClick:Be,class:"cancel-btn"},{default:n(()=>e[44]||(e[44]=[f("取消")])),_:1}),l(r,{type:"primary",onClick:nt,class:"save-btn"},{default:n(()=>e[45]||(e[45]=[f("保存场景")])),_:1})])])])):ae("",!0)],8,["to"])),l(H,{modelValue:Te.value,"onUpdate:modelValue":e[12]||(e[12]=o=>Te.value=o),title:"场景组合",width:"1200px",class:"draw-dialog"},{default:n(()=>[s("div",Wo,[s("div",Ko,[s("div",Zo,[s("div",Go,[(p(!0),b(z,null,B(ce.value,(o,w)=>(p(),b("div",{key:o.id,class:"drawn-card-item"},[s("div",Qo,"场景 "+y(w+1),1),l(Vt,{title:o.title,description:o.description,tags:o.tags,class:"compact-card"},null,8,["title","description","tags"]),w<ce.value.length-1?(p(),b("div",en,[l(i,null,{default:n(()=>[l(_e)]),_:1})])):ae("",!0)]))),128))]),s("div",tn,[l(i,null,{default:n(()=>[l(g(bt))]),_:1}),e[46]||(e[46]=s("p",null,"思考这些场景之间可能存在的联系...",-1))])]),s("div",ln,[s("div",on,[e[50]||(e[50]=s("div",{class:"inspiration-label"},"记录你的灵感：",-1)),l(O,{modelValue:F.value.content,"onUpdate:modelValue":e[9]||(e[9]=o=>F.value.content=o),type:"textarea",rows:10,placeholder:"这些场景之间会发生什么有趣的故事？它们如何推动剧情发展？记录下你的想法...",resize:"none"},null,8,["modelValue"]),s("div",nn,[s("div",sn,[e[47]||(e[47]=s("span",{class:"rating-label"},"灵感评分：",-1)),l(Se,{modelValue:F.value.rating,"onUpdate:modelValue":e[10]||(e[10]=o=>F.value.rating=o),min:1,max:100,"format-tooltip":o=>`${o}分`,"show-input":"","input-size":"small",class:"rating-slider"},{marks:n(()=>[(p(),b(z,null,B([20,40,60,80,100],o=>s("span",{key:o,class:"mark-label"},y(o),1)),64))]),_:1},8,["modelValue","format-tooltip"])]),l(Ke,{modelValue:F.value.isUsed,"onUpdate:modelValue":e[11]||(e[11]=o=>F.value.isUsed=o)},{default:n(()=>e[48]||(e[48]=[f("已使用此灵感")])),_:1},8,["modelValue"])]),s("div",an,[l(r,{type:"primary",onClick:Ot,disabled:!F.value.content.trim()},{default:n(()=>[l(i,null,{default:n(()=>[l(g(xt))]),_:1}),e[49]||(e[49]=f(" 保存灵感 "))]),_:1},8,["disabled"])])])])])])]),_:1},8,["modelValue"]),l(H,{modelValue:ze.value,"onUpdate:modelValue":e[13]||(e[13]=o=>ze.value=o),class:"history-dialog",fullscreen:!0,"show-close":!1,modal:!0,"close-on-click-modal":!1,"close-on-press-escape":!0,"lock-scroll":!0,"destroy-on-close":!1},{header:n(({close:o,titleId:w,titleClass:X})=>[s("div",rn,[s("h4",{id:w,class:ye(X)},[e[51]||(e[51]=f(" 灵感历史记录 ")),s("span",dn,"(共 "+y(at.value)+" 条)",1)],10,un),l(r,{class:"close-btn",onClick:o},{default:n(()=>[l(i,null,{default:n(()=>[l(g(kt))]),_:1})]),_:2},1032,["onClick"])])]),default:n(()=>[s("div",cn,[s("div",vn,[s("div",pn,[s("div",mn,[it.value.length>0?(p(!0),b(z,{key:0},B(it.value,o=>(p(),b("div",{key:o.timestamp,class:ye(["history-item",{active:S.value===o}]),onClick:w=>Xt(o)},[s("div",gn,[s("div",_n,[s("div",yn,[s("span",hn,y(Ae(o.timestamp)),1),l(I,{size:"small",type:"info",class:"pool-tag"},{default:n(()=>[f(y(o.poolName),1)]),_:2},1024),o.fromAllPool?(p(),b(z,{key:0},[l(I,{size:"small",type:"success",class:"pool-tag"},{default:n(()=>e[52]||(e[52]=[f("全部场景")])),_:1}),l(A,{effect:"dark",placement:"top"},{content:n(()=>[s("div",wn,[e[53]||(e[53]=s("div",{class:"tooltip-title"},"关联卡池：",-1)),(p(!0),b(z,null,B(o.relatedPools,w=>(p(),b("div",{key:w.name,class:"related-pool-item"},y(w.name)+" ("+y(w.sceneCount)+"个场景) ",1))),128))])]),default:n(()=>[l(I,{size:"small",type:"warning",class:"pool-tag"},{default:n(()=>[f(y(o.relatedPools.length)+"个卡池 ",1)]),_:2},1024)]),_:2},1024)],64)):ae("",!0)]),s("div",bn,y(o.content),1)]),s("div",Cn,[s("div",kn,[l(I,{type:rt(o.rating),size:"small"},{default:n(()=>[f(y(o.rating)+"分 ",1)]),_:2},1032,["type"]),l(I,{type:o.isUsed?"success":"info",size:"small"},{default:n(()=>[f(y(o.isUsed?"已使用":"未使用"),1)]),_:2},1032,["type"])]),l(r,{type:"danger",size:"small",link:"",class:"delete-history-btn",onClick:P(w=>jt(o),["stop"])},{default:n(()=>[l(i,null,{default:n(()=>[l(g(Le))]),_:1})]),_:2},1032,["onClick"])])])],10,fn))),128)):(p(),$(W,{key:1,description:"暂无灵感记录"}))])]),s("div",xn,[S.value?(p(),b(z,{key:0},[s("div",Sn,[s("div",Vn,[e[54]||(e[54]=f(" 场景组合 ")),l(I,{size:"small",type:"info",class:"scene-count"},{default:n(()=>[f(y(S.value.scenes.length)+"个场景 ",1)]),_:1})]),s("div",In,[(p(!0),b(z,null,B(S.value.scenes,(o,w)=>(p(),b("div",{key:o.id,class:"history-scene"},[s("div",Pn,"场景 "+y(w+1),1),s("div",En,y(o.title),1),o.description?(p(),b("div",Mn,y(o.description),1)):ae("",!0),s("div",Dn,[(p(!0),b(z,null,B(o.tags,X=>(p(),$(I,{key:X,size:"small",class:"scene-tag"},{default:n(()=>[f(y(X),1)]),_:2},1024))),128))]),w<S.value.scenes.length-1?(p(),b("div",Tn,[l(i,null,{default:n(()=>[l(_e)]),_:1})])):ae("",!0)]))),128))])]),s("div",zn,[s("div",$n,[s("div",Fn,[e[55]||(e[55]=f(" 灵感内容 ")),l(I,{type:rt(S.value.rating),size:"small"},{default:n(()=>[f(y(S.value.rating)+"分 ",1)]),_:1},8,["type"]),l(I,{type:S.value.isUsed?"success":"info",size:"small"},{default:n(()=>[f(y(S.value.isUsed?"已使用":"未使用"),1)]),_:1},8,["type"])])]),s("div",On,y(S.value.content),1)])],64)):(p(),b("div",Ln,[l(W,{description:"点击左侧记录查看详情"})]))])]),s("div",Rn,[s("div",An,[l(se,{"current-page":ve.value,"page-size":Fe.value,total:at.value,"page-sizes":[5,10,20,30,50],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ut,onCurrentChange:Yt},null,8,["current-page","page-size","total"])])])])]),_:1},8,["modelValue"]),l(H,{modelValue:be.value,"onUpdate:modelValue":e[16]||(e[16]=o=>be.value=o),title:"创建场景卡池",width:"400px",onClose:Wt},{footer:n(()=>[s("span",Bn,[l(r,{onClick:e[15]||(e[15]=o=>be.value=!1)},{default:n(()=>e[56]||(e[56]=[f("取消")])),_:1}),l(r,{type:"primary",onClick:$e},{default:n(()=>e[57]||(e[57]=[f("确定")])),_:1})])]),default:n(()=>[l(J,{model:te,"label-width":"80px",onSubmit:P($e,["prevent"]),onKeyup:Ie($e,["enter"])},{default:n(()=>[l(E,{label:"卡池名称"},{default:n(()=>[l(O,{modelValue:te.name,"onUpdate:modelValue":e[14]||(e[14]=o=>te.name=o),placeholder:"请输入卡池名称",ref:"poolNameInput",onKeyup:Ie(P($e,["prevent"]),["enter"]),autofocus:""},null,8,["modelValue","onKeyup"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(H,{modelValue:pe.value,"onUpdate:modelValue":e[19]||(e[19]=o=>pe.value=o),title:"导入JSON",width:"400px",onClose:ll},{footer:n(()=>[s("span",Jn,[l(r,{onClick:e[18]||(e[18]=o=>pe.value=!1)},{default:n(()=>e[58]||(e[58]=[f("取消")])),_:1}),l(r,{type:"primary",onClick:Je},{default:n(()=>e[59]||(e[59]=[f("导入")])),_:1})])]),default:n(()=>[l(J,{model:q.value,"label-width":"80px",onSubmit:P(Je,["prevent"]),onKeyup:Ie(Je,["enter"])},{default:n(()=>[l(E,{label:"JSON内容"},{default:n(()=>[l(O,{modelValue:q.value.jsonContent,"onUpdate:modelValue":e[17]||(e[17]=o=>q.value.jsonContent=o),type:"textarea",rows:10,placeholder:"请输入要导入的JSON内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(H,{modelValue:me.value,"onUpdate:modelValue":e[22]||(e[22]=o=>me.value=o),title:"融合JSON到当前卡池",width:"400px",onClose:nl},{footer:n(()=>[s("span",Nn,[l(r,{onClick:e[21]||(e[21]=o=>me.value=!1)},{default:n(()=>e[60]||(e[60]=[f("取消")])),_:1}),l(r,{type:"primary",onClick:Ne},{default:n(()=>e[61]||(e[61]=[f("融合")])),_:1})])]),default:n(()=>[l(J,{model:j.value,"label-width":"80px",onSubmit:P(Ne,["prevent"]),onKeyup:Ie(Ne,["enter"])},{default:n(()=>[l(E,{label:"JSON内容"},{default:n(()=>[l(O,{modelValue:j.value.jsonContent,"onUpdate:modelValue":e[20]||(e[20]=o=>j.value.jsonContent=o),type:"textarea",rows:10,placeholder:"请输入要融合的JSON内容，将添加新场景到当前卡池"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(H,{modelValue:fe.value,"onUpdate:modelValue":e[27]||(e[27]=o=>fe.value=o),title:"管理场景卡池",width:"600px","close-on-click-modal":!1,"append-to-body":"","modal-append-to-body":!1,"lock-scroll":!0,"destroy-on-close":!1,class:"manage-pools-dialog",onOpen:il,onClose:rl},{footer:n(()=>[s("span",Gn,[l(r,{onClick:e[25]||(e[25]=o=>fe.value=!1)},{default:n(()=>e[64]||(e[64]=[f("取消")])),_:1}),l(r,{type:"primary",onClick:e[26]||(e[26]=o=>fe.value=!1)},{default:n(()=>e[65]||(e[65]=[f("完成")])),_:1})])]),default:n(()=>[s("div",Un,[s("div",Yn,[s("div",Xn,[l(O,{modelValue:le.value,"onUpdate:modelValue":e[23]||(e[23]=o=>le.value=o),placeholder:"搜索卡池名称...",clearable:"","prefix-icon":"Search",onInput:gl},null,8,["modelValue"])]),s("div",qn,[l(r,{onClick:ml,class:"scroll-top-btn",type:"default"},{default:n(()=>[l(i,null,{default:n(()=>[l(g(St))]),_:1}),e[62]||(e[62]=s("span",null,"返回顶部",-1))]),_:1})])]),e[63]||(e[63]=s("p",{class:"manage-pools-hint"},"拖拽卡池可调整显示顺序，或点击置顶按钮将卡池移到最前",-1)),l(Ze,{height:"400px",ref_key:"poolsScrollbar",ref:oe,class:"pools-scrollbar"},{default:n(()=>[fl.value.length===0?(p(),$(W,{key:0,description:"暂无卡池"})):(p(),$(g(wo),{key:1,modelValue:c.value.pools,"onUpdate:modelValue":e[24]||(e[24]=o=>c.value.pools=o),"item-key":"id","ghost-class":"ghost-pool",handle:".drag-handle",onEnd:ul},{item:n(({element:o,index:w})=>[s("div",{class:ye(["pool-item",{"is-searched":_l(o)}])},[l(i,{class:"drag-handle"},{default:n(()=>[l(g(_o))]),_:1}),s("div",jn,[s("div",Hn,y(o.name),1),s("div",Wn,[l(I,{size:"small",class:"scene-count-tag"},{default:n(()=>[f(y(o.scenes.length)+"个场景",1)]),_:2},1024),s("span",Kn,y(Ae(o.updateTime)),1)])]),s("div",Zn,[l(A,{content:"置顶此卡池",placement:"top",enterable:!1},{default:n(()=>[l(r,{type:"info",link:"",onClick:X=>pl(w),class:"top-pool-btn",disabled:w===0},{default:n(()=>[l(i,null,{default:n(()=>[l(g(St))]),_:1})]),_:2},1032,["onClick","disabled"])]),_:2},1024),l(A,{content:"选择此卡池",placement:"top",enterable:!1},{default:n(()=>[l(r,{type:"primary",link:"",onClick:X=>dl(o),class:"select-pool-btn"},{default:n(()=>[l(i,null,{default:n(()=>[l(g(xt))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024),l(A,{content:"重命名",placement:"top",enterable:!1},{default:n(()=>[l(r,{type:"warning",link:"",onClick:X=>cl(o),class:"rename-pool-btn"},{default:n(()=>[l(i,null,{default:n(()=>[l(g(yo))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024),l(A,{content:"删除",placement:"top",enterable:!1},{default:n(()=>[c.value.pools.length>1?(p(),$(r,{key:0,type:"danger",link:"",onClick:X=>ut(o),class:"delete-pool-btn"},{default:n(()=>[l(i,null,{default:n(()=>[l(g(Le))]),_:1})]),_:2},1032,["onClick"])):ae("",!0)]),_:2},1024)])],2)]),_:1},8,["modelValue"]))]),_:1},512)])]),_:1},8,["modelValue"]),l(H,{modelValue:Ce.value,"onUpdate:modelValue":e[30]||(e[30]=o=>Ce.value=o),title:"重命名卡池",width:"400px","append-to-body":"","close-on-click-modal":!1,"lock-scroll":!0,class:"rename-pool-dialog",onClose:vl},{footer:n(()=>[s("span",Qn,[l(r,{onClick:e[29]||(e[29]=o=>Ce.value=!1)},{default:n(()=>e[66]||(e[66]=[f("取消")])),_:1}),l(r,{type:"primary",onClick:ct},{default:n(()=>e[67]||(e[67]=[f("确认")])),_:1})])]),default:n(()=>[l(J,{model:N,"label-width":"80px",onSubmit:P(ct,["prevent"])},{default:n(()=>[l(E,{label:"卡池名称"},{default:n(()=>[l(O,{modelValue:N.name,"onUpdate:modelValue":e[28]||(e[28]=o=>N.name=o),placeholder:"请输入卡池名称",ref:"renamePoolInput",autofocus:""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},fs=It(ts,[["__scopeId","data-v-54798296"]]);export{fs as default};
