import{_ as Me,r as k,o as qe,c as te,b as _,d as o,M as Ne,p as A,g as a,n as O,bh as Re,k as ze,i as Oe,m as f,e as s,R as U,S as M,C as g,F as w,t as We,h as je,B as Je,a1 as Ge,U as Xe,bi as oe,v as c,a4 as He,bj as Ke,au as le,a9 as Qe,s as Ye,aA as Ze,j as es,aD as ss,aE as ts,W,bk as ae,aG as os,D as Q,bl as re,q as ls,be as as,bf as rs,y as ns,E as d,ah as q}from"./entry-DxFfH4M0.js";/* empty css                    *//* empty css                       *//* empty css                 *//* empty css                *//* empty css                  *//* empty css               *//* empty css                  *//* empty css                 *//* empty css                */import{u as is}from"./book-mHAKpUpK.js";const ds={class:"writing-manager"},us={class:"card-header"},cs={class:"header-actions"},ps={class:"books-container"},vs={class:"books-grid"},fs=["data-book-type","data-encrypted"],ms={class:"book-header"},_s={class:"title-container"},ws={class:"book-meta"},ys={class:"word-count"},gs={class:"update-time"},ks={class:"book-description"},bs={class:"book-stats"},hs={class:"stat-item"},Vs={class:"stat-item"},Cs={class:"book-actions"},xs={class:"action-group primary"},Bs={class:"action-group secondary"},Es={class:"card-header"},Ps={class:"writing-actions"},As={class:"editor-container"},Us={class:"book-create-content"},Ss={class:"create-form-wrapper"},$s={class:"settings-section"},Ds={class:"form-row"},Ts={class:"form-item"},Fs={class:"form-row"},Ls={class:"form-item"},Is={class:"form-row"},Ms={class:"form-item"},qs={class:"settings-section"},Ns={class:"style-selector"},Rs=["onClick"],zs={class:"style-preview"},Os={class:"preview-content"},Ws={class:"preview-description"},js={key:0,class:"style-check"},Js={class:"settings-section"},Gs={class:"form-row"},Xs={class:"form-item"},Hs={class:"form-row"},Ks={class:"form-item"},Qs={class:"form-row"},Ys={class:"form-item"},Zs={class:"warning-text"},et={class:"dialog-footer"},st={key:0,class:"book-settings-content"},tt={class:"settings-form-wrapper"},ot={class:"settings-section"},lt={class:"form-row"},at={class:"form-item"},rt={class:"form-row"},nt={class:"form-item"},it={class:"settings-section"},dt={class:"style-selector"},ut=["onClick"],ct={class:"style-preview"},pt={class:"preview-content"},vt={class:"preview-desc"},ft={key:0,class:"style-check"},mt={class:"settings-section"},_t={class:"password-section"},wt={key:0,class:"password-setup"},yt={key:1,class:"password-status"},gt={class:"encrypted-info"},kt={class:"encrypted-badge"},bt={class:"dialog-footer"},ht={class:"warning-text"},Vt={class:"dialog-footer"},Ct={class:"warning-text"},xt={class:"dialog-footer"},Bt={key:0,class:"export-panel"},Et={class:"export-header"},Pt={class:"export-options"},At={class:"selection-controls"},Ut={class:"chapters-container"},St={class:"volume-header"},$t={class:"volume-title"},Dt={class:"chapter-list"},Tt={class:"chapter-title"},Ft={class:"chapter-word-count"},Lt={class:"export-summary"},It={class:"dialog-footer"},Mt={__name:"写作",setup(qt){const j=Oe(),y=is(),S=k(!1),$=k(!1),E=k(!1),n=k({}),b=k(null),N=k(""),B=k(null),u=k([]),J=k("txt"),D=k(!1),T=k(!1),h=k({password:"",confirmPassword:""}),P=k({password:""}),F=k(!1),Y=k([{value:"classic-blue",label:"经典蓝调",description:"专业稳重，适合商务类作品",color:"#4a90e2"},{value:"warm-orange",label:"温暖橙光",description:"活力温馨，适合生活类作品",color:"#ff8c42"},{value:"fresh-green",label:"清新绿意",description:"自然清新，适合治愈类作品",color:"#2ecc71"},{value:"elegant-purple",label:"优雅紫韵",description:"神秘优雅，适合奇幻类作品",color:"#9b59b6"},{value:"mysterious-dark",label:"神秘深邃",description:"沉稳内敛，适合悬疑类作品",color:"#34495e"},{value:"minimal-gray",label:"简约灰调",description:"简洁现代，适合科技类作品",color:"#95a5a6"},{value:"sakura-pink",label:"樱花粉韵",description:"浪漫温柔，适合言情类作品",color:"#ff6b9d"},{value:"deep-ocean",label:"深海蓝调",description:"深邃宁静，适合哲学类作品",color:"#1e3a8a"},{value:"emerald-oasis",label:"翡翠绿洲",description:"生机盎然，适合冒险类作品",color:"#059669"},{value:"sunset-glow",label:"夕阳红霞",description:"热情奔放，适合青春类作品",color:"#dc2626"},{value:"lavender-dream",label:"薰衣草紫",description:"梦幻唯美，适合童话类作品",color:"#7c3aed"},{value:"amber-gold",label:"琥珀金辉",description:"典雅华贵，适合历史类作品",color:"#d97706"}]),p=k({title:"",description:"",type:"draft",book_style:"classic-blue",password:"",confirmPassword:""}),ne={title:[{required:!0,message:"请输入书名",trigger:"blur"}],type:[{required:!0,message:"请选择类型",trigger:"blur"}]};qe(()=>{y.loadBooks()});const ie=async()=>{if(F.value){if(p.value.password!==p.value.confirmPassword){d.error("两次输入的密码不一致");return}if(p.value.password.length<6){d.error("密码长度至少6个字符");return}}try{const t=y.bookList.filter(r=>r.title===p.value.title);if(t.length>0)if(t.filter(i=>i.encrypted).length>0){await q.confirm("已存在同名加密书籍，创建新书籍可能导致无法解密原有内容。请使用其他名称。","警告",{confirmButtonText:"更改名称",cancelButtonText:"取消",type:"warning"});return}else await q.confirm("已存在同名书籍，继续创建将可能覆盖现有书籍。是否继续？","提示",{confirmButtonText:"继续",cancelButtonText:"取消",type:"warning"});await y.createBook(p.value)&&(d.success("创建成功"),S.value=!1,p.value={title:"",description:"",type:"draft",book_style:"classic-blue",password:"",confirmPassword:""},F.value=!1)}catch(t){t!=="cancel"&&(console.error("创建失败:",t),d.error("创建失败："+(t.message||"未知错误")))}},de=async t=>{try{await q.confirm("注意：为了安全只会删除到软件垃圾桶目录，请手动找到目录确认删除！！","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await y.removeBook(t)}catch(e){e!=="cancel"&&console.error("删除失败:",e)}},ue=t=>{j.push({name:"bookSettings",params:{id:t.id},query:{title:t.title}})},ce=async t=>{try{j.push(`/book/editor/${t.id}`)}catch(e){console.error("导航到编辑器页面失败:",e),d.error("导航到编辑器页面失败："+e.message)}},pe=async()=>{if(b.value)try{await y.updateBook(b.value.id,{...b.value,content:N.value,word_count:N.value.length})&&(d.success("保存成功"),y.loadBooks())}catch(t){console.error("保存失败:",t),d.error("保存失败："+t.message)}},R=()=>{S.value=!0,p.value={title:"",description:"",type:"draft",book_style:"classic-blue",password:"",confirmPassword:""}},ve=t=>{n.value={...t,book_style:t.book_style||(t.theme_color?G(t.theme_color):"classic-blue")},$.value=!0},fe=t=>{n.value.book_style=t,b.value&&b.value.id===n.value.id&&(b.value.book_style=t);const e=y.bookList.find(r=>r.id===n.value.id);e&&(e.book_style=t)},me=async()=>{try{if(console.log("开始更新书籍设置:",n.value),!n.value||!n.value.id)throw new Error("书籍信息无效");const t={id:n.value.id,title:n.value.title,description:n.value.description,book_style:n.value.book_style,type:n.value.type,encrypted:n.value.encrypted,created_at:n.value.created_at,updated_at:n.value.updated_at,word_count:n.value.word_count};if(n.value.encrypted&&(t.salt=n.value.salt,t.iv=n.value.iv,t.checksum=n.value.checksum),console.log("准备发送的书籍数据:",t),await y.updateBook(n.value.id,t))d.success("设置更新成功"),$.value=!1,setTimeout(async()=>{if(await y.loadBooks(),b.value&&b.value.id===n.value.id){const r=y.bookList.find(i=>i.id===n.value.id);r&&(b.value={...r})}},100);else throw new Error("更新失败，未收到成功响应")}catch(t){console.error("更新失败:",t),d.error("更新失败："+(t.message||"未知错误"))}},Z=t=>t?new Date(t).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"}):"",_e=t=>t.chapter_count||0,we=t=>{if(!t.updated_at)return"未编辑";const e=new Date,r=new Date(t.updated_at),i=e-r,v=Math.floor(i/(1e3*60*60*24));return v===0?"今天":v===1?"昨天":v<30?`${v}天前`:Z(t.updated_at)},ye=t=>{const e="book-style";let r="classic-blue";t.book_style?r=t.book_style:t.theme_color&&(r=G(t.theme_color));const i=t.type==="draft"?"draft-type":"work-type",v=t.encrypted?"encrypted-book":"";return[e,`style-${r}`,i,v].filter(Boolean).join(" ")},G=t=>t&&{"#409EFF":"classic-blue","#67C23A":"fresh-green","#E6A23C":"warm-orange","#F56C6C":"elegant-purple","#909399":"minimal-gray","#000000":"mysterious-dark"}[t]||"classic-blue",ge=t=>{if(!t)return"";let e="classic-blue";return t.book_style?e=t.book_style:t.theme_color&&(e=G(t.theme_color)),`writing-style-${e}`},ke=t=>{if(!t||!t.id){d.error("无效的书籍信息");return}j.push({name:"bookTimeline",params:{id:t.id},query:{title:t.title}})},be=async t=>{try{if(B.value={...t},u.value=[],E.value=!0,d.info("正在加载书籍结构..."),console.log("准备获取书籍结构，书籍ID:",t.id),!t.id)throw new Error("无效的书籍ID");const e=window.pywebview?.api;if(!e)throw console.error("API未正确初始化"),new Error("系统API未就绪");console.log("调用get_volumes方法...");const r=await e.book_controller.get_volumes(t.id);if(console.log("获取书籍结构响应:",r),!r)throw new Error("获取书籍结构时服务器无响应");let i=r;if(typeof r=="string")try{i=JSON.parse(r)}catch(C){throw console.error("解析响应失败:",C),new Error("解析书籍数据失败")}if(i.status!=="success")throw new Error(i.message||"加载书籍结构失败");const v=i.data?.volumes||[];if(v.length===0)throw new Error("此书籍没有任何卷或章节");console.log("成功获取卷数量:",v.length),u.value=v.map(C=>({...C,selected:!0,chapters:(C.chapters||[]).map(L=>({...L,selected:!0}))}));const m=u.value.reduce((C,L)=>C+L.chapters.length,0);m===0?d.warning("此书籍没有任何章节内容"):d.success(`成功加载 ${u.value.length} 卷 ${m} 章内容`)}catch(e){console.error("准备导出失败:",e),d.error("准备导出失败："+(e.message||"未知错误")),u.value=[],B.value=null,E.value=!1}},he=()=>{Array.isArray(u.value)&&u.value.forEach(t=>{t.selected=!0,t.chapters&&t.chapters.forEach(e=>{e.selected=!0})})},Ve=()=>{Array.isArray(u.value)&&u.value.forEach(t=>{t.selected=!1,t.chapters&&t.chapters.forEach(e=>{e.selected=!1})})},Ce=(t,e)=>{if(!Array.isArray(u.value))return;const r=u.value[t];r&&r.chapters&&r.chapters.forEach(i=>{i.selected=e})},xe=t=>{if(!Array.isArray(u.value))return;const e=u.value[t];e&&e.chapters&&(e.selected=e.chapters.every(r=>r.selected))},X=te(()=>Array.isArray(u.value)?u.value.reduce((t,e)=>t+(e.chapters||[]).filter(r=>r.selected).length,0):0),Be=te(()=>Array.isArray(u.value)?u.value.reduce((t,e)=>t+(e.chapters||[]).filter(r=>r.selected).reduce((r,i)=>r+(i.word_count||0),0),0):0),Ee=async()=>{try{if(!Array.isArray(u.value)){console.error("exportVolumes 不是数组:",u.value),d.error("导出数据异常，请重新打开导出面板");return}if(X.value===0){d.warning("请选择至少一个章节");return}d.info("正在准备导出..."),console.log("准备导出数据，书籍ID:",B.value.id),console.log("exportVolumes 类型:",typeof u.value,"是否为数组:",Array.isArray(u.value)),console.log("exportVolumes 内容:",u.value);const t={book_id:B.value.id,format:J.value,volumes:u.value.map(v=>({id:v.id,title:v.title,chapters:(v.chapters||[]).filter(m=>m.selected).map(m=>({id:m.id,title:m.title}))})).filter(v=>v.chapters.length>0)};console.log("导出数据结构:",JSON.stringify(t,null,2));const e=window.pywebview?.api;if(!e)throw console.error("API未正确初始化"),new Error("系统API未就绪");console.log("调用export_book方法...");const r=await e.book_controller.export_book(t);if(console.log("导出响应:",r),!r)throw new Error("导出时服务器无响应");let i=r;if(typeof r=="string")try{i=JSON.parse(r)}catch(v){throw console.error("解析响应失败:",v),new Error("解析导出结果失败")}if(i.status!=="success")throw new Error(i.message||"导出失败");d.success(i.message||"导出成功"),E.value=!1}catch(t){console.error("导出失败:",t),d.error("导出失败："+(t.message||"未知错误"))}},Pe=async()=>{if(b.value)try{d.info("正在导出内容..."),d.success("内容导出成功")}catch(t){console.error("导出失败:",t),d.error("导出失败："+(t.message||"未知错误"))}},Ae=(t,e,r)=>{e!==h.value.password?r(new Error("两次输入的密码不一致")):r()},Ue=()=>{D.value=!0,h.value={password:"",confirmPassword:""}},Se=()=>{T.value=!0,P.value={password:""}},$e=async()=>{if(h.value.password!==h.value.confirmPassword){d.error("两次输入的密码不一致");return}if(h.value.password.length<6){d.error("密码长度至少6个字符");return}try{await q.confirm("设置密码后，书籍内容将被加密保存。请务必记住密码，密码丢失将无法恢复内容！","重要提示",{confirmButtonText:"确认设置",cancelButtonText:"取消",type:"warning"}),d.info("正在加密书籍内容，请稍候..."),await y.setBookPassword(n.value.id,h.value.password)&&(d.success("密码设置成功，书籍内容已加密"),D.value=!1,setTimeout(async()=>{await y.loadBooks();const e=y.bookList.find(r=>r.id===n.value.id);e&&(n.value={...e})},100))}catch(t){t!=="cancel"&&(console.error("设置密码失败:",t),d.error("设置密码失败："+t.message))}},De=async()=>{if(!P.value.password){d.error("请输入当前密码");return}try{await q.confirm("移除密码保护后，书籍内容将以明文形式保存，确定要继续吗？","确认移除",{confirmButtonText:"确认移除",cancelButtonText:"取消",type:"warning"}),d.info("正在解密书籍内容，请稍候..."),await y.removeBookPassword(n.value.id,P.value.password)&&(d.success("密码已移除，书籍内容已解密"),T.value=!1,setTimeout(async()=>{await y.loadBooks();const e=y.bookList.find(r=>r.id===n.value.id);e&&(n.value={...e})},100))}catch(t){t!=="cancel"&&(console.error("移除密码失败:",t),d.error("移除密码失败："+(t.message||"密码可能错误")))}};return(t,e)=>{const r=We,i=Je,v=Re,m=Ye,C=ts,L=ss,Te=os,z=es,H=Ze,I=ze,K=ls,ee=rs,Fe=as,se=ns;return f(),_("div",ds,[o(v,{class:"book-list"},{header:a(()=>[s("div",us,[s("div",cs,[o(r,{type:"primary",onClick:R},{default:a(()=>e[23]||(e[23]=[c("新建草稿")])),_:1}),o(r,{type:"primary",onClick:R},{default:a(()=>e[24]||(e[24]=[c("新建作品")])),_:1}),o(r,{type:"primary",onClick:R},{default:a(()=>e[25]||(e[25]=[c("导入草稿")])),_:1})])])]),default:a(()=>[s("div",ps,[s("div",vs,[(f(!0),_(U,null,M(g(y).bookList,l=>(f(),_("div",{key:l.id,class:"book-card"},[s("div",{class:O(["book-content",ye(l)]),"data-book-type":l.type,"data-encrypted":l.encrypted},[s("div",ms,[s("div",_s,[s("h3",null,w(l.title),1)]),o(r,{class:"settings-btn",onClick:je(V=>ve(l),["stop"])},{default:a(()=>[o(i,null,{default:a(()=>[o(g(Ge))]),_:1})]),_:2},1032,["onClick"])]),s("div",ws,[s("span",ys,w(l.word_count||0)+"字",1),s("span",gs,w(Z(l.updated_at)),1)]),s("p",ks,w(l.description),1),s("div",bs,[s("div",hs,[o(i,null,{default:a(()=>[o(g(Xe))]),_:1}),s("span",null,w(_e(l))+"章",1)]),s("div",Vs,[o(i,null,{default:a(()=>[o(g(oe))]),_:1}),s("span",null,w(we(l)),1)])])],10,fs),s("div",Cs,[s("div",xs,[o(r,{type:"primary",class:"action-btn",onClick:V=>ce(l)},{default:a(()=>[o(i,null,{default:a(()=>[o(g(He))]),_:1}),e[26]||(e[26]=c(" 写作 "))]),_:2},1032,["onClick"])]),s("div",Bs,[o(r,{type:"success",class:"action-btn",onClick:V=>ue(l)},{default:a(()=>e[27]||(e[27]=[c(" 设定 ")])),_:2},1032,["onClick"]),o(r,{type:"warning",class:"action-btn icon-btn",onClick:V=>ke(l)},{default:a(()=>[o(i,null,{default:a(()=>[o(g(oe))]),_:1})]),_:2},1032,["onClick"]),o(r,{type:"info",class:"action-btn icon-btn",onClick:V=>be(l)},{default:a(()=>[o(i,null,{default:a(()=>[o(g(Ke))]),_:1})]),_:2},1032,["onClick"]),o(r,{type:"danger",class:"action-btn icon-btn",onClick:V=>de(l)},{default:a(()=>[o(i,null,{default:a(()=>[o(g(le))]),_:1})]),_:2},1032,["onClick"])])])]))),128)),s("div",{class:"book-card new-book",onClick:R},[o(i,null,{default:a(()=>[o(g(Qe))]),_:1}),e[28]||(e[28]=s("span",null,"新建书籍",-1))])])])]),_:1}),b.value?(f(),Ne(v,{key:0,class:O(["writing-area",ge(b.value)])},{header:a(()=>[s("div",Es,[s("span",null,w(b.value.title),1),s("div",Ps,[o(r,{type:"success",onClick:pe},{default:a(()=>e[29]||(e[29]=[c("保存")])),_:1}),o(r,{type:"info",onClick:Pe},{default:a(()=>e[30]||(e[30]=[c("导出")])),_:1})])])]),default:a(()=>[s("div",As,[o(m,{modelValue:N.value,"onUpdate:modelValue":e[0]||(e[0]=l=>N.value=l),type:"textarea",rows:20,placeholder:"开始创作...",resize:"none"},null,8,["modelValue"])])]),_:1},8,["class"])):A("",!0),o(I,{modelValue:S.value,"onUpdate:modelValue":e[8]||(e[8]=l=>S.value=l),title:p.value.type==="draft"?"📝 新建草稿":"📚 新建作品",width:"700px",class:"book-create-dialog","close-on-click-modal":!1,"destroy-on-close":!1,"append-to-body":""},{footer:a(()=>[s("span",et,[o(r,{onClick:e[7]||(e[7]=l=>S.value=!1)},{default:a(()=>e[41]||(e[41]=[c("取消")])),_:1}),o(r,{type:"primary",onClick:ie},{default:a(()=>e[42]||(e[42]=[c("创建")])),_:1})])]),default:a(()=>[s("div",Us,[o(H,{class:"create-scroll-container","max-height":"500px"},{default:a(()=>[s("div",Ss,[o(z,{ref:"bookFormRef",model:p.value,rules:ne,"label-width":"0px",class:"create-form"},{default:a(()=>[s("div",$s,[e[34]||(e[34]=s("h3",{class:"section-title"},"📝 基本信息",-1)),s("div",Ds,[s("div",Ts,[e[31]||(e[31]=s("label",{class:"form-label"},"书名",-1)),o(m,{modelValue:p.value.title,"onUpdate:modelValue":e[1]||(e[1]=l=>p.value.title=l),placeholder:"请输入书名",class:"styled-input"},null,8,["modelValue"])])]),s("div",Fs,[s("div",Ls,[e[32]||(e[32]=s("label",{class:"form-label"},"描述",-1)),o(m,{modelValue:p.value.description,"onUpdate:modelValue":e[2]||(e[2]=l=>p.value.description=l),type:"textarea",rows:3,placeholder:"请输入书籍描述",class:"styled-textarea"},null,8,["modelValue"])])]),s("div",Is,[s("div",Ms,[e[33]||(e[33]=s("label",{class:"form-label"},"类型",-1)),o(L,{modelValue:p.value.type,"onUpdate:modelValue":e[3]||(e[3]=l=>p.value.type=l),placeholder:"请选择类型",class:"styled-select"},{default:a(()=>[o(C,{label:"草稿",value:"draft"}),o(C,{label:"作品",value:"work"})]),_:1},8,["modelValue"])])])]),s("div",qs,[e[35]||(e[35]=s("h3",{class:"section-title"},"🎨 书籍风格",-1)),s("div",Ns,[(f(!0),_(U,null,M(Y.value,l=>(f(),_("div",{key:l.value,class:O(["style-card",{selected:p.value.book_style===l.value,[`preview-${l.value}`]:!0}]),onClick:V=>p.value.book_style=l.value},[s("div",zs,[s("div",{class:"preview-header",style:W({backgroundColor:l.color})},null,4),s("div",Os,[s("div",{class:"preview-title",style:W({color:l.color})},w(l.label),5),s("div",Ws,w(l.description),1)])]),p.value.book_style===l.value?(f(),_("div",js,[o(i,null,{default:a(()=>[o(g(ae))]),_:1})])):A("",!0)],10,Rs))),128))])]),s("div",Js,[e[40]||(e[40]=s("h3",{class:"section-title"},"🔒 密码保护",-1)),s("div",Gs,[s("div",Xs,[e[36]||(e[36]=s("label",{class:"form-label"},"启用密码",-1)),o(Te,{modelValue:F.value,"onUpdate:modelValue":e[4]||(e[4]=l=>F.value=l)},null,8,["modelValue"])])]),F.value?(f(),_(U,{key:0},[s("div",Hs,[s("div",Ks,[e[37]||(e[37]=s("label",{class:"form-label"},"密码",-1)),o(m,{modelValue:p.value.password,"onUpdate:modelValue":e[5]||(e[5]=l=>p.value.password=l),type:"password",placeholder:"请输入密码","show-password":"",class:"styled-input"},null,8,["modelValue"])])]),s("div",Qs,[s("div",Ys,[e[38]||(e[38]=s("label",{class:"form-label"},"确认密码",-1)),o(m,{modelValue:p.value.confirmPassword,"onUpdate:modelValue":e[6]||(e[6]=l=>p.value.confirmPassword=l),type:"password",placeholder:"请再次输入密码","show-password":"",class:"styled-input"},null,8,["modelValue"])])]),s("div",Zs,[o(i,null,{default:a(()=>[o(g(Q))]),_:1}),e[39]||(e[39]=s("span",null,"请牢记您的密码，密码丢失将无法恢复书籍内容！",-1))])],64)):A("",!0)])]),_:1},8,["model"])])]),_:1})])]),_:1},8,["modelValue","title"]),o(I,{modelValue:$.value,"onUpdate:modelValue":e[12]||(e[12]=l=>$.value=l),title:"📚 书籍设置",width:"700px",class:"book-settings-dialog","close-on-click-modal":!1,"destroy-on-close":!1,"append-to-body":""},{footer:a(()=>[s("span",bt,[o(r,{onClick:e[11]||(e[11]=l=>$.value=!1)},{default:a(()=>e[53]||(e[53]=[c("取消")])),_:1}),o(r,{type:"primary",onClick:me},{default:a(()=>e[54]||(e[54]=[c("保存")])),_:1})])]),default:a(()=>[n.value?(f(),_("div",st,[o(H,{class:"settings-scroll-container","max-height":"500px"},{default:a(()=>[s("div",tt,[o(z,{model:n.value,"label-width":"0px",class:"settings-form"},{default:a(()=>[s("div",ot,[e[45]||(e[45]=s("h3",{class:"section-title"},"📝 基本信息",-1)),s("div",lt,[s("div",at,[e[43]||(e[43]=s("label",{class:"form-label"},"书名",-1)),o(m,{modelValue:n.value.title,"onUpdate:modelValue":e[9]||(e[9]=l=>n.value.title=l),placeholder:"请输入书名",class:"styled-input"},null,8,["modelValue"])])]),s("div",rt,[s("div",nt,[e[44]||(e[44]=s("label",{class:"form-label"},"描述",-1)),o(m,{modelValue:n.value.description,"onUpdate:modelValue":e[10]||(e[10]=l=>n.value.description=l),type:"textarea",rows:3,placeholder:"请输入书籍描述",class:"styled-textarea"},null,8,["modelValue"])])])]),s("div",it,[e[46]||(e[46]=s("h3",{class:"section-title"},"🎨 书籍风格",-1)),s("div",dt,[(f(!0),_(U,null,M(Y.value,l=>(f(),_("div",{key:l.value,class:O(["style-card",{selected:n.value.book_style===l.value,[`preview-${l.value}`]:!0}]),onClick:V=>fe(l.value)},[s("div",ct,[s("div",{class:"preview-header",style:W({backgroundColor:l.color})},null,4),s("div",pt,[s("div",{class:"preview-title",style:W({color:l.color})},w(l.label),5),s("div",vt,w(l.description),1)])]),n.value.book_style===l.value?(f(),_("div",ft,[o(i,null,{default:a(()=>[o(g(ae))]),_:1})])):A("",!0)],10,ut))),128))])]),s("div",mt,[e[52]||(e[52]=s("h3",{class:"section-title"},"🔒 密码保护",-1)),s("div",_t,[n.value.encrypted?(f(),_("div",yt,[s("div",gt,[s("div",kt,[o(i,{class:"lock-icon"},{default:a(()=>[o(g(re))]),_:1}),e[49]||(e[49]=s("span",null,"此书籍已加密",-1))]),e[50]||(e[50]=s("p",{class:"info-text"},"书籍内容已加密保护，访问时需要输入密码。",-1))]),o(r,{type:"danger",onClick:Se,class:"password-btn danger"},{default:a(()=>[o(i,null,{default:a(()=>[o(g(le))]),_:1}),e[51]||(e[51]=c(" 移除密码保护 "))]),_:1})])):(f(),_("div",wt,[e[48]||(e[48]=s("div",{class:"password-info"},[s("p",{class:"info-text"},"设置密码后，所有章节内容将被加密保存，访问时需要输入密码。")],-1)),o(r,{type:"primary",onClick:Ue,class:"password-btn"},{default:a(()=>[o(i,null,{default:a(()=>[o(g(re))]),_:1}),e[47]||(e[47]=c(" 设置密码保护 "))]),_:1})]))])])]),_:1},8,["model"])])]),_:1})])):A("",!0)]),_:1},8,["modelValue"]),o(I,{modelValue:D.value,"onUpdate:modelValue":e[16]||(e[16]=l=>D.value=l),title:"设置密码保护",width:"400px","append-to-body":"","destroy-on-close":""},{footer:a(()=>[s("span",Vt,[o(r,{onClick:e[15]||(e[15]=l=>D.value=!1)},{default:a(()=>e[56]||(e[56]=[c("取消")])),_:1}),o(r,{type:"primary",onClick:$e},{default:a(()=>e[57]||(e[57]=[c("确认设置")])),_:1})])]),default:a(()=>[o(z,{model:h.value,ref:"passwordFormRef","label-width":"100px"},{default:a(()=>[o(K,{label:"密码",prop:"password",rules:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度至少6个字符",trigger:"blur"}]},{default:a(()=>[o(m,{modelValue:h.value.password,"onUpdate:modelValue":e[13]||(e[13]=l=>h.value.password=l),type:"password",placeholder:"请输入密码","show-password":""},null,8,["modelValue"])]),_:1}),o(K,{label:"确认密码",prop:"confirmPassword",rules:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:Ae,trigger:"blur"}]},{default:a(()=>[o(m,{modelValue:h.value.confirmPassword,"onUpdate:modelValue":e[14]||(e[14]=l=>h.value.confirmPassword=l),type:"password",placeholder:"请再次输入密码","show-password":""},null,8,["modelValue"])]),_:1},8,["rules"])]),_:1},8,["model"]),s("div",ht,[o(i,null,{default:a(()=>[o(g(Q))]),_:1}),e[55]||(e[55]=s("span",null,"请牢记您的密码，密码丢失将无法恢复书籍内容！",-1))])]),_:1},8,["modelValue"]),o(I,{modelValue:T.value,"onUpdate:modelValue":e[19]||(e[19]=l=>T.value=l),title:"移除密码保护",width:"400px","append-to-body":"","destroy-on-close":""},{footer:a(()=>[s("span",xt,[o(r,{onClick:e[18]||(e[18]=l=>T.value=!1)},{default:a(()=>e[59]||(e[59]=[c("取消")])),_:1}),o(r,{type:"danger",onClick:De},{default:a(()=>e[60]||(e[60]=[c("确认移除")])),_:1})])]),default:a(()=>[o(z,{model:P.value,ref:"removePasswordFormRef","label-width":"100px"},{default:a(()=>[o(K,{label:"当前密码",prop:"password",rules:[{required:!0,message:"请输入当前密码",trigger:"blur"}]},{default:a(()=>[o(m,{modelValue:P.value.password,"onUpdate:modelValue":e[17]||(e[17]=l=>P.value.password=l),type:"password",placeholder:"请输入当前密码","show-password":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),s("div",Ct,[o(i,null,{default:a(()=>[o(g(Q))]),_:1}),e[58]||(e[58]=s("span",null,"移除密码保护后，书籍内容将以明文形式保存。",-1))])]),_:1},8,["modelValue"]),o(I,{modelValue:E.value,"onUpdate:modelValue":e[22]||(e[22]=l=>E.value=l),title:"导出书籍内容",width:"700px",class:"export-dialog","append-to-body":"","close-on-click-modal":!1,"modal-append-to-body":!1},{footer:a(()=>[s("span",It,[o(r,{onClick:e[21]||(e[21]=l=>E.value=!1)},{default:a(()=>e[65]||(e[65]=[c("取消")])),_:1}),o(r,{type:"primary",onClick:Ee,disabled:X.value===0},{default:a(()=>e[66]||(e[66]=[c(" 导出 ")])),_:1},8,["disabled"])])]),default:a(()=>[B.value?(f(),_("div",Bt,[s("div",Et,[s("h3",null,"《"+w(B.value.title)+"》",1),s("p",null,w(B.value.description),1),s("div",Pt,[o(Fe,{modelValue:J.value,"onUpdate:modelValue":e[20]||(e[20]=l=>J.value=l)},{default:a(()=>[o(ee,{value:"txt"},{default:a(()=>e[61]||(e[61]=[c("纯文本 (TXT)")])),_:1}),o(ee,{value:"md",disabled:""},{default:a(()=>e[62]||(e[62]=[c("Markdown (MD)")])),_:1})]),_:1},8,["modelValue"]),s("div",At,[o(r,{size:"small",type:"primary",onClick:he},{default:a(()=>e[63]||(e[63]=[c("全选")])),_:1}),o(r,{size:"small",onClick:Ve},{default:a(()=>e[64]||(e[64]=[c("取消全选")])),_:1})])])]),s("div",Ut,[o(H,{height:"300px"},{default:a(()=>[(f(!0),_(U,null,M(u.value,(l,V)=>(f(),_("div",{key:V,class:"volume-item"},[s("div",St,[o(se,{modelValue:l.selected,"onUpdate:modelValue":x=>l.selected=x,onChange:x=>Ce(V,x)},{default:a(()=>[s("span",$t,w(l.title),1)]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),s("div",Dt,[(f(!0),_(U,null,M(l.chapters,(x,Le)=>(f(),_("div",{key:Le,class:"chapter-item"},[o(se,{modelValue:x.selected,"onUpdate:modelValue":Ie=>x.selected=Ie,onChange:()=>xe(V)},{default:a(()=>[s("span",Tt,w(x.title),1),s("span",Ft,w(x.word_count||0)+"字",1)]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]))),128))])]))),128))]),_:1})]),s("div",Lt,[s("span",null,"已选择 "+w(X.value)+" 个章节，共 "+w(Be.value)+" 字",1)])])):A("",!0)]),_:1},8,["modelValue"])])}}},Qt=Me(Mt,[["__scopeId","data-v-c26f83e1"]]);export{Qt as default};
