import{_ as ue,r as u,c as y,w as de,o as pe,b as v,m as c,e as r,d as t,v as d,g as l,C as m,cq as ve,B as me,bQ as _e,F as w,t as fe,bn as G,bj as ye,cr as we,M as E,p as ge,V as be,K as he,aB as Oe,aC as ke,cs as Ee,j as Me,q as Ne,aD as De,aE as Ce,R as M,S as I,aw as Re,P as Ie,bh as Ve,n as Te,ct as Be,a$ as Se,s as xe,k as Je,E as n,bW as Ae}from"./entry-DxFfH4M0.js";/* empty css                 *//* empty css               *//* empty css                *//* empty css                *//* empty css               *//* empty css                  *//* empty css                 *//* empty css                 */const $e={class:"character-inspiration-page"},je={class:"page-header"},Le={class:"title"},Pe={class:"card-header-actions"},Ue={class:"actions-group"},Fe={key:0,class:"no-data-state"},ze={key:1,class:"data-loaded-content"},Ke={class:"section-title"},qe={class:"drawing-controls"},Qe={class:"section-title"},We={class:"results-area"},Ge={class:"drawn-card-header"},He={class:"header-main"},Xe={class:"mbti-badge"},Ye={class:"role-type"},Ze={class:"drawn-character-details-content"},ea={class:"profile-label"},aa={key:0,class:"list-items"},ta={class:"dialog-footer"},la={class:"import-textarea-container"},oa={class:"dialog-footer"},sa={__name:"CharacterInspiration",setup(ra){const p=u({}),$=u(""),V=u(""),b=u(!1),N=u(""),h=u(!1),O=u(""),D=y(()=>Object.keys(p.value).length>0),H=y(()=>Object.keys(p.value).length);de(p,a=>{try{$.value=JSON.stringify(a,null,2),C.value="RANDOM_MBTI",R.value="RANDOM_ROLE",f.value=null}catch(e){console.error("Error stringifying character profiles:",e),$.value=""}},{deep:!0});const j=()=>{V.value="",O.value="",h.value=!0},X=async()=>{try{const a=JSON.parse(O.value),e=await window.pywebview.api.book_controller.save_character_inspiration(a),o=typeof e=="string"?JSON.parse(e):e;if(o&&o.status==="success")p.value=a,n.success("数据导入成功"),h.value=!1,O.value="";else throw new Error(o?.message||"导入失败")}catch(a){n.error("导入失败："+a.message)}},Y=()=>{if(!D.value){n.warning("没有数据可导出");return}try{const a=JSON.stringify(p.value,null,2),e=document.createElement("textarea");e.value=a,document.body.appendChild(e),e.select(),document.execCommand("copy"),document.body.removeChild(e),n.success("数据已复制到剪贴板")}catch(a){n.error("导出失败："+a.message)}},Z=()=>{N.value=JSON.stringify(p.value,null,2),b.value=!0},ee=async()=>{try{const a=JSON.parse(N.value),e=await window.pywebview.api.book_controller.save_character_inspiration(a),o=typeof e=="string"?JSON.parse(e):e;if(o&&o.status==="success")p.value=a,n.success("保存成功"),b.value=!1;else throw new Error(o?.message||"保存失败")}catch(a){n.error("保存失败："+a.message)}},T=y(()=>Object.keys(p.value)),C=u("RANDOM_MBTI"),L=y(()=>{const a=new Set(["RANDOM_ROLE"]);return Object.values(p.value).forEach(e=>{e&&typeof e=="object"&&Object.keys(e).forEach(o=>{a.add(o)})}),Array.from(a)}),k=y(()=>{const a={RANDOM_ROLE:"随机角色类型"};return L.value.forEach(e=>{e!=="RANDOM_ROLE"&&(a[e]=e)}),a}),ae=y(()=>L.value.map(a=>({label:k.value[a]||a,value:a}))),R=u("RANDOM_ROLE"),f=u(null),B=u(""),S=u(""),_=u(!1),P=y(()=>k.value[S.value]||S.value),x=a=>a[Math.floor(Math.random()*a.length)],te=async()=>{if(!D.value){n.warning("请先导入角色数据.");return}_.value=!0,f.value=null,await new Promise(a=>setTimeout(a,300));try{let a=C.value;if(a==="RANDOM_MBTI"){if(T.value.length===0){n.error("没有可用的MBTI类型进行随机抽取."),_.value=!1;return}a=x(T.value)}const e=p.value[a];if(!e){n.error(`未找到MBTI类型 ${a} 的数据.`),_.value=!1;return}let o=R.value;const i=Object.keys(e);if(o==="RANDOM_ROLE"){if(i.length===0){n.error(`MBTI类型 ${a} 下没有定义角色分类.`),_.value=!1;return}o=x(i)}else if(!e[o]){if(n.warning(`MBTI类型 ${a} 下未找到 ${k.value[o]}，尝试随机抽取一个分类.`),i.length===0){n.error(`MBTI类型 ${a} 下没有可供随机抽取的角色分类.`),_.value=!1;return}o=x(i)}const g=e[o];if(typeof g!="object"||g===null){n.error(`在 ${a} - ${k.value[o]} 下未找到有效的角色对象数据.`),_.value=!1;return}B.value=a,S.value=o,f.value=g,Ae({title:"抽取成功!",message:`抽到 ${a} - ${k.value[o]||o}`,type:"success",duration:2e3})}catch(a){console.error("抽取角色失败:",a),n.error("抽取角色时发生错误，请检查数据格式或控制台日志.")}finally{_.value=!1}},U=a=>a.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase()),le=()=>{if(!f.value)return;let a=`MBTI: ${B.value}
角色分类: ${P.value}
---
`;for(const[e,o]of Object.entries(f.value)){const i=Array.isArray(o)?o.join(", "):o;a+=`${U(e)}: ${i}
`}window.pywebview.api.copy_to_clipboard(a).then(()=>{n.success("角色信息已复制到剪贴板!")}).catch(e=>{n.error("复制失败，请手动复制."),console.error("Clipboard copy failed:",e)})};u({});const oe=async()=>{try{const a=await window.pywebview.api.book_controller.get_character_inspiration(),e=typeof a=="string"?JSON.parse(a):a;if(e&&e.status==="success"){const o=typeof e.data=="string"?JSON.parse(e.data):e.data;p.value=o}else throw new Error(e?.message||"加载失败")}catch(a){console.error("加载数据错误:",a),n.error("加载数据失败："+a.message)}};return pe(()=>{oe()}),(a,e)=>{const o=me,i=fe,g=be,se=he,J=Ce,F=De,A=Ne,re=Me,z=ke,K=Ve,ne=Oe,q=xe,Q=Je;return c(),v("div",$e,[r("div",je,[r("h2",Le,[t(o,null,{default:l(()=>[t(m(ve))]),_:1}),e[8]||(e[8]=d(" 人设灵感工坊 (MBTI 抽卡) "))])]),t(K,{class:"content-card glass-card"},_e({default:l(()=>[D.value?(c(),v("div",ze,[t(ne,{gutter:30},{default:l(()=>[t(z,{xs:24,sm:24,md:8},{default:l(()=>[r("h3",Ke,[t(o,null,{default:l(()=>[t(m(Ee))]),_:1}),e[13]||(e[13]=d(" 抽取设置"))]),r("div",qe,[t(re,{"label-position":"top","label-width":"100px"},{default:l(()=>[t(A,{label:"选择MBTI类型"},{default:l(()=>[t(F,{modelValue:C.value,"onUpdate:modelValue":e[0]||(e[0]=s=>C.value=s),placeholder:"请选择MBTI类型",class:"full-width-select"},{default:l(()=>[t(J,{label:"随机MBTI类型",value:"RANDOM_MBTI"}),(c(!0),v(M,null,I(T.value,s=>(c(),E(J,{key:s,label:s,value:s},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(A,{label:"选择角色分类"},{default:l(()=>[t(F,{modelValue:R.value,"onUpdate:modelValue":e[1]||(e[1]=s=>R.value=s),placeholder:"请选择角色分类",class:"full-width-select"},{default:l(()=>[(c(!0),v(M,null,I(ae.value,s=>(c(),E(J,{key:s.value,label:s.label,value:s.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(A,null,{default:l(()=>[t(i,{type:"primary",onClick:te,class:"full-width-button draw-button",icon:m(Re),loading:_.value},{default:l(()=>[d(w(_.value?"抽取中...":"开始抽取角色"),1)]),_:1},8,["icon","loading"])]),_:1})]),_:1})])]),_:1}),t(z,{xs:24,sm:24,md:16},{default:l(()=>[r("h3",Qe,[t(o,null,{default:l(()=>[t(m(Ie))]),_:1}),e[14]||(e[14]=d(" 抽取结果"))]),r("div",We,[f.value?(c(),E(K,{key:0,class:"drawn-character-card",shadow:"hover"},{header:l(()=>[r("div",Ge,[r("div",He,[r("div",Xe,w(B.value),1),r("div",Ye,w(P.value),1)]),t(i,{type:"text",icon:m(Se),onClick:le},{default:l(()=>e[15]||(e[15]=[d("复制")])),_:1},8,["icon"])])]),default:l(()=>[r("div",Ze,[(c(!0),v(M,null,I(f.value,(s,W)=>(c(),v("div",{key:W,class:"profile-item"},[r("div",ea,w(U(W)),1),r("div",{class:Te(["profile-content",{"list-content":Array.isArray(s)}])},[Array.isArray(s)?(c(),v("div",aa,[(c(!0),v(M,null,I(s,(ie,ce)=>(c(),v("div",{key:ce,class:"list-item"},[t(o,{class:"list-icon"},{default:l(()=>[t(m(Be))]),_:1}),r("span",null,w(ie),1)]))),128))])):(c(),v(M,{key:1},[d(w(s),1)],64))],2)]))),128))])]),_:1})):(c(),E(g,{key:1,description:"点击开始抽取角色获取灵感"}))])]),_:1})]),_:1})])):(c(),v("div",Fe,[t(g,{description:"尚未导入角色数据"},{default:l(()=>[t(i,{type:"primary",icon:m(G),onClick:j,size:"large"},{default:l(()=>e[12]||(e[12]=[d(" 导入数据 (粘贴JSON) ")])),_:1},8,["icon"])]),_:1}),V.value?(c(),E(se,{key:0,title:V.value,type:"error","show-icon":"",class:"error-alert",closable:!1},null,8,["title"])):ge("",!0)]))]),_:2},[D.value?{name:"header",fn:l(()=>[r("div",Pe,[r("span",null,"角色数据已加载 ("+w(H.value)+" MBTI 类型)",1),r("div",Ue,[t(i,{type:"primary",icon:m(G),onClick:j},{default:l(()=>e[9]||(e[9]=[d("导入/替换数据")])),_:1},8,["icon"]),t(i,{type:"success",icon:m(ye),onClick:Y},{default:l(()=>e[10]||(e[10]=[d("导出JSON")])),_:1},8,["icon"]),t(i,{type:"warning",icon:m(we),onClick:Z},{default:l(()=>e[11]||(e[11]=[d("编辑数据")])),_:1},8,["icon"])])])]),key:"0"}:void 0]),1024),t(Q,{modelValue:b.value,"onUpdate:modelValue":e[4]||(e[4]=s=>b.value=s),title:"编辑角色数据 (JSON)",width:"60%","close-on-click-modal":!1,draggable:"","destroy-on-close":"",top:"10vh","modal-class":"edit-dialog-modal"},{footer:l(()=>[r("span",ta,[t(i,{onClick:e[3]||(e[3]=s=>b.value=!1)},{default:l(()=>e[16]||(e[16]=[d("取消")])),_:1}),t(i,{type:"primary",onClick:ee},{default:l(()=>e[17]||(e[17]=[d(" 保存修改 ")])),_:1})])]),default:l(()=>[t(q,{modelValue:N.value,"onUpdate:modelValue":e[2]||(e[2]=s=>N.value=s),type:"textarea",autosize:{minRows:15,maxRows:25},placeholder:"在这里编辑角色数据的JSON...",class:"json-editor-dialog"},null,8,["modelValue"])]),_:1},8,["modelValue"]),t(Q,{modelValue:h.value,"onUpdate:modelValue":e[7]||(e[7]=s=>h.value=s),title:"粘贴导入JSON数据",width:"50%","close-on-click-modal":!1,draggable:"","destroy-on-close":"",top:"15vh",class:"character-import-dialog"},{footer:l(()=>[r("div",oa,[t(i,{onClick:e[6]||(e[6]=s=>h.value=!1)},{default:l(()=>e[18]||(e[18]=[d("取消")])),_:1}),t(i,{type:"primary",onClick:X},{default:l(()=>e[19]||(e[19]=[d("执行导入")])),_:1})])]),default:l(()=>[r("div",la,[t(q,{modelValue:O.value,"onUpdate:modelValue":e[5]||(e[5]=s=>O.value=s),type:"textarea",autosize:{minRows:10,maxRows:20},placeholder:"在此处粘贴JSON数据...",resize:"none",class:"import-textarea"},null,8,["modelValue"])])]),_:1},8,["modelValue"])])}}},fa=ue(sa,[["__scopeId","data-v-1c6279c3"]]);export{fa as default};
