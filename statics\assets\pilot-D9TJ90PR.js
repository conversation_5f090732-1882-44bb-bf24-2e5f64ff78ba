import{_ as Ue,i as Je,r as c,c as q,w as ue,o as Te,E as p,b as r,m as i,e as l,d as o,g as u,v as w,B as Le,C,a0 as Me,t as Fe,R as y,S as b,M as G,aE as Ie,aD as $e,F as v,aF as Re,h as D,O as Ae,p as V,a4 as ce,V as je,P as He,co as qe,J as Ge,s as Ke,aw as We,bk as Ye,aN as Qe,k as Xe,n as Ze,aG as es,bm as ss,j as ts,q as ls,x as as,ah as ns,Y as de}from"./entry-DxFfH4M0.js";/* empty css                *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                 *//* empty css                  *//* empty css                 *//* empty css                        */import{u as os}from"./book-mHAKpUpK.js";const is={class:"pilot-generator glass-bg"},rs={class:"page-header"},us={class:"function-area glass-bg"},cs={class:"selectors-group"},ds={class:"entity-option"},vs={class:"option-name"},ps={class:"option-count"},ms={class:"scene-option"},_s={class:"option-name"},gs={class:"scene-option-controls"},fs={class:"option-count"},hs={class:"main-content"},ys={class:"dialog-header glass-morphism"},ws={class:"dialog-actions"},ks={key:0,class:"plot-content glass-morphism"},bs={class:"plot-section entities-section"},Vs={class:"section-header glass-card"},Cs={class:"entity-cards"},Es={class:"entity-header"},Ss={class:"entity-name"},xs={class:"entity-details"},Ps={class:"dimensions-container"},zs={class:"dimension-label"},Bs={class:"dimension-value"},Ds={class:"plot-section scenes-section"},Os={class:"section-header glass-card"},Ns={class:"scene-cards"},Us={class:"scene-header"},Js={class:"scene-name"},Ts={class:"scene-content"},Ls={key:0,class:"scene-description glass-effect"},Ms={key:1,class:"scene-elements"},Fs={class:"insight-section glass-card"},Is={class:"insight-header"},$s={class:"history-container"},Rs={class:"history-content"},As={class:"history-list"},js={key:0,class:"empty-history"},Hs=["onClick"],qs={class:"item-header"},Gs={class:"item-title"},Ks={class:"item-actions"},Ws={class:"item-time"},Ys={key:0,class:"history-detail"},Qs={class:"sections-container"},Xs={class:"detail-section"},Zs={class:"entity-cards"},et={class:"entity-header"},st={class:"entity-name"},tt={class:"entity-details"},lt={key:0,class:"dimensions-container"},at={class:"dimension-label"},nt={class:"dimension-value"},ot={key:1,class:"entity-description"},it={class:"detail-section"},rt={class:"scene-cards"},ut={class:"scene-header"},ct={class:"scene-name"},dt={class:"scene-content"},vt={key:0,class:"scene-description"},pt={key:1,class:"scene-elements"},mt={key:2,class:"scene-content-text"},_t={class:"detail-section"},gt={class:"insight-actions"},ft={key:0,class:"pagination-container"},ht={class:"dialog-footer"},yt={__name:"pilot",setup(wt){const ve=Je(),F=os(),m=c(""),I=c([]),Z=c([]),O=c([]),x=c([]),N=c([]),E=c({}),$=c(null),R=c([]),U=c([]),pe=c(null),J=c(!1),A=c(!1),T=c(!1),j=c(""),f=c([]),S=c(1),L=c(10),ee=q(()=>f.value.length),d=c(null),H=c(!1),M=c(""),se=c(null),P=c({title:""}),te=c(null),me=()=>{de(()=>{te.value?.input?.focus()})};q(()=>{const t={};return I.value.forEach(e=>{t[e.template]||(t[e.template]={template:e.template,entities:[]}),t[e.template].entities.push(e)}),Object.values(t)});const _e=q(()=>m.value&&O.value.length>0&&N.value.length>0),z=q(()=>{const t=(S.value-1)*L.value,e=t+L.value;return f.value.slice(t,e)});ue([S,L],()=>{d.value&&(z.value.some(e=>e.id===d.value.id)||(d.value=null)),!d.value&&z.value.length>0&&(d.value=z.value[0])});const ge=()=>{ve.push({name:"bookWriting"})},fe=t=>I.value.filter(e=>e.template_id===t.id).length,he=t=>Object.entries(t).filter(([e])=>!["id","name","template","template_id"].includes(e)).map(([e,a])=>({key:e,value:a})),le=async t=>{try{const e=await window.pywebview.api.book_controller.get_entities(t),a=typeof e=="string"?JSON.parse(e):e;a.status==="success"?I.value=a.data||[]:p.error(a.message||"加载实体失败")}catch(e){p.error("加载实体失败："+e.message)}},ae=async t=>{if(t)try{const e=await window.pywebview.api.book_controller.get_templates(t),a=typeof e=="string"?JSON.parse(e):e;a.status==="success"?Z.value=a.data||[]:p.error(a.message||"加载模板失败")}catch(e){p.error("加载模板失败："+e.message)}},K=async()=>{try{const t=await window.pywebview.api.book_controller.get_plots(m.value),e=typeof t=="string"?JSON.parse(t):t;if(e.status==="success")f.value=e.data||[],f.value.sort((a,n)=>new Date(n.created_at)-new Date(a.created_at)),S.value=1,f.value.length>0?d.value=f.value[0]:d.value=null;else throw new Error(e.message||"加载失败")}catch(t){console.error("加载灵感历史失败:",t),p.error("加载失败："+t.message)}};ue(A,async t=>{t&&m.value&&await K()});const ye=async t=>{try{O.value=[],N.value=[],E.value={},$.value=null,R.value=[],U.value=[],await Promise.all([ae(t),le(t),ne(t)]),await K(),p.success("书籍数据加载完成")}catch(e){console.error("加载书籍数据失败:",e),p.error("加载书籍数据失败，请重试")}},ne=async t=>{if(t)try{const e=await window.pywebview.api.book_controller.get_scene_events(t),a=typeof e=="string"?JSON.parse(e):e;if(a.status==="success"){const n=a.data||{},g=Array.isArray(n.pools)?n.pools:[];x.value=g.filter(h=>Array.isArray(h.scenes)&&h.scenes.length>0).map(h=>({id:h.id,name:h.name||"未命名卡池",scenes:h.scenes.map(k=>({id:k.id,title:k.title,description:k.description}))}))}else console.error("加载场景失败:",a.message),p.error(a.message||"加载场景失败"),x.value=[]}catch(e){console.error("加载场景失败:",e),p.error("加载场景失败："+e.message),x.value=[]}},oe=(t,e)=>[...t].sort(()=>.5-Math.random()).slice(0,e),ie=()=>{const t=O.value.map(a=>{const n=I.value.filter(g=>g.template_id===a);return oe(n,1)[0]}).filter(Boolean),e=N.value.map(a=>{const n=x.value.find(k=>k.id===a);if(!n)return null;const g=E.value[a]||1,h=oe(n.scenes,g);return{pool:n,scenes:h}}).filter(Boolean);R.value=t,U.value=e.flatMap(a=>a.scenes),pe.value=null,$.value={entities:t,scenes:U.value},J.value=!0},we=()=>{ie()},ke=()=>{P.value.title="",T.value=!0},W=async()=>{try{if(!m.value){p.error("请先选择书籍");return}const t={title:P.value.title.trim(),entities:R.value.map(n=>({id:n.id,name:n.name,template_id:n.template_id,dimensions:n.dimensions||{},description:n.description||""})),scenes:U.value.map(n=>({id:n.id,title:n.title,description:n.description||"",entities:n.entities||[],content:n.content||""})),insight:j.value,is_used:!1,created_at:new Date().toISOString()},e=await window.pywebview.api.book_controller.save_plot(m.value,t),a=typeof e=="string"?JSON.parse(e):e;if(a.status==="success")p.success("保存成功"),await K(),J.value=!1,T.value=!1,j.value="";else throw new Error(a.message||"保存失败")}catch(t){console.error("保存剧情组合失败:",t),p.error("保存失败："+t.message)}},be=async t=>{try{await ns.confirm("确定要删除这条灵感记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await window.pywebview.api.book_controller.delete_plot(m.value,t),a=typeof e=="string"?JSON.parse(e):e;if(a.status==="success")p.success("删除成功"),f.value=f.value.filter(n=>n.id!==t),z.value.length===0&&S.value>1&&S.value--,d.value?.id===t&&(d.value=z.value[0]||null);else throw new Error(a.message||"删除失败")}catch(e){if(e.toString().includes("cancel"))return;console.error("删除剧情组合失败:",e),p.error("删除失败："+e.message)}},Ve=t=>{const e=x.value.find(a=>a.id===t);e&&(E.value[t]>e.scenes.length&&(E.value[t]=e.scenes.length),E.value[t]<1&&(E.value[t]=1))},Ce=()=>{M.value=d.value.insight||"",H.value=!0,de(()=>{se.value?.input?.focus()})},Ee=()=>{H.value=!1,M.value=""},Se=async()=>{try{if(!d.value||!m.value)return;const t={...d.value,insight:M.value};console.log(t);const e=await window.pywebview.api.book_controller.update_plot(m.value,d.value.id,t),a=typeof e=="string"?JSON.parse(e):e;if(a.status==="success"){p.success("保存成功"),d.value={...t};const n=f.value.findIndex(g=>g.id===d.value.id);n!==-1&&(f.value[n]={...t}),H.value=!1}else throw new Error(a.message||"保存失败")}catch(t){console.error("保存感悟失败:",t),p.error(t.message||"保存失败")}},xe=async(t,e)=>{try{const a=f.value.find(k=>k.id===t);if(!a)return;const n={...a,is_used:e},g=await window.pywebview.api.book_controller.update_plot(m.value,t,n),h=typeof g=="string"?JSON.parse(g):g;if(h.status==="success"){p.success(e?"已标记为已使用":"已标记为未使用");const k=f.value.findIndex(Y=>Y.id===t);k!==-1&&(f.value[k]={...n}),d.value?.id===t&&(d.value={...n})}else throw new Error(h.message||"更新状态失败")}catch(a){console.error("更新使用状态失败:",a),p.error("更新状态失败："+a.message);const n=f.value.find(g=>g.id===t);n&&(n.is_used=!e)}},Pe=t=>{L.value=t,S.value=1},ze=t=>{S.value=t};return Te(async()=>{try{await F.loadBooks(),F.bookList?.length>0&&(m.value=F.bookList[0].id,await Promise.all([ae(m.value),le(m.value),ne(m.value)]))}catch(t){console.error("初始化失败:",t),p.error("初始化失败，请刷新页面重试")}}),(t,e)=>{const a=Le,n=Fe,g=Ie,h=$e,k=Re,Y=je,re=Ge,Q=Ke,X=Xe,Be=es,De=ss,Oe=ls,Ne=ts;return i(),r("div",is,[l("div",rs,[e[15]||(e[15]=l("h1",{class:"page-title"},"剧情生成器",-1)),o(n,{class:"back-button",onClick:ge,type:"primary",plain:""},{default:u(()=>[o(a,null,{default:u(()=>[o(C(Me))]),_:1}),e[14]||(e[14]=w(" 返回写作 "))]),_:1})]),l("div",us,[l("div",cs,[o(h,{modelValue:m.value,"onUpdate:modelValue":e[0]||(e[0]=s=>m.value=s),placeholder:"选择书籍",class:"selector-item",onChange:ye},{default:u(()=>[(i(!0),r(y,null,b(C(F).bookList,s=>(i(),G(g,{key:s.id,label:s.title,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),o(h,{modelValue:O.value,"onUpdate:modelValue":e[1]||(e[1]=s=>O.value=s),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",disabled:!m.value,placeholder:"选择实体模板",class:"selector-item"},{default:u(()=>[(i(!0),r(y,null,b(Z.value,s=>(i(),G(g,{key:s.id,label:s.name,value:s.id},{default:u(()=>[l("span",ds,[l("span",vs,v(s.name),1),l("span",ps,v(fe(s))+"个实体",1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled"]),o(h,{modelValue:N.value,"onUpdate:modelValue":e[3]||(e[3]=s=>N.value=s),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",disabled:!m.value,placeholder:"选择场景卡池",class:"selector-item"},{default:u(()=>[(i(!0),r(y,null,b(x.value,s=>(i(),G(g,{key:s.id,label:s.name,value:s.id},{default:u(()=>[l("div",ms,[l("span",_s,v(s.name),1),l("div",gs,[o(k,{modelValue:E.value[s.id],"onUpdate:modelValue":_=>E.value[s.id]=_,min:1,max:s.scenes?.length||1,size:"small",onChange:_=>Ve(s.id),onClick:e[2]||(e[2]=D(()=>{},["stop"]))},null,8,["modelValue","onUpdate:modelValue","max","onChange"]),l("span",fs,v(s.scenes?.length||0)+"个场景",1)])])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled"]),o(n,{type:"primary",disabled:!_e.value,onClick:ie,class:"generate-button"},{default:u(()=>e[16]||(e[16]=[w(" 生成剧情 ")])),_:1},8,["disabled"]),o(n,{type:"info",plain:"",disabled:!m.value,onClick:e[4]||(e[4]=s=>A.value=!0),class:"history-button"},{default:u(()=>[o(a,null,{default:u(()=>[o(C(Ae))]),_:1}),e[17]||(e[17]=w(" 灵感历史 "))]),_:1},8,["disabled"])])]),l("div",hs,[$.value?V("",!0):(i(),G(Y,{key:0,description:"请选择书籍、实体和场景后生成剧情","image-size":200},{image:u(()=>[o(a,{size:64,class:"empty-icon"},{default:u(()=>[o(C(ce))]),_:1})]),_:1}))]),o(X,{modelValue:J.value,"onUpdate:modelValue":e[7]||(e[7]=s=>J.value=s),fullscreen:!0,"show-close":!1,class:"plot-dialog"},{header:u(()=>[l("div",ys,[e[20]||(e[20]=l("h2",null,"剧情组合",-1)),l("div",ws,[o(n,{type:"primary",class:"action-button",onClick:we},{default:u(()=>[o(a,null,{default:u(()=>[o(C(We))]),_:1}),e[18]||(e[18]=w(" 重新生成 "))]),_:1}),o(n,{type:"success",class:"action-button",onClick:ke},{default:u(()=>[o(a,null,{default:u(()=>[o(C(Ye))]),_:1}),e[19]||(e[19]=w(" 保存剧情 "))]),_:1}),o(n,{class:"close-button",onClick:e[5]||(e[5]=s=>J.value=!1)},{default:u(()=>[o(a,null,{default:u(()=>[o(C(Qe))]),_:1})]),_:1})])])]),default:u(()=>[$.value?(i(),r("div",ks,[l("div",bs,[l("div",Vs,[o(a,null,{default:u(()=>[o(C(He))]),_:1}),e[21]||(e[21]=l("span",null,"参与角色",-1))]),l("div",Cs,[(i(!0),r(y,null,b(R.value,s=>(i(),r("div",{key:s.id,class:"entity-card glass-card"},[l("div",Es,[l("span",Ss,v(s.name),1)]),l("div",xs,[l("div",Ps,[(i(!0),r(y,null,b(he(s.dimensions),_=>(i(),r("div",{key:_.key,class:"dimension-item glass-effect"},[l("span",zs,v(_.key)+"：",1),l("span",Bs,v(_.value),1)]))),128))])])]))),128))])]),l("div",Ds,[l("div",Os,[o(a,null,{default:u(()=>[o(C(qe))]),_:1}),e[22]||(e[22]=l("span",null,"场景组合",-1))]),l("div",Ns,[(i(!0),r(y,null,b(U.value,s=>(i(),r("div",{key:s.id,class:"scene-card glass-card"},[l("div",Us,[l("span",Js,v(s.title),1)]),l("div",Ts,[s.description?(i(),r("div",Ls,v(s.description),1)):V("",!0),s.elements?(i(),r("div",Ms,[(i(!0),r(y,null,b(s.elements,(_,B)=>(i(),r("div",{class:"element-item",key:B},[o(re,{size:"small",class:"element-tag glass-tag"},{default:u(()=>[w(v(_),1)]),_:2},1024)]))),128))])):V("",!0)])]))),128))]),l("div",Fs,[l("div",Is,[o(a,null,{default:u(()=>[o(C(ce))]),_:1}),e[23]||(e[23]=l("span",null,"创作感悟",-1))]),o(Q,{modelValue:j.value,"onUpdate:modelValue":e[6]||(e[6]=s=>j.value=s),type:"textarea",rows:4,placeholder:"记录下这个剧情组合给你的灵感和感悟...",resize:"none",class:"insight-input glass-input"},null,8,["modelValue"])])])])):V("",!0)]),_:1},8,["modelValue"]),o(X,{modelValue:A.value,"onUpdate:modelValue":e[10]||(e[10]=s=>A.value=s),title:"灵感历史",fullscreen:!0,class:"history-dialog",style:{"user-select":"none"}},{default:u(()=>[l("div",$s,[l("div",Rs,[l("div",As,[f.value.length?(i(!0),r(y,{key:1},b(z.value,s=>(i(),r("div",{key:s.id,class:Ze(["history-item",{"is-active":d.value?.id===s.id}]),onClick:_=>d.value=s},[l("div",qs,[l("span",Gs,v(s.title),1),l("div",Ks,[o(Be,{modelValue:s.is_used,"onUpdate:modelValue":_=>s.is_used=_,onChange:_=>xe(s.id,_),size:"small","inline-prompt":"","active-text":"已用","inactive-text":"未用",onClick:e[8]||(e[8]=D(()=>{},["stop"]))},null,8,["modelValue","onUpdate:modelValue","onChange"]),o(n,{type:"danger",size:"small",onClick:D(_=>be(s.id),["stop"])},{default:u(()=>e[24]||(e[24]=[w(" 删除 ")])),_:2},1032,["onClick"])])]),l("div",Ws,v(new Date(s.created_at).toLocaleString()),1)],10,Hs))),128)):(i(),r("div",js," 暂无灵感历史记录 "))]),d.value?(i(),r("div",Ys,[l("div",Qs,[l("div",Xs,[e[25]||(e[25]=l("h3",null,"参与角色",-1)),l("div",Zs,[(i(!0),r(y,null,b(d.value.entities,s=>(i(),r("div",{key:s.id,class:"entity-card"},[l("div",et,[l("span",st,v(s.name),1)]),l("div",tt,[s.dimensions?(i(),r("div",lt,[(i(!0),r(y,null,b(s.dimensions,(_,B)=>(i(),r("div",{key:B,class:"dimension-item"},[l("span",at,v(B)+"：",1),l("span",nt,v(_),1)]))),128))])):V("",!0),s.description?(i(),r("div",ot,v(s.description),1)):V("",!0)])]))),128))])]),l("div",it,[e[26]||(e[26]=l("h3",null,"场景组合",-1)),l("div",rt,[(i(!0),r(y,null,b(d.value.scenes,s=>(i(),r("div",{key:s.id,class:"scene-card"},[l("div",ut,[l("span",ct,v(s.title),1)]),l("div",dt,[s.description?(i(),r("div",vt,v(s.description),1)):V("",!0),s.entities&&s.entities.length?(i(),r("div",pt,[(i(!0),r(y,null,b(s.entities,(_,B)=>(i(),r("div",{class:"element-item",key:B},[o(re,{size:"small",class:"element-tag"},{default:u(()=>[w(v(_),1)]),_:2},1024)]))),128))])):V("",!0),s.content?(i(),r("div",mt,v(s.content),1)):V("",!0)])]))),128))])])]),l("div",_t,[e[29]||(e[29]=l("h3",null,"创作感悟",-1)),l("div",{class:"insight-content",onDblclick:Ce},[H.value?(i(),r(y,{key:1},[o(Q,{modelValue:M.value,"onUpdate:modelValue":e[9]||(e[9]=s=>M.value=s),type:"textarea",rows:10,resize:"none",placeholder:"记录下这个剧情组合给你的灵感和感悟...",ref_key:"insightInput",ref:se},null,8,["modelValue"]),l("div",gt,[o(n,{type:"primary",size:"small",onClick:D(Se,["stop"])},{default:u(()=>e[27]||(e[27]=[w("保存修改")])),_:1}),o(n,{size:"small",onClick:D(Ee,["stop"])},{default:u(()=>e[28]||(e[28]=[w("取消")])),_:1})])],64)):(i(),r(y,{key:0},[w(v(d.value.insight||"暂无感悟"),1)],64))],32)])])):V("",!0)]),ee.value?(i(),r("div",ft,[o(De,{"current-page":S.value,"page-size":L.value,"page-sizes":[5,10,20,50],total:ee.value,layout:"total, sizes, prev, pager, next",onSizeChange:Pe,onCurrentChange:ze},null,8,["current-page","page-size","total"])])):V("",!0)])]),_:1},8,["modelValue"]),o(X,{modelValue:T.value,"onUpdate:modelValue":e[13]||(e[13]=s=>T.value=s),title:"保存剧情",width:"30%","close-on-click-modal":!1,"close-on-press-escape":!1,onOpened:me},{footer:u(()=>[l("span",ht,[o(n,{onClick:e[12]||(e[12]=s=>T.value=!1)},{default:u(()=>e[30]||(e[30]=[w("取消")])),_:1}),o(n,{type:"primary",onClick:W,disabled:!P.value.title},{default:u(()=>e[31]||(e[31]=[w(" 确认保存 ")])),_:1},8,["disabled"])])]),default:u(()=>[o(Ne,{model:P.value,"label-position":"top",onSubmit:D(W,["prevent"])},{default:u(()=>[o(Oe,{label:"剧情标题",required:""},{default:u(()=>[o(Q,{ref_key:"titleInputRef",ref:te,modelValue:P.value.title,"onUpdate:modelValue":e[11]||(e[11]=s=>P.value.title=s),placeholder:"请输入剧情标题",maxlength:"50","show-word-limit":"",onKeyup:as(W,["enter"])},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Dt=Ue(yt,[["__scopeId","data-v-672b39a5"]]);export{Dt as default};
