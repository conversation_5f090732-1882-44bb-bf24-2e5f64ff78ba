import{_ as ce,r as f,c as K,w as ve,o as _e,b as c,e as l,d as o,v as h,g as d,F as y,x as ye,C as p,p as k,n as q,R as C,S as O,bW as P,B as pe,be as fe,s as he,aK as we,bN as ge,ba as xe,t as be,k as me,a7 as J,E as w,m as r,O as ke,bX as Ce,M as G,aX as M,L as Ee,N as Q,bY as X,U as Z,bZ as Ve,b_ as Le,b$ as $e,a$ as De,bM as ee}from"./entry-DxFfH4M0.js";/* empty css                  *//* empty css                 *//* empty css                        *//* empty css                          *//* empty css                         *//* empty css                       *//* empty css                        */const Me={class:"chinese-dictionary"},Te={class:"app-container"},Re={class:"page-header"},Ae={class:"header-content"},Se={class:"title"},Be={class:"dictionary-type-selector-wrapper"},Ne={class:"dictionary-type-selector"},We={key:0,class:"loading-container"},Ue={class:"loading-text"},je={key:1,class:"main-content"},Ie={class:"settings-panel"},ze={class:"glass-card"},Fe={class:"card-header"},He={class:"settings search-section"},Ke={class:"search-box"},Oe={class:"search-options"},Pe={class:"card-header"},Ge={class:"dictionary-info"},Xe={class:"count-badge"},Ze={class:"settings"},Ye={class:"setting-item"},qe={class:"setting-label"},Je={class:"setting-control"},Qe={class:"setting-item"},el={class:"setting-label"},ll={class:"setting-control"},tl={class:"actions"},ol={class:"button-group"},al={class:"results-panel"},sl={key:0,class:"loading-container specific-loader"},nl={key:1,class:"loading-container specific-loader"},il={key:2,class:"glass-card result-card"},dl={class:"card-header"},ul={class:"result-count"},rl=["onClick"],cl={class:"word-content"},vl={class:"word-text riddle-text"},_l={class:"word-pinyin xiehouyu-answer"},yl={class:"word-text"},pl=["innerHTML"],fl={class:"word-footer"},hl={class:"word-category"},wl={key:3,class:"glass-card empty-tip"},gl={key:4,class:"glass-card empty-tip"},xl={key:5,class:"glass-card empty-tip"},bl={key:6,class:"glass-card empty-tip"},ml={key:7,class:"glass-card empty-tip"},kl={key:0,class:"word-detail glass-effect"},Cl={class:"detail-header"},El={class:"word-title"},Vl={class:"detail-category"},Ll={class:"detail-content"},$l={class:"section"},Dl={class:"section-content"},Ml={class:"detail-header"},Tl={class:"word-title"},Rl=["innerHTML"],Al={class:"detail-category"},Sl={class:"detail-content"},Bl={class:"section"},Nl={class:"section-content"},Wl={key:1},Ul={key:0,class:"section"},jl={class:"section-content"},Il={__name:"ChineseDictionary",setup(zl){const E=f([]),L=f([]),g=f(!1),x=f(!1),v=f(!1),U=f(!0),T=f(!1),a=f("xiehouyu"),R=K(()=>a.value==="word_entry"?E.value:a.value==="xiehouyu"?L.value:[]),A=f([2,3,4]),j=f(10),$=f(""),m=f("word"),S=f(!1),B=f(!1),b=f([]),I=f(!1),_=f(null),V=K(()=>R.value.length);K(()=>{const i=new Set;return R.value.forEach(e=>{e.category&&i.add(e.category)}),Array.from(i).map(e=>({value:e,label:e})).sort((e,t)=>e.label.localeCompare(t.label,"zh-CN"))});const z=i=>i?i.category||(i.type==="xiehouyu"?"歇后语":i.type==="word_entry"?"词汇":"未分类"):"未分类",F=i=>i?/[^a-zA-Zāáǎàēéěèīíǐìōóǒòūúǔùǖǘǚǜü\s]/.test(i)?`【${i}】`:`【${i}】`:"",N=async i=>{if(i==="word_entry"&&!g.value){v.value=!0;try{const e=await J(()=>import("./word-JUwTMPif.js"),[],import.meta.url);let t=e.default||e;Array.isArray(t)?E.value=t.map((n,s)=>({type:"word_entry",id:`word-${s}`,word:n.word,pinyin:n.pinyin||"",definition:n.explanation,category:"词汇",abbr:n.abbr})).filter(n=>n.word&&n.word.trim().length>0):typeof t=="object"&&t!==null?E.value=Object.values(t).map((n,s)=>({type:"word_entry",id:`word-${s}`,word:n.word,pinyin:n.pinyin||"",definition:n.explanation,category:"词汇",abbr:n.abbr})).filter(n=>n.word&&n.word.trim().length>0):console.warn("Word.json 数据格式不符合预期或为空"),g.value=!0,P({title:"成功",message:`现代词汇数据加载完成 (${E.value.length} 条)。`,type:"success"})}catch(e){console.error("Failed to load word.json on demand",e),w.error("现代词汇数据加载失败。")}finally{v.value=!1}}else if(i==="xiehouyu"&&!x.value){v.value=!0;try{const e=await J(()=>import("./xiehouyu-CzgqJG6Z.js"),[],import.meta.url);let t=e.default||e;Array.isArray(t)?L.value=t.map((n,s)=>({type:"xiehouyu",id:`xh-${s}`,word:n.riddle,definition:n.answer,pinyin:"",category:"歇后语",riddle:n.riddle,answer:n.answer})).filter(n=>n.riddle&&n.riddle.trim().length>0):console.warn("歇后语数据格式不符合预期或为空"),x.value=!0,P({title:"成功",message:`歇后语数据加载完成 (${L.value.length} 条)。`,type:"success"})}catch(e){console.error("Failed to load xiehouyu.json on demand",e),w.error("歇后语数据加载失败。")}finally{v.value=!1}}},le=async()=>{U.value=!0,await N("xiehouyu"),x.value&&L.value.length>0?(a.value="xiehouyu",m.value="riddle"):(a.value="word_entry",await N("word_entry"),g.value&&E.value.length>0?m.value="word":(a.value="xiehouyu",m.value="riddle",P({title:"提示",message:"所有词典数据均未能加载，请检查数据文件或网络连接。",type:"warning"}))),U.value=!1};ve(a,async(i,e)=>{b.value=[],$.value="",B.value=!1,i==="word_entry"?(m.value="word",g.value||await N("word_entry")):i==="xiehouyu"&&(m.value="riddle",x.value||await N("xiehouyu"))});const te=()=>{const i=R.value;if(i.length===0){w.warning(`当前${a.value==="word_entry"?"词汇":"歇后语"}词典数据为空，无法抽取`);return}T.value=!0,b.value=[];try{let e=i.filter(s=>{let W=!0;const D=s.type==="xiehouyu"?s.riddle:s.word;return A.value.length>0&&(W=A.value.includes(D?.length||0)),W});if(e.length===0){w.warning("没有符合条件的条目，请调整筛选条件"),T.value=!1;return}const t=Math.min(j.value,e.length),n=[];for(;n.length<t&&e.length>0;){const s=Math.floor(Math.random()*e.length);n.push(e[s]),e.splice(s,1)}b.value=n,B.value=!1,w.success(`成功抽取 ${n.length} 个条目`)}catch(e){console.error("随机抽取失败:",e),w.error("抽取失败，请重试")}finally{T.value=!1}},Y=()=>{const i=R.value;if(v.value){w.warning("词典数据正在加载中，请稍候...");return}if(i.length===0){w.warning(`当前${a.value==="word_entry"?"词汇":"歇后语"}词典数据为空，无法搜索`);return}if(!$.value.trim()){w.warning("请输入搜索关键词");return}S.value=!0,b.value=[];try{const e=$.value.trim().toLowerCase();let t=[];if(a.value==="word_entry"?m.value==="word"?t=i.filter(s=>s.word&&s.word.toLowerCase().includes(e)):m.value==="pinyin"?t=i.filter(s=>s.pinyin&&s.pinyin.toLowerCase().includes(e)):m.value==="definition"?t=i.filter(s=>s.definition&&s.definition.toLowerCase().includes(e)):t=i.filter(s=>s.word&&s.word.toLowerCase().includes(e)||s.definition&&s.definition.toLowerCase().includes(e)):a.value==="xiehouyu"&&(m.value==="riddle"||m.value==="word"?t=i.filter(s=>s.riddle&&s.riddle.toLowerCase().includes(e)):m.value==="answer"||m.value==="definition"?t=i.filter(s=>s.answer&&s.answer.toLowerCase().includes(e)):t=i.filter(s=>s.riddle&&s.riddle.toLowerCase().includes(e)||s.answer&&s.answer.toLowerCase().includes(e))),t.length===0){let s=a.value==="word_entry"?"词汇":"歇后语";w.warning(`在${s}中未找到相关条目`),S.value=!1;return}const n=100;t.length>n?(b.value=t.slice(0,n),w.info(`共找到 ${t.length} 个结果，仅显示前 ${n} 个`)):(b.value=t,w.success(`找到 ${t.length} 个结果`)),B.value=!0}catch(e){console.error("搜索词典失败:",e),w.error("搜索失败，请重试")}finally{S.value=!1}},oe=()=>{if(b.value.length===0){w.warning("没有可复制的内容");return}const i=b.value.map(e=>{let t="";return e.type==="word_entry"?(t=e.word||"",e.pinyin&&(t+=` ${F(e.pinyin)}`),e.definition&&(t+=`：${e.definition}`),e.abbr&&(t+=` (缩写: ${e.abbr})`)):e.type==="xiehouyu"&&(t=`歇后语：${e.riddle} —— ${e.answer}`),t}).join(`
`);window.pywebview.api.copy_to_clipboard(i).then(()=>{w.success("已复制到剪贴板")}).catch(e=>{console.error("复制失败:",e);try{const t=document.createElement("textarea");t.value=i,document.body.appendChild(t),t.focus(),t.select(),document.execCommand("copy"),document.body.removeChild(t),w.success("已复制到剪贴板 (备用方法)")}catch(t){console.error("备用复制方法也失败:",t),w.error("复制失败，请手动复制")}})},ae=i=>{_.value=i,I.value=!0};return _e(()=>{le()}),(i,e)=>{const t=pe,n=Ce,s=fe,W=Ee,D=be,se=he,ne=we,ie=Le,de=ge,ue=xe,re=me;return r(),c("div",Me,[e[32]||(e[32]=l("div",{class:"bg-decoration"},[l("div",{class:"bg-gradient"}),l("div",{class:"bg-circles"})],-1)),l("div",Te,[l("div",Re,[l("div",Ae,[l("h2",Se,[o(t,null,{default:d(()=>[o(p(ke))]),_:1}),e[6]||(e[6]=h(" 灵感词典 "))]),e[7]||(e[7]=l("p",{class:"subtitle"},"在词汇与歇后语的海洋中遨游，激发无限创作灵感",-1))]),l("div",Be,[l("div",Ne,[o(s,{modelValue:a.value,"onUpdate:modelValue":e[0]||(e[0]=u=>a.value=u),size:"large",disabled:v.value},{default:d(()=>[o(n,{value:"word_entry"},{default:d(()=>[h(" 现代词汇 ("+y(g.value?E.value.length:"未加载")+") ",1),a.value==="word_entry"&&v.value?(r(),G(t,{key:0,class:"el-icon--right is-loading"},{default:d(()=>[o(p(M))]),_:1})):k("",!0)]),_:1}),o(n,{value:"xiehouyu"},{default:d(()=>[h(" 歇后语 ("+y(x.value?L.value.length:"未加载")+") ",1),a.value==="xiehouyu"&&v.value?(r(),G(t,{key:0,class:"el-icon--right is-loading"},{default:d(()=>[o(p(M))]),_:1})):k("",!0)]),_:1})]),_:1},8,["modelValue","disabled"])])])]),U.value?(r(),c("div",We,[o(W,{rows:10,animated:""}),l("div",Ue,[o(t,{class:"loading-icon"},{default:d(()=>[o(p(M))]),_:1}),e[8]||(e[8]=h(" 正在加载词典数据，请稍候... "))])])):(r(),c("div",je,[l("div",Ie,[l("div",ze,[l("div",Fe,[l("h3",null,[o(t,null,{default:d(()=>[o(p(Q))]),_:1}),h(" "+y(a.value==="xiehouyu"?"歇后语搜索":"词汇搜索"),1)])]),l("div",He,[l("div",Ke,[o(se,{modelValue:$.value,"onUpdate:modelValue":e[1]||(e[1]=u=>$.value=u),placeholder:a.value==="xiehouyu"?"搜索谜面或谜底":"搜索词语、拼音或释义",clearable:"","prefix-icon":p(Q),onKeyup:ye(Y,["enter"]),disabled:v.value||a.value==="word_entry"&&!g.value||a.value==="xiehouyu"&&!x.value},{append:d(()=>[o(D,{onClick:Y,loading:S.value,disabled:v.value||a.value==="word_entry"&&!g.value||a.value==="xiehouyu"&&!x.value},{default:d(()=>e[9]||(e[9]=[h(" 搜索 ")])),_:1},8,["loading","disabled"])]),_:1},8,["modelValue","placeholder","prefix-icon","disabled"])]),l("div",Oe,[o(s,{modelValue:m.value,"onUpdate:modelValue":e[2]||(e[2]=u=>m.value=u),size:"small",disabled:v.value||a.value==="word_entry"&&!g.value||a.value==="xiehouyu"&&!x.value},{default:d(()=>[a.value==="word_entry"?(r(),c(C,{key:0},[o(n,{value:"word"},{default:d(()=>e[10]||(e[10]=[h("词语")])),_:1}),o(n,{value:"pinyin"},{default:d(()=>e[11]||(e[11]=[h("拼音")])),_:1}),o(n,{value:"definition"},{default:d(()=>e[12]||(e[12]=[h("释义")])),_:1})],64)):a.value==="xiehouyu"?(r(),c(C,{key:1},[o(n,{value:"riddle"},{default:d(()=>e[13]||(e[13]=[h("谜面")])),_:1}),o(n,{value:"answer"},{default:d(()=>e[14]||(e[14]=[h("谜底")])),_:1})],64)):k("",!0)]),_:1},8,["modelValue","disabled"])])]),e[19]||(e[19]=l("div",{class:"separator"},[l("div",{class:"separator-line"}),l("div",{class:"separator-text"},"或者"),l("div",{class:"separator-line"})],-1)),l("div",Pe,[l("h3",null,[o(t,null,{default:d(()=>[o(p(X))]),_:1}),e[15]||(e[15]=h(" 随机抽取 "))]),l("div",Ge,[o(ne,{content:`当前词典 (${a.value==="word_entry"?"词汇":"歇后语"}) 总条目数`},{default:d(()=>[l("div",Xe,[o(t,null,{default:d(()=>[o(p(Z))]),_:1}),l("span",null,y(a.value==="word_entry"&&g.value||a.value==="xiehouyu"&&x.value?V.value:"-")+" 条",1)])]),_:1},8,["content"])])]),l("div",Ze,[l("div",Ye,[l("div",qe,[o(t,null,{default:d(()=>[o(p(Ve))]),_:1}),l("span",null,y(a.value==="xiehouyu"?"谜面长度":"词语长度")+"：",1)]),l("div",Je,[o(de,{modelValue:A.value,"onUpdate:modelValue":e[3]||(e[3]=u=>A.value=u),disabled:v.value||a.value==="word_entry"&&!g.value||a.value==="xiehouyu"&&!x.value},{default:d(()=>[(r(!0),c(C,null,O(a.value==="xiehouyu"?[3,4,5,6,7,8,9,10]:[1,2,3,4,5,6],u=>(r(),G(ie,{key:u,value:u},{default:d(()=>[h(y(u)+"字 ",1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue","disabled"])])]),l("div",Qe,[l("div",el,[o(t,null,{default:d(()=>[o(p($e))]),_:1}),e[16]||(e[16]=l("span",null,"抽取数量：",-1))]),l("div",ll,[o(ue,{modelValue:j.value,"onUpdate:modelValue":e[4]||(e[4]=u=>j.value=u),min:1,max:50,step:1,"show-stops":"","show-input":"",marks:{1:"1",10:"10",25:"25",50:"50"},disabled:v.value||a.value==="word_entry"&&!g.value||a.value==="xiehouyu"&&!x.value},null,8,["modelValue","disabled"])])]),l("div",tl,[l("div",ol,[o(D,{type:"primary",class:"extract-button",icon:p(X),onClick:te,disabled:v.value||T.value||V.value===0||a.value==="word_entry"&&!g.value||a.value==="xiehouyu"&&!x.value},{default:d(()=>e[17]||(e[17]=[h(" 随机抽取 ")])),_:1},8,["icon","disabled"]),o(D,{type:"success",class:"copy-button",icon:p(De),onClick:oe,disabled:!b.value.length},{default:d(()=>e[18]||(e[18]=[h(" 复制结果 ")])),_:1},8,["icon","disabled"])])])])])]),l("div",al,[v.value&&a.value==="word_entry"&&!g.value?(r(),c("div",sl,[o(t,{class:"loading-icon"},{default:d(()=>[o(p(M))]),_:1}),e[20]||(e[20]=h()),e[21]||(e[21]=l("span",null,"正在加载现代词汇数据...",-1))])):v.value&&a.value==="xiehouyu"&&!x.value?(r(),c("div",nl,[o(t,{class:"loading-icon"},{default:d(()=>[o(p(M))]),_:1}),e[22]||(e[22]=h()),e[23]||(e[23]=l("span",null,"正在加载歇后语数据...",-1))])):b.value.length>0?(r(),c("div",il,[l("div",dl,[l("h3",null,y(B.value?"搜索结果":"抽取结果"),1),l("div",ul,y(b.value.length)+" 条",1)]),l("div",{class:q(["result-grid",{"large-grid":b.value.length<=15,"medium-grid":b.value.length>15&&b.value.length<=24,"small-grid":b.value.length>24}])},[(r(!0),c(C,null,O(b.value,(u,H)=>(r(),c("div",{key:u.id||H,class:"word-card",onClick:Fl=>ae(u)},[l("div",cl,[u.type==="xiehouyu"?(r(),c(C,{key:0},[l("div",vl,y(u.riddle),1),l("div",_l,y(u.answer),1)],64)):u.type==="word_entry"?(r(),c(C,{key:1},[l("div",yl,y(u.word),1),u.pinyin?(r(),c("div",{key:0,class:"word-pinyin",innerHTML:F(u.pinyin)},null,8,pl)):k("",!0)],64)):k("",!0)]),l("div",fl,[l("span",hl,y(z(u)),1)])],8,rl))),128))],2)])):!v.value&&(a.value==="word_entry"&&g.value&&V.value>0||a.value==="xiehouyu"&&x.value&&V.value>0)?(r(),c("div",wl,[o(t,null,{default:d(()=>[o(p(X))]),_:1}),e[24]||(e[24]=l("p",null,"请在上方操作面板中搜索或随机抽取条目",-1))])):!v.value&&a.value==="word_entry"&&g.value&&V.value===0?(r(),c("div",gl,[o(t,null,{default:d(()=>[o(p(Z))]),_:1}),e[25]||(e[25]=l("p",null,"现代词汇数据为空，但已加载。",-1))])):!v.value&&a.value==="xiehouyu"&&x.value&&V.value===0?(r(),c("div",xl,[o(t,null,{default:d(()=>[o(p(Z))]),_:1}),e[26]||(e[26]=l("p",null,"歇后语数据为空，但已加载。",-1))])):!v.value&&a.value==="word_entry"&&!g.value?(r(),c("div",bl,[o(t,null,{default:d(()=>[o(p(ee))]),_:1}),e[27]||(e[27]=l("p",null,"现代词汇数据加载失败或尚未加载。请尝试切换词典类型或刷新页面。",-1))])):!v.value&&a.value==="xiehouyu"&&!x.value?(r(),c("div",ml,[o(t,null,{default:d(()=>[o(p(ee))]),_:1}),e[28]||(e[28]=l("p",null,"歇后语数据加载失败或尚未加载。请尝试切换词典类型或刷新页面。",-1))])):k("",!0)])]))]),o(re,{modelValue:I.value,"onUpdate:modelValue":e[5]||(e[5]=u=>I.value=u),title:_.value?.type==="xiehouyu"?"歇后语详情":_.value?.word||"词条详情",width:"600px","close-on-click-modal":!0,"modal-class":"detail-dialog-bg","show-close":!0,"destroy-on-close":"",top:"8vh"},{default:d(()=>[_.value?(r(),c("div",kl,[_.value.type==="xiehouyu"?(r(),c(C,{key:0},[l("div",Cl,[l("div",El,[l("h3",null,y(_.value.riddle),1)]),l("div",Vl,y(z(_.value)),1)]),l("div",Ll,[l("div",$l,[e[29]||(e[29]=l("div",{class:"section-title"},"谜底",-1)),l("div",Dl,[l("p",{class:q({"dialog-xiehouyu-answer":_.value.type==="xiehouyu"})},y(_.value.answer),3)])])])],64)):(r(),c(C,{key:1},[l("div",Ml,[l("div",Tl,[l("h3",null,y(_.value.word),1),_.value.pinyin?(r(),c("div",{key:0,class:"word-pinyin",innerHTML:F(_.value.pinyin)},null,8,Rl)):k("",!0)]),l("div",Al,y(z(_.value)),1)]),l("div",Sl,[l("div",Bl,[e[30]||(e[30]=l("div",{class:"section-title"},"释义",-1)),l("div",Nl,[_.value.definition&&typeof _.value.definition=="string"&&_.value.definition.includes(`
`)?(r(!0),c(C,{key:0},O(_.value.definition.split(`
`).filter(u=>u.trim()),(u,H)=>(r(),c("p",{key:H,class:"definition-item"},y(u),1))),128)):(r(),c("p",Wl,y(_.value.definition||"暂无释义"),1))])]),_.value.type==="word_entry"&&_.value.abbr?(r(),c("div",Ul,[e[31]||(e[31]=l("div",{class:"section-title"},"缩写",-1)),l("div",jl,[l("p",null,y(_.value.abbr),1)])])):k("",!0)])],64))])):k("",!0)]),_:1},8,["modelValue","title"])])}}},ql=ce(Il,[["__scopeId","data-v-fd090c25"]]);export{ql as default};
