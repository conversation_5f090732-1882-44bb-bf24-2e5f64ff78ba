import{_ as Ar,r as <PERSON>,l as Dr,E as x,i as Or,c as U,w as Xt,o as Ir,$ as Rr,b as I,e as _,d as f,F as O,g as h,n as He,a8 as Xe,ap as Kt,M as le,af as zr,R as ue,Y as Ke,t as Mr,aD as Lr,s as Jr,ab as qr,az as Br,bm as Pr,k as Ur,m as A,v as S,B as Fr,C as $,O as Hr,U as Yt,a0 as Xr,S as Ee,aE as Kr,J as Yr,N as Gt,ad as Gr,ae as Qr,bn as Qt,a9 as _t,V as Wr,ax as Zr,p as Ne,b2 as jr,h as G,ay as ei,au as Wt,aW as Zt,a4 as ti,bj as ni,x as jt,bk as en,aN as ri,j as ii,q as si,aA as oi,bo as ai,aP as li,aQ as ui,G as tn,ah as Ye}from"./entry-DxFfH4M0.js";/* empty css                   *//* empty css                         *//* empty css                *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    *//* empty css                 */import{d as ci}from"./vuedraggable-umd-CerWkR59.js";var fi={value:()=>{}};function yn(){for(var e=0,t=arguments.length,n={},r;e<t;++e){if(!(r=arguments[e]+"")||r in n||/[\s.]/.test(r))throw new Error("illegal type: "+r);n[r]=[]}return new Ze(n)}function Ze(e){this._=e}function di(e,t){return e.trim().split(/^|\s+/).map(function(n){var r="",s=n.indexOf(".");if(s>=0&&(r=n.slice(s+1),n=n.slice(0,s)),n&&!t.hasOwnProperty(n))throw new Error("unknown type: "+n);return{type:n,name:r}})}Ze.prototype=yn.prototype={constructor:Ze,on:function(e,t){var n=this._,r=di(e+"",n),s,a=-1,o=r.length;if(arguments.length<2){for(;++a<o;)if((s=(e=r[a]).type)&&(s=pi(n[s],e.name)))return s;return}if(t!=null&&typeof t!="function")throw new Error("invalid callback: "+t);for(;++a<o;)if(s=(e=r[a]).type)n[s]=nn(n[s],e.name,t);else if(t==null)for(s in n)n[s]=nn(n[s],e.name,null);return this},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new Ze(e)},call:function(e,t){if((s=arguments.length-2)>0)for(var n=new Array(s),r=0,s,a;r<s;++r)n[r]=arguments[r+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(a=this._[e],r=0,s=a.length;r<s;++r)a[r].value.apply(t,n)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var r=this._[e],s=0,a=r.length;s<a;++s)r[s].value.apply(t,n)}};function pi(e,t){for(var n=0,r=e.length,s;n<r;++n)if((s=e[n]).name===t)return s.value}function nn(e,t,n){for(var r=0,s=e.length;r<s;++r)if(e[r].name===t){e[r]=fi,e=e.slice(0,r).concat(e.slice(r+1));break}return n!=null&&e.push({name:t,value:n}),e}var wt="http://www.w3.org/1999/xhtml";const rn={svg:"http://www.w3.org/2000/svg",xhtml:wt,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function at(e){var t=e+="",n=t.indexOf(":");return n>=0&&(t=e.slice(0,n))!=="xmlns"&&(e=e.slice(n+1)),rn.hasOwnProperty(t)?{space:rn[t],local:e}:e}function mi(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===wt&&t.documentElement.namespaceURI===wt?t.createElement(e):t.createElementNS(n,e)}}function hi(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function wn(e){var t=at(e);return(t.local?hi:mi)(t)}function vi(){}function St(e){return e==null?vi:function(){return this.querySelector(e)}}function _i(e){typeof e!="function"&&(e=St(e));for(var t=this._groups,n=t.length,r=new Array(n),s=0;s<n;++s)for(var a=t[s],o=a.length,u=r[s]=new Array(o),c,d,p=0;p<o;++p)(c=a[p])&&(d=e.call(c,c.__data__,p,a))&&("__data__"in c&&(d.__data__=c.__data__),u[p]=d);return new M(r,this._parents)}function gi(e){return e==null?[]:Array.isArray(e)?e:Array.from(e)}function yi(){return[]}function xn(e){return e==null?yi:function(){return this.querySelectorAll(e)}}function wi(e){return function(){return gi(e.apply(this,arguments))}}function xi(e){typeof e=="function"?e=wi(e):e=xn(e);for(var t=this._groups,n=t.length,r=[],s=[],a=0;a<n;++a)for(var o=t[a],u=o.length,c,d=0;d<u;++d)(c=o[d])&&(r.push(e.call(c,c.__data__,d,o)),s.push(c));return new M(r,s)}function bn(e){return function(){return this.matches(e)}}function kn(e){return function(t){return t.matches(e)}}var bi=Array.prototype.find;function ki(e){return function(){return bi.call(this.children,e)}}function Ei(){return this.firstElementChild}function Ni(e){return this.select(e==null?Ei:ki(typeof e=="function"?e:kn(e)))}var Ci=Array.prototype.filter;function Si(){return Array.from(this.children)}function Ti(e){return function(){return Ci.call(this.children,e)}}function $i(e){return this.selectAll(e==null?Si:Ti(typeof e=="function"?e:kn(e)))}function Vi(e){typeof e!="function"&&(e=bn(e));for(var t=this._groups,n=t.length,r=new Array(n),s=0;s<n;++s)for(var a=t[s],o=a.length,u=r[s]=[],c,d=0;d<o;++d)(c=a[d])&&e.call(c,c.__data__,d,a)&&u.push(c);return new M(r,this._parents)}function En(e){return new Array(e.length)}function Ai(){return new M(this._enter||this._groups.map(En),this._parents)}function tt(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}tt.prototype={constructor:tt,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};function Di(e){return function(){return e}}function Oi(e,t,n,r,s,a){for(var o=0,u,c=t.length,d=a.length;o<d;++o)(u=t[o])?(u.__data__=a[o],r[o]=u):n[o]=new tt(e,a[o]);for(;o<c;++o)(u=t[o])&&(s[o]=u)}function Ii(e,t,n,r,s,a,o){var u,c,d=new Map,p=t.length,y=a.length,w=new Array(p),k;for(u=0;u<p;++u)(c=t[u])&&(w[u]=k=o.call(c,c.__data__,u,t)+"",d.has(k)?s[u]=c:d.set(k,c));for(u=0;u<y;++u)k=o.call(e,a[u],u,a)+"",(c=d.get(k))?(r[u]=c,c.__data__=a[u],d.delete(k)):n[u]=new tt(e,a[u]);for(u=0;u<p;++u)(c=t[u])&&d.get(w[u])===c&&(s[u]=c)}function Ri(e){return e.__data__}function zi(e,t){if(!arguments.length)return Array.from(this,Ri);var n=t?Ii:Oi,r=this._parents,s=this._groups;typeof e!="function"&&(e=Di(e));for(var a=s.length,o=new Array(a),u=new Array(a),c=new Array(a),d=0;d<a;++d){var p=r[d],y=s[d],w=y.length,k=Mi(e.call(p,p&&p.__data__,d,r)),N=k.length,b=u[d]=new Array(N),P=o[d]=new Array(N),X=c[d]=new Array(w);n(p,y,b,P,X,k,t);for(var J=0,j=0,ze,Me;J<N;++J)if(ze=b[J]){for(J>=j&&(j=J+1);!(Me=P[j])&&++j<N;);ze._next=Me||null}}return o=new M(o,r),o._enter=u,o._exit=c,o}function Mi(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function Li(){return new M(this._exit||this._groups.map(En),this._parents)}function Ji(e,t,n){var r=this.enter(),s=this,a=this.exit();return typeof e=="function"?(r=e(r),r&&(r=r.selection())):r=r.append(e+""),t!=null&&(s=t(s),s&&(s=s.selection())),n==null?a.remove():n(a),r&&s?r.merge(s).order():s}function qi(e){for(var t=e.selection?e.selection():e,n=this._groups,r=t._groups,s=n.length,a=r.length,o=Math.min(s,a),u=new Array(s),c=0;c<o;++c)for(var d=n[c],p=r[c],y=d.length,w=u[c]=new Array(y),k,N=0;N<y;++N)(k=d[N]||p[N])&&(w[N]=k);for(;c<s;++c)u[c]=n[c];return new M(u,this._parents)}function Bi(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var r=e[t],s=r.length-1,a=r[s],o;--s>=0;)(o=r[s])&&(a&&o.compareDocumentPosition(a)^4&&a.parentNode.insertBefore(o,a),a=o);return this}function Pi(e){e||(e=Ui);function t(y,w){return y&&w?e(y.__data__,w.__data__):!y-!w}for(var n=this._groups,r=n.length,s=new Array(r),a=0;a<r;++a){for(var o=n[a],u=o.length,c=s[a]=new Array(u),d,p=0;p<u;++p)(d=o[p])&&(c[p]=d);c.sort(t)}return new M(s,this._parents).order()}function Ui(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function Fi(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this}function Hi(){return Array.from(this)}function Xi(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],s=0,a=r.length;s<a;++s){var o=r[s];if(o)return o}return null}function Ki(){let e=0;for(const t of this)++e;return e}function Yi(){return!this.node()}function Gi(e){for(var t=this._groups,n=0,r=t.length;n<r;++n)for(var s=t[n],a=0,o=s.length,u;a<o;++a)(u=s[a])&&e.call(u,u.__data__,a,s);return this}function Qi(e){return function(){this.removeAttribute(e)}}function Wi(e){return function(){this.removeAttributeNS(e.space,e.local)}}function Zi(e,t){return function(){this.setAttribute(e,t)}}function ji(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function es(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttribute(e):this.setAttribute(e,n)}}function ts(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function ns(e,t){var n=at(e);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((t==null?n.local?Wi:Qi:typeof t=="function"?n.local?ts:es:n.local?ji:Zi)(n,t))}function Nn(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function rs(e){return function(){this.style.removeProperty(e)}}function is(e,t,n){return function(){this.style.setProperty(e,t,n)}}function ss(e,t,n){return function(){var r=t.apply(this,arguments);r==null?this.style.removeProperty(e):this.style.setProperty(e,r,n)}}function os(e,t,n){return arguments.length>1?this.each((t==null?rs:typeof t=="function"?ss:is)(e,t,n??"")):fe(this.node(),e)}function fe(e,t){return e.style.getPropertyValue(t)||Nn(e).getComputedStyle(e,null).getPropertyValue(t)}function as(e){return function(){delete this[e]}}function ls(e,t){return function(){this[e]=t}}function us(e,t){return function(){var n=t.apply(this,arguments);n==null?delete this[e]:this[e]=n}}function cs(e,t){return arguments.length>1?this.each((t==null?as:typeof t=="function"?us:ls)(e,t)):this.node()[e]}function Cn(e){return e.trim().split(/^|\s+/)}function Tt(e){return e.classList||new Sn(e)}function Sn(e){this._node=e,this._names=Cn(e.getAttribute("class")||"")}Sn.prototype={add:function(e){var t=this._names.indexOf(e);t<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};function Tn(e,t){for(var n=Tt(e),r=-1,s=t.length;++r<s;)n.add(t[r])}function $n(e,t){for(var n=Tt(e),r=-1,s=t.length;++r<s;)n.remove(t[r])}function fs(e){return function(){Tn(this,e)}}function ds(e){return function(){$n(this,e)}}function ps(e,t){return function(){(t.apply(this,arguments)?Tn:$n)(this,e)}}function ms(e,t){var n=Cn(e+"");if(arguments.length<2){for(var r=Tt(this.node()),s=-1,a=n.length;++s<a;)if(!r.contains(n[s]))return!1;return!0}return this.each((typeof t=="function"?ps:t?fs:ds)(n,t))}function hs(){this.textContent=""}function vs(e){return function(){this.textContent=e}}function _s(e){return function(){var t=e.apply(this,arguments);this.textContent=t??""}}function gs(e){return arguments.length?this.each(e==null?hs:(typeof e=="function"?_s:vs)(e)):this.node().textContent}function ys(){this.innerHTML=""}function ws(e){return function(){this.innerHTML=e}}function xs(e){return function(){var t=e.apply(this,arguments);this.innerHTML=t??""}}function bs(e){return arguments.length?this.each(e==null?ys:(typeof e=="function"?xs:ws)(e)):this.node().innerHTML}function ks(){this.nextSibling&&this.parentNode.appendChild(this)}function Es(){return this.each(ks)}function Ns(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Cs(){return this.each(Ns)}function Ss(e){var t=typeof e=="function"?e:wn(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})}function Ts(){return null}function $s(e,t){var n=typeof e=="function"?e:wn(e),r=t==null?Ts:typeof t=="function"?t:St(t);return this.select(function(){return this.insertBefore(n.apply(this,arguments),r.apply(this,arguments)||null)})}function Vs(){var e=this.parentNode;e&&e.removeChild(this)}function As(){return this.each(Vs)}function Ds(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Os(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Is(e){return this.select(e?Os:Ds)}function Rs(e){return arguments.length?this.property("__data__",e):this.node().__data__}function zs(e){return function(t){e.call(this,t,this.__data__)}}function Ms(e){return e.trim().split(/^|\s+/).map(function(t){var n="",r=t.indexOf(".");return r>=0&&(n=t.slice(r+1),t=t.slice(0,r)),{type:t,name:n}})}function Ls(e){return function(){var t=this.__on;if(t){for(var n=0,r=-1,s=t.length,a;n<s;++n)a=t[n],(!e.type||a.type===e.type)&&a.name===e.name?this.removeEventListener(a.type,a.listener,a.options):t[++r]=a;++r?t.length=r:delete this.__on}}}function Js(e,t,n){return function(){var r=this.__on,s,a=zs(t);if(r){for(var o=0,u=r.length;o<u;++o)if((s=r[o]).type===e.type&&s.name===e.name){this.removeEventListener(s.type,s.listener,s.options),this.addEventListener(s.type,s.listener=a,s.options=n),s.value=t;return}}this.addEventListener(e.type,a,n),s={type:e.type,name:e.name,value:t,listener:a,options:n},r?r.push(s):this.__on=[s]}}function qs(e,t,n){var r=Ms(e+""),s,a=r.length,o;if(arguments.length<2){var u=this.node().__on;if(u){for(var c=0,d=u.length,p;c<d;++c)for(s=0,p=u[c];s<a;++s)if((o=r[s]).type===p.type&&o.name===p.name)return p.value}return}for(u=t?Js:Ls,s=0;s<a;++s)this.each(u(r[s],t,n));return this}function Vn(e,t,n){var r=Nn(e),s=r.CustomEvent;typeof s=="function"?s=new s(t,n):(s=r.document.createEvent("Event"),n?(s.initEvent(t,n.bubbles,n.cancelable),s.detail=n.detail):s.initEvent(t,!1,!1)),e.dispatchEvent(s)}function Bs(e,t){return function(){return Vn(this,e,t)}}function Ps(e,t){return function(){return Vn(this,e,t.apply(this,arguments))}}function Us(e,t){return this.each((typeof t=="function"?Ps:Bs)(e,t))}function*Fs(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],s=0,a=r.length,o;s<a;++s)(o=r[s])&&(yield o)}var Hs=[null];function M(e,t){this._groups=e,this._parents=t}function Ie(){return new M([[document.documentElement]],Hs)}function Xs(){return this}M.prototype=Ie.prototype={constructor:M,select:_i,selectAll:xi,selectChild:Ni,selectChildren:$i,filter:Vi,data:zi,enter:Ai,exit:Li,join:Ji,merge:qi,selection:Xs,order:Bi,sort:Pi,call:Fi,nodes:Hi,node:Xi,size:Ki,empty:Yi,each:Gi,attr:ns,style:os,property:cs,classed:ms,text:gs,html:bs,raise:Es,lower:Cs,append:Ss,insert:$s,remove:As,clone:Is,datum:Rs,on:qs,dispatch:Us,[Symbol.iterator]:Fs};function $t(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function An(e,t){var n=Object.create(e.prototype);for(var r in t)n[r]=t[r];return n}function Re(){}var Ve=.7,nt=1/Ve,ce="\\s*([+-]?\\d+)\\s*",Ae="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",q="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Ks=/^#([0-9a-f]{3,8})$/,Ys=new RegExp(`^rgb\\(${ce},${ce},${ce}\\)$`),Gs=new RegExp(`^rgb\\(${q},${q},${q}\\)$`),Qs=new RegExp(`^rgba\\(${ce},${ce},${ce},${Ae}\\)$`),Ws=new RegExp(`^rgba\\(${q},${q},${q},${Ae}\\)$`),Zs=new RegExp(`^hsl\\(${Ae},${q},${q}\\)$`),js=new RegExp(`^hsla\\(${Ae},${q},${q},${Ae}\\)$`),sn={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};$t(Re,De,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:on,formatHex:on,formatHex8:eo,formatHsl:to,formatRgb:an,toString:an});function on(){return this.rgb().formatHex()}function eo(){return this.rgb().formatHex8()}function to(){return Dn(this).formatHsl()}function an(){return this.rgb().formatRgb()}function De(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=Ks.exec(e))?(n=t[1].length,t=parseInt(t[1],16),n===6?ln(t):n===3?new R(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):n===8?Ge(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):n===4?Ge(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=Ys.exec(e))?new R(t[1],t[2],t[3],1):(t=Gs.exec(e))?new R(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=Qs.exec(e))?Ge(t[1],t[2],t[3],t[4]):(t=Ws.exec(e))?Ge(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=Zs.exec(e))?fn(t[1],t[2]/100,t[3]/100,1):(t=js.exec(e))?fn(t[1],t[2]/100,t[3]/100,t[4]):sn.hasOwnProperty(e)?ln(sn[e]):e==="transparent"?new R(NaN,NaN,NaN,0):null}function ln(e){return new R(e>>16&255,e>>8&255,e&255,1)}function Ge(e,t,n,r){return r<=0&&(e=t=n=NaN),new R(e,t,n,r)}function no(e){return e instanceof Re||(e=De(e)),e?(e=e.rgb(),new R(e.r,e.g,e.b,e.opacity)):new R}function xt(e,t,n,r){return arguments.length===1?no(e):new R(e,t,n,r??1)}function R(e,t,n,r){this.r=+e,this.g=+t,this.b=+n,this.opacity=+r}$t(R,xt,An(Re,{brighter(e){return e=e==null?nt:Math.pow(nt,e),new R(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?Ve:Math.pow(Ve,e),new R(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new R(W(this.r),W(this.g),W(this.b),rt(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:un,formatHex:un,formatHex8:ro,formatRgb:cn,toString:cn}));function un(){return`#${Q(this.r)}${Q(this.g)}${Q(this.b)}`}function ro(){return`#${Q(this.r)}${Q(this.g)}${Q(this.b)}${Q((isNaN(this.opacity)?1:this.opacity)*255)}`}function cn(){const e=rt(this.opacity);return`${e===1?"rgb(":"rgba("}${W(this.r)}, ${W(this.g)}, ${W(this.b)}${e===1?")":`, ${e})`}`}function rt(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function W(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Q(e){return e=W(e),(e<16?"0":"")+e.toString(16)}function fn(e,t,n,r){return r<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new z(e,t,n,r)}function Dn(e){if(e instanceof z)return new z(e.h,e.s,e.l,e.opacity);if(e instanceof Re||(e=De(e)),!e)return new z;if(e instanceof z)return e;e=e.rgb();var t=e.r/255,n=e.g/255,r=e.b/255,s=Math.min(t,n,r),a=Math.max(t,n,r),o=NaN,u=a-s,c=(a+s)/2;return u?(t===a?o=(n-r)/u+(n<r)*6:n===a?o=(r-t)/u+2:o=(t-n)/u+4,u/=c<.5?a+s:2-a-s,o*=60):u=c>0&&c<1?0:o,new z(o,u,c,e.opacity)}function io(e,t,n,r){return arguments.length===1?Dn(e):new z(e,t,n,r??1)}function z(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}$t(z,io,An(Re,{brighter(e){return e=e==null?nt:Math.pow(nt,e),new z(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?Ve:Math.pow(Ve,e),new z(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*t,s=2*n-r;return new R(gt(e>=240?e-240:e+120,s,r),gt(e,s,r),gt(e<120?e+240:e-120,s,r),this.opacity)},clamp(){return new z(dn(this.h),Qe(this.s),Qe(this.l),rt(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=rt(this.opacity);return`${e===1?"hsl(":"hsla("}${dn(this.h)}, ${Qe(this.s)*100}%, ${Qe(this.l)*100}%${e===1?")":`, ${e})`}`}}));function dn(e){return e=(e||0)%360,e<0?e+360:e}function Qe(e){return Math.max(0,Math.min(1,e||0))}function gt(e,t,n){return(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)*255}const On=e=>()=>e;function so(e,t){return function(n){return e+n*t}}function oo(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(r){return Math.pow(e+r*t,n)}}function ao(e){return(e=+e)==1?In:function(t,n){return n-t?oo(t,n,e):On(isNaN(t)?n:t)}}function In(e,t){var n=t-e;return n?so(e,n):On(isNaN(e)?t:e)}const pn=function e(t){var n=ao(t);function r(s,a){var o=n((s=xt(s)).r,(a=xt(a)).r),u=n(s.g,a.g),c=n(s.b,a.b),d=In(s.opacity,a.opacity);return function(p){return s.r=o(p),s.g=u(p),s.b=c(p),s.opacity=d(p),s+""}}return r.gamma=e,r}(1);function Y(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}var bt=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,yt=new RegExp(bt.source,"g");function lo(e){return function(){return e}}function uo(e){return function(t){return e(t)+""}}function co(e,t){var n=bt.lastIndex=yt.lastIndex=0,r,s,a,o=-1,u=[],c=[];for(e=e+"",t=t+"";(r=bt.exec(e))&&(s=yt.exec(t));)(a=s.index)>n&&(a=t.slice(n,a),u[o]?u[o]+=a:u[++o]=a),(r=r[0])===(s=s[0])?u[o]?u[o]+=s:u[++o]=s:(u[++o]=null,c.push({i:o,x:Y(r,s)})),n=yt.lastIndex;return n<t.length&&(a=t.slice(n),u[o]?u[o]+=a:u[++o]=a),u.length<2?c[0]?uo(c[0].x):lo(t):(t=c.length,function(d){for(var p=0,y;p<t;++p)u[(y=c[p]).i]=y.x(d);return u.join("")})}var mn=180/Math.PI,kt={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function Rn(e,t,n,r,s,a){var o,u,c;return(o=Math.sqrt(e*e+t*t))&&(e/=o,t/=o),(c=e*n+t*r)&&(n-=e*c,r-=t*c),(u=Math.sqrt(n*n+r*r))&&(n/=u,r/=u,c/=u),e*r<t*n&&(e=-e,t=-t,c=-c,o=-o),{translateX:s,translateY:a,rotate:Math.atan2(t,e)*mn,skewX:Math.atan(c)*mn,scaleX:o,scaleY:u}}var We;function fo(e){const t=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?kt:Rn(t.a,t.b,t.c,t.d,t.e,t.f)}function po(e){return e==null||(We||(We=document.createElementNS("http://www.w3.org/2000/svg","g")),We.setAttribute("transform",e),!(e=We.transform.baseVal.consolidate()))?kt:(e=e.matrix,Rn(e.a,e.b,e.c,e.d,e.e,e.f))}function zn(e,t,n,r){function s(d){return d.length?d.pop()+" ":""}function a(d,p,y,w,k,N){if(d!==y||p!==w){var b=k.push("translate(",null,t,null,n);N.push({i:b-4,x:Y(d,y)},{i:b-2,x:Y(p,w)})}else(y||w)&&k.push("translate("+y+t+w+n)}function o(d,p,y,w){d!==p?(d-p>180?p+=360:p-d>180&&(d+=360),w.push({i:y.push(s(y)+"rotate(",null,r)-2,x:Y(d,p)})):p&&y.push(s(y)+"rotate("+p+r)}function u(d,p,y,w){d!==p?w.push({i:y.push(s(y)+"skewX(",null,r)-2,x:Y(d,p)}):p&&y.push(s(y)+"skewX("+p+r)}function c(d,p,y,w,k,N){if(d!==y||p!==w){var b=k.push(s(k)+"scale(",null,",",null,")");N.push({i:b-4,x:Y(d,y)},{i:b-2,x:Y(p,w)})}else(y!==1||w!==1)&&k.push(s(k)+"scale("+y+","+w+")")}return function(d,p){var y=[],w=[];return d=e(d),p=e(p),a(d.translateX,d.translateY,p.translateX,p.translateY,y,w),o(d.rotate,p.rotate,y,w),u(d.skewX,p.skewX,y,w),c(d.scaleX,d.scaleY,p.scaleX,p.scaleY,y,w),d=p=null,function(k){for(var N=-1,b=w.length,P;++N<b;)y[(P=w[N]).i]=P.x(k);return y.join("")}}}var mo=zn(fo,"px, ","px)","deg)"),ho=zn(po,", ",")",")"),de=0,Se=0,Ce=0,Mn=1e3,it,Te,st=0,Z=0,lt=0,Oe=typeof performance=="object"&&performance.now?performance:Date,Ln=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function Vt(){return Z||(Ln(vo),Z=Oe.now()+lt)}function vo(){Z=0}function ot(){this._call=this._time=this._next=null}ot.prototype=Jn.prototype={constructor:ot,restart:function(e,t,n){if(typeof e!="function")throw new TypeError("callback is not a function");n=(n==null?Vt():+n)+(t==null?0:+t),!this._next&&Te!==this&&(Te?Te._next=this:it=this,Te=this),this._call=e,this._time=n,Et()},stop:function(){this._call&&(this._call=null,this._time=1/0,Et())}};function Jn(e,t,n){var r=new ot;return r.restart(e,t,n),r}function _o(){Vt(),++de;for(var e=it,t;e;)(t=Z-e._time)>=0&&e._call.call(void 0,t),e=e._next;--de}function hn(){Z=(st=Oe.now())+lt,de=Se=0;try{_o()}finally{de=0,yo(),Z=0}}function go(){var e=Oe.now(),t=e-st;t>Mn&&(lt-=t,st=e)}function yo(){for(var e,t=it,n,r=1/0;t;)t._call?(r>t._time&&(r=t._time),e=t,t=t._next):(n=t._next,t._next=null,t=e?e._next=n:it=n);Te=e,Et(r)}function Et(e){if(!de){Se&&(Se=clearTimeout(Se));var t=e-Z;t>24?(e<1/0&&(Se=setTimeout(hn,e-Oe.now()-lt)),Ce&&(Ce=clearInterval(Ce))):(Ce||(st=Oe.now(),Ce=setInterval(go,Mn)),de=1,Ln(hn))}}function vn(e,t,n){var r=new ot;return t=t==null?0:+t,r.restart(s=>{r.stop(),e(s+t)},t,n),r}var wo=yn("start","end","cancel","interrupt"),xo=[],qn=0,_n=1,Nt=2,je=3,gn=4,Ct=5,et=6;function ut(e,t,n,r,s,a){var o=e.__transition;if(!o)e.__transition={};else if(n in o)return;bo(e,n,{name:t,index:r,group:s,on:wo,tween:xo,time:a.time,delay:a.delay,duration:a.duration,ease:a.ease,timer:null,state:qn})}function At(e,t){var n=L(e,t);if(n.state>qn)throw new Error("too late; already scheduled");return n}function B(e,t){var n=L(e,t);if(n.state>je)throw new Error("too late; already running");return n}function L(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function bo(e,t,n){var r=e.__transition,s;r[t]=n,n.timer=Jn(a,0,n.time);function a(d){n.state=_n,n.timer.restart(o,n.delay,n.time),n.delay<=d&&o(d-n.delay)}function o(d){var p,y,w,k;if(n.state!==_n)return c();for(p in r)if(k=r[p],k.name===n.name){if(k.state===je)return vn(o);k.state===gn?(k.state=et,k.timer.stop(),k.on.call("interrupt",e,e.__data__,k.index,k.group),delete r[p]):+p<t&&(k.state=et,k.timer.stop(),k.on.call("cancel",e,e.__data__,k.index,k.group),delete r[p])}if(vn(function(){n.state===je&&(n.state=gn,n.timer.restart(u,n.delay,n.time),u(d))}),n.state=Nt,n.on.call("start",e,e.__data__,n.index,n.group),n.state===Nt){for(n.state=je,s=new Array(w=n.tween.length),p=0,y=-1;p<w;++p)(k=n.tween[p].value.call(e,e.__data__,n.index,n.group))&&(s[++y]=k);s.length=y+1}}function u(d){for(var p=d<n.duration?n.ease.call(null,d/n.duration):(n.timer.restart(c),n.state=Ct,1),y=-1,w=s.length;++y<w;)s[y].call(e,p);n.state===Ct&&(n.on.call("end",e,e.__data__,n.index,n.group),c())}function c(){n.state=et,n.timer.stop(),delete r[t];for(var d in r)return;delete e.__transition}}function ko(e,t){var n=e.__transition,r,s,a=!0,o;if(n){t=t==null?null:t+"";for(o in n){if((r=n[o]).name!==t){a=!1;continue}s=r.state>Nt&&r.state<Ct,r.state=et,r.timer.stop(),r.on.call(s?"interrupt":"cancel",e,e.__data__,r.index,r.group),delete n[o]}a&&delete e.__transition}}function Eo(e){return this.each(function(){ko(this,e)})}function No(e,t){var n,r;return function(){var s=B(this,e),a=s.tween;if(a!==n){r=n=a;for(var o=0,u=r.length;o<u;++o)if(r[o].name===t){r=r.slice(),r.splice(o,1);break}}s.tween=r}}function Co(e,t,n){var r,s;if(typeof n!="function")throw new Error;return function(){var a=B(this,e),o=a.tween;if(o!==r){s=(r=o).slice();for(var u={name:t,value:n},c=0,d=s.length;c<d;++c)if(s[c].name===t){s[c]=u;break}c===d&&s.push(u)}a.tween=s}}function So(e,t){var n=this._id;if(e+="",arguments.length<2){for(var r=L(this.node(),n).tween,s=0,a=r.length,o;s<a;++s)if((o=r[s]).name===e)return o.value;return null}return this.each((t==null?No:Co)(n,e,t))}function Dt(e,t,n){var r=e._id;return e.each(function(){var s=B(this,r);(s.value||(s.value={}))[t]=n.apply(this,arguments)}),function(s){return L(s,r).value[t]}}function Bn(e,t){var n;return(typeof t=="number"?Y:t instanceof De?pn:(n=De(t))?(t=n,pn):co)(e,t)}function To(e){return function(){this.removeAttribute(e)}}function $o(e){return function(){this.removeAttributeNS(e.space,e.local)}}function Vo(e,t,n){var r,s=n+"",a;return function(){var o=this.getAttribute(e);return o===s?null:o===r?a:a=t(r=o,n)}}function Ao(e,t,n){var r,s=n+"",a;return function(){var o=this.getAttributeNS(e.space,e.local);return o===s?null:o===r?a:a=t(r=o,n)}}function Do(e,t,n){var r,s,a;return function(){var o,u=n(this),c;return u==null?void this.removeAttribute(e):(o=this.getAttribute(e),c=u+"",o===c?null:o===r&&c===s?a:(s=c,a=t(r=o,u)))}}function Oo(e,t,n){var r,s,a;return function(){var o,u=n(this),c;return u==null?void this.removeAttributeNS(e.space,e.local):(o=this.getAttributeNS(e.space,e.local),c=u+"",o===c?null:o===r&&c===s?a:(s=c,a=t(r=o,u)))}}function Io(e,t){var n=at(e),r=n==="transform"?ho:Bn;return this.attrTween(e,typeof t=="function"?(n.local?Oo:Do)(n,r,Dt(this,"attr."+e,t)):t==null?(n.local?$o:To)(n):(n.local?Ao:Vo)(n,r,t))}function Ro(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}function zo(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}function Mo(e,t){var n,r;function s(){var a=t.apply(this,arguments);return a!==r&&(n=(r=a)&&zo(e,a)),n}return s._value=t,s}function Lo(e,t){var n,r;function s(){var a=t.apply(this,arguments);return a!==r&&(n=(r=a)&&Ro(e,a)),n}return s._value=t,s}function Jo(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(t==null)return this.tween(n,null);if(typeof t!="function")throw new Error;var r=at(e);return this.tween(n,(r.local?Mo:Lo)(r,t))}function qo(e,t){return function(){At(this,e).delay=+t.apply(this,arguments)}}function Bo(e,t){return t=+t,function(){At(this,e).delay=t}}function Po(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?qo:Bo)(t,e)):L(this.node(),t).delay}function Uo(e,t){return function(){B(this,e).duration=+t.apply(this,arguments)}}function Fo(e,t){return t=+t,function(){B(this,e).duration=t}}function Ho(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?Uo:Fo)(t,e)):L(this.node(),t).duration}function Xo(e,t){if(typeof t!="function")throw new Error;return function(){B(this,e).ease=t}}function Ko(e){var t=this._id;return arguments.length?this.each(Xo(t,e)):L(this.node(),t).ease}function Yo(e,t){return function(){var n=t.apply(this,arguments);if(typeof n!="function")throw new Error;B(this,e).ease=n}}function Go(e){if(typeof e!="function")throw new Error;return this.each(Yo(this._id,e))}function Qo(e){typeof e!="function"&&(e=bn(e));for(var t=this._groups,n=t.length,r=new Array(n),s=0;s<n;++s)for(var a=t[s],o=a.length,u=r[s]=[],c,d=0;d<o;++d)(c=a[d])&&e.call(c,c.__data__,d,a)&&u.push(c);return new H(r,this._parents,this._name,this._id)}function Wo(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,r=t.length,s=n.length,a=Math.min(r,s),o=new Array(r),u=0;u<a;++u)for(var c=t[u],d=n[u],p=c.length,y=o[u]=new Array(p),w,k=0;k<p;++k)(w=c[k]||d[k])&&(y[k]=w);for(;u<r;++u)o[u]=t[u];return new H(o,this._parents,this._name,this._id)}function Zo(e){return(e+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||t==="start"})}function jo(e,t,n){var r,s,a=Zo(t)?At:B;return function(){var o=a(this,e),u=o.on;u!==r&&(s=(r=u).copy()).on(t,n),o.on=s}}function ea(e,t){var n=this._id;return arguments.length<2?L(this.node(),n).on.on(e):this.each(jo(n,e,t))}function ta(e){return function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}}function na(){return this.on("end.remove",ta(this._id))}function ra(e){var t=this._name,n=this._id;typeof e!="function"&&(e=St(e));for(var r=this._groups,s=r.length,a=new Array(s),o=0;o<s;++o)for(var u=r[o],c=u.length,d=a[o]=new Array(c),p,y,w=0;w<c;++w)(p=u[w])&&(y=e.call(p,p.__data__,w,u))&&("__data__"in p&&(y.__data__=p.__data__),d[w]=y,ut(d[w],t,n,w,d,L(p,n)));return new H(a,this._parents,t,n)}function ia(e){var t=this._name,n=this._id;typeof e!="function"&&(e=xn(e));for(var r=this._groups,s=r.length,a=[],o=[],u=0;u<s;++u)for(var c=r[u],d=c.length,p,y=0;y<d;++y)if(p=c[y]){for(var w=e.call(p,p.__data__,y,c),k,N=L(p,n),b=0,P=w.length;b<P;++b)(k=w[b])&&ut(k,t,n,b,w,N);a.push(w),o.push(p)}return new H(a,o,t,n)}var sa=Ie.prototype.constructor;function oa(){return new sa(this._groups,this._parents)}function aa(e,t){var n,r,s;return function(){var a=fe(this,e),o=(this.style.removeProperty(e),fe(this,e));return a===o?null:a===n&&o===r?s:s=t(n=a,r=o)}}function Pn(e){return function(){this.style.removeProperty(e)}}function la(e,t,n){var r,s=n+"",a;return function(){var o=fe(this,e);return o===s?null:o===r?a:a=t(r=o,n)}}function ua(e,t,n){var r,s,a;return function(){var o=fe(this,e),u=n(this),c=u+"";return u==null&&(c=u=(this.style.removeProperty(e),fe(this,e))),o===c?null:o===r&&c===s?a:(s=c,a=t(r=o,u))}}function ca(e,t){var n,r,s,a="style."+t,o="end."+a,u;return function(){var c=B(this,e),d=c.on,p=c.value[a]==null?u||(u=Pn(t)):void 0;(d!==n||s!==p)&&(r=(n=d).copy()).on(o,s=p),c.on=r}}function fa(e,t,n){var r=(e+="")=="transform"?mo:Bn;return t==null?this.styleTween(e,aa(e,r)).on("end.style."+e,Pn(e)):typeof t=="function"?this.styleTween(e,ua(e,r,Dt(this,"style."+e,t))).each(ca(this._id,e)):this.styleTween(e,la(e,r,t),n).on("end.style."+e,null)}function da(e,t,n){return function(r){this.style.setProperty(e,t.call(this,r),n)}}function pa(e,t,n){var r,s;function a(){var o=t.apply(this,arguments);return o!==s&&(r=(s=o)&&da(e,o,n)),r}return a._value=t,a}function ma(e,t,n){var r="style."+(e+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(t==null)return this.tween(r,null);if(typeof t!="function")throw new Error;return this.tween(r,pa(e,t,n??""))}function ha(e){return function(){this.textContent=e}}function va(e){return function(){var t=e(this);this.textContent=t??""}}function _a(e){return this.tween("text",typeof e=="function"?va(Dt(this,"text",e)):ha(e==null?"":e+""))}function ga(e){return function(t){this.textContent=e.call(this,t)}}function ya(e){var t,n;function r(){var s=e.apply(this,arguments);return s!==n&&(t=(n=s)&&ga(s)),t}return r._value=e,r}function wa(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(e==null)return this.tween(t,null);if(typeof e!="function")throw new Error;return this.tween(t,ya(e))}function xa(){for(var e=this._name,t=this._id,n=Un(),r=this._groups,s=r.length,a=0;a<s;++a)for(var o=r[a],u=o.length,c,d=0;d<u;++d)if(c=o[d]){var p=L(c,t);ut(c,e,n,d,o,{time:p.time+p.delay+p.duration,delay:0,duration:p.duration,ease:p.ease})}return new H(r,this._parents,e,n)}function ba(){var e,t,n=this,r=n._id,s=n.size();return new Promise(function(a,o){var u={value:o},c={value:function(){--s===0&&a()}};n.each(function(){var d=B(this,r),p=d.on;p!==e&&(t=(e=p).copy(),t._.cancel.push(u),t._.interrupt.push(u),t._.end.push(c)),d.on=t}),s===0&&a()})}var ka=0;function H(e,t,n,r){this._groups=e,this._parents=t,this._name=n,this._id=r}function Un(){return++ka}var F=Ie.prototype;H.prototype={constructor:H,select:ra,selectAll:ia,selectChild:F.selectChild,selectChildren:F.selectChildren,filter:Qo,merge:Wo,selection:oa,transition:xa,call:F.call,nodes:F.nodes,node:F.node,size:F.size,empty:F.empty,each:F.each,on:ea,attr:Io,attrTween:Jo,style:fa,styleTween:ma,text:_a,textTween:wa,remove:na,tween:So,delay:Po,duration:Ho,ease:Ko,easeVarying:Go,end:ba,[Symbol.iterator]:F[Symbol.iterator]};function Ea(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}var Na={time:null,delay:0,duration:250,ease:Ea};function Ca(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}function Sa(e){var t,n;e instanceof H?(t=e._id,e=e._name):(t=Un(),(n=Na).time=Vt(),e=e==null?null:e+"");for(var r=this._groups,s=r.length,a=0;a<s;++a)for(var o=r[a],u=o.length,c,d=0;d<u;++d)(c=o[d])&&ut(c,e,t,d,o,n||Ca(c,t));return new H(r,this._parents,e,t)}Ie.prototype.interrupt=Eo;Ie.prototype.transition=Sa;function $e(e,t,n){this.k=e,this.x=t,this.y=n}$e.prototype={constructor:$e,scale:function(e){return e===1?this:new $e(this.k*e,this.x,this.y)},translate:function(e,t){return e===0&t===0?this:new $e(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};$e.prototype;const Ta={class:"setting-manager glass-bg"},$a={class:"page-header"},Va={class:"left-section"},Aa={class:"book-title"},Da={class:"tab-buttons"},Oa={class:"main-content"},Ia={class:"content-container"},Ra={class:"entities-container glass-bg"},za={class:"section-header"},Ma={class:"header-content"},La={class:"template-option"},Ja={class:"template-name"},qa={class:"button-group"},Ba={class:"table-container"},Pa={class:"entity-name-cell"},Ua={class:"entity-name"},Fa={class:"entity-meta"},Ha={class:"dimension-grid"},Xa={class:"dimension-label"},Ka={class:"dimension-value"},Ya={key:0,class:"dimension-item more-dimensions"},Ga={class:"dimension-value"},Qa={class:"operation-buttons-wrapper"},Wa={class:"pagination-container"},Za={class:"templates-container glass-bg"},ja={class:"section-header"},el={class:"header-content"},tl={class:"template-actions"},nl={class:"table-container"},rl=["data-index"],il={class:"sort-number"},sl={class:"template-name-cell"},ol={class:"template-name"},al={key:0,class:"template-description"},ll={class:"dimensions-grid"},ul={key:0,class:"dimension-chip more-chip"},cl={class:"template-stats"},fl={class:"stat-item"},dl={class:"stat-value"},pl={class:"stat-item"},ml={class:"stat-value"},hl={class:"operation-buttons-wrapper"},vl={class:"pagination-container"},_l={class:"dialog-fixed-content"},gl={class:"basic-info-section"},yl={class:"form-row"},wl={class:"form-field"},xl={class:"form-row"},bl={class:"form-field full-width"},kl={class:"dialog-scrollable-content"},El={class:"add-dimension-section"},Nl={key:0,class:"add-dimension-trigger"},Cl={key:1,class:"dimension-input-inline"},Sl={class:"dimensions-list-container"},Tl={class:"dimensions-list-scrollable"},$l={key:0,class:"empty-dimensions"},Vl={key:1,class:"dimensions-list-native"},Al={class:"dimension-item-native"},Dl={class:"dimension-text"},Ol={class:"dialog-footer-fixed"},Il={class:"entity-card"},Rl={class:"entity-header"},zl={key:0,class:"entity-dimensions"},Ml={class:"dimensions-container"},Ll={class:"dialog-footer"},Jl={class:"entity-card"},ql={class:"entity-header"},Bl={key:0,class:"entity-dimensions"},Pl={class:"dimensions-container"},Ul={class:"dialog-footer"},Fl={class:"import-content"},Hl={class:"format-hint-title"},Xl={class:"dialog-footer"},Kl={class:"import-content"},Yl={class:"format-hint-title"},Gl={class:"dialog-footer"},Ql={__name:"设定",setup(e){const t=Dr(),n=Or(),r=C(t.query.id||t.params.id),s=C(t.query.title||t.params.title);r.value||(x.error("未找到书籍信息"),n.push("/book/writing"));const a=C("entities"),o=C(""),u=C(!1),c=C(!1),d=C(!1),p=C(!1),y=C([]),w=C([]),k=C([]);C(null);const N=C({name:"",description:"",dimensions:[]}),b=C({});C(!1),C(""),C(null);const P=C(null),X=C(1),J=C(10),j=U(()=>Ot.value.length),ze=U(()=>{const l=(X.value-1)*J.value,i=l+J.value;return Ot.value.slice(l,i)}),Me=l=>{X.value=l},Fn=l=>{J.value=l,X.value=1},pe=C(""),Hn=()=>{pe.value="",X.value=1},Ot=U(()=>{if(!o.value)return[];let l=w.value.filter(i=>i.template_id===o.value);if(pe.value.trim()){const i=pe.value.toLowerCase().trim();l=l.filter(m=>m.name.toLowerCase().includes(i))}return l.sort((i,m)=>{const g=new Date(i.updated_at||i.created_at).getTime();return new Date(m.updated_at||m.created_at).getTime()-g}),l}),It=U(()=>N.value.name.trim()&&N.value.dimensions.length>0),ct=U(()=>y.value.find(l=>l.id===o.value)),Xn=l=>{const i=y.value.find(m=>m.id===l);return i?i.name:"未知模板"},ft=l=>k.value.filter(i=>i.template_id===l.id).length,Kn=()=>{n.push({name:"bookWriting"})},Rt=(l={})=>{N.value={name:"",description:"",dimensions:[],...l},u.value=!0},zt=async()=>{try{console.log(r.value);const l=await window.pywebview.api.book_controller.save_template({...N.value,book_id:r.value}),i=typeof l=="string"?JSON.parse(l):l;i.status==="success"?(x.success("保存成功"),u.value=!1,me()):x.error(i.message||"保存失败")}catch(l){x.error("保存失败："+l.message)}},Yn=async l=>{try{await Ye.confirm("删除模板将同时删除该模板下的所有实体，是否继续？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const i=await window.pywebview.api.book_controller.delete_template(l.id,r.value),m=typeof i=="string"?JSON.parse(i):i;m.status==="success"?(x.success("删除成功"),me(),await he()):x.error(m.message||"删除失败")}catch(i){i!=="cancel"&&x.error("删除失败："+i.message)}},Gn=(l={})=>{const i=y.value.find(m=>m.id===(l.template_id||o.value));if(b.value={id:"",name:"",description:"",template_id:i?.id||"",dimensions:{},...l},i&&i.dimensions){const m=i.dimensions.reduce((g,E)=>(g[E.name]=b.value.dimensions?.[E.name]||"",g),{});b.value.dimensions=m}c.value=!0},ee=l=>{l.key==="Enter"&&(l.shiftKey?(l.preventDefault(),te()):!l.ctrlKey&&!l.altKey&&!l.metaKey&&l.target.tagName!=="TEXTAREA"&&(l.preventDefault(),te().then(()=>{c.value=!1,d.value=!1})))},te=async()=>{try{if(!b.value.name)return x.error("请输入实体名称"),Promise.reject("请输入实体名称");if(!b.value.template_id)return x.error("请选择模板类型"),Promise.reject("请选择模板类型");const l=y.value.find(E=>E.id===b.value.template_id);if(!l)return x.error("无效的模板类型"),Promise.reject("无效的模板类型");if(w.value.find(E=>E.template_id===b.value.template_id&&E.name===b.value.name&&E.id!==b.value.id))return x.error(`当前模板下已存在名为"${b.value.name}"的实体`),Promise.reject(`当前模板下已存在名为"${b.value.name}"的实体`);l.dimensions.forEach(E=>{const T=b.value.dimensions[E.name];T==null||typeof T=="string"&&T.trim()===""?b.value.dimensions[E.name]="未设定":b.value.dimensions[E.name]=String(T)});const m=await window.pywebview.api.book_controller.save_entity({...b.value,book_id:r.value}),g=typeof m=="string"?JSON.parse(m):m;if(g.status==="success")return x.success(b.value.id?"更新成功":"创建成功"),c.value=!1,await he(),Promise.resolve();throw new Error(g.message||"保存失败")}catch(l){return console.error("保存实体失败:",l),x.error("保存失败："+l.message),Promise.reject(l)}},Qn=async l=>{try{await Ye.confirm("确定要删除这个实体吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const i=await window.pywebview.api.book_controller.delete_entity(l.id,r.value,l.template_id),m=typeof i=="string"?JSON.parse(i):i;if(m.status==="success"){x.success("删除成功");const g=k.value.findIndex(E=>E.id===l.id);g!==-1&&k.value.splice(g,1),o.value&&(w.value=k.value.filter(E=>E.template_id===o.value)),await he()}else x.error(m.message||"删除失败")}catch(i){i!=="cancel"&&x.error("删除失败："+i.message)}},Wn=async l=>{o.value=l,l?w.value=k.value.filter(i=>i.template_id===l):w.value=[]},Zn=(l,i=3)=>{const g=Object.entries(l).slice(0,i);return Object.fromEntries(g)},jn=U(()=>b.value?.template_id?y.value.find(i=>i.id===b.value.template_id)?.dimensions||[]:[]),er=l=>{const i=y.value.find(m=>m.id===l.template_id);if(!i){x.error("找不到对应的模板");return}b.value=JSON.parse(JSON.stringify(l)),b.value.dimensions||(b.value.dimensions={}),i.dimensions.forEach(m=>{m.name in b.value.dimensions||(b.value.dimensions[m.name]="")}),d.value=!0},me=async()=>{p.value=!0;try{const l=await window.pywebview.api.book_controller.get_templates(r.value),i=typeof l=="string"?JSON.parse(l):l;if(i.status==="success"){let m=i.data||[];m.sort((g,E)=>g.sort_order!==void 0&&E.sort_order!==void 0?g.sort_order-E.sort_order:g.sort_order!==void 0?-1:E.sort_order!==void 0?1:new Date(g.created_at||0)-new Date(E.created_at||0)),y.value=m,y.value.length>0&&!o.value&&(o.value=y.value[0].id),await he(),setTimeout(()=>{Je()},100)}else x.error(i.message||"加载模板失败")}catch(l){x.error("加载模板失败："+l.message)}finally{p.value=!1}},he=async()=>{try{const l=await window.pywebview.api.book_controller.get_entities(r.value),i=typeof l=="string"?JSON.parse(l):l;i.status==="success"?(k.value=i.data||[],o.value&&(w.value=k.value.filter(m=>m.template_id===o.value))):x.error(i.message||"加载实体失败")}catch(l){console.error("加载实体失败：",l),x.error("加载实体失败："+l.message)}},Le=C(!1),ne=C(-1),Je=()=>{Ke(()=>{const l=document.querySelector(".sortable-table .el-table__body-wrapper tbody");if(!l){console.log("未找到表格body");return}console.log("初始化拖拽排序"),l.removeEventListener("dragstart",dt),l.removeEventListener("dragover",pt),l.removeEventListener("drop",mt),l.removeEventListener("dragend",ht),l.addEventListener("dragstart",dt),l.addEventListener("dragover",pt),l.addEventListener("drop",mt),l.addEventListener("dragend",ht);const i=l.querySelectorAll("tr");console.log("找到行数:",i.length),i.forEach((m,g)=>{m.dataset.index=g,console.log(`设置第${g}行索引`);const E=m.querySelector(".sort-handle");E&&(E.draggable=!0,E.dataset.rowIndex=g)})})},dt=l=>{if(!l.target.closest(".sort-handle")){l.preventDefault();return}const m=l.target.closest("tr");if(!m||!m.dataset.index){l.preventDefault();return}console.log("开始拖拽，行索引:",m.dataset.index),Le.value=!0,ne.value=parseInt(m.dataset.index),m.style.opacity="0.5",m.classList.add("dragging"),l.dataTransfer.effectAllowed="move",l.dataTransfer.setData("text/plain",m.dataset.index)},pt=l=>{if(!Le.value)return;l.preventDefault(),l.dataTransfer.dropEffect="move";const i=l.target.closest("tr");i&&(i.style.borderTop="2px solid var(--el-color-primary)")},mt=l=>{if(!Le.value)return;l.preventDefault();const i=l.target.closest("tr");if(i){const m=parseInt(i.dataset.index);console.log("拖拽放下，从",ne.value,"到",m),ne.value!==m&&!isNaN(ne.value)&&!isNaN(m)&&tr(ne.value,m),i.style.borderTop=""}},ht=l=>{console.log("拖拽结束"),Le.value=!1,ne.value=-1;const i=l.target.closest("tr");i&&(i.style.opacity="1",i.classList.remove("dragging")),document.querySelectorAll(".sortable-table tbody tr").forEach(g=>{g.style.borderTop=""})},tr=async(l,i)=>{try{console.log("移动模板从",l,"到",i);const m=[...y.value],[g]=m.splice(l,1);m.splice(i,0,g),m.forEach((E,T)=>{E.sort_order=T}),y.value=m,await nr(),x.success("模板排序已更新"),setTimeout(()=>{Je()},100)}catch(m){console.error("移动模板失败:",m),x.error("移动模板失败: "+m.message),await me()}},nr=async()=>{try{const l=y.value.map((g,E)=>({id:g.id,sort_order:E})),i=await window.pywebview.api.book_controller.update_template_order({book_id:r.value,templates:l}),m=typeof i=="string"?JSON.parse(i):i;if(m.status!=="success")throw new Error(m.message||"保存排序失败")}catch(l){throw console.error("保存模板排序失败:",l),l}};Xt(a,l=>{l==="templates"&&setTimeout(()=>{console.log("切换到模板管理，重新初始化拖拽"),Je()},200)}),Ir(async()=>{console.log("组件挂载，bookId:",r.value),r.value&&(await me(),setTimeout(()=>{console.log("延迟初始化拖拽"),Je()},500))}),Rr(()=>{const l=document.querySelector(".sortable-table .el-table__body-wrapper tbody");l&&(l.removeEventListener("dragstart",dt),l.removeEventListener("dragover",pt),l.removeEventListener("drop",mt),l.removeEventListener("dragend",ht))});const rr=async()=>{try{if(!y.value.find(T=>T.id===o.value)){x.error("请先选择一个模板");return}if(w.value.filter(T=>T.template_id===o.value).length===0){x.error("当前模板下没有实体");return}const m={book_id:r.value,template_id:o.value,type:"names"},g=await window.pywebview.api.book_controller.save_entity_export(m),E=typeof g=="string"?JSON.parse(g):g;E.status==="success"?x.success("导出成功，文件已保存到指定目录"):x.error(`导出失败: ${E.message}`)}catch(l){console.error("导出错误:",l),x.error(`导出出错: ${l.message}`)}},ir=l=>{switch(l){case"exportCurrentNames":rr();break;case"exportCurrentDetails":or();break;case"exportCurrentDetailsJson":ar();break;case"exportAllNames":sr();break}},sr=async()=>{try{if(w.value.length===0){x.error("没有可导出的实体");return}const l={book_id:r.value,template_id:"all",type:"all_names"},i=await window.pywebview.api.book_controller.save_entity_export(l),m=typeof i=="string"?JSON.parse(i):i;m.status==="success"?x.success("导出成功，文件已保存到指定目录"):x.error(`导出失败: ${m.message}`)}catch(l){console.error("导出错误:",l),x.error(`导出出错: ${l.message}`)}},or=async()=>{try{if(!y.value.find(T=>T.id===o.value)){x.error("请先选择一个模板");return}if(w.value.filter(T=>T.template_id===o.value).length===0){x.error("当前模板下没有实体");return}const m={book_id:r.value,template_id:o.value,type:"details"},g=await window.pywebview.api.book_controller.save_entity_export(m),E=typeof g=="string"?JSON.parse(g):g;E.status==="success"?x.success("导出成功，文件已保存到指定目录"):x.error(`导出失败: ${E.message}`)}catch(l){console.error("导出错误:",l),x.error(`导出出错: ${l.message}`)}},ar=async()=>{try{if(!y.value.find(T=>T.id===o.value)){x.error("请先选择一个模板");return}if(w.value.filter(T=>T.template_id===o.value).length===0){x.error("当前模板下没有实体");return}const m={book_id:r.value,template_id:o.value,type:"details_json"},g=await window.pywebview.api.book_controller.save_entity_export(m),E=typeof g=="string"?JSON.parse(g):g;E.status==="success"?x.success("导出成功，文件已保存到指定目录"):x.error(`导出失败: ${E.message}`)}catch(l){console.error("导出错误:",l),x.error(`导出出错: ${l.message}`)}},lr=async l=>{try{let i=`【${l.name}】
`;if(l.description&&l.description.trim()){const m=l.description.replace(/\r?\n/g," ").replace(/\s+/g," ").trim();i+=`  描述: ${m}
`}if(l.dimensions&&Object.keys(l.dimensions).length>0){const m=Object.entries(l.dimensions).filter(([,g])=>g&&g.trim()!==""&&g!=="未设定");m.length>0&&(i+=`
  维度信息:
`,m.forEach(([g,E])=>{const T=E.replace(/\r?\n/g," ").replace(/\s+/g," ").trim();i+=`    • ${g}: ${T}
`}))}i+=`
`+"─".repeat(30)+`
`,await window.pywebview.api.copy_to_clipboard(i),x.success("已复制到剪贴板")}catch(i){console.error("复制失败:",i),x.error("复制失败："+i.message)}},ur=async l=>{try{await window.pywebview.api.copy_to_clipboard(JSON.stringify(l,null,2)),x.success("实体完整JSON数据已复制到剪贴板")}catch(i){console.error("复制完整JSON失败:",i),x.error("复制完整JSON失败："+i.message)}},ve=C(!1),_e=C(""),cr=()=>{ve.value=!0,_e.value=""},fr=async()=>{try{if(!_e.value.trim()){x.error("请输入JSON字符串");return}const l=JSON.parse(_e.value);if(!l.name){x.error("导入失败：缺少实体名称");return}const i=o.value;if(!i){x.error("请先选择一个模板");return}const m=w.value.find(V=>V.template_id===i&&V.name===l.name);if(m&&!await Ye.confirm(`当前模板下已存在名为"${l.name}"的实体，是否覆盖？`,"警告",{confirmButtonText:"覆盖",cancelButtonText:"取消",type:"warning"}).catch(()=>!1))return;const g={name:l.name,description:l.description||"",dimensions:{},template_id:i,book_id:r.value};m&&(g.id=m.id),y.value.find(V=>V.id===i).dimensions.forEach(V=>{g.dimensions[V.name]=l.dimensions&&l.dimensions[V.name]!==void 0?l.dimensions[V.name]:"未设定"});const T=await window.pywebview.api.book_controller.save_entity(g),Pe=typeof T=="string"?JSON.parse(T):T;if(Pe.status==="success")x.success(m?"更新成功":"导入成功"),ve.value=!1,he();else throw new Error(Pe.message||"导入失败")}catch(l){l instanceof SyntaxError?x.error("导入失败：JSON格式不正确"):(console.error("导入失败:",l),x.error("导入失败："+l.message))}},ge=C(!1),ye=C(""),dr=()=>{ge.value=!0,ye.value=""},pr=async()=>{try{if(!ye.value.trim()){x.error("请输入模板数据");return}const l=JSON.parse(ye.value);if(!l.name||!Array.isArray(l.dimensions)){x.error("模板数据格式不正确");return}const i=y.value.find(T=>T.name===l.name);if(i&&!await Ye.confirm(`已存在名为"${l.name}"的模板，是否覆盖？`,"警告",{confirmButtonText:"覆盖",cancelButtonText:"取消",type:"warning"}).catch(()=>!1))return;const m={...l,book_id:r.value};i&&(m.id=i.id);const g=await window.pywebview.api.book_controller.save_template(m),E=typeof g=="string"?JSON.parse(g):g;if(E.status==="success")x.success(i?"更新成功":"导入成功"),ge.value=!1,me();else throw new Error(E.message||"导入失败")}catch(l){l instanceof SyntaxError?x.error("导入失败：JSON格式不正确"):(console.error("导入失败:",l),x.error("导入失败："+l.message))}},mr=async l=>{try{const i={name:l.name,description:l.description,dimensions:l.dimensions};await window.pywebview.api.copy_to_clipboard(JSON.stringify(i,null,2)),x.success("模板数据已复制到剪贴板")}catch(i){console.error("导出失败:",i),x.error("导出失败："+i.message)}},re=C(1),qe=C(10),hr=U(()=>Mt.value.length),vr=U(()=>{const l=(re.value-1)*qe.value,i=l+qe.value;return Mt.value.slice(l,i)}),_r=l=>{re.value=l},gr=l=>{qe.value=l,re.value=1},we=C(""),Mt=U(()=>{if(!we.value.trim())return y.value;const l=we.value.toLowerCase().trim();return y.value.filter(i=>i.name.toLowerCase().includes(l))}),yr=()=>{we.value="",re.value=1},ie=C(!1),wr=()=>{document.body.style.overflow="hidden",b.value.dimensions&&Object.keys(b.value.dimensionValues||{}).length>6&&(ie.value=!0)},xr=()=>{document.body.style.overflow="",ie.value=!1},br=()=>{document.body.style.overflow="hidden",document.body.classList.add("modal-open"),Ke(()=>{vt.value&&vt.value.focus()})},kr=()=>{document.body.style.overflow="",document.body.classList.remove("modal-open"),se.value=!1,xe.value=""},Er=l=>{N.value.dimensions.splice(l,1)},Lt=()=>{const l=xe.value.trim();if(l){if(N.value.dimensions.some(m=>m.name===l)){x.warning("已存在相同名称的维度");return}N.value.dimensions.push({name:l,type:"text",required:!1}),xe.value="",Ke(()=>{be.value&&be.value.focus()})}return!1},Nr=()=>{Be.value||setTimeout(()=>{Be.value||(se.value=!1)},200)},xe=C(""),se=C(!1),be=C(null),vt=C(null);Xt(se,l=>{l&&Ke(()=>{be.value&&be.value.focus()})});const Cr=()=>{Be.value=!0,Lt(),setTimeout(()=>{Be.value=!1},100)},Be=C(!1),Jt=l=>{l.key==="Enter"&&(l.preventDefault(),l.shiftKey&&It.value&&zt())};return(l,i)=>{const m=Fr,g=Mr,E=Yr,T=Kr,Pe=Lr,V=Jr,Ue=Qr,Sr=Gr,Tr=qr,$r=Wr,K=Zr,qt=jr,Bt=Br,Pt=Pr,ke=Ur,oe=si,Fe=ii,Vr=oi,Ut=ui,Ft=li,Ht=zr;return A(),I(ue,null,[_("div",Ta,[_("div",$a,[_("div",Va,[_("h1",Aa,O(s.value),1),_("div",Da,[f(g,{class:He(["tab-button",{active:a.value==="entities"}]),onClick:i[0]||(i[0]=v=>a.value="entities"),type:"primary",plain:a.value!=="entities"},{default:h(()=>[f(m,null,{default:h(()=>[f($(Hr))]),_:1}),i[32]||(i[32]=S(" 设定实体 "))]),_:1},8,["class","plain"]),f(g,{class:He(["tab-button",{active:a.value==="templates"}]),onClick:i[1]||(i[1]=v=>a.value="templates"),type:"primary",plain:a.value!=="templates"},{default:h(()=>[f(m,null,{default:h(()=>[f($(Yt))]),_:1}),i[33]||(i[33]=S(" 模板管理 "))]),_:1},8,["class","plain"])])]),f(g,{class:"back-button modern-button",onClick:Kn,type:"primary",size:"large"},{default:h(()=>[f(m,null,{default:h(()=>[f($(Xr))]),_:1}),i[34]||(i[34]=S(" 返回写作 "))]),_:1})]),_("div",Oa,[_("div",Ia,[Xe(_("div",Ra,[_("div",za,[_("div",Ma,[i[35]||(i[35]=_("h2",null,"设定实体列表",-1)),f(Pe,{modelValue:o.value,"onUpdate:modelValue":i[2]||(i[2]=v=>o.value=v),placeholder:"请选择模板",class:"template-select",onChange:Wn},{default:h(()=>[(A(!0),I(ue,null,Ee(y.value,v=>(A(),le(T,{key:v.id,label:`${v.name} (${ft(v)}个实体)`,value:v.id},{default:h(()=>[_("div",La,[_("span",Ja,O(v.name),1),f(E,{size:"small",type:"info",effect:"plain",class:"entity-count-tag"},{default:h(()=>[S(O(ft(v))+"个实体 ",1)]),_:2},1024)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"]),f(V,{modelValue:pe.value,"onUpdate:modelValue":i[3]||(i[3]=v=>pe.value=v),placeholder:"搜索实体名称",class:"search-input",clearable:"",onClear:Hn},{prefix:h(()=>[f(m,null,{default:h(()=>[f($(Gt))]),_:1})]),_:1},8,["modelValue"])]),_("div",qa,[f(Tr,{onCommand:ir,"split-button":"",type:"primary"},{dropdown:h(()=>[f(Sr,null,{default:h(()=>[f(Ue,{command:"exportCurrentNames",disabled:!o.value},{default:h(()=>i[36]||(i[36]=[S("导出当前模板实体名称")])),_:1},8,["disabled"]),f(Ue,{command:"exportCurrentDetails",disabled:!o.value},{default:h(()=>i[37]||(i[37]=[S("导出当前模板实体详情")])),_:1},8,["disabled"]),f(Ue,{command:"exportCurrentDetailsJson",disabled:!o.value},{default:h(()=>i[38]||(i[38]=[S("导出当前模板实体详情(JSON)")])),_:1},8,["disabled"]),f(Ue,{command:"exportAllNames",disabled:w.value.length===0},{default:h(()=>i[39]||(i[39]=[S("导出所有模板实体名称")])),_:1},8,["disabled"])]),_:1})]),default:h(()=>[i[40]||(i[40]=_("span",null,"导出",-1))]),_:1}),f(g,{type:"success",onClick:cr,disabled:!o.value},{default:h(()=>[f(m,null,{default:h(()=>[f($(Qt))]),_:1}),i[41]||(i[41]=S(" 导入实体 "))]),_:1},8,["disabled"]),f(g,{type:"primary",onClick:i[4]||(i[4]=v=>Gn()),disabled:!o.value,class:"create-entity-button"},{default:h(()=>[f(m,null,{default:h(()=>[f($(_t))]),_:1}),i[42]||(i[42]=S(" 新建设定实体 "))]),_:1},8,["disabled"])])]),_("div",Ba,[o.value?Xe((A(),le(Bt,{key:1,data:ze.value,style:{width:"100%"},onRowClick:er,"row-style":{height:"120px"},class:"modern-entity-table"},{default:h(()=>[f(K,{prop:"name",label:"实体名称",width:"200",fixed:"left"},{default:h(({row:v})=>[_("div",Pa,[_("div",Ua,O(v.name),1),_("div",Fa,[f(E,{size:"small",type:"info",effect:"plain"},{default:h(()=>[S(O(Xn(v.template_id)),1)]),_:2},1024)])])]),_:1}),f(K,{label:"维度信息","min-width":"400"},{default:h(({row:v})=>[_("div",Ha,[(A(!0),I(ue,null,Ee(Zn(v.dimensions,6),(D,ae)=>(A(),I("div",{key:ae,class:He(["dimension-item",{unset:D==="未设定"}])},[_("div",Xa,O(ae),1),_("div",Ka,O(D==="未设定"?"—":D),1)],2))),128)),Object.keys(v.dimensions).length>6?(A(),I("div",Ya,[i[43]||(i[43]=_("div",{class:"dimension-label"},"更多",-1)),_("div",Ga,"+"+O(Object.keys(v.dimensions).length-6),1)])):Ne("",!0)])]),_:1}),f(K,{label:"操作",width:"280",fixed:"right",align:"center"},{default:h(({row:v})=>[_("div",Qa,[f(qt,{class:"operation-buttons"},{default:h(()=>[f(g,{size:"small",type:"primary",onClick:G(D=>lr(v),["stop"])},{default:h(()=>[f(m,null,{default:h(()=>[f($(Yt))]),_:1}),i[44]||(i[44]=S(" 复制 "))]),_:2},1032,["onClick"]),f(g,{size:"small",type:"info",onClick:G(D=>ur(v),["stop"])},{default:h(()=>[f(m,null,{default:h(()=>[f($(ei))]),_:1}),i[45]||(i[45]=S(" JSON "))]),_:2},1032,["onClick"]),f(g,{size:"small",type:"danger",onClick:G(D=>Qn(v),["stop"])},{default:h(()=>[f(m,null,{default:h(()=>[f($(Wt))]),_:1}),i[46]||(i[46]=S(" 删除 "))]),_:2},1032,["onClick"])]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[Ht,p.value]]):(A(),le($r,{key:0,description:"请先选择一个模板来查看相关实体"}))]),_("div",Wa,[f(Pt,{modelValue:X.value,"onUpdate:modelValue":i[5]||(i[5]=v=>X.value=v),"page-size":J.value,"page-sizes":[5,10,20,50,100],total:j.value,"pager-count":5,onSizeChange:Fn,onCurrentChange:Me,layout:"total, sizes, prev, pager, next",background:""},null,8,["modelValue","page-size","total"])])],512),[[Kt,a.value==="entities"]]),Xe(_("div",Za,[_("div",ja,[_("div",el,[i[47]||(i[47]=_("h2",null,"设定模板",-1)),f(V,{modelValue:we.value,"onUpdate:modelValue":i[6]||(i[6]=v=>we.value=v),placeholder:"搜索模板名称",class:"search-input",clearable:"",onClear:yr},{prefix:h(()=>[f(m,null,{default:h(()=>[f($(Gt))]),_:1})]),_:1},8,["modelValue"])]),_("div",tl,[f(g,{type:"success",class:"import-button",onClick:dr},{default:h(()=>[f(m,null,{default:h(()=>[f($(Qt))]),_:1}),i[48]||(i[48]=S(" 导入模板 "))]),_:1}),f(g,{type:"primary",class:"create-button",onClick:i[7]||(i[7]=v=>Rt())},{default:h(()=>[f(m,null,{default:h(()=>[f($(_t))]),_:1}),i[49]||(i[49]=S(" 新建模板 "))]),_:1})])]),_("div",nl,[Xe((A(),le(Bt,{data:vr.value,style:{width:"100%"},"row-style":{height:"100px"},class:"modern-template-table sortable-table","row-key":"id"},{default:h(()=>[f(K,{label:"排序",width:"80",align:"center",fixed:"left"},{default:h(({$index:v})=>[_("div",{class:"sort-handle","data-index":v,title:"拖拽此处可调整模板顺序"},[f(m,{class:"drag-icon"},{default:h(()=>[f($(Zt))]),_:1}),_("span",il,O(v+1),1)],8,rl)]),_:1}),f(K,{prop:"name",label:"模板名称",width:"200",fixed:"left"},{default:h(({row:v})=>[_("div",sl,[_("div",ol,O(v.name),1),v.description?(A(),I("div",al,O(v.description),1)):Ne("",!0)])]),_:1}),f(K,{label:"维度配置","min-width":"350"},{default:h(({row:v})=>[_("div",ll,[(A(!0),I(ue,null,Ee(v.dimensions.slice(0,8),(D,ae)=>(A(),I("div",{key:D.name,class:He(["dimension-chip",{primary:ae<3,secondary:ae>=3}])},O(D.name),3))),128)),v.dimensions.length>8?(A(),I("div",ul," +"+O(v.dimensions.length-8),1)):Ne("",!0)])]),_:1}),f(K,{label:"统计信息",width:"160",align:"center"},{default:h(({row:v})=>[_("div",cl,[_("div",fl,[_("div",dl,O(ft(v)),1),i[50]||(i[50]=_("div",{class:"stat-label"},"实体",-1))]),_("div",pl,[_("div",ml,O(v.dimensions.length),1),i[51]||(i[51]=_("div",{class:"stat-label"},"维度",-1))])])]),_:1}),f(K,{label:"操作",width:"260",fixed:"right",align:"center"},{default:h(({row:v})=>[_("div",hl,[f(qt,{class:"operation-buttons"},{default:h(()=>[f(g,{size:"small",type:"primary",onClick:D=>Rt(v)},{default:h(()=>[f(m,null,{default:h(()=>[f($(ti))]),_:1}),i[52]||(i[52]=S(" 编辑 "))]),_:2},1032,["onClick"]),f(g,{size:"small",type:"success",onClick:D=>mr(v)},{default:h(()=>[f(m,null,{default:h(()=>[f($(ni))]),_:1}),i[53]||(i[53]=S(" 导出 "))]),_:2},1032,["onClick"]),f(g,{size:"small",type:"danger",onClick:D=>Yn(v)},{default:h(()=>[f(m,null,{default:h(()=>[f($(Wt))]),_:1}),i[54]||(i[54]=S(" 删除 "))]),_:2},1032,["onClick"])]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[Ht,p.value]])]),_("div",vl,[f(Pt,{modelValue:re.value,"onUpdate:modelValue":i[8]||(i[8]=v=>re.value=v),"page-size":qe.value,"page-sizes":[5,10,20,50],total:hr.value,"pager-count":5,onSizeChange:gr,onCurrentChange:_r,layout:"total, sizes, prev, pager, next",background:""},null,8,["modelValue","page-size","total"])])],512),[[Kt,a.value==="templates"]])])])]),f(ke,{modelValue:u.value,"onUpdate:modelValue":i[16]||(i[16]=v=>u.value=v),title:N.value.id?"编辑模板":"创建模板","close-on-click-modal":!1,"append-to-body":!0,"lock-scroll":!0,"destroy-on-close":!1,class:"native-template-dialog",width:"800px",modal:!0,"show-close":!0,onOpen:br,onClose:kr},{footer:h(()=>[_("div",Ol,[f(g,{onClick:i[15]||(i[15]=v=>u.value=!1),size:"large"},{default:h(()=>i[62]||(i[62]=[S(" 取消 ")])),_:1}),f(g,{type:"primary",onClick:zt,disabled:!It.value,size:"large"},{default:h(()=>[f(m,null,{default:h(()=>[f($(en))]),_:1}),S(" "+O(N.value.id?"保存修改":"创建模板"),1)]),_:1},8,["disabled"])])]),default:h(()=>[_("div",_l,[_("div",gl,[i[57]||(i[57]=_("h4",{class:"section-title"},"基本信息",-1)),_("div",yl,[_("div",wl,[i[55]||(i[55]=_("label",{class:"field-label"},[S("模板名称 "),_("span",{class:"required"},"*")],-1)),f(V,{modelValue:N.value.name,"onUpdate:modelValue":i[9]||(i[9]=v=>N.value.name=v),placeholder:"请输入模板名称",clearable:"",ref_key:"templateNameInput",ref:vt,onKeydown:Jt},null,8,["modelValue"])])]),_("div",xl,[_("div",bl,[i[56]||(i[56]=_("label",{class:"field-label"},"模板描述",-1)),f(V,{modelValue:N.value.description,"onUpdate:modelValue":i[10]||(i[10]=v=>N.value.description=v),type:"textarea",rows:2,placeholder:"请输入模板描述（可选）",onKeydown:Jt},null,8,["modelValue"])])])]),i[58]||(i[58]=_("div",{class:"dimensions-section"},[_("div",{class:"section-header-fixed"},[_("h4",{class:"section-title"},"维度配置")])],-1))]),_("div",kl,[_("div",El,[se.value?(A(),I("div",Cl,[f(V,{modelValue:xe.value,"onUpdate:modelValue":i[12]||(i[12]=v=>xe.value=v),placeholder:"请输入维度名称",ref_key:"dimensionNameInput",ref:be,onKeydown:[jt(G(Lt,["prevent"]),["enter"]),i[13]||(i[13]=jt(v=>se.value=!1,["esc"]))],onBlur:Nr,size:"small"},{append:h(()=>[f(g,{type:"primary",onClick:G(Cr,["prevent"]),size:"small"},{default:h(()=>[f(m,null,{default:h(()=>[f($(en))]),_:1})]),_:1})]),_:1},8,["modelValue","onKeydown"]),i[60]||(i[60]=_("div",{class:"input-tip"},"按回车确认，ESC取消",-1))])):(A(),I("div",Nl,[f(g,{type:"primary",onClick:i[11]||(i[11]=v=>se.value=!0),class:"add-btn-full"},{default:h(()=>[f(m,null,{default:h(()=>[f($(_t))]),_:1}),i[59]||(i[59]=S(" 添加新维度 "))]),_:1})]))]),_("div",Sl,[_("div",Tl,[N.value.dimensions.length===0?(A(),I("div",$l,i[61]||(i[61]=[_("div",{class:"empty-icon"},"📝",-1),_("div",{class:"empty-text"},"暂无维度",-1),_("div",{class:"empty-hint"},"点击上方按钮添加第一个维度",-1)]))):(A(),I("div",Vl,[f($(ci),{modelValue:N.value.dimensions,"onUpdate:modelValue":i[14]||(i[14]=v=>N.value.dimensions=v),group:"dimensions","item-key":"name",handle:".drag-handle","ghost-class":"ghost-dimension",animation:200,class:"dimensions-draggable"},{item:h(({element:v,index:D})=>[_("div",Al,[f(m,{class:"drag-handle"},{default:h(()=>[f($(Zt))]),_:1}),_("span",Dl,O(v.name),1),f(g,{type:"danger",size:"small",text:"",onClick:ae=>Er(D),class:"remove-button"},{default:h(()=>[f(m,null,{default:h(()=>[f($(ri))]),_:1})]),_:2},1032,["onClick"])])]),_:1},8,["modelValue"])]))])])])]),_:1},8,["modelValue","title"]),f(ke,{modelValue:c.value,"onUpdate:modelValue":i[21]||(i[21]=v=>c.value=v),title:b.value.id?"编辑实体":"创建实体","close-on-click-modal":!1,"append-to-body":!0,"lock-scroll":!0,"destroy-on-close":!1,class:"entity-detail-dialog",fullscreen:ie.value,onOpen:wr,onClose:xr},{footer:h(()=>[_("div",Ll,[f(g,{onClick:i[19]||(i[19]=v=>ie.value=!ie.value)},{default:h(()=>[f(m,null,{default:h(()=>[f($(ai))]),_:1}),S(" "+O(ie.value?"退出全屏":"全屏模式"),1)]),_:1}),f(g,{onClick:i[20]||(i[20]=v=>c.value=!1)},{default:h(()=>i[64]||(i[64]=[S("取消")])),_:1}),f(g,{type:"primary",onClick:te},{default:h(()=>i[65]||(i[65]=[S("保存")])),_:1})])]),default:h(()=>[_("div",Il,[_("div",Rl,[f(Fe,{model:b.value,"label-width":"100px",onSubmit:G(te,["prevent"])},{default:h(()=>[f(oe,{label:"实体名称",required:""},{default:h(()=>[f(V,{modelValue:b.value.name,"onUpdate:modelValue":i[17]||(i[17]=v=>b.value.name=v),placeholder:"输入实体名称",clearable:"",onKeydown:ee,ref_key:"entityNameInput",ref:P},null,8,["modelValue"])]),_:1}),f(oe,{label:"描述"},{default:h(()=>[f(V,{modelValue:b.value.description,"onUpdate:modelValue":i[18]||(i[18]=v=>b.value.description=v),type:"textarea",rows:3,placeholder:"输入实体描述",onKeydown:ee},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),ct.value?(A(),I("div",zl,[i[63]||(i[63]=_("h3",{class:"dimensions-title"},"维度信息",-1)),_("div",Ml,[f(Vr,{height:"400px",class:"dimensions-scrollbar"},{default:h(()=>[f(Fe,{model:b.value,"label-position":"top",class:"dimensions-form"},{default:h(()=>[(A(!0),I(ue,null,Ee(ct.value.dimensions,v=>(A(),le(oe,{key:v.name,label:v.name},{default:h(()=>[f(V,{modelValue:b.value.dimensions[v.name],"onUpdate:modelValue":D=>b.value.dimensions[v.name]=D,placeholder:"输入"+v.name,type:"textarea",rows:2,onKeydown:ee},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),_:2},1032,["label"]))),128))]),_:1},8,["model"])]),_:1})])])):Ne("",!0)])]),_:1},8,["modelValue","title","fullscreen"]),f(ke,{modelValue:d.value,"onUpdate:modelValue":i[25]||(i[25]=v=>d.value=v),fullscreen:"","show-close":!1,"close-on-click-modal":!1,class:"entity-detail-dialog fullscreen-dialog"},{footer:h(()=>[_("div",Ul,[f(g,{size:"large",onClick:i[24]||(i[24]=v=>d.value=!1)},{default:h(()=>i[67]||(i[67]=[S("取消")])),_:1}),f(g,{size:"large",type:"primary",onClick:te},{default:h(()=>i[68]||(i[68]=[S("保存")])),_:1})])]),default:h(()=>[_("div",Jl,[_("div",ql,[f(Fe,{model:b.value,"label-width":"120px",onSubmit:G(te,["prevent"])},{default:h(()=>[f(oe,{label:"实体名称",required:""},{default:h(()=>[f(V,{modelValue:b.value.name,"onUpdate:modelValue":i[22]||(i[22]=v=>b.value.name=v),placeholder:"实体名称",size:"large",onKeydown:ee},null,8,["modelValue"])]),_:1}),f(oe,{label:"描述"},{default:h(()=>[f(V,{modelValue:b.value.description,"onUpdate:modelValue":i[23]||(i[23]=v=>b.value.description=v),type:"textarea",rows:4,placeholder:"输入实体描述",onKeydown:ee},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),ct.value?(A(),I("div",Bl,[i[66]||(i[66]=_("h3",{class:"dimensions-title"},"维度信息",-1)),_("div",Pl,[f(Fe,{model:b.value,"label-width":"120px",class:"dimensions-form"},{default:h(()=>[(A(!0),I(ue,null,Ee(jn.value,v=>(A(),le(oe,{key:v.name,label:v.name},{default:h(()=>[f(V,{modelValue:b.value.dimensions[v.name],"onUpdate:modelValue":D=>b.value.dimensions[v.name]=D,placeholder:"输入"+v.name,type:"textarea",rows:4,resize:"none",onKeydown:ee},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),_:2},1032,["label"]))),128))]),_:1},8,["model"])])])):Ne("",!0)])]),_:1},8,["modelValue"]),f(ke,{modelValue:ve.value,"onUpdate:modelValue":i[28]||(i[28]=v=>ve.value=v),title:"导入实体",width:"600px",class:"import-dialog"},{footer:h(()=>[_("span",Xl,[f(g,{onClick:i[27]||(i[27]=v=>ve.value=!1)},{default:h(()=>i[71]||(i[71]=[S("取消")])),_:1}),f(g,{type:"primary",onClick:fr},{default:h(()=>i[72]||(i[72]=[S("确认导入")])),_:1})])]),default:h(()=>[_("div",Fl,[f(Ft,null,{default:h(()=>[f(Ut,null,{title:h(()=>[_("div",Hl,[f(m,null,{default:h(()=>[f($(tn))]),_:1}),i[69]||(i[69]=_("span",null,"查看JSON格式示例",-1))])]),default:h(()=>[i[70]||(i[70]=_("div",{class:"format-hint-content"},[_("pre",null,`{
  "name": "实体名称",
  "description": "实体描述",
  "dimensions": {
    "维度1": "值1",
    "维度2": "值2"
  }
}`)],-1))]),_:1})]),_:1}),f(V,{modelValue:_e.value,"onUpdate:modelValue":i[26]||(i[26]=v=>_e.value=v),type:"textarea",rows:10,placeholder:"请输入JSON字符串",class:"import-input"},null,8,["modelValue"])])]),_:1},8,["modelValue"]),f(ke,{modelValue:ge.value,"onUpdate:modelValue":i[31]||(i[31]=v=>ge.value=v),title:"导入模板",width:"800px",class:"import-template-dialog","close-on-click-modal":!1,"show-close":!0},{footer:h(()=>[_("div",Gl,[f(g,{size:"large",onClick:i[30]||(i[30]=v=>ge.value=!1)},{default:h(()=>i[75]||(i[75]=[S("取消")])),_:1}),f(g,{size:"large",type:"primary",onClick:pr},{default:h(()=>i[76]||(i[76]=[S("确认导入")])),_:1})])]),default:h(()=>[_("div",Kl,[f(Ft,null,{default:h(()=>[f(Ut,null,{title:h(()=>[_("div",Yl,[f(m,null,{default:h(()=>[f($(tn))]),_:1}),i[73]||(i[73]=_("span",null,"查看JSON格式示例",-1))])]),default:h(()=>[i[74]||(i[74]=_("div",{class:"format-hint-content"},[_("pre",null,`{
  "name": "模板名称",
  "description": "模板描述",
  "dimensions": [
    {
      "name": "维度1",
      "type": "text"
    },
    {
      "name": "维度2",
      "type": "text"
    }
  ]
}`)],-1))]),_:1})]),_:1}),f(V,{modelValue:ye.value,"onUpdate:modelValue":i[29]||(i[29]=v=>ye.value=v),type:"textarea",rows:10,placeholder:"请输入模板JSON数据",class:"import-input"},null,8,["modelValue"])])]),_:1},8,["modelValue"])],64)}}},uu=Ar(Ql,[["__scopeId","data-v-5dae515b"]]);export{uu as default};
