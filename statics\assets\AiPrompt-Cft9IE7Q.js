import{_ as tl,r as i,w as re,M as S,m as p,g as a,e as u,d as t,b as k,p as ue,F as f,t as al,v as m,B as nl,C as D,aw as el,U as ol,a9 as $e,a4 as ll,s as sl,k as ul,j as Sl,h as _t,q as El,x as bt,E as r,Y as ze,c as N,a0 as wt,N as Tl,R as L,S as M,n as Pe,V as Dl,aq as ht,bk as Nl,aO as Vt,l as $t,i as kt,o as kl,aE as It,aD as Ct,a8 as Il,ap as Cl,ax as xt,J as Pt,cj as St,b2 as Et,aK as Tt,aM as Dt,au as xl,az as Nt,bm as Ut,aG as Ot,be as Rt,bX as Lt,bN as Bt,y as jt,aA as zt,aI as At,aF as Ft,ah as Pl}from"./entry-DxFfH4M0.js";/* empty css                 *//* empty css                        *//* empty css                  *//* empty css                          *//* empty css                    *//* empty css                       *//* empty css                        *//* empty css                  *//* empty css                *//* empty css                      *//* empty css               *//* empty css                        *//* empty css                 *//* empty css                 */import{u as Mt}from"./book-mHAKpUpK.js";/* empty css                   */const Jt={class:"preview-content"},qt={class:"preview-header"},Gt={class:"preview-info"},Qt={class:"preview-title"},Kt={key:0,class:"preview-description"},Wt={class:"preview-actions"},Ht={class:"preview-body"},Xt={class:"edit-hint"},Yt={class:"content-preview-text"},Zt={class:"dialog-footer"},ea={__name:"PromptPreviewDialog",props:{modelValue:{type:Boolean,default:!1},promptName:{type:String,default:""},promptDescription:{type:String,default:""},promptContent:{type:String,default:""},previewContent:{type:String,default:""}},emits:["update:modelValue","save-to-rule","regenerate"],setup(ie,{emit:ee}){const g=ie,_=ee,J=i(!1),A=i("");re(J,O=>{_("update:modelValue",O)}),re(()=>g.modelValue,O=>{J.value=O,O&&(A.value=g.previewContent||g.promptContent)}),re(()=>g.previewContent,O=>{O&&(A.value=O)}),re(()=>g.promptContent,O=>{O&&!g.previewContent&&(A.value=O)});const q=i(!1),ae=async()=>{if(!q.value)try{q.value=!0,await _("regenerate")}catch(O){console.error("重新生成失败:",O),r.error("重新生成失败，请重试")}finally{q.value=!1}},ne=()=>{if(!A.value){r.warning("没有可复制的内容");return}window.pywebview.api.copy_to_clipboard(A.value).then(()=>{r.success("复制成功")}).catch(()=>{r.error("复制失败")})},j=i(!1),B=i(null),K=i({name:"",description:""}),ve={name:[{required:!0,message:"请输入提示词名称",trigger:"blur"},{min:1,max:50,message:"长度在 1 到 50 个字符",trigger:"blur"}]},fe=i(null),U=()=>{K.value={name:`提示词 ${new Date().toLocaleString("zh-CN",{month:"numeric",day:"numeric",hour:"numeric",minute:"numeric"})}`,description:""},j.value=!0},I=async()=>{if(B.value)try{await B.value.validate(),_("save-to-rule",{name:K.value.name,description:K.value.description,content:A.value,timestamp:new Date().toISOString()}),j.value=!1,J.value=!1,r.success("提示词已添加到规则")}catch(O){console.error("表单验证失败:",O)}},de=()=>{ze(()=>{fe.value&&fe.value.focus()})};return(O,y)=>{const G=nl,oe=al,ge=sl,Q=El,be=Sl,X=ul;return p(),S(X,{modelValue:J.value,"onUpdate:modelValue":y[5]||(y[5]=F=>J.value=F),title:"提示词",style:{"user-select":"none"},width:"70%",class:"prompt-preview-dialog","close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,fullscreen:!1,"modal-class":"prompt-preview-modal"},{default:a(()=>[u("div",Jt,[u("div",qt,[u("div",Gt,[u("h3",Qt,f(ie.promptName),1),ie.promptDescription?(p(),k("p",Kt,f(ie.promptDescription),1)):ue("",!0)]),u("div",Wt,[t(oe,{type:"primary",onClick:ae,loading:q.value},{default:a(()=>[t(G,null,{default:a(()=>[t(D(el))]),_:1}),y[6]||(y[6]=m(" 重新生成 "))]),_:1},8,["loading"]),t(oe,{type:"primary",onClick:ne},{default:a(()=>[t(G,null,{default:a(()=>[t(D(ol))]),_:1}),y[7]||(y[7]=m(" 复制内容 "))]),_:1}),t(oe,{type:"success",onClick:U},{default:a(()=>[t(G,null,{default:a(()=>[t(D($e))]),_:1}),y[8]||(y[8]=m(" 添加到当前规则 "))]),_:1})])]),u("div",Ht,[u("div",Xt,[t(G,null,{default:a(()=>[t(D(ll))]),_:1}),y[9]||(y[9]=u("span",null,"您可以直接编辑下方的提示词内容",-1))]),t(ge,{modelValue:A.value,"onUpdate:modelValue":y[0]||(y[0]=F=>A.value=F),type:"textarea",rows:18,class:"prompt-content-editor",spellcheck:!1,resize:"none"},null,8,["modelValue"])])]),t(X,{modelValue:j.value,"onUpdate:modelValue":y[4]||(y[4]=F=>j.value=F),title:"添加到当前规则",width:"500px","append-to-body":"",class:"save-prompt-dialog",onOpened:de},{footer:a(()=>[u("div",Zt,[t(oe,{onClick:y[3]||(y[3]=F=>j.value=!1)},{default:a(()=>y[10]||(y[10]=[m("取消")])),_:1}),t(oe,{type:"primary",onClick:I},{default:a(()=>y[11]||(y[11]=[m("确认添加")])),_:1})])]),default:a(()=>[t(be,{model:K.value,ref_key:"saveFormRef",ref:B,rules:ve,"label-position":"top",onSubmit:_t(I,["prevent"])},{default:a(()=>[t(Q,{label:"提示词名称",prop:"name"},{default:a(()=>[t(ge,{modelValue:K.value.name,"onUpdate:modelValue":y[1]||(y[1]=F=>K.value.name=F),placeholder:"请输入提示词名称",ref_key:"nameInputRef",ref:fe,onKeyup:bt(I,["enter"])},null,8,["modelValue"])]),_:1}),t(Q,{label:"提示词描述",prop:"description"},{default:a(()=>[t(ge,{modelValue:K.value.description,"onUpdate:modelValue":y[2]||(y[2]=F=>K.value.description=F),type:"textarea",rows:2,placeholder:"请输入提示词描述（可选）"},null,8,["modelValue"])]),_:1}),t(Q,{label:"提示词内容",class:"content-preview"},{default:a(()=>[u("pre",Yt,f(A.value),1)]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])}}},la=tl(ea,[["__scopeId","data-v-d6a530aa"]]),ta={class:"prompt-list-container"},aa={class:"prompt-list-header"},na={class:"header-left"},oa={class:"dialog-title"},sa={class:"prompt-list-content"},ua={class:"prompt-sidebar"},ra={class:"sidebar-content"},ia={class:"prompt-list"},da=["onClick"],ca={class:"prompt-list-item-content"},pa={class:"prompt-list-item-title"},ma={class:"prompt-list-item-preview"},va={class:"prompt-list-item-time"},fa={class:"prompt-content-view"},ga={class:"prompt-view-header"},ya={class:"prompt-view-title"},_a={class:"prompt-view-actions"},ba={class:"prompt-view-content"},wa={class:"content-wrapper"},ha={class:"prompt-content-text"},Va={__name:"PromptListDialog",props:{modelValue:{type:Boolean,default:!1},ruleName:{type:String,required:!0},prompts:{type:Array,default:()=>[]}},emits:["update:modelValue","use-prompt"],setup(ie,{emit:ee}){const g=ie,_=ee,J=i(!1),A=i(""),q=i(0),ae=i(!1);re(J,U=>{_("update:modelValue",U)}),re(()=>g.modelValue,U=>{J.value=U,U&&(A.value="",q.value=g.prompts.length>0?0:-1)});const ne=N(()=>{if(!g.prompts)return[];if(!A.value)return g.prompts;const U=A.value.toLowerCase();return g.prompts.filter(I=>I.name&&I.name.toLowerCase().includes(U)||I.content&&I.content.toLowerCase().includes(U))}),j=N(()=>ne.value.length===0?null:q.value<0||q.value>=ne.value.length?ne.value[0]:ne.value[q.value]),B=U=>{q.value=U},K=U=>{if(!U)return"无内容";const I=U.replace(/\s+/g," ").trim();return I.length>60?I.substring(0,60)+"...":I},ve=U=>{window.pywebview.api.copy_to_clipboard(U).then(()=>{ae.value=!0,setTimeout(()=>{ae.value=!1},2e3),r({message:"内容已复制到剪贴板",type:"success",duration:2e3,offset:80})}).catch(()=>{r.error("复制失败")})},fe=U=>U?new Date(U).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"";return(U,I)=>{const de=nl,O=al,y=sl,G=Dl,oe=Vt,ge=ul;return p(),S(ge,{modelValue:J.value,"onUpdate:modelValue":I[3]||(I[3]=Q=>J.value=Q),title:`${ie.ruleName} - 提示词列表`,width:"85%",class:"prompts-dialog","close-on-press-escape":!0,"destroy-on-close":!1,"modal-append-to-body":!0,fullscreen:!0},{default:a(()=>[u("div",ta,[u("div",aa,[u("div",na,[t(O,{onClick:I[0]||(I[0]=Q=>J.value=!1),class:"close-button"},{default:a(()=>[t(de,null,{default:a(()=>[t(D(wt))]),_:1}),I[4]||(I[4]=m(" 返回 "))]),_:1}),I[5]||(I[5]=u("div",{class:"divider"},null,-1)),u("h2",oa,f(ie.ruleName),1)]),t(y,{modelValue:A.value,"onUpdate:modelValue":I[1]||(I[1]=Q=>A.value=Q),placeholder:"搜索提示词...",class:"prompt-search"},{prefix:a(()=>[t(de,null,{default:a(()=>[t(D(Tl))]),_:1})]),_:1},8,["modelValue"])]),u("div",sa,[u("div",ua,[u("div",ra,[u("div",ia,[(p(!0),k(L,null,M(ne.value,(Q,be)=>(p(),k("div",{key:Q.id,class:Pe(["prompt-list-item",{active:q.value===be}]),onClick:X=>B(be)},[u("div",ca,[u("div",pa,f(Q.name||"未命名提示词"),1),u("div",ma,f(K(Q.content)),1),u("div",va,f(fe(Q.timestamp)),1)])],10,da))),128)),ne.value.length===0?(p(),S(G,{key:0,description:"暂无提示词"})):ue("",!0)])])]),u("div",fa,[j.value?(p(),k(L,{key:0},[u("div",ga,[u("h3",ya,f(j.value.name||"未命名提示词"),1),u("div",_a,[t(O,{type:"success",size:"large",onClick:I[2]||(I[2]=Q=>ve(j.value.content)),class:Pe(["copy-button",{copied:ae.value}])},{icon:a(()=>[t(de,{class:Pe({"copy-icon":!0,copied:ae.value})},{default:a(()=>[(p(),S(ht(ae.value?D(Nl):D(ol))))]),_:1},8,["class"])]),default:a(()=>[m(" "+f(ae.value?"已复制":"复制到剪贴板"),1)]),_:1},8,["class"])])]),t(oe),u("div",ba,[u("div",wa,[u("pre",ha,f(j.value.content),1)])])],64)):(p(),S(G,{key:1,description:"请从左侧列表选择一个提示词",class:"prompt-view-empty"}))])])])]),_:1},8,["modelValue","title"])}}},$a=tl(Va,[["__scopeId","data-v-8fc3be17"]]),ka={class:"ai-prompt-manager glass-bg"},Ia={class:"header-section"},Ca={class:"left-section"},xa={class:"tab-buttons"},Pa={key:0,class:"main-content"},Sa={class:"prompts-container glass-bg"},Ea={class:"section-header"},Ta={class:"header-content"},Da={class:"header-actions"},Na={class:"prompt-list-wrapper"},Ua={class:"prompt-name-cell"},Oa={class:"prompt-name"},Ra={class:"prompt-description"},La={class:"prompt-count"},Ba={class:"update-time"},ja={class:"operation-buttons"},za={class:"pagination-container"},Aa={class:"generator-container glass-bg"},Fa={class:"section-header"},Ma={class:"button-group"},Ja={class:"generator-content"},qa={class:"generator-left-panel"},Ga={class:"panel-item info-panel"},Qa={class:"panel-item result-panel"},Ka={class:"result-actions"},Wa={class:"result-content-wrapper"},Ha={class:"generator-right-panel"},Xa={class:"rules-container"},Ya={class:"rule-section"},Za={class:"rule-header"},en={class:"rule-content"},ln={class:"option-count"},tn={class:"option-count"},an={key:0,class:"entity-type"},nn={class:"button-group",style:{display:"flex",gap:"12px"}},on={class:"option-count"},sn={class:"rule-section"},un={class:"rule-header"},rn={class:"rule-content"},dn={class:"scene-option"},cn={class:"scene-title"},pn={class:"scene-description"},mn={key:1,class:"empty-state glass-bg"},vn={class:"placeholder-helper"},fn={class:"dialog-footer"},gn={class:"prompt-detail"},yn={class:"prompt-detail-item"},_n={class:"prompt-detail-item"},bn={class:"prompt-detail-item"},wn={class:"prompt-content-box"},hn={class:"prompt-detail-item"},Vn={class:"placeholder-tags"},$n={class:"dialog-footer"},kn={class:"batch-results"},In={class:"result-header"},Cn={class:"result-index"},xn={class:"result-content"},Pn={class:"dialog-footer"},Sn={class:"option-count"},En={class:"dialog-footer"},Tn={class:"option-count"},Dn={key:0,class:"entity-type"},Nn={class:"dialog-footer"},Un={key:2,class:"rule-saved-prompts"},On={class:"panel-title"},Rn={class:"prompt-preview-text"},Ln={class:"dialog-footer"},Bn={__name:"AiPrompt",setup(ie){$t(),kt();const ee=Mt(),g=i("");N(()=>ee.bookList.find(e=>e.id===g.value)?.title||"AI提示词");const _=i({pools:[],currentPoolId:null}),J=i([]),A=i("");N(()=>{let l=[];if(_.value.pools.forEach(e=>{e.scenes&&l.push(...e.scenes)}),A.value){const e=A.value.toLowerCase();l=l.filter(s=>s.title&&s.title.toLowerCase().includes(e)||s.description&&s.description.toLowerCase().includes(e))}return l});const q=N(()=>_.value.currentPoolId?_.value.currentPoolId==="all"?ae.value:_.value.pools.find(l=>l.id===_.value.currentPoolId):null),ae=N(()=>{const l=[];return _.value.pools&&Array.isArray(_.value.pools)&&_.value.pools.forEach(e=>{if(e&&e.scenes&&Array.isArray(e.scenes)){const s=e.scenes.map(d=>({...d,sourcePool:{id:e.id,name:e.name}}));l.push(...s)}}),{id:"all",name:"全部场景",scenes:l,isVirtual:!0}}),ne=i(null);N(()=>J.value.find(l=>l.id===ne.value));const j=i("promptList"),B=i([]),K=i(""),ve=N(()=>{if(!B.value)return[];if(!K.value)return B.value;const l=K.value.toLowerCase();return B.value.filter(e=>e.name&&e.name.toLowerCase().includes(l)||e.description&&e.description.toLowerCase().includes(l))});N(()=>{const l=(xe.value-1)*je.value,e=l+je.value;return ve.value.slice(l,e)});const fe=l=>{je.value=l,xe.value=1},U=()=>{xe.value=1},I=l=>{if(!l)return[];const e=/\{\{([^}]+)\}\}/g,s=[];let d;for(;(d=e.exec(l))!==null;)s.push(d[1].trim());return Array.from(new Set(s))},de=i(!1),O=i(!1);i(!1);const y=i({id:"",name:"",description:"",content:""});i(null);const G=i({id:"",name:"",description:"",content:""}),oe=l=>{ye.value=l.id,X.value=l.name,F.value=l.description,l.rule?(o.value=JSON.parse(JSON.stringify(l.rule)),l.rule.content&&(w.value=l.rule.content),l.rule.editorConfig&&Object.assign(Ze.value,l.rule.editorConfig)):o.value={prompts:[],content:"",editorConfig:{...Ze.value}},j.value="generator"},ge=async l=>{if(!(!l||!l.content))try{await window.pywebview.api.copy_to_clipboard(l.content),r.success("提示词已复制到剪贴板")}catch(e){console.error("复制失败:",e),r.error("复制失败，请手动复制")}},Q=l=>{Pl.confirm(`确定要删除提示词 "${l.name}" 吗？此操作不可撤销。`,"删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning",customClass:"delete-confirm"}).then(async()=>{try{ce.value=!0;const e=await window.pywebview.api.book_controller.delete_prompt(g.value,l.id),s=typeof e=="string"?JSON.parse(e):e;s.status==="success"?(r.success("删除成功"),B.value=B.value.filter(d=>d.id!==l.id)):(console.error("删除失败:",s.message),r.error(`删除失败: ${s.message}`))}catch(e){console.error("删除操作异常:",e),r.error("删除失败: "+(e.message||"未知错误"))}finally{ce.value=!1}}).catch(()=>{})},be=i({}),X=i(""),F=i(""),w=i(""),rl=i("templates"),C=i([]),E=i([]);i([]);const Ae=i("all"),il=i(""),Fe=i(""),dl=i(null),cl=i(null),Me=i(null),o=i({template:{enabled:!1,mode:"single",selectedTemplateId:null,selectedDimensions:[],excludedDimensions:[],entityExcludedDimensions:[],entityOutputFormat:"text",allEntitiesOutputFormat:"text"},scene:{enabled:!1,mode:"manual",selectedSceneIds:[],randomCount:1,selectedPoolId:null,placeholderText:"{{场景描述}}"},prompts:[]}),Ul=async()=>{ce.value=!0;try{const l=await window.pywebview.api.book_controller.get_templates(g.value),e=typeof l=="string"?JSON.parse(l):l;e.status==="success"?(C.value=e.data||[],await pl()):r.error(e.message||"加载模板失败")}catch(l){console.error("加载模板失败:",l),r.error("加载模板失败："+l.message)}finally{ce.value=!1}},pl=async()=>{try{const l=await window.pywebview.api.book_controller.get_entities(g.value),e=typeof l=="string"?JSON.parse(l):l;e.status==="success"?E.value=e.data||[]:r.error(e.message||"加载实体失败")}catch(l){console.error("加载实体失败：",l),r.error("加载实体失败："+l.message)}},ke=l=>!l||!l.id?0:E.value.filter(e=>e.template_id===l.id).length,Je=N(()=>o.value.template.selectedTemplateId?E.value.filter(l=>l.template_id===o.value.template.selectedTemplateId):[]);N(()=>o.value.template.selectedTemplateId?C.value.find(e=>e.id===o.value.template.selectedTemplateId)?.dimensions||[]:[]),N(()=>{if(!o.value.template.selectedEntityId||!o.value.template.selectedTemplateId)return[];const l=E.value.find(e=>e.id===o.value.template.selectedEntityId&&e.template_id===o.value.template.selectedTemplateId);return!l||!l.dimensions?[]:Object.entries(l.dimensions).map(([e,s])=>({name:e,value:s}))});const Ol=N(()=>{if(!o.value.template.selectedTemplateId)return[];const l=E.value.filter(s=>s.template_id===o.value.template.selectedTemplateId),e=new Set;return l.forEach(s=>{s.dimensions&&Object.keys(s.dimensions).forEach(d=>{e.add(d)})}),Array.from(e).map(s=>({name:s}))});N(()=>{const l=[];return _.value.pools.forEach(e=>{e.scenes&&l.push(...e.scenes.map(s=>({key:s.id,label:s.title,description:s.description})))}),l});const ml=N(()=>{if(w.value&&w.value.trim())return!0;if(!g.value)return!1;if(o.value.template.enabled)switch(o.value.template.mode){case"single":if(!o.value.template.selectedTemplateId)return!1;break;case"entity":if(!o.value.template.selectedEntityId)return!1;break;case"all":if(!o.value.template.selectedTemplateId)return!1;break}if(o.value.scene.enabled){if(o.value.scene.mode==="manual"){if(!o.value.scene.selectedSceneIds?.length)return!1}else if(!o.value.scene.selectedPoolId)return!1}return o.value.template.enabled||o.value.scene.enabled}),Se=i(!1),Rl=l=>{Se.value=l},vl=async()=>{if(!g.value){r.warning("请先选择一本书");return}try{if(Rl(!0),w.value&&w.value.trim()){console.log("使用现有内容预览"),await Ke();return}console.log("根据规则生成新内容");let l=[];if(o.value.template.enabled)switch(o.value.template.mode){case"single":if(o.value.template.selectedTemplateId){const e=C.value.find(s=>s.id===o.value.template.selectedTemplateId);e&&l.push(`{{模板:${e.id}【${e.name||"未命名模板"}】}}`)}break;case"entity":if(o.value.template.selectedEntityId){const e=E.value.find(s=>s.id===o.value.template.selectedEntityId);if(e){const s=o.value.template.entityExcludedDimensions||[];l.push(`{{实体:${e.id}:${o.value.template.entityOutputFormat}:${s.join(",")}【${e.name||"未命名实体"}】}}`)}}break;case"all":if(o.value.template.selectedTemplateId){const e=C.value.find(s=>s.id===o.value.template.selectedTemplateId);if(e){const s=o.value.template.excludedDimensions||[];l.push(`{{所有实体:${e.id}:${o.value.template.allEntitiesOutputFormat}:${s.join(",")}【${e.name||"未命名模板"}的所有实体】}}`)}}break}o.value.scene.enabled&&(o.value.scene.mode==="manual"?o.value.scene.selectedSceneIds?.length>0&&l.push(`{{场景:manual:${o.value.scene.selectedSceneIds.join(",")}}}`):o.value.scene.selectedPoolId&&l.push(`{{场景:random:${o.value.scene.selectedPoolId}:${o.value.scene.randomCount}}}`)),w.value=l.join(`

`),Qe.value.unshift({timestamp:new Date,content:w.value,rule:JSON.parse(JSON.stringify(o.value))}),Qe.value.length>50&&Qe.value.pop(),r.success("提示词生成成功")}catch(l){console.error("生成提示词失败:",l),r.error("生成提示词失败: "+l.message)}},Ll=()=>{if(!w.value){r.warning("没有可复制的内容");return}window.pywebview.api.copy_to_clipboard(w.value).then(()=>r.success("已复制到剪贴板")).catch(()=>r.error("复制失败"))},Bl=()=>{w.value="",r.success("已清空生成结果")},jl=l=>{if(!l)return"未知日期";const e=new Date(l);return isNaN(e.getTime())?"无效日期":e.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})},qe=async l=>{try{B.value=[],C.value=[],E.value=[],_.value={pools:[],currentPoolId:null},J.value=[],l&&(ce.value=!0,await Promise.all([fl(),Ul(),pl(),Ge()]),console.log("数据加载完成:",{templates:C.value,entities:E.value}),r.success("书籍数据加载完成"))}catch(e){console.error("加载书籍数据失败:",e),r.error("加载书籍数据失败，请重试")}finally{ce.value=!1}},fl=async()=>{if(g.value)try{gl(!0);const l=await window.pywebview.api.book_controller.get_prompts(g.value),e=typeof l=="string"?JSON.parse(l):l;e.status==="success"?B.value=e.data||[]:console.error("加载提示词失败:",e.message)}catch(l){console.error("加载提示词失败:",l)}finally{gl(!1)}},ce=i(!1),gl=l=>{ce.value=l},Ge=async()=>{if(g.value)try{const l=await window.pywebview.api.book_controller.get_scene_events(g.value),e=typeof l=="string"?JSON.parse(l):l;if(e.status==="success"){const s=e.data||{};_.value={pools:Array.isArray(s.pools)?s.pools:[],currentPoolId:s.currentPoolId||null},_.value.pools.length||console.warn("没有找到场景卡池")}else throw new Error(e.message||"加载失败")}catch(l){console.error("加载场景失败:",l),r.error(`加载场景失败: ${l.message}`),_.value={pools:[],currentPoolId:null}}};re(()=>_.value.currentPoolId,()=>{q.value&&q.value.scenes&&Array.isArray(q.value.scenes)?J.value=q.value.scenes:J.value=[]}),N(()=>(console.log("当前模板列表:",C.value),!C.value||!Array.isArray(C.value)||C.value.length===0?[]:!Fe.value&&!Me.value?C.value:C.value.filter(l=>{let e=!0,s=!0;if(Fe.value){const d=Fe.value.toLowerCase();e=l&&(l.name&&l.name.toLowerCase().includes(d)||l.description&&l.description.toLowerCase().includes(d))}return Me.value&&l.dimensions&&(s=l.dimensions.some(d=>d.name===Me.value)),e&&s}))),N(()=>{if(console.log("当前实体列表:",E.value),!E.value||!Array.isArray(E.value)||E.value.length===0)return[];let l=E.value;if(Ae.value&&Ae.value!=="all"&&(l=l.filter(e=>e.type===Ae.value)),il.value){const e=il.value.toLowerCase();l=l.filter(s=>s&&(s.name&&s.name.toLowerCase().includes(e)||s.description&&s.description.toLowerCase().includes(e)))}return l}),N(()=>rl.value==="templates"&&dl.value?C.value.find(l=>l.id===dl.value):rl.value==="entities"&&cl.value?E.value.find(l=>l.id===cl.value):null);const yl=N(()=>w.value);N(()=>yl.value?yl.value.replace(/\{\{([^}]+)\}\}/g,(l,e)=>`<span class="placeholder-highlight">${l}</span>`):"");const _l=async()=>{try{if(!X.value||!X.value.trim()){r.warning("请输入提示词规则名称");return}ce.value=!0;const l={id:ye.value||bl(),name:X.value,description:F.value||"",rule:{...o.value,content:w.value,editorConfig:Ze.value,prompts:o.value.prompts||[],updateTime:new Date().toISOString()},type:"generator"},e=await window.pywebview.api.book_controller.add_prompt(g.value,l),s=typeof e=="string"?JSON.parse(e):e;if(s.status==="success"){if(r.success(ye.value?"规则更新成功":"规则创建成功"),ye.value){const d=B.value.findIndex(c=>c.id===ye.value);d!==-1&&(B.value[d]=l)}else B.value.push(l);return!0}else return r.error(s.message||"保存失败"),!1}catch(l){return console.error("保存提示词规则失败:",l),r.error("保存失败: "+l.message),!1}finally{ce.value=!1}},zl=async l=>{try{if(!o.value){r.warning("请先创建或选择一个规则");return}if(o.value.prompts||(o.value.prompts=[]),o.value.prompts.push({id:bl(),name:l.name,description:l.description,content:l.content,timestamp:l.timestamp}),await _l()){const s=ye.value;if(s){const d=B.value.findIndex(c=>c.id===s);d!==-1&&(B.value[d].rule=JSON.parse(JSON.stringify(o.value)))}Ie.value=!1,r.success("内容已添加到当前规则")}}catch(e){console.error("添加到规则失败:",e),r.error("添加失败: "+e.message)}},bl=()=>"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(l){const e=Math.random()*16|0;return(l==="x"?e:e&3|8).toString(16)});kl(async()=>{try{await ee.loadBooks(),ee.bookList?.length>0&&(g.value=ee.bookList[0].id,await qe(g.value))}catch(l){console.error("初始化失败:",l),r.error("初始化失败，请刷新页面重试")}await Promise.all([])}),i("current");const Qe=i([]),Ee=i(!1),Te=i({count:5}),De=i([]),Al=async()=>{if(!ml.value){r.warning("请先配置生成规则");return}Se.value=!0,De.value=[];try{for(let l=0;l<Te.value.count;l++){const e=await vl();e&&De.value.push(e)}r.success(`成功生成 ${De.value.length} 个提示词`)}catch(l){console.error("批量生成失败:",l),r.error("批量生成失败")}finally{Se.value=!1}},Fl=l=>{w.value=l,Ee.value=!1,r.success("已使用所选结果")},Ml=l=>{window.pywebview.api.copy_to_clipboard(l).then(()=>r.success("已复制到剪贴板")).catch(()=>r.error("复制失败"))},Jl=()=>{o.value.template.selectedEntityId=null,o.value.template.selectedDimensions=[]},Ie=i(!1),Ke=async()=>{try{if(!w.value){r.warning("没有内容可预览");return}let l=w.value;console.log("开始预览处理，原始内容:",l);const e=/{{模板:([^【}]+)(?:【([^】]+)】)?}}/g;let s;for(;(s=e.exec(l))!==null;){const $=s[1],z=s[2];console.log("匹配到模板占位符:",s[0],"模板ID:",$,"占位符中名称:",z);const T=C.value.find(v=>v.id===$),H=T?T.name:z||"未知模板";if(T){const v={name:T.name,dimensions:Array.isArray(T.dimensions)?T.dimensions:Object.entries(T.dimensions||{}).map(([P,V])=>({name:P,value:V}))},h=`【模板：${H}】
${JSON.stringify(v,null,2)}`;l=l.replace(s[0],h)}else l=l.replace(s[0],`【未找到ID为 ${$} 的模板】`)}const d=/{{实体:random:([^:【}]+)(?::([^:【}]+))?(?::([^:【}]*))?(?:【([^】]+)】)?}}/g;let c;for(;(c=d.exec(l))!==null;){const $=c[1],z=c[2]||"text",T=c[3]||"",H=c[4];console.log("匹配到随机实体占位符:",c[0],"模板ID:",$,"格式:",z,"排除维度:",T,"占位符中名称:",H);const v=T?T.split(",").filter(Boolean):[],h=E.value.filter(P=>P.template_id===$);if(console.log(`找到${h.length}个实体属于模板${$}`),h.length>0){const P=Math.floor(Math.random()*h.length),V=h[P];console.log("随机选择实体:",V.name,V.id);let b=wl(V,z,v);l=l.replace(c[0],b)}else{const P=C.value.find(V=>V.id===$)?.name||(H?H.replace(/^随机/,"").replace(/实体$/,""):"未知模板");l=l.replace(c[0],`【无法找到"${P}"模板的实体】`)}}const x=/{{实体:(?!random:)([^:【}]+)(?::([^:【}]*))?(?::([^:【}]*))?(?:【([^】]+)】)?:?}}/g;let R;for(;(R=x.exec(l))!==null;){const $=R[1],z=R[2]||"text",T=R[3]||"",H=R[4];console.log("匹配到实体占位符:",R[0],"实体ID:",$,"格式:",z,"排除维度:",T,"占位符中名称:",H);const v=T?T.split(",").filter(Boolean):[],h=E.value.find(P=>P.id===$);if(console.log("找到实体:",h?h.name:"未找到"),h){let P=wl(h,z,v);l=l.replace(R[0],P)}else{const P=H||"未知实体";l=l.replace(R[0],`【无法找到"${P}"实体】`)}}const le=/{{所有实体:([^:【}]+)(?::([^:【}]*))?(?::([^:【}]*))?(?:【([^】]+)】)?:?}}/g;let W;for(;(W=le.exec(l))!==null;){const $=W[1],z=W[2]||"text",T=W[3]||"",H=W[4];console.log("匹配到所有实体占位符:",W[0],"模板ID:",$,"格式:",z,"排除维度:",T,"占位符中名称:",H);const v=T?T.split(",").filter(Boolean):[],h=E.value.filter(P=>P.template_id===$);if(console.log(`找到${h.length}个实体属于模板${$}`),h.length>0)if(z==="json"){const P=h.map(b=>{const Y={};for(const[te,_e]of Object.entries(b.dimensions||{}))v.includes(te)||(Y[te]=_e);return{id:b.id,name:b.name,type:b.type,description:b.description,dimensions:Y}}),V=`【模板"${C.value.find(b=>b.id===$)?.name||"未知模板"}"的所有实体】
`+JSON.stringify({template_id:$,template_name:C.value.find(b=>b.id===$)?.name||"未知模板",entity_count:h.length,entities:P},null,2);l=l.replace(W[0],V)}else{const V=`【${C.value.find(b=>b.id===$)?.name||"未知模板"}的所有实体 (共${h.length}个)】

`+h.map(b=>{const Y=[`🔹 ${b.name||"未命名实体"}`];b.type&&Y.push(`类型：${b.type}`),b.description&&Y.push(`描述：${b.description}`);for(const[te,_e]of Object.entries(b.dimensions||{}))v.includes(te)||Y.push(`${te}：${_e}`);return Y.join(`
`)}).join(`

`);l=l.replace(W[0],V)}else l=l.replace(W[0],`【无法找到模板ID为 ${$} 的实体】`)}const se=/{{场景:([^:【}]+):([^:【}]+)(?::([^:【}]*))?(?:【([^】]+)】)?}}/g;let pe;for(;(pe=se.exec(l))!==null;){const $=pe[1],z=pe[2],T=pe[3]||"1",H=pe[4];console.log("匹配到场景占位符:",pe[0],"模式:",$,"ID值:",z,"数量:",T,"占位符中名称:",H);let v="";if($==="manual"){const h=z.split(",").filter(Boolean),P=[];for(const V of _.value.pools)if(V.scenes)for(const b of V.scenes)h.includes(b.id)&&P.push(b);console.log(`找到${P.length}个指定场景`),P.length>0?v=P.map(V=>`【场景：${V.title||"未命名"}】
${V.description||"无描述"}`).join(`

`):v="【未找到指定的场景】"}else if($==="random"){const h=z,P=parseInt(T,10)||1,V=_.value.pools.find(b=>b.id===h);if(V&&V.scenes&&V.scenes.length>0){const b=[...V.scenes].sort(()=>.5-Math.random()),Y=b.slice(0,Math.min(P,b.length));console.log(`从场景池随机抽取了${Y.length}个场景`),v=Y.map(te=>`【场景：${te.title||"未命名"}】
${te.description||"无描述"}`).join(`

`)}else v=`【未找到ID为 ${h} 的场景池或其中没有场景】`}else v=`【不支持的场景模式：${$}】`;l=l.replace(pe[0],v)}console.log("预览处理完成"),Ie.value=!0,We.value=l}catch(l){console.error("生成预览内容失败:",l),r.error("生成预览内容失败: "+l.message)}},wl=(l,e,s)=>{if(!l)return console.warn("formatEntityContent: 实体为空"),"【无效实体】";try{if(console.log("格式化实体:",l.name,"格式:",e,"排除维度:",s),e==="json"){const d={};for(const[c,x]of Object.entries(l.dimensions||{}))s.includes(c)||(d[c]=x);return JSON.stringify({id:l.id,name:l.name,type:l.type,description:l.description,dimensions:d},null,2)}else{const d=[`【实体：${l.name||"未命名实体"}】`];if(l.type&&d.push(`类型：${l.type}`),l.description&&d.push(`描述：${l.description}`),l.dimensions)for(const[c,x]of Object.entries(l.dimensions))s.includes(c)||d.push(`${c}：${x}`);return d.join(`
`)}}catch(d){return console.error("格式化实体内容失败:",d),`【格式化失败：${d.message}】`}},ql=(l,e)=>{if(!l||!l.dimensions)return console.warn("实体没有维度信息"),"";e=e||[],console.log("排除的维度:",e);const s=[];l.description&&s.push(`描述：${l.description}`);for(const d in l.dimensions)e.includes(d)||s.push(`${d}：${l.dimensions[d]}`);return s.join(`
`)},Gl=l=>E.value.filter(e=>e.template_id===l),We=i(""),Ql=l=>{l==="single"?o.value.template.singleTemplate.placeholderText="{{单个模板}}":l==="entity"?o.value.template.entityTemplate.placeholderText="{{单个实体}}":l==="all"&&(o.value.template.allEntitiesTemplate.placeholderText="{{所有实体}}"),w.value+=`
${o.value.template[l].placeholderText}`},He=i(!1),Xe=i(null),Ne=i(!1),Ue=i(null),we=i(null),Kl=()=>{we.value=null},Wl=()=>{if(!we.value)return;const l=E.value.find(d=>d.id===we.value);if(!l)return;const e=ql(l,o.value.template.entityTemplate.excludedDimensions),s=document.querySelector(".generator-content textarea");if(s){const d=s.selectionStart,c=s.selectionEnd,x=w.value.substring(0,d),R=w.value.substring(c);w.value=`${x}${e}${R}`,ze(()=>{s.focus();const le=d+e.length;s.setSelectionRange(le,le)})}else w.value+=`
${e}`;Ne.value=!1},Hl=()=>{if(!o.value.template.selectedTemplateId){r.warning("请先选择一个模板");return}const l=C.value.find(s=>s.id===o.value.template.selectedTemplateId);if(!l){r.warning("未找到选中的模板");return}const e=`{{模板:${l.id}【${l.name||"未命名模板"}】}}`;me(e),r.success(`已插入"${l.name}"模板的占位符`)},Xl=()=>{if(!o.value.template.selectedEntityId){r.warning("请先选择一个实体");return}const l=E.value.find(s=>s.id===o.value.template.selectedEntityId);if(!l){r.warning("未找到选中的实体");return}const e=`{{实体:${o.value.template.selectedEntityId}:${o.value.template.entityOutputFormat}:${o.value.template.entityExcludedDimensions.join(",")}【${l.name||"未命名实体"}】}}`;me(e),r.success(`已插入实体"${l.name}"的占位符`)},Yl=()=>{if(!o.value.template.selectedTemplateId){r.warning("请先选择一个模板");return}const l=C.value.find(s=>s.id===o.value.template.selectedTemplateId);if(!l){r.warning("未找到选中的模板");return}const e=`{{所有实体:${l.id}:${o.value.template.allEntitiesOutputFormat}:${o.value.template.excludedDimensions.join(",")}【${l.name||"未命名模板"}的所有实体】}}`;me(e),r.success(`已插入"${l.name}"模板的所有实体占位符`)},me=l=>{console.log("尝试插入文本:",l),l.includes("{{")&&l.includes("}}")&&hl(l);const e=document.querySelector(".result-content-wrapper .el-textarea__inner");if(e){console.log("找到文本区域元素");const s=e.selectionStart,d=e.selectionEnd,c=w.value||"",x=c.substring(0,s),R=c.substring(d);w.value=`${x}${l}${R}`,r.success("已插入占位符"),ze(()=>{e.focus();const le=s+l.length;e.setSelectionRange(le,le)})}else console.warn("未找到文本区域元素，将直接追加内容"),w.value?w.value+=`
`+l:w.value=l,r.success("已追加占位符到内容末尾");window.debugPVV.lastInsertedText=l};kl(async()=>{try{await ee.loadBooks(),ee.bookList?.length>0&&(g.value=ee.bookList[0].id,await qe(g.value))}catch(l){console.error("初始化失败:",l),r.error("初始化失败，请刷新页面重试")}await Promise.all([Ge()])}),i([]),i([]);const Zl=()=>{if(o.value.scene.mode==="manual"){if(!o.value.scene.selectedSceneIds?.length){r.warning("请先选择至少一个场景");return}const l=[];for(const s of o.value.scene.selectedSceneIds){let d="未知场景";for(const c of _.value.pools)if(c.scenes){const x=c.scenes.find(R=>R.id===s);if(x){d=x.title||"未命名场景";break}}l.push(d)}const e=`{{场景:manual:${o.value.scene.selectedSceneIds.join(",")}【${l.join("、")}】}}`;me(e),r.success(`已插入${l.length}个场景占位符`)}else{if(!o.value.scene.selectedPoolId){r.warning("请先选择一个场景池");return}const l=_.value.pools.find(d=>d.id===o.value.scene.selectedPoolId),e=l?l.name||"未命名场景池":"未知场景池",s=`{{场景:random:${o.value.scene.selectedPoolId}:${o.value.scene.randomCount||1}【随机${e}场景(${o.value.scene.randomCount||1}个)】}}`;me(s),r.success(`已插入"${e}"的随机场景占位符`)}};re(g,async()=>{await Ge()});const et=()=>{if(!o.value.template.selectedTemplateId||!Je.value.length){r.warning("请先选择一个包含实体的模板");return}const l=C.value.find(s=>s.id===o.value.template.selectedTemplateId);if(!l){r.warning("未找到选中的模板");return}const e=`{{实体:random:${o.value.template.selectedTemplateId}:${o.value.template.entityOutputFormat}:${o.value.template.entityExcludedDimensions.join(",")}【随机${l.name||"未命名模板"}实体】}}`;me(e),r.success(`已插入"${l.name}"模板的随机实体占位符`)};re(g,async()=>{if(g.value)try{const l=await window.pywebview.api.book_controller.get_entities(g.value),e=typeof l=="string"?JSON.parse(l):l;e.status==="success"?E.value=e.data||[]:(console.error("加载实体失败:",e.message),r.error("加载实体失败"))}catch(l){console.error("加载实体失败:",l),r.error("加载实体失败")}},{immediate:!0});const lt=()=>{console.log("书籍ID:",g.value),console.log("所有实体:",E.value),console.log("所有模板:",C.value);const l="f0b7cc16-9575-46d7-b5d2-2f8678f436a0",e=E.value.find(s=>s.id===l);return console.log("测试实体查找:",l,e),{selectedBookId:g.value,entityCount:E.value.length,templateCount:C.value.length,testEntity:e}},tt=()=>{console.log("书籍ID:",g.value),console.log("场景数据:",_.value),console.log("场景池数量:",_.value.pools?.length||0),console.log("当前场景池ID:",_.value.currentPoolId);let l=0;return _.value.pools?.forEach(e=>{e.scenes&&(l+=e.scenes.length,console.log(`场景池 "${e.name}" (${e.id}): ${e.scenes.length} 个场景`))}),{selectedBookId:g.value,poolCount:_.value.pools?.length||0,totalScenes:l,currentPoolId:_.value.currentPoolId,pools:_.value.pools}},hl=l=>{if(console.log("=== 占位符调试信息 ==="),console.log("占位符文本:",l),l.includes("{{实体:")){console.log("类型: 实体占位符");const e=l.match(/{{实体:([^:【}]+):([^:【}]+)(?::([^:【}]*))?(?:【([^】]+)】)?}}/);e&&(console.log("模式:",e[1]),console.log("模板ID:",e[2]),console.log("数量:",e[3]||"1"),console.log("显示名称:",e[4]||"无"))}else if(l.includes("{{场景:")){console.log("类型: 场景占位符");const e=l.match(/{{场景:([^:【}]+):([^:【}]+)(?::([^:【}]*))?(?:【([^】]+)】)?}}/);e&&(console.log("模式:",e[1]),console.log("ID值:",e[2]),console.log("数量:",e[3]||"1"),console.log("显示名称:",e[4]||"无"))}else console.log("类型: 未知占位符");console.log("=== 调试信息结束 ===")};window.debugPVV={entityData:lt,sceneData:tt,previewPrompt:Ke,insertTextToEditor:me,debugPlaceholder:hl,testInsert:l=>me(l)};const at=l=>{We.value=l.previewContent||"",w.value=l.content||"",Ie.value=!0},nt=l=>{Pl.confirm("确定要从当前规则中移除此提示词吗？","移除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const e=o.value.savedPrompts.findIndex(s=>s.id===l);e!==-1?(o.value.savedPrompts.splice(e,1),r.success("提示词已从规则中移除")):r.warning("未找到指定的提示词")}).catch(()=>{})},ot=l=>{if(!l.savedPrompts||l.savedPrompts.length===0){r.warning("选择的规则没有保存的提示词");return}const e=l.savedPrompts.filter(s=>!o.value.savedPrompts.some(d=>d.id===s.id));o.value.savedPrompts.push(...e),r.success(`已从规则中导入${e.length}个提示词`)},Oe=i(!1),Ce=i(null),st=async()=>{try{if(!Ce.value){r.warning("请选择要导入的规则");return}const l=B.value.find(e=>e.id===Ce.value);if(!l||!l.rule||!l.rule.savedPrompts){r.warning("选择的规则不包含保存的提示词");return}ot(l.rule),Oe.value=!1}catch(l){console.error("导入规则失败:",l),r.error("导入规则失败: "+l.message)}};re(()=>g.value,l=>{l&&fl()},{immediate:!0});const ut=l=>{if(!l.rule?.prompts||l.rule.prompts.length===0){r.warning("该规则下暂无提示词");return}Re.value=!0,he.value=l.rule.prompts||[],Vl.value=l.name,Le.value=he.value.length>0?0:-1,Ye.value=""},Re=i(!1),he=i([]),Vl=i(""),rt=l=>{l&&(w.value=l,Re.value=!1,j.value="generator",r.success("提示词已加载到编辑器"))},ye=i(null),Ye=i(""),Le=i(0);N(()=>Be.value.length===0?null:Le.value<0||Le.value>=Be.value.length?Be.value[0]:Be.value[Le.value]);const Be=N(()=>{if(!he.value)return[];if(!Ye.value)return he.value;const l=Ye.value.toLowerCase();return he.value.filter(e=>e.name&&e.name.toLowerCase().includes(l)||e.content&&e.content.toLowerCase().includes(l))}),Ze=i({fontSize:14,theme:"default",tabSize:2,lineNumbers:!0}),it=l=>({生成规则:"success",模板规则:"primary",场景规则:"warning",自定义:"info"})[l]||"info",dt=({row:l,rowIndex:e})=>e%2===0?"even-row":"odd-row",ct=l=>{xe.value=l},xe=i(1),je=i(10),pt=()=>{o.value={template:{enabled:!1,mode:"single",selectedTemplateId:null,selectedDimensions:[],excludedDimensions:[],entityExcludedDimensions:[],entityOutputFormat:"text",allEntitiesOutputFormat:"text"},scene:{enabled:!1,mode:"manual",selectedSceneIds:[],randomCount:1,selectedPoolId:null,placeholderText:"{{场景描述}}"},prompts:[]},w.value="",X.value="",F.value="",ye.value=null},mt=()=>{pt(),j.value="generator",ze(()=>{const l=document.querySelector(".prompt-generator");l&&l.scrollIntoView({behavior:"smooth"})}),r.success("已切换到生成器模式，请开始编辑新规则")},vt=async()=>{try{await Ke(),r.success("已重新生成预览内容")}catch(l){console.error("重新生成预览失败：",l),r.error("重新生成预览失败: "+l.message)}},ft=N(()=>{if(!o.value.template.selectedTemplateId)return[];const l=E.value.filter(s=>s.template_id===o.value.template.selectedTemplateId),e=new Set;return l.forEach(s=>{s.dimensions&&Object.keys(s.dimensions).forEach(d=>{e.add(d)})}),Array.from(e).map(s=>({name:s}))});return(l,e)=>{const s=It,d=Ct,c=al,x=nl,R=sl,le=Dl,W=Pt,se=xt,pe=St,$=Tt,z=Et,T=Nt,H=Ut,v=El,h=Sl,P=Ot,V=Lt,b=Rt,Y=jt,te=Bt,_e=zt,gt=At,$l=Ft,Ve=ul;return p(),k("div",ka,[u("div",Ia,[u("div",Ca,[e[46]||(e[46]=u("h1",{class:"page-title"},"AI提示词",-1)),t(d,{modelValue:g.value,"onUpdate:modelValue":e[0]||(e[0]=n=>g.value=n),placeholder:"选择书籍",class:"book-selector",onChange:qe},{default:a(()=>[(p(!0),k(L,null,M(D(ee).bookList,n=>(p(),S(s,{key:n.id,label:n.title,value:n.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),u("div",xa,[t(c,{class:Pe(["tab-button",{active:j.value==="promptList"}]),onClick:e[1]||(e[1]=n=>j.value="promptList")},{default:a(()=>e[47]||(e[47]=[m(" 提示词列表 ")])),_:1},8,["class"]),t(c,{class:Pe(["tab-button",{active:j.value==="generator"}]),onClick:e[2]||(e[2]=n=>j.value="generator")},{default:a(()=>e[48]||(e[48]=[m(" 提示词生成器 ")])),_:1},8,["class"])])]),g.value?(p(),k("div",Pa,[Il(u("div",Sa,[u("div",Ea,[u("div",Ta,[e[49]||(e[49]=u("h2",null,"AI提示词规则列表",-1)),t(R,{modelValue:K.value,"onUpdate:modelValue":e[3]||(e[3]=n=>K.value=n),placeholder:"搜索规则",class:"search-input",clearable:"",onClear:U},{prefix:a(()=>[t(x,null,{default:a(()=>[t(D(Tl))]),_:1})]),_:1},8,["modelValue"])]),u("div",Da,[t(c,{type:"primary",onClick:mt},{default:a(()=>[t(x,null,{default:a(()=>[t(D($e))]),_:1}),e[50]||(e[50]=m(" 新建规则 "))]),_:1})])]),u("div",Na,[B.value.length===0?(p(),S(le,{key:0,description:"暂无提示词，点击新建按钮创建"})):(p(),S(T,{key:1,data:ve.value,style:{width:"100%"},"header-cell-style":{background:"var(--el-bg-color-overlay)",color:"var(--el-text-color-primary)",fontWeight:"600"},"row-class-name":dt},{default:a(()=>[t(se,{prop:"name",label:"规则名称","min-width":"180"},{default:a(({row:n})=>[u("div",Ua,[u("span",Oa,f(n.name),1),n.type?(p(),S(W,{key:0,size:"small",type:it(n.type),class:"prompt-type-tag"},{default:a(()=>[m(f(n.type),1)]),_:2},1032,["type"])):ue("",!0)])]),_:1}),t(se,{prop:"description",label:"描述","min-width":"220"},{default:a(({row:n})=>[u("div",Ra,f(n.description||"暂无描述"),1)]),_:1}),t(se,{label:"提示词数量",width:"120",align:"center"},{default:a(({row:n})=>[u("div",La,[t(pe,{value:n.rule?.prompts?.length||0,type:n.rule?.prompts?.length?"primary":"info",class:"prompt-badge"},null,8,["value","type"])])]),_:1}),t(se,{prop:"updateTime",label:"更新时间",width:"160"},{default:a(({row:n})=>[u("div",Ba,f(jl(n.updateTime)),1)]),_:1}),t(se,{label:"操作",width:"200",fixed:"right"},{default:a(({row:n})=>[u("div",ja,[t(z,null,{default:a(()=>[t($,{content:"查看提示词",placement:"top"},{default:a(()=>[t(c,{type:"primary",link:"",onClick:Z=>ut(n)},{default:a(()=>[t(x,null,{default:a(()=>[t(D(Dt))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024),t($,{content:"编辑规则",placement:"top"},{default:a(()=>[t(c,{type:"primary",link:"",onClick:Z=>oe(n)},{default:a(()=>[t(x,null,{default:a(()=>[t(D(ll))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024),t($,{content:"删除规则",placement:"top"},{default:a(()=>[t(c,{type:"danger",link:"",onClick:Z=>Q(n)},{default:a(()=>[t(x,null,{default:a(()=>[t(D(xl))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)])]),_:1})]),_:1},8,["data"])),u("div",za,[t(H,{"current-page":xe.value,"onUpdate:currentPage":ct,"page-size":je.value,"page-sizes":[10,20,50,100],total:ve.value.length,"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:fe},null,8,["current-page","page-size","total"])])])],512),[[Cl,j.value==="promptList"]]),Il(u("div",Aa,[u("div",Fa,[e[53]||(e[53]=u("div",{class:"header-content"},[u("h2",null,"提示词生成器")],-1)),u("div",Ma,[t(c,{type:"primary",onClick:vl,disabled:!ml.value},{default:a(()=>[t(x,null,{default:a(()=>[t(D(el))]),_:1}),e[51]||(e[51]=m(" 重新生成 "))]),_:1},8,["disabled"]),t(c,{type:"success",onClick:_l,disabled:!X.value||!w.value},{default:a(()=>[t(x,null,{default:a(()=>[t(D(Nl))]),_:1}),e[52]||(e[52]=m(" 保存规则 "))]),_:1},8,["disabled"])])]),u("div",Ja,[u("div",qa,[u("div",Ga,[e[54]||(e[54]=u("h3",{class:"panel-title"},"提示词规则",-1)),t(h,{model:be.value,"label-width":"80px"},{default:a(()=>[t(v,{label:"规则名称",required:""},{default:a(()=>[t(R,{modelValue:X.value,"onUpdate:modelValue":e[4]||(e[4]=n=>X.value=n),placeholder:"输入提示词规则名称"},null,8,["modelValue"])]),_:1}),t(v,{label:"描述"},{default:a(()=>[t(R,{modelValue:F.value,"onUpdate:modelValue":e[5]||(e[5]=n=>F.value=n),type:"textarea",rows:2,placeholder:"描述这个生成规则的用途"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),u("div",Qa,[e[57]||(e[57]=u("h3",{class:"panel-title"},"提示词编辑",-1)),u("div",Ka,[t(z,null,{default:a(()=>[t($,{content:"复制到剪贴板",placement:"top"},{default:a(()=>[t(c,{size:"small",onClick:Ll,disabled:!w.value},{default:a(()=>[t(x,null,{default:a(()=>[t(D(ol))]),_:1}),e[55]||(e[55]=m(" 复制 "))]),_:1},8,["disabled"])]),_:1}),t($,{content:"清空当前内容",placement:"top"},{default:a(()=>[t(c,{size:"small",type:"danger",onClick:Bl},{default:a(()=>[t(x,null,{default:a(()=>[t(D(xl))]),_:1}),e[56]||(e[56]=m(" 清空 "))]),_:1})]),_:1})]),_:1})]),u("div",Wa,[t(R,{modelValue:w.value,"onUpdate:modelValue":e[6]||(e[6]=n=>w.value=n),type:"textarea",rows:12,placeholder:"在此编辑提示词，可以插入模板和场景占位符"},null,8,["modelValue"])])])]),u("div",Ha,[u("div",Xa,[u("div",Ya,[u("div",Za,[e[58]||(e[58]=u("h4",null,"模板规则",-1)),t(P,{modelValue:o.value.template.enabled,"onUpdate:modelValue":e[7]||(e[7]=n=>o.value.template.enabled=n),"active-text":"启用"},null,8,["modelValue"])]),u("div",en,[t(_e,null,{default:a(()=>[o.value.template.enabled?(p(),S(h,{key:0,class:"rule-form","label-position":"top"},{default:a(()=>[t(v,{label:"使用模式"},{default:a(()=>[t(b,{modelValue:o.value.template.mode,"onUpdate:modelValue":e[8]||(e[8]=n=>o.value.template.mode=n)},{default:a(()=>[t(V,{label:"single"},{default:a(()=>e[59]||(e[59]=[m("单个模板")])),_:1}),t(V,{label:"entity"},{default:a(()=>e[60]||(e[60]=[m("单个实体")])),_:1}),t(V,{label:"all"},{default:a(()=>e[61]||(e[61]=[m("所有实体")])),_:1})]),_:1},8,["modelValue"])]),_:1}),o.value.template.mode==="single"?(p(),k(L,{key:0},[t(v,{label:"选择模板"},{default:a(()=>[t(d,{modelValue:o.value.template.selectedTemplateId,"onUpdate:modelValue":e[9]||(e[9]=n=>o.value.template.selectedTemplateId=n),filterable:"",placeholder:"选择模板"},{default:a(()=>[(p(!0),k(L,null,M(C.value,n=>(p(),S(s,{key:n.id,label:n.name,value:n.id},{default:a(()=>[u("span",null,f(n.name),1),u("span",ln,f(ke(n))+"个实体",1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(v,null,{default:a(()=>[t(c,{type:"primary",onClick:Hl,disabled:!o.value.template.selectedTemplateId},{default:a(()=>[t(x,null,{default:a(()=>[t(D($e))]),_:1}),e[62]||(e[62]=m(" 插入此模板 "))]),_:1},8,["disabled"])]),_:1})],64)):o.value.template.mode==="entity"?(p(),k(L,{key:1},[t(v,{label:"选择模板"},{default:a(()=>[t(d,{modelValue:o.value.template.selectedTemplateId,"onUpdate:modelValue":e[10]||(e[10]=n=>o.value.template.selectedTemplateId=n),filterable:"",placeholder:"选择模板",onChange:Jl},{default:a(()=>[(p(!0),k(L,null,M(C.value,n=>(p(),S(s,{key:n.id,label:n.name,value:n.id},{default:a(()=>[u("span",null,f(n.name),1),u("span",tn,f(ke(n))+"个实体",1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(v,{label:"选择实体"},{default:a(()=>[t(d,{modelValue:o.value.template.selectedEntityId,"onUpdate:modelValue":e[11]||(e[11]=n=>o.value.template.selectedEntityId=n),filterable:"",placeholder:"选择实体"},{default:a(()=>[(p(!0),k(L,null,M(Je.value,n=>(p(),S(s,{key:n.id,label:n.name,value:n.id},{default:a(()=>[u("span",null,f(n.name),1),n.type?(p(),k("span",an,"("+f(n.type)+")",1)):ue("",!0)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(v,{label:"输出格式"},{default:a(()=>[t(b,{modelValue:o.value.template.entityOutputFormat,"onUpdate:modelValue":e[12]||(e[12]=n=>o.value.template.entityOutputFormat=n)},{default:a(()=>[t(V,{label:"text"},{default:a(()=>e[63]||(e[63]=[m("文本格式")])),_:1}),t(V,{label:"json"},{default:a(()=>e[64]||(e[64]=[m("JSON格式")])),_:1})]),_:1},8,["modelValue"])]),_:1}),t(v,{label:"排除维度"},{default:a(()=>[t(te,{modelValue:o.value.template.entityExcludedDimensions,"onUpdate:modelValue":e[13]||(e[13]=n=>o.value.template.entityExcludedDimensions=n)},{default:a(()=>[(p(!0),k(L,null,M(ft.value,n=>(p(),S(Y,{key:n.name,label:n.name},{default:a(()=>[m(f(n.name),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(v,null,{default:a(()=>[u("div",nn,[t(c,{type:"primary",onClick:Xl,disabled:!o.value.template.selectedEntityId},{default:a(()=>[t(x,null,{default:a(()=>[t(D($e))]),_:1}),e[65]||(e[65]=m(" 插入此实体 "))]),_:1},8,["disabled"]),t(c,{type:"primary",onClick:et,disabled:!o.value.template.selectedTemplateId||!Je.value.length},{default:a(()=>[t(x,null,{default:a(()=>[t(D(el))]),_:1}),e[66]||(e[66]=m(" 插入随机实体 "))]),_:1},8,["disabled"])])]),_:1})],64)):(p(),k(L,{key:2},[t(v,{label:"选择模板"},{default:a(()=>[t(d,{modelValue:o.value.template.selectedTemplateId,"onUpdate:modelValue":e[14]||(e[14]=n=>o.value.template.selectedTemplateId=n),filterable:"",placeholder:"选择模板"},{default:a(()=>[(p(!0),k(L,null,M(C.value,n=>(p(),S(s,{key:n.id,label:n.name,value:n.id},{default:a(()=>[u("span",null,f(n.name),1),u("span",on,f(ke(n))+"个实体",1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(v,{label:"输出格式"},{default:a(()=>[t(b,{modelValue:o.value.template.allEntitiesOutputFormat,"onUpdate:modelValue":e[15]||(e[15]=n=>o.value.template.allEntitiesOutputFormat=n)},{default:a(()=>[t(V,{label:"text"},{default:a(()=>e[67]||(e[67]=[m("文本格式")])),_:1}),t(V,{label:"json"},{default:a(()=>e[68]||(e[68]=[m("JSON格式")])),_:1})]),_:1},8,["modelValue"])]),_:1}),t(v,{label:"排除维度"},{default:a(()=>[t(te,{modelValue:o.value.template.excludedDimensions,"onUpdate:modelValue":e[16]||(e[16]=n=>o.value.template.excludedDimensions=n)},{default:a(()=>[(p(!0),k(L,null,M(Ol.value,n=>(p(),S(Y,{key:n.name,label:n.name},{default:a(()=>[m(f(n.name),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(v,null,{default:a(()=>[t(c,{type:"primary",onClick:Yl,disabled:!o.value.template.selectedTemplateId},{default:a(()=>[t(x,null,{default:a(()=>[t(D($e))]),_:1}),e[69]||(e[69]=m(" 插入所有实体 "))]),_:1},8,["disabled"])]),_:1})],64))]),_:1})):ue("",!0)]),_:1})])]),u("div",sn,[u("div",un,[e[70]||(e[70]=u("h4",null,"场景规则",-1)),t(P,{modelValue:o.value.scene.enabled,"onUpdate:modelValue":e[17]||(e[17]=n=>o.value.scene.enabled=n),"active-text":"启用"},null,8,["modelValue"])]),u("div",rn,[t(_e,null,{default:a(()=>[o.value.scene.enabled?(p(),S(h,{key:0,class:"rule-form","label-position":"top"},{default:a(()=>[t(v,{label:"场景模式"},{default:a(()=>[t(b,{modelValue:o.value.scene.mode,"onUpdate:modelValue":e[18]||(e[18]=n=>o.value.scene.mode=n)},{default:a(()=>[t(V,{label:"manual"},{default:a(()=>e[71]||(e[71]=[m("指定场景")])),_:1}),t(V,{label:"random"},{default:a(()=>e[72]||(e[72]=[m("随机抽取")])),_:1})]),_:1},8,["modelValue"])]),_:1}),o.value.scene.mode==="manual"?(p(),S(v,{key:0,label:"选择场景"},{default:a(()=>[t(d,{modelValue:o.value.scene.selectedSceneIds,"onUpdate:modelValue":e[19]||(e[19]=n=>o.value.scene.selectedSceneIds=n),multiple:"",filterable:"",placeholder:"选择场景",class:"scene-selector"},{default:a(()=>[(p(!0),k(L,null,M(_.value.pools,n=>(p(),S(gt,{key:n.id,label:n.name},{default:a(()=>[(p(!0),k(L,null,M(n.scenes,Z=>(p(),S(s,{key:Z.id,label:Z.title,value:Z.id},{default:a(()=>[u("div",dn,[u("div",cn,f(Z.title),1),u("div",pn,f(Z.description),1)])]),_:2},1032,["label","value"]))),128))]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})):(p(),k(L,{key:1},[t(v,{label:"场景池"},{default:a(()=>[t(d,{modelValue:o.value.scene.selectedPoolId,"onUpdate:modelValue":e[20]||(e[20]=n=>o.value.scene.selectedPoolId=n)},{default:a(()=>[(p(!0),k(L,null,M(_.value.pools,n=>(p(),S(s,{key:n.id,label:n.name,value:n.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(v,{label:"抽取数量"},{default:a(()=>[t($l,{modelValue:o.value.scene.randomCount,"onUpdate:modelValue":e[21]||(e[21]=n=>o.value.scene.randomCount=n),min:1,max:l.getMaxSceneCount},null,8,["modelValue","max"])]),_:1})],64)),t(v,null,{default:a(()=>[t(c,{type:"primary",onClick:Zl,disabled:o.value.scene.mode==="manual"&&(!o.value.scene.selectedSceneIds||o.value.scene.selectedSceneIds.length===0)||o.value.scene.mode==="random"&&!o.value.scene.selectedPoolId},{default:a(()=>[t(x,null,{default:a(()=>[t(D($e))]),_:1}),e[73]||(e[73]=m(" 插入场景描述 "))]),_:1},8,["disabled"])]),_:1})]),_:1})):ue("",!0)]),_:1})])])])])])],512),[[Cl,j.value==="generator"]])])):(p(),k("div",mn,[t(le,{description:"请先选择一本书籍","image-size":200},{image:a(()=>[t(x,{size:64,class:"empty-icon"},{default:a(()=>[t(D(ll))]),_:1})]),_:1})])),t(Ve,{modelValue:de.value,"onUpdate:modelValue":e[26]||(e[26]=n=>de.value=n),title:y.value.id?"编辑提示词":"新建提示词",width:"800px","close-on-click-modal":!1,class:"prompt-edit-dialog"},{footer:a(()=>[u("div",fn,[t(c,{onClick:e[25]||(e[25]=n=>de.value=!1)},{default:a(()=>e[77]||(e[77]=[m("取消")])),_:1}),t(c,{type:"primary",onClick:l.savePrompt,disabled:!l.canSavePrompt},{default:a(()=>e[78]||(e[78]=[m("确定")])),_:1},8,["onClick","disabled"])])]),default:a(()=>[t(h,{model:y.value,"label-width":"80px"},{default:a(()=>[t(v,{label:"名称",required:""},{default:a(()=>[t(R,{modelValue:y.value.name,"onUpdate:modelValue":e[22]||(e[22]=n=>y.value.name=n),placeholder:"输入提示词名称"},null,8,["modelValue"])]),_:1}),t(v,{label:"描述"},{default:a(()=>[t(R,{modelValue:y.value.description,"onUpdate:modelValue":e[23]||(e[23]=n=>y.value.description=n),type:"textarea",rows:3,placeholder:"输入提示词描述"},null,8,["modelValue"])]),_:1}),t(v,{label:"内容",required:""},{default:a(()=>[t(R,{modelValue:y.value.content,"onUpdate:modelValue":e[24]||(e[24]=n=>y.value.content=n),type:"textarea",rows:10,placeholder:"输入提示词内容"},null,8,["modelValue"]),u("div",vn,[e[76]||(e[76]=u("span",null,"可用占位符格式：",-1)),t(W,{size:"small",effect:"plain"},{default:a(()=>e[74]||(e[74]=[m(f(123))])),_:1}),t(W,{size:"small",effect:"plain"},{default:a(()=>e[75]||(e[75]=[m(f(123))])),_:1})])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),t(Ve,{modelValue:O.value,"onUpdate:modelValue":e[30]||(e[30]=n=>O.value=n),title:G.value?.name||"未命名提示词",width:"800px","close-on-click-modal":!0,class:"prompt-detail-dialog"},{footer:a(()=>[u("div",$n,[t(c,{onClick:e[27]||(e[27]=n=>O.value=!1)},{default:a(()=>e[83]||(e[83]=[m("关闭")])),_:1}),t(c,{type:"primary",onClick:e[28]||(e[28]=n=>oe(G.value))},{default:a(()=>e[84]||(e[84]=[m("编辑")])),_:1}),t(c,{type:"success",onClick:e[29]||(e[29]=n=>ge(G.value))},{default:a(()=>e[85]||(e[85]=[m("复制到剪贴板")])),_:1})])]),default:a(()=>[u("div",gn,[u("div",yn,[e[79]||(e[79]=u("h3",null,"提示词名称",-1)),u("p",null,f(G.value?.name||"未命名提示词"),1)]),u("div",_n,[e[80]||(e[80]=u("h3",null,"描述",-1)),u("p",null,f(G.value?.description||"无描述"),1)]),u("div",bn,[e[81]||(e[81]=u("h3",null,"内容",-1)),u("div",wn,[u("pre",null,f(G.value?.content||"无内容"),1)])]),u("div",hn,[e[82]||(e[82]=u("h3",null,"占位符",-1)),u("div",Vn,[(p(!0),k(L,null,M(I(G.value?.content||""),n=>(p(),S(W,{key:n,size:"large",class:"placeholder-tag",effect:"light"},{default:a(()=>[m(f(n),1)]),_:2},1024))),128)),I(G.value?.content||"").length===0?(p(),S(le,{key:0,description:"此提示词中没有占位符"})):ue("",!0)])])])]),_:1},8,["modelValue","title"]),t(Ve,{modelValue:Ee.value,"onUpdate:modelValue":e[33]||(e[33]=n=>Ee.value=n),title:"批量生成提示词",width:"600px",class:"batch-generate-dialog"},{footer:a(()=>[u("div",Pn,[t(c,{onClick:e[32]||(e[32]=n=>Ee.value=!1)},{default:a(()=>e[88]||(e[88]=[m("关闭")])),_:1}),t(c,{type:"primary",onClick:Al,loading:Se.value},{default:a(()=>e[89]||(e[89]=[m(" 开始生成 ")])),_:1},8,["loading"])])]),default:a(()=>[t(h,{model:Te.value,"label-position":"top"},{default:a(()=>[t(v,{label:"生成数量"},{default:a(()=>[t($l,{modelValue:Te.value.count,"onUpdate:modelValue":e[31]||(e[31]=n=>Te.value.count=n),min:1,max:20,step:1},null,8,["modelValue"])]),_:1}),t(v,{label:"生成结果"},{default:a(()=>[u("div",kn,[t(_e,{height:"300px"},{default:a(()=>[(p(!0),k(L,null,M(De.value,(n,Z)=>(p(),k("div",{key:Z,class:"batch-result-item"},[u("div",In,[u("span",Cn,"结果 #"+f(Z+1),1),t(z,null,{default:a(()=>[t(c,{size:"small",onClick:yt=>Fl(n)},{default:a(()=>e[86]||(e[86]=[m(" 使用 ")])),_:2},1032,["onClick"]),t(c,{size:"small",onClick:yt=>Ml(n)},{default:a(()=>e[87]||(e[87]=[m(" 复制 ")])),_:2},1032,["onClick"])]),_:2},1024)]),u("div",xn,f(n),1)]))),128))]),_:1})])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(la,{modelValue:Ie.value,"onUpdate:modelValue":e[34]||(e[34]=n=>Ie.value=n),"prompt-name":X.value||"生成的提示词","prompt-description":F.value,"prompt-content":We.value,onSaveToRule:zl,onRegenerate:vt},null,8,["modelValue","prompt-name","prompt-description","prompt-content"]),t(Ve,{modelValue:He.value,"onUpdate:modelValue":e[37]||(e[37]=n=>He.value=n),title:"选择模板",width:"50%"},{footer:a(()=>[u("span",En,[t(c,{onClick:e[36]||(e[36]=n=>He.value=!1)},{default:a(()=>e[90]||(e[90]=[m("取消")])),_:1}),t(c,{type:"primary",onClick:Ql,disabled:!Xe.value},{default:a(()=>e[91]||(e[91]=[m(" 插入 ")])),_:1},8,["disabled"])])]),default:a(()=>[t(h,null,{default:a(()=>[t(v,{label:"选择模板"},{default:a(()=>[t(d,{modelValue:Xe.value,"onUpdate:modelValue":e[35]||(e[35]=n=>Xe.value=n),filterable:"",placeholder:"选择模板",style:{width:"100%"}},{default:a(()=>[(p(!0),k(L,null,M(C.value,n=>(p(),S(s,{key:n.id,label:n.name,value:n.id},{default:a(()=>[u("span",null,f(n.name),1),u("span",Sn,f(ke(n))+"个实体",1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),t(Ve,{modelValue:Ne.value,"onUpdate:modelValue":e[41]||(e[41]=n=>Ne.value=n),title:"选择实体",width:"50%"},{footer:a(()=>[u("span",Nn,[t(c,{onClick:e[40]||(e[40]=n=>Ne.value=!1)},{default:a(()=>e[92]||(e[92]=[m("取消")])),_:1}),t(c,{type:"primary",onClick:Wl,disabled:!we.value},{default:a(()=>e[93]||(e[93]=[m(" 插入 ")])),_:1},8,["disabled"])])]),default:a(()=>[t(h,null,{default:a(()=>[t(v,{label:"选择模板"},{default:a(()=>[t(d,{modelValue:Ue.value,"onUpdate:modelValue":e[38]||(e[38]=n=>Ue.value=n),filterable:"",placeholder:"选择模板",onChange:Kl,style:{width:"100%"}},{default:a(()=>[(p(!0),k(L,null,M(C.value,n=>(p(),S(s,{key:n.id,label:n.name,value:n.id},{default:a(()=>[u("span",null,f(n.name),1),u("span",Tn,f(ke(n))+"个实体",1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),Ue.value?(p(),S(v,{key:0,label:"选择实体"},{default:a(()=>[t(d,{modelValue:we.value,"onUpdate:modelValue":e[39]||(e[39]=n=>we.value=n),filterable:"",placeholder:"选择实体",style:{width:"100%"}},{default:a(()=>[(p(!0),k(L,null,M(Gl(Ue.value),n=>(p(),S(s,{key:n.id,label:n.name,value:n.id},{default:a(()=>[u("span",null,f(n.name),1),n.type?(p(),k("span",Dn,"("+f(n.type)+")",1)):ue("",!0)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):ue("",!0)]),_:1})]),_:1},8,["modelValue"]),o.value.savedPrompts&&o.value.savedPrompts.length>0?(p(),k("div",Un,[u("h3",On,"规则已保存提示词 ("+f(o.value.savedPrompts.length)+")",1),t(T,{data:o.value.savedPrompts,style:{width:"100%"},size:"small"},{default:a(()=>[t(se,{label:"保存时间",width:"180"},{default:a(n=>[m(f(new Date(n.row.timestamp).toLocaleString()),1)]),_:1}),t(se,{label:"预览内容"},{default:a(n=>[u("div",Rn,f(n.row.previewContent||n.row.content),1)]),_:1}),t(se,{label:"操作",width:"150"},{default:a(n=>[t(c,{size:"small",onClick:Z=>at(n.row)},{default:a(()=>e[94]||(e[94]=[m("查看")])),_:2},1032,["onClick"]),t(c,{size:"small",type:"danger",onClick:Z=>nt(n.row.id)},{default:a(()=>e[95]||(e[95]=[m("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])])):ue("",!0),t(Ve,{modelValue:Oe.value,"onUpdate:modelValue":e[44]||(e[44]=n=>Oe.value=n),title:"导入规则提示词",width:"500px"},{footer:a(()=>[u("span",Ln,[t(c,{onClick:e[43]||(e[43]=n=>Oe.value=!1)},{default:a(()=>e[96]||(e[96]=[m("取消")])),_:1}),t(c,{type:"primary",onClick:st,disabled:!Ce.value},{default:a(()=>e[97]||(e[97]=[m(" 导入 ")])),_:1},8,["disabled"])])]),default:a(()=>[t(h,null,{default:a(()=>[t(v,{label:"选择规则"},{default:a(()=>[t(d,{modelValue:Ce.value,"onUpdate:modelValue":e[42]||(e[42]=n=>Ce.value=n),filterable:"",placeholder:"选择规则"},{default:a(()=>[(p(!0),k(L,null,M(B.value.filter(n=>n.type==="generator"),n=>(p(),S(s,{key:n.id,label:n.name,value:n.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),t($a,{modelValue:Re.value,"onUpdate:modelValue":e[45]||(e[45]=n=>Re.value=n),"rule-name":Vl.value,prompts:he.value,onUsePrompt:rt},null,8,["modelValue","rule-name","prompts"])])}}},ao=tl(Bn,[["__scopeId","data-v-1feeb1b2"]]);export{ao as default};
