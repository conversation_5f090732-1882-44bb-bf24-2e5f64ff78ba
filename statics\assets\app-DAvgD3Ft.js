import{_ as Ss,r as O,c as gt,w as We,o as vt,b as ee,m as j,p as xe,e as d,d as n,F as ne,g as o,R as tt,S as Mt,n as Us,C as B,J as Bs,v as x,aA as $l,aX as Ll,B as xt,D as $s,G as Le,a as Gl,E as b,$ as fs,bt as Al,a8 as ps,af as Pl,bQ as ms,bb as Ol,k as zl,bc as Ml,j as Fl,q as Rl,s as Hl,b2 as Jl,t as jl,T as je,N as gs,a9 as dt,az as Wl,ax as ql,aG as Xl,aw as ct,M as Fe,V as Zl,aP as Ql,aQ as Kl,aF as Yl,bn as Ot,aK as en,bk as vs,bR as hs,ba as tn,aD as sn,aE as ln,be as nn,bf as an,a1 as on,ab as rn,bS as un,aS as dn,ad as cn,ae as fn,K as pn,aH as bs,z as mn,bT as gn,bU as vn,aM as ys,bV as hn,y as bn,ah as me,bK as et}from"./entry-DxFfH4M0.js";/* empty css                   *//* empty css                    *//* empty css                    *//* empty css                    *//* empty css                *//* empty css                 *//* empty css                       *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                         *//* empty css                 *//* empty css                        *//* empty css                  *//* empty css                */import{useAIRolesStore as yn}from"./aiRoles-Cy8pLKqW.js";import{useAIProvidersStore as wn}from"./aiProviders-Zwr_VLaf.js";var oe;(function(i){i.INSERT="insert",i.DELETE="delete",i.CONTEXT="context"})(oe||(oe={}));const kn={LINE_BY_LINE:"line-by-line"},_n={NONE:"none"},Cn={WORD:"word"};var st;(function(i){i.AUTO="auto",i.DARK="dark",i.LIGHT="light"})(st||(st={}));const xn=["-","[","]","/","{","}","(",")","*","+","?",".","\\","^","$","|"],Tn=RegExp("["+xn.join("\\")+"]","g");function Vn(i){return i.replace(Tn,"\\$&")}function ws(i){return i&&i.replace(/\\/g,"/")}function Dn(i){let s,l,e,r=0;for(s=0,e=i.length;s<e;s++)l=i.charCodeAt(s),r=(r<<5)-r+l,r|=0;return r}function ks(i,s){const l=i.split(".");return l.length>1?l[l.length-1]:s}function _s(i,s){return s.reduce((l,e)=>l||i.startsWith(e),!1)}const Cs=["a/","b/","i/","w/","c/","o/"];function Re(i,s,l){const e=l!==void 0?[...Cs,l]:Cs,r=s?new RegExp(`^${Vn(s)} "?(.+?)"?$`):new RegExp('^"?(.+?)"?$'),[,c=""]=r.exec(i)||[],f=e.find(w=>c.indexOf(w)===0);return(f?c.slice(f.length):c).replace(/\s+\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}(?:\.\d+)? [+-]\d{4}.*$/,"")}function En(i,s){return Re(i,"---",s)}function Nn(i,s){return Re(i,"+++",s)}function Ls(i,s={}){const l=[];let e=null,r=null,c=null,f=null,_=null,w=null,C=null;const V="--- ",E="+++ ",z="@@",g=/^old mode (\d{6})/,y=/^new mode (\d{6})/,U=/^deleted file mode (\d{6})/,D=/^new file mode (\d{6})/,$=/^copy from "?(.+)"?/,Q=/^copy to "?(.+)"?/,G=/^rename from "?(.+)"?/,P=/^rename to "?(.+)"?/,M=/^similarity index (\d+)%/,X=/^dissimilarity index (\d+)%/,ke=/^index ([\da-z]+)\.\.([\da-z]+)\s*(\d{6})?/,m=/^Binary files (.*) and (.*) differ/,T=/^GIT binary patch/,I=/^index ([\da-z]+),([\da-z]+)\.\.([\da-z]+)/,A=/^mode (\d{6}),(\d{6})\.\.(\d{6})/,le=/^new file mode (\d{6})/,fe=/^deleted file mode (\d{6}),(\d{6})/,ce=i.replace(/\\ No newline at end of file/g,"").replace(/\r\n?/g,`
`).split(`
`);function Z(){r!==null&&e!==null&&(e.blocks.push(r),r=null)}function F(){e!==null&&(!e.oldName&&w!==null&&(e.oldName=w),!e.newName&&C!==null&&(e.newName=C),e.newName&&(l.push(e),e=null)),w=null,C=null}function Se(){Z(),F(),e={blocks:[],deletedLines:0,addedLines:0}}function R(N){Z();let W;e!==null&&((W=/^@@ -(\d+)(?:,\d+)? \+(\d+)(?:,\d+)? @@.*/.exec(N))?(e.isCombined=!1,c=parseInt(W[1],10),_=parseInt(W[2],10)):(W=/^@@@ -(\d+)(?:,\d+)? -(\d+)(?:,\d+)? \+(\d+)(?:,\d+)? @@@.*/.exec(N))?(e.isCombined=!0,c=parseInt(W[1],10),f=parseInt(W[2],10),_=parseInt(W[3],10)):(N.startsWith(z)&&console.error("Failed to parse lines, starting in 0!"),c=0,_=0,e.isCombined=!1)),r={lines:[],oldStartLine:c,oldStartLine2:f,newStartLine:_,header:N}}function ie(N){if(e===null||r===null||c===null||_===null)return;const W={content:N},S=e.isCombined?["+ "," +","++"]:["+"],ve=e.isCombined?["- "," -","--"]:["-"];_s(N,S)?(e.addedLines++,W.type=oe.INSERT,W.oldNumber=void 0,W.newNumber=_++):_s(N,ve)?(e.deletedLines++,W.type=oe.DELETE,W.oldNumber=c++,W.newNumber=void 0):(W.type=oe.CONTEXT,W.oldNumber=c++,W.newNumber=_++),r.lines.push(W)}function Te(N,W){let S=W;for(;S<ce.length-3;){if(N.startsWith("diff"))return!1;if(ce[S].startsWith(V)&&ce[S+1].startsWith(E)&&ce[S+2].startsWith(z))return!0;S++}return!1}return ce.forEach((N,W)=>{if(!N||N.startsWith("*"))return;let S;const ve=ce[W-1],ze=ce[W+1],qe=ce[W+2];if(N.startsWith("diff --git")||N.startsWith("diff --combined")){if(Se(),(S=/^diff --git "?([a-ciow]\/.+)"? "?([a-ciow]\/.+)"?/.exec(N))&&(w=Re(S[1],void 0,s.dstPrefix),C=Re(S[2],void 0,s.srcPrefix)),e===null)throw new Error("Where is my file !!!");e.isGitDiff=!0;return}if(N.startsWith("Binary files")&&!e?.isGitDiff){if(Se(),(S=/^Binary files "?([a-ciow]\/.+)"? and "?([a-ciow]\/.+)"? differ/.exec(N))&&(w=Re(S[1],void 0,s.dstPrefix),C=Re(S[2],void 0,s.srcPrefix)),e===null)throw new Error("Where is my file !!!");e.isBinary=!0;return}if((!e||!e.isGitDiff&&e&&N.startsWith(V)&&ze.startsWith(E)&&qe.startsWith(z))&&Se(),e?.isTooBig)return;if(e&&(typeof s.diffMaxChanges=="number"&&e.addedLines+e.deletedLines>s.diffMaxChanges||typeof s.diffMaxLineLength=="number"&&N.length>s.diffMaxLineLength)){e.isTooBig=!0,e.addedLines=0,e.deletedLines=0,e.blocks=[],r=null;const He=typeof s.diffTooBigMessage=="function"?s.diffTooBigMessage(l.length):"Diff too big to be displayed";R(He);return}if(N.startsWith(V)&&ze.startsWith(E)||N.startsWith(E)&&ve.startsWith(V)){if(e&&!e.oldName&&N.startsWith("--- ")&&(S=En(N,s.srcPrefix))){e.oldName=S,e.language=ks(e.oldName,e.language);return}if(e&&!e.newName&&N.startsWith("+++ ")&&(S=Nn(N,s.dstPrefix))){e.newName=S,e.language=ks(e.newName,e.language);return}}if(e&&(N.startsWith(z)||e.isGitDiff&&e.oldName&&e.newName&&!r)){R(N);return}if(r&&(N.startsWith("+")||N.startsWith("-")||N.startsWith(" "))){ie(N);return}const Ae=!Te(N,W);if(e===null)throw new Error("Where is my file !!!");(S=g.exec(N))?e.oldMode=S[1]:(S=y.exec(N))?e.newMode=S[1]:(S=U.exec(N))?(e.deletedFileMode=S[1],e.isDeleted=!0):(S=D.exec(N))?(e.newFileMode=S[1],e.isNew=!0):(S=$.exec(N))?(Ae&&(e.oldName=S[1]),e.isCopy=!0):(S=Q.exec(N))?(Ae&&(e.newName=S[1]),e.isCopy=!0):(S=G.exec(N))?(Ae&&(e.oldName=S[1]),e.isRename=!0):(S=P.exec(N))?(Ae&&(e.newName=S[1]),e.isRename=!0):(S=m.exec(N))?(e.isBinary=!0,e.oldName=Re(S[1],void 0,s.srcPrefix),e.newName=Re(S[2],void 0,s.dstPrefix),R("Binary file")):T.test(N)?(e.isBinary=!0,R(N)):(S=M.exec(N))?e.unchangedPercentage=parseInt(S[1],10):(S=X.exec(N))?e.changedPercentage=parseInt(S[1],10):(S=ke.exec(N))?(e.checksumBefore=S[1],e.checksumAfter=S[2],S[3]&&(e.mode=S[3])):(S=I.exec(N))?(e.checksumBefore=[S[2],S[3]],e.checksumAfter=S[1]):(S=A.exec(N))?(e.oldMode=[S[2],S[3]],e.newMode=S[1]):(S=le.exec(N))?(e.newFileMode=S[1],e.isNew=!0):(S=fe.exec(N))&&(e.deletedFileMode=S[1],e.isDeleted=!0)}),Z(),F(),l}function Ie(){}Ie.prototype={diff:function(s,l){var e,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},c=r.callback;typeof r=="function"&&(c=r,r={});var f=this;function _(P){return P=f.postProcess(P,r),c?(setTimeout(function(){c(P)},0),!0):P}s=this.castInput(s,r),l=this.castInput(l,r),s=this.removeEmpty(this.tokenize(s,r)),l=this.removeEmpty(this.tokenize(l,r));var w=l.length,C=s.length,V=1,E=w+C;r.maxEditLength!=null&&(E=Math.min(E,r.maxEditLength));var z=(e=r.timeout)!==null&&e!==void 0?e:1/0,g=Date.now()+z,y=[{oldPos:-1,lastComponent:void 0}],U=this.extractCommon(y[0],l,s,0,r);if(y[0].oldPos+1>=C&&U+1>=w)return _(xs(f,y[0].lastComponent,l,s,f.useLongestToken));var D=-1/0,$=1/0;function Q(){for(var P=Math.max(D,-V);P<=Math.min($,V);P+=2){var M=void 0,X=y[P-1],ke=y[P+1];X&&(y[P-1]=void 0);var m=!1;if(ke){var T=ke.oldPos-P;m=ke&&0<=T&&T<w}var I=X&&X.oldPos+1<C;if(!m&&!I){y[P]=void 0;continue}if(!I||m&&X.oldPos<ke.oldPos?M=f.addToPath(ke,!0,!1,0,r):M=f.addToPath(X,!1,!0,1,r),U=f.extractCommon(M,l,s,P,r),M.oldPos+1>=C&&U+1>=w)return _(xs(f,M.lastComponent,l,s,f.useLongestToken));y[P]=M,M.oldPos+1>=C&&($=Math.min($,P-1)),U+1>=w&&(D=Math.max(D,P+1))}V++}if(c)(function P(){setTimeout(function(){if(V>E||Date.now()>g)return c();Q()||P()},0)})();else for(;V<=E&&Date.now()<=g;){var G=Q();if(G)return G}},addToPath:function(s,l,e,r,c){var f=s.lastComponent;return f&&!c.oneChangePerToken&&f.added===l&&f.removed===e?{oldPos:s.oldPos+r,lastComponent:{count:f.count+1,added:l,removed:e,previousComponent:f.previousComponent}}:{oldPos:s.oldPos+r,lastComponent:{count:1,added:l,removed:e,previousComponent:f}}},extractCommon:function(s,l,e,r,c){for(var f=l.length,_=e.length,w=s.oldPos,C=w-r,V=0;C+1<f&&w+1<_&&this.equals(e[w+1],l[C+1],c);)C++,w++,V++,c.oneChangePerToken&&(s.lastComponent={count:1,previousComponent:s.lastComponent,added:!1,removed:!1});return V&&!c.oneChangePerToken&&(s.lastComponent={count:V,previousComponent:s.lastComponent,added:!1,removed:!1}),s.oldPos=w,C},equals:function(s,l,e){return e.comparator?e.comparator(s,l):s===l||e.ignoreCase&&s.toLowerCase()===l.toLowerCase()},removeEmpty:function(s){for(var l=[],e=0;e<s.length;e++)s[e]&&l.push(s[e]);return l},castInput:function(s){return s},tokenize:function(s){return Array.from(s)},join:function(s){return s.join("")},postProcess:function(s){return s}};function xs(i,s,l,e,r){for(var c=[],f;s;)c.push(s),f=s.previousComponent,delete s.previousComponent,s=f;c.reverse();for(var _=0,w=c.length,C=0,V=0;_<w;_++){var E=c[_];if(E.removed)E.value=i.join(e.slice(V,V+E.count)),V+=E.count;else{if(!E.added&&r){var z=l.slice(C,C+E.count);z=z.map(function(g,y){var U=e[V+y];return U.length>g.length?U:g}),E.value=i.join(z)}else E.value=i.join(l.slice(C,C+E.count));C+=E.count,E.added||(V+=E.count)}}return c}var In=new Ie;function Sn(i,s,l){return In.diff(i,s,l)}function Ts(i,s){var l;for(l=0;l<i.length&&l<s.length;l++)if(i[l]!=s[l])return i.slice(0,l);return i.slice(0,l)}function Vs(i,s){var l;if(!i||!s||i[i.length-1]!=s[s.length-1])return"";for(l=0;l<i.length&&l<s.length;l++)if(i[i.length-(l+1)]!=s[s.length-(l+1)])return i.slice(-l);return i.slice(-l)}function Ft(i,s,l){if(i.slice(0,s.length)!=s)throw Error("string ".concat(JSON.stringify(i)," doesn't start with prefix ").concat(JSON.stringify(s),"; this is a bug"));return l+i.slice(s.length)}function Rt(i,s,l){if(!s)return i+l;if(i.slice(-s.length)!=s)throw Error("string ".concat(JSON.stringify(i)," doesn't end with suffix ").concat(JSON.stringify(s),"; this is a bug"));return i.slice(0,-s.length)+l}function ft(i,s){return Ft(i,s,"")}function Ct(i,s){return Rt(i,s,"")}function Ds(i,s){return s.slice(0,Un(i,s))}function Un(i,s){var l=0;i.length>s.length&&(l=i.length-s.length);var e=s.length;i.length<s.length&&(e=i.length);var r=Array(e),c=0;r[0]=0;for(var f=1;f<e;f++){for(s[f]==s[c]?r[f]=r[c]:r[f]=c;c>0&&s[f]!=s[c];)c=r[c];s[f]==s[c]&&c++}c=0;for(var _=l;_<i.length;_++){for(;c>0&&i[_]!=s[c];)c=r[c];i[_]==s[c]&&c++}return c}var Tt="a-zA-Z0-9_\\u{C0}-\\u{FF}\\u{D8}-\\u{F6}\\u{F8}-\\u{2C6}\\u{2C8}-\\u{2D7}\\u{2DE}-\\u{2FF}\\u{1E00}-\\u{1EFF}",Bn=new RegExp("[".concat(Tt,"]+|\\s+|[^").concat(Tt,"]"),"ug"),Et=new Ie;Et.equals=function(i,s,l){return l.ignoreCase&&(i=i.toLowerCase(),s=s.toLowerCase()),i.trim()===s.trim()};Et.tokenize=function(i){var s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},l;if(s.intlSegmenter){if(s.intlSegmenter.resolvedOptions().granularity!="word")throw new Error('The segmenter passed must have a granularity of "word"');l=Array.from(s.intlSegmenter.segment(i),function(c){return c.segment})}else l=i.match(Bn)||[];var e=[],r=null;return l.forEach(function(c){/\s/.test(c)?r==null?e.push(c):e.push(e.pop()+c):/\s/.test(r)?e[e.length-1]==r?e.push(e.pop()+c):e.push(r+c):e.push(c),r=c}),e};Et.join=function(i){return i.map(function(s,l){return l==0?s:s.replace(/^\s+/,"")}).join("")};Et.postProcess=function(i,s){if(!i||s.oneChangePerToken)return i;var l=null,e=null,r=null;return i.forEach(function(c){c.added?e=c:c.removed?r=c:((e||r)&&Es(l,r,e,c),l=c,e=null,r=null)}),(e||r)&&Es(l,r,e,null),i};function Es(i,s,l,e){if(s&&l){var r=s.value.match(/^\s*/)[0],c=s.value.match(/\s*$/)[0],f=l.value.match(/^\s*/)[0],_=l.value.match(/\s*$/)[0];if(i){var w=Ts(r,f);i.value=Rt(i.value,f,w),s.value=ft(s.value,w),l.value=ft(l.value,w)}if(e){var C=Vs(c,_);e.value=Ft(e.value,_,C),s.value=Ct(s.value,C),l.value=Ct(l.value,C)}}else if(l)i&&(l.value=l.value.replace(/^\s*/,"")),e&&(e.value=e.value.replace(/^\s*/,""));else if(i&&e){var V=e.value.match(/^\s*/)[0],E=s.value.match(/^\s*/)[0],z=s.value.match(/\s*$/)[0],g=Ts(V,E);s.value=ft(s.value,g);var y=Vs(ft(V,g),z);s.value=Ct(s.value,y),e.value=Ft(e.value,V,y),i.value=Rt(i.value,V,V.slice(0,V.length-y.length))}else if(e){var U=e.value.match(/^\s*/)[0],D=s.value.match(/\s*$/)[0],$=Ds(D,U);s.value=Ct(s.value,$)}else if(i){var Q=i.value.match(/\s*$/)[0],G=s.value.match(/^\s*/)[0],P=Ds(Q,G);s.value=ft(s.value,P)}}var Gs=new Ie;Gs.tokenize=function(i){var s=new RegExp("(\\r?\\n)|[".concat(Tt,"]+|[^\\S\\n\\r]+|[^").concat(Tt,"]"),"ug");return i.match(s)||[]};function $n(i,s,l){return Gs.diff(i,s,l)}var Wt=new Ie;Wt.tokenize=function(i,s){s.stripTrailingCr&&(i=i.replace(/\r\n/g,`
`));var l=[],e=i.split(/(\n|\r\n)/);e[e.length-1]||e.pop();for(var r=0;r<e.length;r++){var c=e[r];r%2&&!s.newlineIsToken?l[l.length-1]+=c:l.push(c)}return l};Wt.equals=function(i,s,l){return l.ignoreWhitespace?((!l.newlineIsToken||!i.includes(`
`))&&(i=i.trim()),(!l.newlineIsToken||!s.includes(`
`))&&(s=s.trim())):l.ignoreNewlineAtEof&&!l.newlineIsToken&&(i.endsWith(`
`)&&(i=i.slice(0,-1)),s.endsWith(`
`)&&(s=s.slice(0,-1))),Ie.prototype.equals.call(this,i,s,l)};var Ln=new Ie;Ln.tokenize=function(i){return i.split(/(\S.+?[.!?])(?=\s+|$)/)};var Gn=new Ie;Gn.tokenize=function(i){return i.split(/([{}:;,]|\s+)/)};function Ht(i){"@babel/helpers - typeof";return Ht=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(s){return typeof s}:function(s){return s&&typeof Symbol=="function"&&s.constructor===Symbol&&s!==Symbol.prototype?"symbol":typeof s},Ht(i)}var ht=new Ie;ht.useLongestToken=!0;ht.tokenize=Wt.tokenize;ht.castInput=function(i,s){var l=s.undefinedReplacement,e=s.stringifyReplacer,r=e===void 0?function(c,f){return typeof f>"u"?l:f}:e;return typeof i=="string"?i:JSON.stringify(Jt(i,null,null,r),r,"  ")};ht.equals=function(i,s,l){return Ie.prototype.equals.call(ht,i.replace(/,([\r\n])/g,"$1"),s.replace(/,([\r\n])/g,"$1"),l)};function Jt(i,s,l,e,r){s=s||[],l=l||[],e&&(i=e(r,i));var c;for(c=0;c<s.length;c+=1)if(s[c]===i)return l[c];var f;if(Object.prototype.toString.call(i)==="[object Array]"){for(s.push(i),f=new Array(i.length),l.push(f),c=0;c<i.length;c+=1)f[c]=Jt(i[c],s,l,e,r);return s.pop(),l.pop(),f}if(i&&i.toJSON&&(i=i.toJSON()),Ht(i)==="object"&&i!==null){s.push(i),f={},l.push(f);var _=[],w;for(w in i)Object.prototype.hasOwnProperty.call(i,w)&&_.push(w);for(_.sort(),c=0;c<_.length;c+=1)w=_[c],f[w]=Jt(i[w],s,l,e,w);s.pop(),l.pop()}else f=i;return f}var jt=new Ie;jt.tokenize=function(i){return i.slice()};jt.join=jt.removeEmpty=function(i){return i};function An(i,s){if(i.length===0)return s.length;if(s.length===0)return i.length;const l=[];let e;for(e=0;e<=s.length;e++)l[e]=[e];let r;for(r=0;r<=i.length;r++)l[0][r]=r;for(e=1;e<=s.length;e++)for(r=1;r<=i.length;r++)s.charAt(e-1)===i.charAt(r-1)?l[e][r]=l[e-1][r-1]:l[e][r]=Math.min(l[e-1][r-1]+1,Math.min(l[e][r-1]+1,l[e-1][r]+1));return l[s.length][i.length]}function qt(i){return(s,l)=>{const e=i(s).trim(),r=i(l).trim();return An(e,r)/(e.length+r.length)}}function Xt(i){function s(e,r,c=new Map){let f=1/0,_;for(let w=0;w<e.length;++w)for(let C=0;C<r.length;++C){const V=JSON.stringify([e[w],r[C]]);let E;c.has(V)&&(E=c.get(V))||(E=i(e[w],r[C]),c.set(V,E)),E<f&&(f=E,_={indexA:w,indexB:C,score:f})}return _}function l(e,r,c=0,f=new Map){const _=s(e,r,f);if(!_||e.length+r.length<3)return[[e,r]];const w=e.slice(0,_.indexA),C=r.slice(0,_.indexB),V=[e[_.indexA]],E=[r[_.indexB]],z=_.indexA+1,g=_.indexB+1,y=e.slice(z),U=r.slice(g),D=l(w,C,c+1,f),$=l(V,E,c+1,f),Q=l(y,U,c+1,f);let G=$;return(_.indexA>0||_.indexB>0)&&(G=D.concat(G)),(e.length>z||r.length>g)&&(G=G.concat(Q)),G}return l}const ge={INSERTS:"d2h-ins",DELETES:"d2h-del",CONTEXT:"d2h-cntx",INFO:"d2h-info",INSERT_CHANGES:"d2h-ins d2h-change",DELETE_CHANGES:"d2h-del d2h-change"},Nt={matching:_n.NONE,matchWordsThreshold:.25,maxLineLengthHighlight:1e4,diffStyle:Cn.WORD,colorScheme:st.LIGHT},$e="/",As=qt(i=>i.value),Pn=Xt(As);function zt(i){return i.indexOf("dev/null")!==-1}function On(i){return i.replace(/(<ins[^>]*>((.|\n)*?)<\/ins>)/g,"")}function zn(i){return i.replace(/(<del[^>]*>((.|\n)*?)<\/del>)/g,"")}function Vt(i){switch(i){case oe.CONTEXT:return ge.CONTEXT;case oe.INSERT:return ge.INSERTS;case oe.DELETE:return ge.DELETES}}function Zt(i){switch(i){case st.DARK:return"d2h-dark-color-scheme";case st.AUTO:return"d2h-auto-color-scheme";case st.LIGHT:default:return"d2h-light-color-scheme"}}function Mn(i){return i?2:1}function lt(i){return i.slice(0).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;").replace(/\//g,"&#x2F;")}function Ge(i,s,l=!0){const e=Mn(s);return{prefix:i.substring(0,e),content:l?lt(i.substring(e)):i.substring(e)}}function It(i){const s=ws(i.oldName),l=ws(i.newName);if(s!==l&&!zt(s)&&!zt(l)){const e=[],r=[],c=s.split($e),f=l.split($e),_=c.length,w=f.length;let C=0,V=_-1,E=w-1;for(;C<V&&C<E&&c[C]===f[C];)e.push(f[C]),C+=1;for(;V>C&&E>C&&c[V]===f[E];)r.unshift(f[E]),V-=1,E-=1;const z=e.join($e),g=r.join($e),y=c.slice(C,V+1).join($e),U=f.slice(C,E+1).join($e);return z.length&&g.length?z+$e+"{"+y+" → "+U+"}"+$e+g:z.length?z+$e+"{"+y+" → "+U+"}":g.length?"{"+y+" → "+U+"}"+$e+g:s+" → "+l}else return zt(l)?s:l}function Qt(i){return`d2h-${Dn(It(i)).toString().slice(-6)}`}function Kt(i){let s="file-changed";return i.isRename||i.isCopy?s="file-renamed":i.isNew?s="file-added":i.isDeleted?s="file-deleted":i.newName!==i.oldName&&(s="file-renamed"),s}function Ps(i,s,l,e={}){const{matching:r,maxLineLengthHighlight:c,matchWordsThreshold:f,diffStyle:_}=Object.assign(Object.assign({},Nt),e),w=Ge(i,l,!1),C=Ge(s,l,!1);if(w.content.length>c||C.content.length>c)return{oldLine:{prefix:w.prefix,content:lt(w.content)},newLine:{prefix:C.prefix,content:lt(C.content)}};const V=_==="char"?Sn(w.content,C.content):$n(w.content,C.content),E=[];if(_==="word"&&r==="words"){const g=V.filter(D=>D.removed),y=V.filter(D=>D.added);Pn(y,g).forEach(D=>{D[0].length===1&&D[1].length===1&&As(D[0][0],D[1][0])<f&&(E.push(D[0][0]),E.push(D[1][0]))})}const z=V.reduce((g,y)=>{const U=y.added?"ins":y.removed?"del":null,D=E.indexOf(y)>-1?' class="d2h-change"':"",$=lt(y.value);return U!==null?`${g}<${U}${D}>${$}</${U}>`:`${g}${$}`},"");return{oldLine:{prefix:w.prefix,content:On(z)},newLine:{prefix:C.prefix,content:zn(z)}}}const Ns="file-summary",Fn="icon",Rn={colorScheme:Nt.colorScheme};class Hn{constructor(s,l={}){this.hoganUtils=s,this.config=Object.assign(Object.assign({},Rn),l)}render(s){const l=s.map(e=>this.hoganUtils.render(Ns,"line",{fileHtmlId:Qt(e),oldName:e.oldName,newName:e.newName,fileName:It(e),deletedLines:"-"+e.deletedLines,addedLines:"+"+e.addedLines},{fileIcon:this.hoganUtils.template(Fn,Kt(e))})).join(`
`);return this.hoganUtils.render(Ns,"wrapper",{colorScheme:Zt(this.config.colorScheme),filesNumber:s.length,files:l})}}const Os=Object.assign(Object.assign({},Nt),{renderNothingWhenEmpty:!1,matchingMaxComparisons:2500,maxLineSizeInBlockForComparison:200}),pt="generic",Is="line-by-line",Jn="icon",jn="tag";class Wn{constructor(s,l={}){this.hoganUtils=s,this.config=Object.assign(Object.assign({},Os),l)}render(s){const l=s.map(e=>{let r;return e.blocks.length?r=this.generateFileHtml(e):r=this.generateEmptyDiff(),this.makeFileDiffHtml(e,r)}).join(`
`);return this.hoganUtils.render(pt,"wrapper",{colorScheme:Zt(this.config.colorScheme),content:l})}makeFileDiffHtml(s,l){if(this.config.renderNothingWhenEmpty&&Array.isArray(s.blocks)&&s.blocks.length===0)return"";const e=this.hoganUtils.template(Is,"file-diff"),r=this.hoganUtils.template(pt,"file-path"),c=this.hoganUtils.template(Jn,"file"),f=this.hoganUtils.template(jn,Kt(s));return e.render({file:s,fileHtmlId:Qt(s),diffs:l,filePath:r.render({fileDiffName:It(s)},{fileIcon:c,fileTag:f})})}generateEmptyDiff(){return this.hoganUtils.render(pt,"empty-diff",{contentClass:"d2h-code-line",CSSLineClass:ge})}generateFileHtml(s){const l=Xt(qt(e=>Ge(e.content,s.isCombined).content));return s.blocks.map(e=>{let r=this.hoganUtils.render(pt,"block-header",{CSSLineClass:ge,blockHeader:s.isTooBig?e.header:lt(e.header),lineClass:"d2h-code-linenumber",contentClass:"d2h-code-line"});return this.applyLineGroupping(e).forEach(([c,f,_])=>{if(f.length&&_.length&&!c.length)this.applyRematchMatching(f,_,l).map(([w,C])=>{const{left:V,right:E}=this.processChangedLines(s,s.isCombined,w,C);r+=V,r+=E});else if(c.length)c.forEach(w=>{const{prefix:C,content:V}=Ge(w.content,s.isCombined);r+=this.generateSingleLineHtml(s,{type:ge.CONTEXT,prefix:C,content:V,oldNumber:w.oldNumber,newNumber:w.newNumber})});else if(f.length||_.length){const{left:w,right:C}=this.processChangedLines(s,s.isCombined,f,_);r+=w,r+=C}else console.error("Unknown state reached while processing groups of lines",c,f,_)}),r}).join(`
`)}applyLineGroupping(s){const l=[];let e=[],r=[];for(let c=0;c<s.lines.length;c++){const f=s.lines[c];(f.type!==oe.INSERT&&r.length||f.type===oe.CONTEXT&&e.length>0)&&(l.push([[],e,r]),e=[],r=[]),f.type===oe.CONTEXT?l.push([[f],[],[]]):f.type===oe.INSERT&&e.length===0?l.push([[],[],[f]]):f.type===oe.INSERT&&e.length>0?r.push(f):f.type===oe.DELETE&&e.push(f)}return(e.length||r.length)&&(l.push([[],e,r]),e=[],r=[]),l}applyRematchMatching(s,l,e){const r=s.length*l.length,c=Math.max.apply(null,[0].concat(s.concat(l).map(_=>_.content.length)));return r<this.config.matchingMaxComparisons&&c<this.config.maxLineSizeInBlockForComparison&&(this.config.matching==="lines"||this.config.matching==="words")?e(s,l):[[s,l]]}processChangedLines(s,l,e,r){const c={right:"",left:""},f=Math.max(e.length,r.length);for(let _=0;_<f;_++){const w=e[_],C=r[_],V=w!==void 0&&C!==void 0?Ps(w.content,C.content,l,this.config):void 0,E=w!==void 0&&w.oldNumber!==void 0?Object.assign(Object.assign({},V!==void 0?{prefix:V.oldLine.prefix,content:V.oldLine.content,type:ge.DELETE_CHANGES}:Object.assign(Object.assign({},Ge(w.content,l)),{type:Vt(w.type)})),{oldNumber:w.oldNumber,newNumber:w.newNumber}):void 0,z=C!==void 0&&C.newNumber!==void 0?Object.assign(Object.assign({},V!==void 0?{prefix:V.newLine.prefix,content:V.newLine.content,type:ge.INSERT_CHANGES}:Object.assign(Object.assign({},Ge(C.content,l)),{type:Vt(C.type)})),{oldNumber:C.oldNumber,newNumber:C.newNumber}):void 0,{left:g,right:y}=this.generateLineHtml(s,E,z);c.left+=g,c.right+=y}return c}generateLineHtml(s,l,e){return{left:this.generateSingleLineHtml(s,l),right:this.generateSingleLineHtml(s,e)}}generateSingleLineHtml(s,l){if(l===void 0)return"";const e=this.hoganUtils.render(Is,"numbers",{oldNumber:l.oldNumber||"",newNumber:l.newNumber||""});return this.hoganUtils.render(pt,"line",{type:l.type,lineClass:"d2h-code-linenumber",contentClass:"d2h-code-line",prefix:l.prefix===" "?"&nbsp;":l.prefix,content:l.content,lineNumber:e,line:l,file:s})}}const zs=Object.assign(Object.assign({},Nt),{renderNothingWhenEmpty:!1,matchingMaxComparisons:2500,maxLineSizeInBlockForComparison:200}),mt="generic",qn="side-by-side",Xn="icon",Zn="tag";class Qn{constructor(s,l={}){this.hoganUtils=s,this.config=Object.assign(Object.assign({},zs),l)}render(s){const l=s.map(e=>{let r;return e.blocks.length?r=this.generateFileHtml(e):r=this.generateEmptyDiff(),this.makeFileDiffHtml(e,r)}).join(`
`);return this.hoganUtils.render(mt,"wrapper",{colorScheme:Zt(this.config.colorScheme),content:l})}makeFileDiffHtml(s,l){if(this.config.renderNothingWhenEmpty&&Array.isArray(s.blocks)&&s.blocks.length===0)return"";const e=this.hoganUtils.template(qn,"file-diff"),r=this.hoganUtils.template(mt,"file-path"),c=this.hoganUtils.template(Xn,"file"),f=this.hoganUtils.template(Zn,Kt(s));return e.render({file:s,fileHtmlId:Qt(s),diffs:l,filePath:r.render({fileDiffName:It(s)},{fileIcon:c,fileTag:f})})}generateEmptyDiff(){return{right:"",left:this.hoganUtils.render(mt,"empty-diff",{contentClass:"d2h-code-side-line",CSSLineClass:ge})}}generateFileHtml(s){const l=Xt(qt(e=>Ge(e.content,s.isCombined).content));return s.blocks.map(e=>{const r={left:this.makeHeaderHtml(e.header,s),right:this.makeHeaderHtml("")};return this.applyLineGroupping(e).forEach(([c,f,_])=>{if(f.length&&_.length&&!c.length)this.applyRematchMatching(f,_,l).map(([w,C])=>{const{left:V,right:E}=this.processChangedLines(s.isCombined,w,C);r.left+=V,r.right+=E});else if(c.length)c.forEach(w=>{const{prefix:C,content:V}=Ge(w.content,s.isCombined),{left:E,right:z}=this.generateLineHtml({type:ge.CONTEXT,prefix:C,content:V,number:w.oldNumber},{type:ge.CONTEXT,prefix:C,content:V,number:w.newNumber});r.left+=E,r.right+=z});else if(f.length||_.length){const{left:w,right:C}=this.processChangedLines(s.isCombined,f,_);r.left+=w,r.right+=C}else console.error("Unknown state reached while processing groups of lines",c,f,_)}),r}).reduce((e,r)=>({left:e.left+r.left,right:e.right+r.right}),{left:"",right:""})}applyLineGroupping(s){const l=[];let e=[],r=[];for(let c=0;c<s.lines.length;c++){const f=s.lines[c];(f.type!==oe.INSERT&&r.length||f.type===oe.CONTEXT&&e.length>0)&&(l.push([[],e,r]),e=[],r=[]),f.type===oe.CONTEXT?l.push([[f],[],[]]):f.type===oe.INSERT&&e.length===0?l.push([[],[],[f]]):f.type===oe.INSERT&&e.length>0?r.push(f):f.type===oe.DELETE&&e.push(f)}return(e.length||r.length)&&(l.push([[],e,r]),e=[],r=[]),l}applyRematchMatching(s,l,e){const r=s.length*l.length,c=Math.max.apply(null,[0].concat(s.concat(l).map(_=>_.content.length)));return r<this.config.matchingMaxComparisons&&c<this.config.maxLineSizeInBlockForComparison&&(this.config.matching==="lines"||this.config.matching==="words")?e(s,l):[[s,l]]}makeHeaderHtml(s,l){return this.hoganUtils.render(mt,"block-header",{CSSLineClass:ge,blockHeader:l?.isTooBig?s:lt(s),lineClass:"d2h-code-side-linenumber",contentClass:"d2h-code-side-line"})}processChangedLines(s,l,e){const r={right:"",left:""},c=Math.max(l.length,e.length);for(let f=0;f<c;f++){const _=l[f],w=e[f],C=_!==void 0&&w!==void 0?Ps(_.content,w.content,s,this.config):void 0,V=_!==void 0&&_.oldNumber!==void 0?Object.assign(Object.assign({},C!==void 0?{prefix:C.oldLine.prefix,content:C.oldLine.content,type:ge.DELETE_CHANGES}:Object.assign(Object.assign({},Ge(_.content,s)),{type:Vt(_.type)})),{number:_.oldNumber}):void 0,E=w!==void 0&&w.newNumber!==void 0?Object.assign(Object.assign({},C!==void 0?{prefix:C.newLine.prefix,content:C.newLine.content,type:ge.INSERT_CHANGES}:Object.assign(Object.assign({},Ge(w.content,s)),{type:Vt(w.type)})),{number:w.newNumber}):void 0,{left:z,right:g}=this.generateLineHtml(V,E);r.left+=z,r.right+=g}return r}generateLineHtml(s,l){return{left:this.generateSingleHtml(s),right:this.generateSingleHtml(l)}}generateSingleHtml(s){const l="d2h-code-side-linenumber",e="d2h-code-side-line";return this.hoganUtils.render(mt,"line",{type:s?.type||`${ge.CONTEXT} d2h-emptyplaceholder`,lineClass:s!==void 0?l:`${l} d2h-code-side-emptyplaceholder`,contentClass:s!==void 0?e:`${e} d2h-code-side-emptyplaceholder`,prefix:s?.prefix===" "?"&nbsp;":s?.prefix,content:s?.content,lineNumber:s?.number})}}var Ms={};(function(i){(function(s){var l=/\S/,e=/\"/g,r=/\n/g,c=/\r/g,f=/\\/g,_=/\u2028/,w=/\u2029/;s.tags={"#":1,"^":2,"<":3,$:4,"/":5,"!":6,">":7,"=":8,_v:9,"{":10,"&":11,_t:12},s.scan=function(T,I){var A=T.length,le=0,fe=1,ce=2,Z=le,F=null,Se=null,R="",ie=[],Te=!1,N=0,W=0,S="{{",ve="}}";function ze(){R.length>0&&(ie.push({tag:"_t",text:new String(R)}),R="")}function qe(){for(var Ve=!0,H=W;H<ie.length;H++)if(Ve=s.tags[ie[H].tag]<s.tags._v||ie[H].tag=="_t"&&ie[H].text.match(l)===null,!Ve)return!1;return Ve}function Ae(Ve,H){if(ze(),Ve&&qe())for(var _e=W,Me;_e<ie.length;_e++)ie[_e].text&&((Me=ie[_e+1])&&Me.tag==">"&&(Me.indent=ie[_e].text.toString()),ie.splice(_e,1));else H||ie.push({tag:`
`});Te=!1,W=ie.length}function He(Ve,H){var _e="="+ve,Me=Ve.indexOf(_e,H),q=V(Ve.substring(Ve.indexOf("=",H)+1,Me)).split(" ");return S=q[0],ve=q[q.length-1],Me+_e.length-1}for(I&&(I=I.split(" "),S=I[0],ve=I[1]),N=0;N<A;N++)Z==le?E(S,T,N)?(--N,ze(),Z=fe):T.charAt(N)==`
`?Ae(Te):R+=T.charAt(N):Z==fe?(N+=S.length-1,Se=s.tags[T.charAt(N+1)],F=Se?T.charAt(N+1):"_v",F=="="?(N=He(T,N),Z=le):(Se&&N++,Z=ce),Te=N):E(ve,T,N)?(ie.push({tag:F,n:V(R),otag:S,ctag:ve,i:F=="/"?Te-S.length:N+ve.length}),R="",N+=ve.length-1,Z=le,F=="{"&&(ve=="}}"?N++:C(ie[ie.length-1]))):R+=T.charAt(N);return Ae(Te,!0),ie};function C(m){m.n.substr(m.n.length-1)==="}"&&(m.n=m.n.substring(0,m.n.length-1))}function V(m){return m.trim?m.trim():m.replace(/^\s*|\s*$/g,"")}function E(m,T,I){if(T.charAt(I)!=m.charAt(0))return!1;for(var A=1,le=m.length;A<le;A++)if(T.charAt(I+A)!=m.charAt(A))return!1;return!0}var z={_t:!0,"\n":!0,$:!0,"/":!0};function g(m,T,I,A){var le=[],fe=null,ce=null,Z=null;for(ce=I[I.length-1];m.length>0;){if(Z=m.shift(),ce&&ce.tag=="<"&&!(Z.tag in z))throw new Error("Illegal content in < super tag.");if(s.tags[Z.tag]<=s.tags.$||y(Z,A))I.push(Z),Z.nodes=g(m,Z.tag,I,A);else if(Z.tag=="/"){if(I.length===0)throw new Error("Closing tag without opener: /"+Z.n);if(fe=I.pop(),Z.n!=fe.n&&!U(Z.n,fe.n,A))throw new Error("Nesting error: "+fe.n+" vs. "+Z.n);return fe.end=Z.i,le}else Z.tag==`
`&&(Z.last=m.length==0||m[0].tag==`
`);le.push(Z)}if(I.length>0)throw new Error("missing closing tag: "+I.pop().n);return le}function y(m,T){for(var I=0,A=T.length;I<A;I++)if(T[I].o==m.n)return m.tag="#",!0}function U(m,T,I){for(var A=0,le=I.length;A<le;A++)if(I[A].c==m&&I[A].o==T)return!0}function D(m){var T=[];for(var I in m)T.push('"'+G(I)+'": function(c,p,t,i) {'+m[I]+"}");return"{ "+T.join(",")+" }"}function $(m){var T=[];for(var I in m.partials)T.push('"'+G(I)+'":{name:"'+G(m.partials[I].name)+'", '+$(m.partials[I])+"}");return"partials: {"+T.join(",")+"}, subs: "+D(m.subs)}s.stringify=function(m,T,I){return"{code: function (c,p,i) { "+s.wrapMain(m.code)+" },"+$(m)+"}"};var Q=0;s.generate=function(m,T,I){Q=0;var A={code:"",subs:{},partials:{}};return s.walk(m,A),I.asString?this.stringify(A,T,I):this.makeTemplate(A,T,I)},s.wrapMain=function(m){return'var t=this;t.b(i=i||"");'+m+"return t.fl();"},s.template=s.Template,s.makeTemplate=function(m,T,I){var A=this.makePartials(m);return A.code=new Function("c","p","i",this.wrapMain(m.code)),new this.template(A,T,this,I)},s.makePartials=function(m){var T,I={subs:{},partials:m.partials,name:m.name};for(T in I.partials)I.partials[T]=this.makePartials(I.partials[T]);for(T in m.subs)I.subs[T]=new Function("c","p","t","i",m.subs[T]);return I};function G(m){return m.replace(f,"\\\\").replace(e,'\\"').replace(r,"\\n").replace(c,"\\r").replace(_,"\\u2028").replace(w,"\\u2029")}function P(m){return~m.indexOf(".")?"d":"f"}function M(m,T){var I="<"+(T.prefix||""),A=I+m.n+Q++;return T.partials[A]={name:m.n,partials:{}},T.code+='t.b(t.rp("'+G(A)+'",c,p,"'+(m.indent||"")+'"));',A}s.codegen={"#":function(m,T){T.code+="if(t.s(t."+P(m.n)+'("'+G(m.n)+'",c,p,1),c,p,0,'+m.i+","+m.end+',"'+m.otag+" "+m.ctag+'")){t.rs(c,p,function(c,p,t){',s.walk(m.nodes,T),T.code+="});c.pop();}"},"^":function(m,T){T.code+="if(!t.s(t."+P(m.n)+'("'+G(m.n)+'",c,p,1),c,p,1,0,0,"")){',s.walk(m.nodes,T),T.code+="};"},">":M,"<":function(m,T){var I={partials:{},code:"",subs:{},inPartial:!0};s.walk(m.nodes,I);var A=T.partials[M(m,T)];A.subs=I.subs,A.partials=I.partials},$:function(m,T){var I={subs:{},code:"",partials:T.partials,prefix:m.n};s.walk(m.nodes,I),T.subs[m.n]=I.code,T.inPartial||(T.code+='t.sub("'+G(m.n)+'",c,p,i);')},"\n":function(m,T){T.code+=ke('"\\n"'+(m.last?"":" + i"))},_v:function(m,T){T.code+="t.b(t.v(t."+P(m.n)+'("'+G(m.n)+'",c,p,0)));'},_t:function(m,T){T.code+=ke('"'+G(m.text)+'"')},"{":X,"&":X};function X(m,T){T.code+="t.b(t.t(t."+P(m.n)+'("'+G(m.n)+'",c,p,0)));'}function ke(m){return"t.b("+m+");"}s.walk=function(m,T){for(var I,A=0,le=m.length;A<le;A++)I=s.codegen[m[A].tag],I&&I(m[A],T);return T},s.parse=function(m,T,I){return I=I||{},g(m,"",[],I.sectionTags||[])},s.cache={},s.cacheKey=function(m,T){return[m,!!T.asString,!!T.disableLambda,T.delimiters,!!T.modelGet].join("||")},s.compile=function(m,T){T=T||{};var I=s.cacheKey(m,T),A=this.cache[I];if(A){var le=A.partials;for(var fe in le)delete le[fe].instance;return A}return A=this.generate(this.parse(this.scan(m,T.delimiters),m,T),m,T),this.cache[I]=A}})(i)})(Ms);var Fs={};(function(i){(function(s){s.Template=function(g,y,U,D){g=g||{},this.r=g.code||this.r,this.c=U,this.options=D||{},this.text=y||"",this.partials=g.partials||{},this.subs=g.subs||{},this.buf=""},s.Template.prototype={r:function(g,y,U){return""},v:E,t:V,render:function(y,U,D){return this.ri([y],U||{},D)},ri:function(g,y,U){return this.r(g,y,U)},ep:function(g,y){var U=this.partials[g],D=y[U.name];if(U.instance&&U.base==D)return U.instance;if(typeof D=="string"){if(!this.c)throw new Error("No compiler available.");D=this.c.compile(D,this.options)}if(!D)return null;if(this.partials[g].base=D,U.subs){y.stackText||(y.stackText={});for(key in U.subs)y.stackText[key]||(y.stackText[key]=this.activeSub!==void 0&&y.stackText[this.activeSub]?y.stackText[this.activeSub]:this.text);D=e(D,U.subs,U.partials,this.stackSubs,this.stackPartials,y.stackText)}return this.partials[g].instance=D,D},rp:function(g,y,U,D){var $=this.ep(g,U);return $?$.ri(y,U,D):""},rs:function(g,y,U){var D=g[g.length-1];if(!z(D)){U(g,y,this);return}for(var $=0;$<D.length;$++)g.push(D[$]),U(g,y,this),g.pop()},s:function(g,y,U,D,$,Q,G){var P;return z(g)&&g.length===0?!1:(typeof g=="function"&&(g=this.ms(g,y,U,D,$,Q,G)),P=!!g,!D&&P&&y&&y.push(typeof g=="object"?g:y[y.length-1]),P)},d:function(g,y,U,D){var $,Q=g.split("."),G=this.f(Q[0],y,U,D),P=this.options.modelGet,M=null;if(g==="."&&z(y[y.length-2]))G=y[y.length-1];else for(var X=1;X<Q.length;X++)$=l(Q[X],G,P),$!==void 0?(M=G,G=$):G="";return D&&!G?!1:(!D&&typeof G=="function"&&(y.push(M),G=this.mv(G,y,U),y.pop()),G)},f:function(g,y,U,D){for(var $=!1,Q=null,G=!1,P=this.options.modelGet,M=y.length-1;M>=0;M--)if(Q=y[M],$=l(g,Q,P),$!==void 0){G=!0;break}return G?(!D&&typeof $=="function"&&($=this.mv($,y,U)),$):D?!1:""},ls:function(g,y,U,D,$){var Q=this.options.delimiters;return this.options.delimiters=$,this.b(this.ct(V(g.call(y,D)),y,U)),this.options.delimiters=Q,!1},ct:function(g,y,U){if(this.options.disableLambda)throw new Error("Lambda features disabled.");return this.c.compile(g,this.options).render(y,U)},b:function(g){this.buf+=g},fl:function(){var g=this.buf;return this.buf="",g},ms:function(g,y,U,D,$,Q,G){var P,M=y[y.length-1],X=g.call(M);return typeof X=="function"?D?!0:(P=this.activeSub&&this.subsText&&this.subsText[this.activeSub]?this.subsText[this.activeSub]:this.text,this.ls(X,M,U,P.substring($,Q),G)):X},mv:function(g,y,U){var D=y[y.length-1],$=g.call(D);return typeof $=="function"?this.ct(V($.call(D)),D,U):$},sub:function(g,y,U,D){var $=this.subs[g];$&&(this.activeSub=g,$(y,U,this,D),this.activeSub=!1)}};function l(g,y,U){var D;return y&&typeof y=="object"&&(y[g]!==void 0?D=y[g]:U&&y.get&&typeof y.get=="function"&&(D=y.get(g))),D}function e(g,y,U,D,$,Q){function G(){}G.prototype=g;function P(){}P.prototype=g.subs;var M,X=new G;X.subs=new P,X.subsText={},X.buf="",D=D||{},X.stackSubs=D,X.subsText=Q;for(M in y)D[M]||(D[M]=y[M]);for(M in D)X.subs[M]=D[M];$=$||{},X.stackPartials=$;for(M in U)$[M]||($[M]=U[M]);for(M in $)X.partials[M]=$[M];return X}var r=/&/g,c=/</g,f=/>/g,_=/\'/g,w=/\"/g,C=/[&<>\"\']/;function V(g){return String(g??"")}function E(g){return g=V(g),C.test(g)?g.replace(r,"&amp;").replace(c,"&lt;").replace(f,"&gt;").replace(_,"&#39;").replace(w,"&quot;"):g}var z=Array.isArray||function(g){return Object.prototype.toString.call(g)==="[object Array]"}})(i)})(Fs);var Dt=Ms;Dt.Template=Fs.Template;Dt.template=Dt.Template;var ae=Dt;const re={};re["file-summary-line"]=new ae.Template({code:function(i,s,l){var e=this;return e.b(l=l||""),e.b('<li class="d2h-file-list-line">'),e.b(`
`+l),e.b('    <span class="d2h-file-name-wrapper">'),e.b(`
`+l),e.b(e.rp("<fileIcon0",i,s,"      ")),e.b('      <a href="#'),e.b(e.v(e.f("fileHtmlId",i,s,0))),e.b('" class="d2h-file-name">'),e.b(e.v(e.f("fileName",i,s,0))),e.b("</a>"),e.b(`
`+l),e.b('      <span class="d2h-file-stats">'),e.b(`
`+l),e.b('          <span class="d2h-lines-added">'),e.b(e.v(e.f("addedLines",i,s,0))),e.b("</span>"),e.b(`
`+l),e.b('          <span class="d2h-lines-deleted">'),e.b(e.v(e.f("deletedLines",i,s,0))),e.b("</span>"),e.b(`
`+l),e.b("      </span>"),e.b(`
`+l),e.b("    </span>"),e.b(`
`+l),e.b("</li>"),e.fl()},partials:{"<fileIcon0":{name:"fileIcon",partials:{},subs:{}}},subs:{}});re["file-summary-wrapper"]=new ae.Template({code:function(i,s,l){var e=this;return e.b(l=l||""),e.b('<div class="d2h-file-list-wrapper '),e.b(e.v(e.f("colorScheme",i,s,0))),e.b('">'),e.b(`
`+l),e.b('    <div class="d2h-file-list-header">'),e.b(`
`+l),e.b('        <span class="d2h-file-list-title">Files changed ('),e.b(e.v(e.f("filesNumber",i,s,0))),e.b(")</span>"),e.b(`
`+l),e.b('        <a class="d2h-file-switch d2h-hide">hide</a>'),e.b(`
`+l),e.b('        <a class="d2h-file-switch d2h-show">show</a>'),e.b(`
`+l),e.b("    </div>"),e.b(`
`+l),e.b('    <ol class="d2h-file-list">'),e.b(`
`+l),e.b("    "),e.b(e.t(e.f("files",i,s,0))),e.b(`
`+l),e.b("    </ol>"),e.b(`
`+l),e.b("</div>"),e.fl()},partials:{},subs:{}});re["generic-block-header"]=new ae.Template({code:function(i,s,l){var e=this;return e.b(l=l||""),e.b("<tr>"),e.b(`
`+l),e.b('    <td class="'),e.b(e.v(e.f("lineClass",i,s,0))),e.b(" "),e.b(e.v(e.d("CSSLineClass.INFO",i,s,0))),e.b('"></td>'),e.b(`
`+l),e.b('    <td class="'),e.b(e.v(e.d("CSSLineClass.INFO",i,s,0))),e.b('">'),e.b(`
`+l),e.b('        <div class="'),e.b(e.v(e.f("contentClass",i,s,0))),e.b('">'),e.s(e.f("blockHeader",i,s,1),i,s,0,156,173,"{{ }}")&&(e.rs(i,s,function(r,c,f){f.b(f.t(f.f("blockHeader",r,c,0)))}),i.pop()),e.s(e.f("blockHeader",i,s,1),i,s,1,0,0,"")||e.b("&nbsp;"),e.b("</div>"),e.b(`
`+l),e.b("    </td>"),e.b(`
`+l),e.b("</tr>"),e.fl()},partials:{},subs:{}});re["generic-empty-diff"]=new ae.Template({code:function(i,s,l){var e=this;return e.b(l=l||""),e.b("<tr>"),e.b(`
`+l),e.b('    <td class="'),e.b(e.v(e.d("CSSLineClass.INFO",i,s,0))),e.b('">'),e.b(`
`+l),e.b('        <div class="'),e.b(e.v(e.f("contentClass",i,s,0))),e.b('">'),e.b(`
`+l),e.b("            File without changes"),e.b(`
`+l),e.b("        </div>"),e.b(`
`+l),e.b("    </td>"),e.b(`
`+l),e.b("</tr>"),e.fl()},partials:{},subs:{}});re["generic-file-path"]=new ae.Template({code:function(i,s,l){var e=this;return e.b(l=l||""),e.b('<span class="d2h-file-name-wrapper">'),e.b(`
`+l),e.b(e.rp("<fileIcon0",i,s,"    ")),e.b('    <span class="d2h-file-name">'),e.b(e.v(e.f("fileDiffName",i,s,0))),e.b("</span>"),e.b(`
`+l),e.b(e.rp("<fileTag1",i,s,"    ")),e.b("</span>"),e.b(`
`+l),e.b('<label class="d2h-file-collapse">'),e.b(`
`+l),e.b('    <input class="d2h-file-collapse-input" type="checkbox" name="viewed" value="viewed">'),e.b(`
`+l),e.b("    Viewed"),e.b(`
`+l),e.b("</label>"),e.fl()},partials:{"<fileIcon0":{name:"fileIcon",partials:{},subs:{}},"<fileTag1":{name:"fileTag",partials:{},subs:{}}},subs:{}});re["generic-line"]=new ae.Template({code:function(i,s,l){var e=this;return e.b(l=l||""),e.b("<tr>"),e.b(`
`+l),e.b('    <td class="'),e.b(e.v(e.f("lineClass",i,s,0))),e.b(" "),e.b(e.v(e.f("type",i,s,0))),e.b('">'),e.b(`
`+l),e.b("      "),e.b(e.t(e.f("lineNumber",i,s,0))),e.b(`
`+l),e.b("    </td>"),e.b(`
`+l),e.b('    <td class="'),e.b(e.v(e.f("type",i,s,0))),e.b('">'),e.b(`
`+l),e.b('        <div class="'),e.b(e.v(e.f("contentClass",i,s,0))),e.b('">'),e.b(`
`+l),e.s(e.f("prefix",i,s,1),i,s,0,162,238,"{{ }}")&&(e.rs(i,s,function(r,c,f){f.b('            <span class="d2h-code-line-prefix">'),f.b(f.t(f.f("prefix",r,c,0))),f.b("</span>"),f.b(`
`+l)}),i.pop()),e.s(e.f("prefix",i,s,1),i,s,1,0,0,"")||(e.b('            <span class="d2h-code-line-prefix">&nbsp;</span>'),e.b(`
`+l)),e.s(e.f("content",i,s,1),i,s,0,371,445,"{{ }}")&&(e.rs(i,s,function(r,c,f){f.b('            <span class="d2h-code-line-ctn">'),f.b(f.t(f.f("content",r,c,0))),f.b("</span>"),f.b(`
`+l)}),i.pop()),e.s(e.f("content",i,s,1),i,s,1,0,0,"")||(e.b('            <span class="d2h-code-line-ctn"><br></span>'),e.b(`
`+l)),e.b("        </div>"),e.b(`
`+l),e.b("    </td>"),e.b(`
`+l),e.b("</tr>"),e.fl()},partials:{},subs:{}});re["generic-wrapper"]=new ae.Template({code:function(i,s,l){var e=this;return e.b(l=l||""),e.b('<div class="d2h-wrapper '),e.b(e.v(e.f("colorScheme",i,s,0))),e.b('">'),e.b(`
`+l),e.b("    "),e.b(e.t(e.f("content",i,s,0))),e.b(`
`+l),e.b("</div>"),e.fl()},partials:{},subs:{}});re["icon-file-added"]=new ae.Template({code:function(i,s,l){var e=this;return e.b(l=l||""),e.b('<svg aria-hidden="true" class="d2h-icon d2h-added" height="16" title="added" version="1.1" viewBox="0 0 14 16"'),e.b(`
`+l),e.b('     width="14">'),e.b(`
`+l),e.b('    <path d="M13 1H1C0.45 1 0 1.45 0 2v12c0 0.55 0.45 1 1 1h12c0.55 0 1-0.45 1-1V2c0-0.55-0.45-1-1-1z m0 13H1V2h12v12zM6 9H3V7h3V4h2v3h3v2H8v3H6V9z"></path>'),e.b(`
`+l),e.b("</svg>"),e.fl()},partials:{},subs:{}});re["icon-file-changed"]=new ae.Template({code:function(i,s,l){var e=this;return e.b(l=l||""),e.b('<svg aria-hidden="true" class="d2h-icon d2h-changed" height="16" title="modified" version="1.1"'),e.b(`
`+l),e.b('     viewBox="0 0 14 16" width="14">'),e.b(`
`+l),e.b('    <path d="M13 1H1C0.45 1 0 1.45 0 2v12c0 0.55 0.45 1 1 1h12c0.55 0 1-0.45 1-1V2c0-0.55-0.45-1-1-1z m0 13H1V2h12v12zM4 8c0-1.66 1.34-3 3-3s3 1.34 3 3-1.34 3-3 3-3-1.34-3-3z"></path>'),e.b(`
`+l),e.b("</svg>"),e.fl()},partials:{},subs:{}});re["icon-file-deleted"]=new ae.Template({code:function(i,s,l){var e=this;return e.b(l=l||""),e.b('<svg aria-hidden="true" class="d2h-icon d2h-deleted" height="16" title="removed" version="1.1"'),e.b(`
`+l),e.b('     viewBox="0 0 14 16" width="14">'),e.b(`
`+l),e.b('    <path d="M13 1H1C0.45 1 0 1.45 0 2v12c0 0.55 0.45 1 1 1h12c0.55 0 1-0.45 1-1V2c0-0.55-0.45-1-1-1z m0 13H1V2h12v12zM11 9H3V7h8v2z"></path>'),e.b(`
`+l),e.b("</svg>"),e.fl()},partials:{},subs:{}});re["icon-file-renamed"]=new ae.Template({code:function(i,s,l){var e=this;return e.b(l=l||""),e.b('<svg aria-hidden="true" class="d2h-icon d2h-moved" height="16" title="renamed" version="1.1"'),e.b(`
`+l),e.b('     viewBox="0 0 14 16" width="14">'),e.b(`
`+l),e.b('    <path d="M6 9H3V7h3V4l5 4-5 4V9z m8-7v12c0 0.55-0.45 1-1 1H1c-0.55 0-1-0.45-1-1V2c0-0.55 0.45-1 1-1h12c0.55 0 1 0.45 1 1z m-1 0H1v12h12V2z"></path>'),e.b(`
`+l),e.b("</svg>"),e.fl()},partials:{},subs:{}});re["icon-file"]=new ae.Template({code:function(i,s,l){var e=this;return e.b(l=l||""),e.b('<svg aria-hidden="true" class="d2h-icon" height="16" version="1.1" viewBox="0 0 12 16" width="12">'),e.b(`
`+l),e.b('    <path d="M6 5H2v-1h4v1zM2 8h7v-1H2v1z m0 2h7v-1H2v1z m0 2h7v-1H2v1z m10-7.5v9.5c0 0.55-0.45 1-1 1H1c-0.55 0-1-0.45-1-1V2c0-0.55 0.45-1 1-1h7.5l3.5 3.5z m-1 0.5L8 2H1v12h10V5z"></path>'),e.b(`
`+l),e.b("</svg>"),e.fl()},partials:{},subs:{}});re["line-by-line-file-diff"]=new ae.Template({code:function(i,s,l){var e=this;return e.b(l=l||""),e.b('<div id="'),e.b(e.v(e.f("fileHtmlId",i,s,0))),e.b('" class="d2h-file-wrapper" data-lang="'),e.b(e.v(e.d("file.language",i,s,0))),e.b('">'),e.b(`
`+l),e.b('    <div class="d2h-file-header">'),e.b(`
`+l),e.b("    "),e.b(e.t(e.f("filePath",i,s,0))),e.b(`
`+l),e.b("    </div>"),e.b(`
`+l),e.b('    <div class="d2h-file-diff">'),e.b(`
`+l),e.b('        <div class="d2h-code-wrapper">'),e.b(`
`+l),e.b('            <table class="d2h-diff-table">'),e.b(`
`+l),e.b('                <tbody class="d2h-diff-tbody">'),e.b(`
`+l),e.b("                "),e.b(e.t(e.f("diffs",i,s,0))),e.b(`
`+l),e.b("                </tbody>"),e.b(`
`+l),e.b("            </table>"),e.b(`
`+l),e.b("        </div>"),e.b(`
`+l),e.b("    </div>"),e.b(`
`+l),e.b("</div>"),e.fl()},partials:{},subs:{}});re["line-by-line-numbers"]=new ae.Template({code:function(i,s,l){var e=this;return e.b(l=l||""),e.b('<div class="line-num1">'),e.b(e.v(e.f("oldNumber",i,s,0))),e.b("</div>"),e.b(`
`+l),e.b('<div class="line-num2">'),e.b(e.v(e.f("newNumber",i,s,0))),e.b("</div>"),e.fl()},partials:{},subs:{}});re["side-by-side-file-diff"]=new ae.Template({code:function(i,s,l){var e=this;return e.b(l=l||""),e.b('<div id="'),e.b(e.v(e.f("fileHtmlId",i,s,0))),e.b('" class="d2h-file-wrapper" data-lang="'),e.b(e.v(e.d("file.language",i,s,0))),e.b('">'),e.b(`
`+l),e.b('    <div class="d2h-file-header">'),e.b(`
`+l),e.b("      "),e.b(e.t(e.f("filePath",i,s,0))),e.b(`
`+l),e.b("    </div>"),e.b(`
`+l),e.b('    <div class="d2h-files-diff">'),e.b(`
`+l),e.b('        <div class="d2h-file-side-diff">'),e.b(`
`+l),e.b('            <div class="d2h-code-wrapper">'),e.b(`
`+l),e.b('                <table class="d2h-diff-table">'),e.b(`
`+l),e.b('                    <tbody class="d2h-diff-tbody">'),e.b(`
`+l),e.b("                    "),e.b(e.t(e.d("diffs.left",i,s,0))),e.b(`
`+l),e.b("                    </tbody>"),e.b(`
`+l),e.b("                </table>"),e.b(`
`+l),e.b("            </div>"),e.b(`
`+l),e.b("        </div>"),e.b(`
`+l),e.b('        <div class="d2h-file-side-diff">'),e.b(`
`+l),e.b('            <div class="d2h-code-wrapper">'),e.b(`
`+l),e.b('                <table class="d2h-diff-table">'),e.b(`
`+l),e.b('                    <tbody class="d2h-diff-tbody">'),e.b(`
`+l),e.b("                    "),e.b(e.t(e.d("diffs.right",i,s,0))),e.b(`
`+l),e.b("                    </tbody>"),e.b(`
`+l),e.b("                </table>"),e.b(`
`+l),e.b("            </div>"),e.b(`
`+l),e.b("        </div>"),e.b(`
`+l),e.b("    </div>"),e.b(`
`+l),e.b("</div>"),e.fl()},partials:{},subs:{}});re["tag-file-added"]=new ae.Template({code:function(i,s,l){var e=this;return e.b(l=l||""),e.b('<span class="d2h-tag d2h-added d2h-added-tag">ADDED</span>'),e.fl()},partials:{},subs:{}});re["tag-file-changed"]=new ae.Template({code:function(i,s,l){var e=this;return e.b(l=l||""),e.b('<span class="d2h-tag d2h-changed d2h-changed-tag">CHANGED</span>'),e.fl()},partials:{},subs:{}});re["tag-file-deleted"]=new ae.Template({code:function(i,s,l){var e=this;return e.b(l=l||""),e.b('<span class="d2h-tag d2h-deleted d2h-deleted-tag">DELETED</span>'),e.fl()},partials:{},subs:{}});re["tag-file-renamed"]=new ae.Template({code:function(i,s,l){var e=this;return e.b(l=l||""),e.b('<span class="d2h-tag d2h-moved d2h-moved-tag">RENAMED</span>'),e.fl()},partials:{},subs:{}});class Kn{constructor({compiledTemplates:s={},rawTemplates:l={}}){const e=Object.entries(l).reduce((r,[c,f])=>{const _=ae.compile(f,{asString:!1});return Object.assign(Object.assign({},r),{[c]:_})},{});this.preCompiledTemplates=Object.assign(Object.assign(Object.assign({},re),s),e)}static compile(s){return ae.compile(s,{asString:!1})}render(s,l,e,r,c){const f=this.templateKey(s,l);try{return this.preCompiledTemplates[f].render(e,r,c)}catch{throw new Error(`Could not find template to render '${f}'`)}}template(s,l){return this.preCompiledTemplates[this.templateKey(s,l)]}templateKey(s,l){return`${s}-${l}`}}const Rs=Object.assign(Object.assign(Object.assign({},Os),zs),{outputFormat:kn.LINE_BY_LINE,drawFileList:!0});function Yn(i,s={}){return Ls(i,Object.assign(Object.assign({},Rs),s))}function ea(i,s={}){const l=Object.assign(Object.assign({},Rs),s),e=typeof i=="string"?Ls(i,l):i,r=new Kn(l),{colorScheme:c}=l,f={colorScheme:c},_=l.drawFileList?new Hn(r,f).render(e):"",w=l.outputFormat==="side-by-side"?new Qn(r,l).render(e):new Wn(r,l).render(e);return _+w}const ta={class:"git-diff-viewer"},sa={key:0,class:"file-list"},la=["onClick"],na={class:"filename"},aa={class:"diff-content"},ia={key:0,class:"loading-container"},oa={key:1,class:"error-message"},ra={key:2,class:"no-diff-message"},ua={__name:"GitDiffViewer",props:{diffText:String,changedFiles:{type:Array,default:()=>[]},loading:Boolean,error:String},emits:["refresh"],setup(i,{emit:s}){const l=i,e=O(null),r=O(""),c=O(""),f=C=>({M:"warning",A:"success",D:"danger",R:"info",C:"info"})[C]||"info",_=gt(()=>{if(!l.diffText||!r.value)return l.diffText;const C=l.diffText.split(`
`),V=[];let E=!1,z=/^diff --git a\/(.*) b\/(.*)$/;for(const g of C){const y=g.match(z);y?(E=y[1]===r.value,E&&V.push(g)):(E||!r.value)&&V.push(g)}return V.join(`
`)}),w=()=>{if(!l.diffText){c.value="";return}try{const C=Yn(_.value);c.value=ea(C,{drawFileList:!1,matching:"lines",outputFormat:"side-by-side"}),e.value&&(e.value.innerHTML=c.value)}catch(C){console.error("渲染差异失败:",C),c.value=""}};return We(()=>l.diffText,w,{immediate:!0}),We(()=>r.value,w),vt(()=>{l.changedFiles.length>0&&(r.value=l.changedFiles[0].filename),w()}),(C,V)=>(j(),ee("div",ta,[i.changedFiles.length>0?(j(),ee("div",sa,[d("h3",null,"变更文件 ("+ne(i.changedFiles.length)+")",1),n(B($l),{height:"200px"},{default:o(()=>[(j(!0),ee(tt,null,Mt(i.changedFiles,E=>(j(),ee("div",{key:E.filename,class:Us(["file-item",{active:r.value===E.filename}]),onClick:z=>r.value=E.filename},[n(B(Bs),{type:f(E.status),size:"small"},{default:o(()=>[x(ne(E.status_text),1)]),_:2},1032,["type"]),d("span",na,ne(E.filename),1)],10,la))),128))]),_:1})])):xe("",!0),d("div",aa,[i.loading?(j(),ee("div",ia,[n(B(xt),{class:"is-loading"},{default:o(()=>[n(B(Ll))]),_:1}),V[0]||(V[0]=d("span",null,"加载差异内容...",-1))])):i.error?(j(),ee("div",oa,[n(B(xt),null,{default:o(()=>[n(B($s))]),_:1}),d("span",null,ne(i.error),1)])):c.value?(j(),ee("div",{key:3,ref_key:"diffContainer",ref:e,class:"diff-container"},null,512)):(j(),ee("div",ra,[n(B(xt),null,{default:o(()=>[n(B(Le))]),_:1}),V[1]||(V[1]=d("span",null,"没有变更内容",-1))]))])]))}},da=Ss(ua,[["__scopeId","data-v-407491ad"]]),ca={class:"app-settings"},fa={class:"settings-container","element-loading-text":"加载中...","element-loading-background":"rgba(0, 0, 0, 0.7)"},pa={class:"settings-panel chrome-settings"},ma={class:"panel-content"},ga={class:"settings-card"},va={class:"path-input-group"},ha={class:"path-input-group"},ba={class:"section-header"},ya={class:"header-actions"},wa={class:"settings-card table-container"},ka={class:"settings-panel ai-roles-container"},_a={class:"section-header"},Ca={class:"header-actions"},xa={class:"panel-content"},Ta={class:"settings-card table-container"},Va={class:"settings-section"},Da={class:"section-header"},Ea={class:"header-actions"},Na={class:"panel-content"},Ia={key:0,class:"empty-providers"},Sa={key:1,class:"provider-list"},Ua={class:"provider-header"},Ba={class:"provider-name"},$a={class:"provider-content"},La={class:"subsection"},Ga={class:"subsection-header"},Aa={class:"models-list"},Pa={key:0,class:"empty-models"},Oa={class:"empty-actions"},za={class:"subsection"},Ma={class:"subsection-header"},Fa={class:"keys-list"},Ra={key:0,class:"empty-keys"},Ha={class:"provider-actions mt-4"},Ja={class:"settings-section"},ja={class:"panel-content"},Wa={class:"settings-card"},qa={class:"settings-section"},Xa={class:"section-header compact"},Za={class:"header-actions"},Qa={class:"panel-content"},Ka={class:"backup-content"},Ya={class:"settings-card backup-settings"},ei={class:"input-with-tip"},ti={class:"input-with-tip"},si={class:"path-input-group"},li={class:"path-input-group"},ni={class:"settings-card backup-history"},ai={class:"card-header"},ii={class:"table-container"},oi={class:"settings-section"},ri={class:"section-header"},ui={class:"header-actions"},di={class:"panel-content"},ci={class:"settings-card"},fi={class:"slider-with-input"},pi={class:"slider-with-input"},mi={class:"settings-section git-backup-container"},gi={class:"section-header"},vi={class:"header-actions"},hi={class:"panel-content"},bi={class:"git-panel-layout"},yi={class:"git-config-section"},wi={key:0,class:"git-warning-message"},ki={key:1,class:"git-checking-message"},_i={key:2,class:"git-success-message"},Ci={class:"git-settings-form"},xi={class:"form-item-tip"},Ti={class:"path-input-group"},Vi={class:"form-item-tip warning-tip"},Di={class:"form-item-tip"},Ei={class:"path-input-group"},Ni={class:"form-item-tip"},Ii={class:"path-input-group"},Si={key:0,class:"form-item-tip"},Ui={class:"input-with-tip"},Bi={class:"git-history-section"},$i={class:"section-header"},Li={key:0,class:"history-error"},Gi={class:"commit-card"},Ai={class:"commit-header"},Pi={class:"commit-date"},Oi={class:"commit-message"},zi={class:"commit-meta"},Mi={class:"hash-container"},Fi={class:"author"},Ri={class:"history-actions"},Hi={key:2,class:"empty-history"},Ji={class:"path-input-group"},ji={class:"path-input-group"},Wi={class:"dialog-footer"},qi={class:"dialog-footer"},Xi={class:"dialog-footer"},Zi={class:"param-input-group"},Qi={class:"param-input-group"},Ki={class:"param-input-group"},Yi={class:"param-input-group"},eo={class:"param-input-group"},to={class:"dialog-footer"},so={class:"backup-progress"},lo={class:"progress-message"},no={key:0,class:"backup-info"},ao={class:"backup-path"},io={class:"backup-actions"},oo={class:"git-diff-container"},ro={class:"git-diff-content"},uo={class:"dialog-footer"},co={class:"backup-progress"},fo={class:"progress-message"},po={class:"backup-actions"},mo={class:"token-help-content"},go={class:"security-tip"},vo={class:"form-tip"},ho={class:"form-tip"},bo={class:"form-actions"},yo={class:"form-tip"},wo={class:"dialog-footer"},ko={class:"diff-dialog-content"},_o={class:"dialog-footer"},Co={__name:"app",setup(i){const s=Gl(),l=yn(),e=wn(),r=O("chrome"),c=gt(()=>s.chrome?.userDataDirs||[]),f=O({});O(!1);const _=O(null),w=O(null),C=gt(()=>s.chrome?.default_path||""),V=O({isEnabled:!1,inProgress:!1,lastBackupTime:"从未备份"}),E=O(!1),z=async a=>{try{const t=await window.pywebview.api.drssion_controller.check_chrome_status({path:`${a.path}/${a.name}`,port:a.port}),p=typeof t=="string"?JSON.parse(t):t;p.status==="success"?f.value[a.id]=p.data.running:f.value[a.id]=!1}catch(t){console.error("检查Chrome状态失败:",t),f.value[a.id]=!1}},g=async()=>{try{const a=[...c.value];for(const t of a)await z(t)}catch(a){console.error("检查状态失败:",a)}};async function y(a){try{if(f.value[a.id]){const t=await window.pywebview.api.drssion_controller.stop_chrome_profile({path:`${a.path}/${a.name}`,port:a.port}),p=typeof t=="string"?JSON.parse(t):p;if(p.status==="success")f.value[a.id]=!1,b.success("Chrome实例已停止");else throw new Error(p.message||"停止失败")}else{const t=await window.pywebview.api.drssion_controller.start_chrome_with_profile({path:`${a.path}/${a.name}`,port:a.port,browser_path:C.value,extensions_enabled:a.enableExtensions,extensions_path:a.extensionsPath}),p=typeof t=="string"?JSON.parse(t):p;if(p.status==="success")f.value[a.id]=!0,b.success("Chrome实例已启动");else throw new Error(p.message||"启动失败")}}catch(t){b.error(t.message||(f.value[a.id]?"停止失败":"启动失败"))}}const U=async a=>{const t=await window.pywebview.api.select_file_path(),p=typeof t=="string"?JSON.parse(t):t;p&&p.status==="success"&&p.data.length>0&&(s.chrome.default_path=p.data[0],await s.updateConfigItem("chrome.default_path",p.data[0]))},D=async a=>{try{const t=await window.pywebview.api.select_directory(),p=typeof t=="string"?JSON.parse(t):t;p.status==="success"&&p.data&&(a==="userDataPath"?H.value.form.path=p.data:a==="extensionsPath"?H.value.form.extensionsPath=p.data:a==="downloadDir"?s.chrome.downloadDir=p.data:a==="gitLocalPath"?v.value.localPath=p.data:a==="gitBackupDir"&&(v.value.backupDir=p.data))}catch(t){b.error(`选择目录失败: ${t}`)}},$=async()=>{try{const a=await window.pywebview.api.drssion_controller.detect_chrome_path(),t=typeof a=="string"?JSON.parse(a):t;t.status==="success"&&(await s.updateConfigItem("chrome.default_path",t.data.path),b.success("已自动检测并更新Chrome路径"))}catch(a){b.error("检测Chrome径失败："+a.message)}},Q=()=>{H.value={visible:!0,isEdit:!1,form:{name:"",path:"",port:9222,isDefault:!1,enableExtensions:!1,extensionsPath:""}}},G=a=>{H.value={visible:!0,isEdit:!0,form:{...a}}},P=async()=>{const a=_e.value;a&&await a.validate(async t=>{if(t){const{form:p}=H.value;if(p.isDefault&&s.chrome.userDataDirs.forEach(h=>{h.id!==p.id&&(h.isDefault=!1)}),!s.chrome.userDataDirs.some(h=>h.isDefault&&h.id!==p.id)&&!p.isDefault&&s.chrome.userDataDirs.length===0&&(p.isDefault=!0),H.value.isEdit){const h=s.chrome.userDataDirs.findIndex(te=>te.id===p.id);h!==-1&&(s.chrome.userDataDirs[h]={...p})}else{const h={...p,id:Date.now().toString()};s.chrome.userDataDirs.push(h)}await s.updateConfigItem("chrome.userDataDirs",s.chrome.userDataDirs),H.value.visible=!1,b.success("保存成功")}})},M=async a=>{try{await me.confirm("确定要删除该用户数据目录吗？","提示",{type:"warning"});const t=s.chrome.userDataDirs.filter(p=>p.id!==a.id);await s.updateConfigItem("chrome.userDataDirs",t),b.success("删除成功")}catch(t){t!=="cancel"&&b.error(t.message)}},X=()=>{q.value.isEdit=!1,q.value.title="添加AI角色",q.value.form={id:"",name:"",prompt:"",isEnabled:!0},q.value.visible=!0},ke=a=>{q.value.isEdit=!0,q.value.title="编辑AI角色",q.value.form={...a},q.value.visible=!0},m=async(a,t)=>{try{await l.updateRole(a.id,{isEnabled:t}),b.success(`已${t?"启用":"禁用"}角色 "${a.name}"`)}catch(p){b.error("操作失败: "+p.message),a.isEnabled=!t}},T=async()=>{try{if(!q.value.form.name){b.warning("角色名称不能为空");return}if(!q.value.form.prompt){b.warning("提示词不能为空");return}let a;q.value.isEdit?(a=await l.updateRole(q.value.form.id,q.value.form),b.success("AI角色更新成功")):(a=await l.addRole(q.value.form),b.success("AI角色添加成功")),q.value.visible=!1,await l.loadRoles(),!q.value.isEdit&&a&&a.data&&setTimeout(()=>{const t=a.data.id;!l.roles.some(k=>k.id===t)&&a.data&&l.roles.push(a.data)},100)}catch(a){b.error("操作失败: "+a.message)}},I=async a=>{try{await me.confirm(`确定要删除角色 "${a.name}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await l.deleteRole(a.id),b.success("角色已删除")}catch(t){t!=="cancel"&&b.error("删除失败: "+t.message)}},A=()=>{try{const a=l.roles,t=JSON.stringify(a,null,2),p=new Blob([t],{type:"application/json"}),k=URL.createObjectURL(p),h=document.createElement("a");h.href=k,h.download=`AI角色_${new Date().toISOString().split("T")[0]}.json`,h.click(),URL.revokeObjectURL(k),b.success("导出成功")}catch(a){b.error("导出失败: "+a.message)}},le=()=>{const a=s.openai;Hs.value={api_key:a.api_key||"",base_url:a.base_url||""}},fe=()=>{const a=s.feishu;De.value={app_id:a.app_id||"",app_secret:a.app_secret||"",encrypt_key:a.encrypt_key||"",verification_token:a.verification_token||""}},ce=async()=>{try{await s.updateConfigItem("feishu",De.value),b.success("飞书配置保存成功")}catch(a){console.error("保存飞书配置失败:",a),b.error("保存失败: "+a.message)}},Z=()=>{fe()},F=O({backupDir:"",targetDir:"",autoBackup:!1,backupInterval:30,keepBackups:7,useZip:!1}),Se=O([]),R=O({visible:!1,percent:0,status:"normal",message:""}),ie=a=>{try{console.log("收到备份进度:",a);const t=typeof a=="string"?JSON.parse(a):a;R.value={...R.value,percent:t.percent||0,message:t.message||"",status:t.status||"normal",visible:!0,backupPath:t.path||R.value.backupPath},t.status==="success"&&t.percent===100&&setTimeout(()=>{S()},500)}catch(t){console.error("处理备份进度失败:",t)}};window.receiveBackupProgress=ie;const Te=async()=>{try{if(!F.value.backupDir){b.warning("请先选择需要备份的目录");return}if(!F.value.targetDir){b.warning("请先选择备份保存的目录");return}R.value={visible:!0,percent:0,status:"normal",message:"正在准备备份..."};const a=await window.pywebview.api.backup_data({backup_dir:F.value.backupDir,target_dir:F.value.targetDir,use_zip:F.value.useZip}),t=typeof a=="string"?JSON.parse(a):a;if(t.status==="success")b.success("备份成功"),S();else throw new Error(t.message||"备份失败")}catch(a){R.value.status="exception",R.value.message="备份失败："+a.message,b.error("备份失败："+a.message)}},N=async a=>{try{await me.confirm("恢复备份将覆盖当前数据，是否继续？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const t=await window.pywebview.api.restore_backup({backup_path:a.path}),p=typeof t=="string"?JSON.parse(t):t;if(p.status==="success")b.success("恢复成功，应用将在3秒后重启"),setTimeout(async()=>{try{await window.pywebview.api.restart_application()}catch(k){b.error("重启应用失败，请手动重启"),console.error("重启失败:",k)}},3e3);else throw new Error(p.message||"恢复失败")}catch(t){t!=="cancel"&&b.error("恢复失败："+t.message)}},W=async a=>{try{await me.confirm("确定要删除这个备份吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const t=await window.pywebview.api.delete_backup({backup_path:a.path}),p=typeof t=="string"?JSON.parse(t):t;if(p.status==="success")b.success("删除成功"),S();else throw new Error(p.message||"删除失败")}catch(t){t!=="cancel"&&b.error("删除失败："+t.message)}},S=async()=>{try{console.log("开始获取备份历史, 目标目录:",F.value.targetDir);const a=await window.pywebview.api.get_backup_history({target_dir:F.value.targetDir});console.log("API返回原始数据:",a);const t=typeof a=="string"?JSON.parse(a):a;if(console.log("解析后的数据:",t),t.status==="success"){const p=t.data.map(k=>(console.log("处理备份项:",k),{...k,time:k.time||(k.name?new Date(k.name.replace("backup_","").replace("_","T").replace(/(\d{2})(\d{2})(\d{2})$/,"$1:$2:$3")).getTime()/1e3:null)}));console.log("处理后的数据:",p),Se.value=p}else throw console.error("获取备份历史失败:",t.message),new Error(t.message||"获取备份历史失败")}catch(a){console.error("获取备份历史出错:",a),b.error("获取备份历史失败："+a.message)}},ve=async a=>{try{if(await s.updateConfigItem("backup.autoBackup",a),a){const t=await window.pywebview.api.check_auto_backup(),p=typeof t=="string"?JSON.parse(t):t;if(p.status==="success")b.success("自动备份已启动");else throw new Error(p.message||"启动自动备份失败")}else await window.pywebview.api.stop_auto_backup(),b.info("自动备份已停止")}catch(t){b.error("更新自动备份设置失败："+t.message),F.value.autoBackup=!a}},ze=async a=>{try{await s.updateConfigItem("backup.backupInterval",a)}catch(t){b.error("更新备份周期失败："+t.message),F.value.backupInterval=s.backup.backupInterval}},qe=async a=>{try{await s.updateConfigItem("backup.keepBackups",a)}catch(t){b.error("更新保留备份数量失败："+t.message),F.value.keepBackups=s.backup.keepBackups}};We(()=>F.value.backupInterval,ze),We(()=>F.value.keepBackups,qe),We(()=>F.value.targetDir,a=>{a&&S()});const Ae=async()=>{const a=s.backup||{};F.value={backupDir:a.backupDir||"",targetDir:a.targetDir||"",autoBackup:a.autoBackup||!1,backupInterval:a.backupInterval||30,keepBackups:a.keepBackups||7,useZip:a.useZip||!1},F.value.targetDir&&S()},He=a=>{if(a==null)return"0 B";const t=["B","KB","MB","GB","TB"];let p=Math.abs(Number(a)),k=0;for(;p>=1024&&k<t.length-1;)p/=1024,k++;let h=2;return k===0?h=0:p>=100&&(h=1),`${p.toFixed(h)} ${t[k]}`},Ve=a=>{if(console.log("格式化时间戳:",a),!a)return console.log("时间戳为空"),"";try{const t=new Date(a*1e3);console.log("转换后的日期对象:",t);const p=t.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1});return console.log("格式化后的时间:",p),p}catch(t){return console.error("格式化时间失败:",t),""}};vt(async()=>{try{await s.loadConfig(),w.value&&clearInterval(w.value),w.value=setInterval(g,1e3),await g(),await l.loadRoles(),le(),fe(),await Ae();try{await Promise.race([Pt(!0),new Promise((a,t)=>setTimeout(()=>t(new Error("初始化加载超时")),8e3))])}catch(a){console.error("加载AI服务商失败，但继续初始化其他模块:",a),e.loading=!1}s.backup?.autoBackup&&setTimeout(()=>{window.pywebview.api.check_auto_backup().then(a=>{const t=typeof a=="string"?JSON.parse(a):a;console.log("自动备份检查结果:",t)}).catch(a=>console.error("启动自动备份失败:",a))},5e3)}catch(a){console.error("初始化失败:",a),b.error("初始化失败: "+a.message),s.loading.value=!1,l.loading.value=!1,e.loading=!1}}),fs(()=>{w.value&&(clearInterval(w.value),w.value=null)});const H=O({visible:!1,isEdit:!1,form:{name:"",path:"",port:9222,isDefault:!1,enableExtensions:!1,extensionsPath:""}}),_e=O(null),Me={name:[{required:!0,message:"请输入名称",trigger:"blur"},{min:2,max:20,message:"长度在 2 到 20 个字符",trigger:"blur"}],path:[{required:!0,message:"请选择路径",trigger:"blur"}],port:[{required:!0,message:"请输入端口号",trigger:"blur"},{type:"number",min:1024,max:65535,message:"端口号范围在1024-65535之间",trigger:"blur"}]},q=O({visible:!1,title:"添加AI角色",form:{id:"",name:"",prompt:"",isEnabled:!0},isEdit:!1}),Xe=O({visible:!1,content:""});O(null);const Hs=O({api_key:"",base_url:""}),Js=O(null),De=O({app_id:"",app_secret:"",encrypt_key:"",verification_token:""}),ye=O({...s.chat}),Yt=O({...s.chat}),js=async()=>{try{await s.updateConfigItem("chat",ye.value),b.success("聊天设置保存成功"),Yt.value={...ye.value}}catch(a){b.error(`保存失败: ${a}`)}},Ws=()=>{ye.value={...Yt.value},b.info("已重置为上次保存的设置")},qs=async()=>{try{R.value.visible=!1,await nextTick(),Te()}catch(a){b.error("重试备份失败："+a.message)}},Xs=async a=>{try{await s.updateConfigItem("backup.useZip",a)}catch(t){b.error("更新压缩备份设置失败："+t.message),F.value.useZip=!a}},Zs=a=>{N(a)},nt=O(!1);O("");const Pe=O(null),bt=O(!1),v=O({repoUrl:"",authType:"token",username:"",password:"",token:"",tokenUsername:"git",localPath:"",backupDir:"",autoBackup:!1,backupInterval:60,manualBackupInfo:{commitMessage:"",tagName:""}}),Oe=O([]);O({visible:!1,form:{commitMessage:"",tagName:""}});const Ee=O({visible:!1,content:"",loading:!1}),Ne=O({visible:!1,percent:0,status:"normal",message:""}),St=gt(()=>v.value.repoUrl&&v.value.repoUrl.includes("coding.net")),Ze=gt(()=>v.value.repoUrl&&v.value.backupDir?v.value.authType==="password"?v.value.username&&v.value.password:St.value?v.value.token&&v.value.tokenUsername:!!v.value.token:!1),Qs=()=>{const a=s.git||{};v.value={repoUrl:a.repoUrl||"",authType:a.authType||"token",username:a.username||"",password:a.password||"",token:a.token||"",tokenUsername:a.tokenUsername||"",localPath:a.localPath||"",backupDir:a.backupDir||"",autoBackup:a.autoBackup||!1,backupInterval:a.backupInterval||60},Ze.value&&at()},es=async()=>{try{bt.value=!0,Pe.value=null,console.log("正在检查Git安装状态...");const a=await window.pywebview.api.check_git_installation(),t=typeof a=="string"?JSON.parse(a):a;if(console.log("Git检查结果:",t),t.status==="success")Pe.value=t.data,nt.value=Pe.value.installed,Pe.value.installed?b.success(`Git已安装: ${Pe.value.version}`):b.warning("未检测到Git安装，请先安装Git才能使用备份功能");else throw new Error(t.message||"检测Git失败")}catch(a){console.error("检查Git安装失败:",a),b.error(`检测Git失败: ${a.message||"未知错误"}`),Pe.value={installed:!1},nt.value=!1}finally{bt.value=!1}},Ks=async()=>{try{await s.updateConfigItem("git",{repoUrl:v.value.repoUrl,authType:v.value.authType,username:v.value.username,password:v.value.password,token:v.value.token,tokenUsername:v.value.tokenUsername,localPath:v.value.localPath,backupDir:v.value.backupDir,autoBackup:v.value.autoBackup,backupInterval:v.value.backupInterval}),b.success("Git配置保存成功")}catch(a){b.error(`保存Git配置失败: ${a.message}`)}},Ys=async()=>{try{if(!Ze.value)return b.warning("请先完成Git配置"),!1;const a=et.service({text:"正在初始化Git仓库...",background:"rgba(0, 0, 0, 0.7)"}),t={repo_url:v.value.repoUrl,auth_type:v.value.authType,username:v.value.username,password:v.value.password,token:v.value.token,token_username:v.value.tokenUsername,backup_dir:v.value.backupDir},p=await window.pywebview.api.init_git_repo(t);a.close();const k=typeof p=="string"?JSON.parse(p):p;return k.status==="success"?(b.success("Git仓库初始化成功"),!0):(b.error(`初始化Git仓库失败: ${k.message}`),!1)}catch(a){return b.error(`初始化Git仓库失败: ${a.message||a}`),!1}},el=async()=>{try{backupDialogVisible.value=!0,R.value=0,V.value="正在准备备份...";const a={repo_url:v.value.repoUrl,auth_type:v.value.authType,username:v.value.username,password:v.value.password,token:v.value.token,token_username:v.value.tokenUsername,local_path:v.value.localPath,backup_dir:v.value.backupDir};R.value=30,V.value="正在执行备份...";const t=await window.pywebview.api.backup_to_git(a),p=typeof t=="string"?JSON.parse(t):t;if(p.status==="success")R.value=100,V.value="备份完成",backupResult.value=p.data||{},backupSuccess.value=!0,await nl();else throw new Error(p.message||"备份失败")}catch(a){R.value=100,V.value=`备份失败: ${a.message}`,backupSuccess.value=!1}},at=async()=>{try{if(E.value=!0,_.value=null,!v.value||!v.value.backupDir){_.value="仓库URL或本地路径未提供",Oe.value=[];return}console.log("正在加载Git历史，配置:",v.value);const a=await window.pywebview.api.get_git_history({repo_url:v.value.repoUrl,local_path:v.value.backupDir,count:50}),t=typeof a=="string"?JSON.parse(a):a;console.log("Git历史响应:",t),t.status==="success"?Oe.value=(t.history||[]).map(p=>({...p,hash:p.hash.replace(/"/g,"").trim(),message:p.message.replace(/"/g,"").trim()})):(_.value=t.message||"加载历史记录失败",Oe.value=[])}catch(a){console.error("加载Git历史错误:",a),_.value=`加载历史记录失败: ${a.message||a}`,Oe.value=[]}finally{E.value=!1}},tl=async a=>{try{if(await s.updateConfigItem("git.autoBackup",a),a){const t=await window.pywebview.api.start_auto_git_backup(),p=typeof t=="string"?JSON.parse(t):t;if(p.status==="success")b.success("Git自动备份已启动");else throw new Error(p.message||"启动Git自动备份失败")}else await window.pywebview.api.stop_auto_git_backup(),b.info("Git自动备份已停止")}catch(t){b.error(`更新Git自动备份设置失败: ${t.message}`),v.value.autoBackup=!a}},sl=()=>{Ne.value.visible=!1,setTimeout(()=>{el()},100)};vt(()=>{Qs()});const ts=async()=>{try{if(!v.value.repoUrl){b.warning("请填写Git仓库URL");return}const a={repo_url:v.value.repoUrl,auth_type:v.value.authType};if(v.value.authType==="password"){if(!v.value.username||!v.value.password){b.warning("请填写用户名和密码");return}a.username=v.value.username,a.password=v.value.password}else{if(!v.value.token){b.warning("请填写访问令牌");return}if(a.token=v.value.token,St.value){if(!v.value.tokenUsername){b.warning("Coding平台需要填写用户名");return}a.token_username=v.value.tokenUsername}}const t=et.service({text:"正在测试Git连接...",background:"rgba(0, 0, 0, 0.7)"});console.log("发送测试连接请求参数:",a);const p=await window.pywebview.api.test_git_credentials(a),k=typeof p=="string"?JSON.parse(p):p;if(console.log("测试连接响应:",k),t.close(),k.status==="success")b.success(k.message||"Git凭据验证成功");else throw new Error(k.message||"Git凭据验证失败")}catch(a){b.error(`Git连接测试失败: ${a.message}`)}},Ut=O(!1),ll=()=>{Ut.value=!0};We(r,a=>{console.log("Tab切换为:",a),a==="git-backup"?(console.log("加载Git配置和检测Git安装"),Bt(),es()):a==="editor"?initEditorSettings():a==="system"?initSystemSettings():a==="chat"&&initChatSettings()},{immediate:!0});const Bt=()=>{console.log("加载Git配置...");const a=s.git||{};console.log("获取到的Git配置:",a),v.value={repoUrl:a.repoUrl||"",authType:a.authType||"token",username:a.username||"",password:a.password||"",token:a.token||"",tokenUsername:a.tokenUsername||"",localPath:a.localPath||"",backupDir:a.backupDir||"",autoBackup:a.autoBackup||!1,backupInterval:a.backupInterval||60},console.log("设置后的gitConfig:",v.value)};vt(()=>{Bt()});const nl=async()=>{try{if(!v.value.repoUrl||!v.value.backupDir){Oe.value=[];return}const a=et.service({target:".git-history",text:"加载Git历史记录...",background:"rgba(0, 0, 0, 0.7)"}),t={repo_url:v.value.repoUrl,auth_type:v.value.authType,username:v.value.username,password:v.value.password,token:v.value.token,token_username:v.value.tokenUsername,local_path:v.value.localPath,backup_dir:v.value.backupDir},p=await window.pywebview.api.get_git_history(t),k=typeof p=="string"?JSON.parse(p):p;if(a.close(),k.status==="success")Oe.value=(k.data||[]).slice(0,50);else throw new Error(k.message||"获取Git历史记录失败")}catch(a){console.error("加载Git历史记录失败:",a),Oe.value=[],b.error(`加载Git历史记录失败: ${a.message}`)}},Je=O(!1),we=Al({message:"",force:!1,tagName:""}),$t=O(!1),al=()=>{if(!Ze.value){b.warning("请先完成Git配置并保存");return}Je.value=!0},ss=async()=>{try{if(!we.message.trim()){b.warning("请输入提交说明");return}/[^\x00-\x7F]/.test(we.message)&&await me.confirm("提交信息包含中文或特殊字符，可能导致备份失败。建议仅使用英文字母、数字和基本符号。是否继续？","编码警告",{confirmButtonText:"继续备份",cancelButtonText:"返回修改",type:"warning"});const{repoUrl:t,authType:p,username:k,password:h,token:te,tokenUsername:L,backupDir:de}=v.value;if(!de){b.error("备份目录不能为空，请在Git配置中填写");return}try{const se=await window.pywebview.api.check_path_exists({path:de});if(!se||!se.exists){b.error(`备份目录不存在: ${de}`);return}}catch(se){b.error(`验证备份目录失败: ${se.message||se}`);return}$t.value=!0;const Y=et.service({text:"准备备份...",background:"rgba(0, 0, 0, 0.7)"});try{const se={repo_url:t,auth_type:p,username:k,password:h,token:te,token_username:L,backup_dir:de,commit_message:we.message.trim(),tag_name:we.tagName,force:we.force};console.log("备份参数:",{...se,password:"***",token:"***"}),Y.text="正在提交备份...";const he=await window.pywebview.api.backup_to_git(se);Y.close();const be=typeof he=="string"?JSON.parse(he):he;if(be.status==="success")b.success("备份提交成功"),Je.value=!1,we.message="",await at();else if(be.status==="info")b.info(be.message),Je.value=!1;else if(be.status==="warning")me.alert(be.message,"备份部分成功",{type:"warning",confirmButtonText:"确定"}),Je.value=!1;else if(be.status==="error"&&be.message.includes("nothing to commit"))me.confirm("似乎文件已被暂存但尚未成功提交。要清空暂存区并重试吗？","需要重置Git状态",{confirmButtonText:"清空暂存区",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await window.pywebview.api.reset_git_changes({backup_dir:v.value.backupDir,mode:"mixed"}),b.success("已清空暂存区，请重新提交")}catch(Ue){b.error(`重置失败: ${Ue.message||Ue}`)}}).catch(()=>{});else throw new Error(be.message||"备份提交失败")}catch(se){Y.close(),b.error(`备份提交失败: ${se.message||se}`)}}catch(a){if(a==="cancel")return;b.error(`备份操作出错: ${a.message||a}`)}finally{$t.value=!1}},il=async()=>{try{await me.confirm("初始化仓库将创建一个新的Git仓库连接，确定要继续吗？","初始化确认",{confirmButtonText:"确认初始化",cancelButtonText:"取消",type:"warning"});const a=et.service({text:"正在初始化Git仓库...",background:"rgba(0, 0, 0, 0.7)"});await Ys(),a.close(),b.success("Git仓库初始化成功")}catch(a){a!=="cancel"&&b.error(`初始化Git仓库失败: ${a.message||a}`)}},ol=async a=>{try{if(!v.value.backupDir){b.error("备份目录不能为空，请先配置");return}if(a==="checkStatus"){const t=await window.pywebview.api.check_git_status({backup_dir:v.value.backupDir}),p=typeof t=="string"?JSON.parse(t):t;if(p.status==="success"){const k=p.git_status;me.alert(`<div style="text-align:left">
            <p><strong>当前分支:</strong> ${k.branch}</p>
            <p><strong>上次提交:</strong> ${k.last_commit}</p>
            <p><strong>是否有变更:</strong> ${k.has_changes?"是":"否"}</p>
            <p><strong>是否有暂存文件:</strong> ${k.has_staged?"是":"否"}</p>
            ${k.status_details?`<p><strong>详细状态:</strong><br><pre>${k.status_details}</pre></p>`:""}
          </div>`,"Git仓库状态",{dangerouslyUseHTMLString:!0,confirmButtonText:"确定"})}else b.error(p.message||"检查状态失败")}else if(["resetSoft","resetMixed","resetHard"].includes(a)){let t="确定要撤销上次提交吗？",p="warning";a==="resetMixed"?t="确定要重置并清空暂存区吗？这将保留文件更改，但撤销暂存操作。":a==="resetHard"&&(t="警告：此操作将丢失所有未提交的文件更改，无法恢复！确定要强制重置吗？",p="error");try{await me.confirm(t,"Git仓库重置确认",{confirmButtonText:"确定重置",cancelButtonText:"取消",type:p});const k=a.replace("reset","").toLowerCase(),h=await window.pywebview.api.reset_git_changes({backup_dir:v.value.backupDir,mode:k}),te=typeof h=="string"?JSON.parse(h):h;te.status==="success"?(b.success(te.message),await at()):b.error(te.message||"重置操作失败")}catch(k){k!=="cancel"&&b.error(`重置操作出错: ${k.message||k}`)}}else if(a==="cleanStaged"){const t=await window.pywebview.api.reset_git_changes({backup_dir:v.value.backupDir,mode:"mixed"}),p=typeof t=="string"?JSON.parse(t):t;p.status==="success"?b.success("已清空暂存区"):b.error(p.message||"清空暂存区失败")}}catch(t){b.error(`Git操作失败: ${t.message||t}`)}},rl=async a=>{try{Ee.value.loading=!0,Ee.value.visible=!0,Ee.value.content="加载中...";const t=a.hash.replace(/"/g,"").trim();console.log("查看提交详情:",t,v.value.backupDir);const p=await window.pywebview.api.get_git_commit_details({commit_hash:t,repo_path:v.value.backupDir});if(console.log("提交详情响应:",p),p.status==="success")Ee.value.content=p.diff||"此提交没有变更";else throw new Error(p.message||"获取提交详情失败")}catch(t){console.error("查看提交详情错误:",t),Ee.value.content=`加载提交详情失败: ${t.message}`,b.error(`查看提交详情失败: ${t.message}`)}finally{Ee.value.loading=!1}},ul=async a=>{try{const t=a.hash.replace(/"/g,"").trim();await me.confirm(`确定要恢复到此版本吗？

      提交: ${t.substring(0,7)} - ${a.message}

      日期: ${a.date}

      作者: ${a.author}


      此操作会丢失当前未提交的更改!`,"恢复确认",{confirmButtonText:"确认恢复",cancelButtonText:"取消",type:"warning"});const p=et.service({text:"恢复中...",background:"rgba(0, 0, 0, 0.7)"}),k=await window.pywebview.api.reset_to_commit({backup_dir:v.value.backupDir,commit_hash:t});p.close();const h=typeof k=="string"?JSON.parse(k):k;h.status==="success"?(await me.alert(`<div style="text-align:center;">
          <i class="el-icon-success" style="font-size: 48px; color: #67C23A; margin-bottom: 20px;"></i>
          <h2 style="margin-bottom: 15px;">恢复成功</h2>
          <p>已成功恢复到版本: ${t.substring(0,7)}</p>
          <p>提交说明: ${a.message}</p>
          <p>恢复时间: ${new Date().toLocaleString()}</p>
        </div>`,"操作成功",{dangerouslyUseHTMLString:!0,confirmButtonText:"确定",callback:()=>{b.success("版本恢复完成")}}),await at()):me.alert(`<div style="text-align:center;">
          <i class="el-icon-error" style="font-size: 48px; color: #F56C6C; margin-bottom: 20px;"></i>
          <h2 style="margin-bottom: 15px;">恢复失败</h2>
          <p>${h.message||"恢复操作未能完成"}</p>
        </div>`,"操作失败",{dangerouslyUseHTMLString:!0,confirmButtonText:"确定",type:"error"})}catch(t){t!=="cancel"&&b.error(`恢复操作失败: ${t.message||t}`)}},ls=async()=>{try{const a=await window.pywebview.api.get_backup_status(),t=typeof a=="string"?JSON.parse(a):a;t.status==="success"&&(V.value={isEnabled:t.data.auto_backup_enabled,inProgress:t.data.backup_in_progress,lastBackupTime:t.data.last_backup_time||"从未备份"})}catch(a){console.error("获取备份状态失败:",a)}};let Lt;vt(()=>{Bt(),ls(),Lt=setInterval(ls,3e4)}),fs(()=>{Lt&&clearInterval(Lt)});const yt=O(!1),Gt=O(""),At=O([]),wt=O(!1),it=O(""),dl=async()=>{try{it.value="",wt.value=!0,yt.value=!0;const a=await window.pywebview.api.get_git_diff({backup_dir:v.value.backupDir}),t=typeof a=="string"?JSON.parse(a):a;t.status==="success"?(Gt.value=t.data.diff,At.value=t.data.changed_files,!Gt.value.trim()&&At.value.length===0&&(it.value="没有检测到任何变更")):it.value=t.message||"获取变更内容失败"}catch(a){it.value=`加载变更内容失败: ${a.message||a}`,console.error("加载变更内容失败:",a)}finally{wt.value=!1}},cl=a=>{try{return new Date(a).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}).replace(/\//g,"-")}catch{return a}},kt=O([]),ns=()=>{const a={name:"新服务商",baseUrl:"",apiKeys:[],models:[]},t=e.addProvider(a);kt.value=[t.id]},fl=a=>{me.confirm("确定要删除该服务商配置吗？此操作不可恢复。","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const t=e.providers[a];e.removeProvider(t.id),b.success("服务商已删除")}).catch(()=>{})},as=a=>{e.addApiKey(a)},pl=(a,t)=>{e.removeApiKey(a,t)},ml=async(a,t)=>{await e.testApiKey(a,t)},is=a=>{me({title:"添加模型",dangerouslyUseHTMLString:!0,message:`
    <div class="add-model-form" style="padding: 10px;">
      <div class="form-item" style="margin-bottom: 15px;">
        <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: bold;">模型ID (必填):</label>
        <input
          id="model-id-input"
          class="el-input__inner"
          placeholder="例如: gpt-4-turbo"
          style="width: 100%; padding: 8px 12px; border: 1px solid #dcdfe6; border-radius: 4px; font-size: 14px;"
        />
      </div>
      <div class="form-item" style="margin-bottom: 15px;">
        <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: bold;">显示别名 (可选):</label>
        <input
          id="model-name-input"
          class="el-input__inner"
          placeholder="例如: GPT-4 Turbo (留空则使用模型ID)"
          style="width: 100%; padding: 8px 12px; border: 1px solid #dcdfe6; border-radius: 4px; font-size: 14px;"
        />
      </div>
      <div class="form-item" style="margin-bottom: 15px;">
        <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: bold;">
          <input
            id="model-available-input"
            type="checkbox"
            checked
            style="margin-right: 8px;"
          />
          启用此模型
        </label>
      </div>
      <div class="form-tip" style="font-size: 12px; color: #909399; line-height: 1.4;">
        • 模型ID用于与AI服务商通信，必须准确<br/>
        • 显示别名用于界面显示，可以自定义以避免重复<br/>
        • 只有启用的模型才会在选择器中显示
      </div>
    </div>
  `,showCancelButton:!0,confirmButtonText:"确定",cancelButtonText:"取消",beforeClose:(p,k,h)=>{if(p==="confirm"){const te=document.getElementById("model-id-input")?.value?.trim(),L=document.getElementById("model-name-input")?.value?.trim(),de=document.getElementById("model-available-input")?.checked;if(!te){b.warning("请输入模型ID");return}if(e.providers.find(se=>se.id===a)?.models?.some(se=>se.id===te)){b.warning("该模型ID已存在");return}try{e.addModel(a,{id:te,name:L||te,available:de!==!1}),b.success("模型添加成功"),h()}catch(se){b.error("添加模型失败: "+se.message)}}else h()}})},gl=(a,t)=>{e.removeModel(a,t)},vl=(a,t)=>{if(!t.id||!t.id.trim())return b.warning("模型ID不能为空"),!1;const p=e.providers.find(k=>k.id===a);return p?.models&&p.models.filter(h=>h.id===t.id).length>1?(b.warning("模型ID不能重复"),!1):((!t.name||!t.name.trim())&&(t.name=t.id),!0)},hl=a=>{console.log(`模型 ${a.name} (${a.id}) 可用性变更为: ${a.available}`)},os=async a=>{try{await e.fetchModels(a)}catch(t){console.error("获取模型错误",t)}},bl=async()=>{try{await e.saveProviders(),b.success("AI服务商配置保存成功")}catch(a){console.error("保存服务商配置错误",a),b.error(`保存失败: ${a.message||"未知错误"}`)}},Pt=async(a=!1)=>{try{if(a||!e.initialized){const t=new Promise((p,k)=>{setTimeout(()=>k(new Error("加载AI服务商配置超时")),1e4)});await Promise.race([e.loadProviders(a),t]),e.providers.length>0&&(kt.value=[e.providers[0].id])}}catch(t){console.error("加载服务商配置错误",t),e.loading=!1}};We(r,a=>{a==="openai-config"&&Pt()});const yl=async()=>{try{await l.loadRoles(!0),b.success("AI角色列表已刷新")}catch(a){b.error("刷新AI角色失败: "+a.message)}},wl=async()=>{try{await Pt(!0),b.success("AI服务商配置已刷新")}catch(a){b.error("刷新AI服务商配置失败: "+a.message)}},K=O({visible:!1,providerId:"",modelId:"",modelName:"",form:{temperature:.8,max_tokens:8192,top_p:.8,frequency_penalty:0,presence_penalty:0,stream:!0}}),kl=(a,t)=>{K.value.visible=!0,K.value.providerId=a,K.value.modelId=t.id,K.value.modelName=t.name||t.id,t.config?K.value.form={temperature:t.config.temperature??.8,max_tokens:t.config.max_tokens??4096,top_p:t.config.top_p??.8,frequency_penalty:t.config.frequency_penalty??0,presence_penalty:t.config.presence_penalty??0,stream:t.config.stream??!0}:(t.config={temperature:.8,max_tokens:8192,top_p:.8,frequency_penalty:0,presence_penalty:0,stream:!0},K.value.form={...t.config})},_l=async()=>{try{const a=e.providers.find(p=>p.id===K.value.providerId);if(!a){b.error("未找到对应的服务商");return}const t=a.models.find(p=>p.id===K.value.modelId);if(!t){b.error("未找到对应的模型");return}t.config={...K.value.form},await e.saveProviders(),K.value.visible=!1,b.success("模型参数配置保存成功")}catch(a){console.error("保存模型配置失败:",a),b.error("保存模型配置失败: "+a.message)}},Cl=()=>{K.value.form={temperature:.8,max_tokens:8192,top_p:.8,frequency_penalty:0,presence_penalty:0,stream:!0},b.info("已重置为默认配置")};return(a,t)=>{const p=Hl,k=xt,h=jl,te=Jl,L=Rl,de=Fl,Y=ql,se=Bs,he=Xl,be=Wl,Ue=Ml,_t=Zl,Qe=Yl,xl=Kl,Tl=Ql,Ke=en,Ye=tn,Ce=ln,rs=sn,ot=an,us=nn,rt=fn,Vl=cn,Dl=rn,ut=pn,El=mn,Nl=vn,Il=gn,Sl=Ol,Be=zl,ds=hn,Ul=bn,cs=Pl;return j(),ee("div",ca,[ps((j(),ee("div",fa,[n(Sl,{modelValue:r.value,"onUpdate:modelValue":t[33]||(t[33]=u=>r.value=u),class:"settings-tabs"},{default:o(()=>[n(Ue,{label:"Chrome设置",name:"chrome"},{default:o(()=>[d("div",pa,[t[81]||(t[81]=d("div",{class:"section-header"},[d("h2",{class:"section-title"},"Chrome 设置")],-1)),d("div",ma,[d("div",ga,[n(de,{"label-width":"120px"},{default:o(()=>[n(L,{label:"Chrome路径"},{default:o(()=>[d("div",va,[n(p,{modelValue:B(s).chrome.default_path,"onUpdate:modelValue":t[0]||(t[0]=u=>B(s).chrome.default_path=u),placeholder:"请选择 Chrome 浏览器路径"},null,8,["modelValue"]),n(te,null,{default:o(()=>[n(h,{type:"primary",onClick:t[1]||(t[1]=u=>U("chromePath"))},{default:o(()=>[n(k,null,{default:o(()=>[n(B(je))]),_:1}),t[74]||(t[74]=x(" 选择路径 "))]),_:1}),n(h,{type:"success",onClick:$},{default:o(()=>[n(k,null,{default:o(()=>[n(B(gs))]),_:1}),t[75]||(t[75]=x(" 自动检测 "))]),_:1})]),_:1})])]),_:1}),n(L,{label:"下载目录"},{default:o(()=>[d("div",ha,[n(p,{modelValue:B(s).chrome.downloadDir,"onUpdate:modelValue":t[2]||(t[2]=u=>B(s).chrome.downloadDir=u),placeholder:"请选择下载目录"},null,8,["modelValue"]),n(h,{type:"primary",onClick:t[3]||(t[3]=u=>D("downloadDir"))},{default:o(()=>[n(k,null,{default:o(()=>[n(B(je))]),_:1}),t[76]||(t[76]=x(" 选择路径 "))]),_:1})])]),_:1})]),_:1})]),d("div",ba,[t[78]||(t[78]=d("h2",{class:"section-title"},"用户数据目录",-1)),d("div",ya,[n(h,{type:"primary",onClick:Q},{default:o(()=>[n(k,null,{default:o(()=>[n(B(dt))]),_:1}),t[77]||(t[77]=x(" 添加目录 "))]),_:1})])]),d("div",wa,[n(be,{data:c.value,style:{width:"100%"},border:""},{default:o(()=>[n(Y,{prop:"name",label:"名称","min-width":"120"}),n(Y,{prop:"path",label:"路径","min-width":"200","show-overflow-tooltip":""}),n(Y,{prop:"port",label:"端口",width:"100",align:"center"}),n(Y,{label:"状态",width:"100",align:"center"},{default:o(({row:u})=>[n(se,{type:f.value[u.id]?"success":"info"},{default:o(()=>[x(ne(f.value[u.id]?"运行中":"已停止"),1)]),_:2},1032,["type"])]),_:1}),n(Y,{label:"默认",width:"80",align:"center"},{default:o(({row:u})=>[n(he,{modelValue:u.isDefault,"onUpdate:modelValue":ue=>u.isDefault=ue,onChange:ue=>a.handleDefaultChange(u,ue)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),n(Y,{label:"操作",width:"280",align:"center",fixed:"right"},{default:o(({row:u})=>[n(te,null,{default:o(()=>[n(h,{type:f.value[u.id]?"danger":"success",size:"small",onClick:ue=>y(u)},{default:o(()=>[x(ne(f.value[u.id]?"停止":"启动"),1)]),_:2},1032,["type","onClick"]),n(h,{type:"primary",size:"small",onClick:ue=>G(u)},{default:o(()=>t[79]||(t[79]=[x(" 编辑 ")])),_:2},1032,["onClick"]),n(h,{type:"danger",size:"small",onClick:ue=>M(u)},{default:o(()=>t[80]||(t[80]=[x(" 删除 ")])),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])])])])]),_:1}),n(Ue,{label:"AI角色",name:"ai"},{default:o(()=>[d("div",ka,[d("div",_a,[t[86]||(t[86]=d("h2",{class:"section-title"},"AI角色管理",-1)),d("div",Ca,[n(te,null,{default:o(()=>[n(h,{type:"primary",onClick:X},{default:o(()=>[n(k,null,{default:o(()=>[n(B(dt))]),_:1}),t[82]||(t[82]=x(" 添加角色 "))]),_:1}),n(h,{onClick:yl,loading:B(l).loading},{default:o(()=>[n(k,null,{default:o(()=>[n(B(ct))]),_:1}),t[83]||(t[83]=x(" 刷新 "))]),_:1},8,["loading"]),n(h,{onClick:t[4]||(t[4]=u=>Xe.value.visible=!0)},{default:o(()=>t[84]||(t[84]=[x(" 导入 ")])),_:1}),n(h,{onClick:A},{default:o(()=>t[85]||(t[85]=[x(" 导出 ")])),_:1})]),_:1})])]),d("div",xa,[d("div",Ta,[(j(),Fe(be,{data:B(l).roles,style:{width:"100%"},border:"",key:"aiRoles-"+B(l).roles.length},{default:o(()=>[n(Y,{prop:"name",label:"名称","min-width":"120"}),n(Y,{prop:"prompt",label:"提示词","min-width":"200","show-overflow-tooltip":""}),n(Y,{label:"启用",width:"80",align:"center"},{default:o(({row:u})=>[n(he,{modelValue:u.isEnabled,"onUpdate:modelValue":ue=>u.isEnabled=ue,onChange:ue=>m(u,ue)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),n(Y,{label:"操作",width:"200",align:"center",fixed:"right"},{default:o(({row:u})=>[n(te,null,{default:o(()=>[n(h,{type:"primary",size:"small",onClick:ue=>ke(u)},{default:o(()=>t[87]||(t[87]=[x(" 编辑 ")])),_:2},1032,["onClick"]),n(h,{type:"danger",size:"small",onClick:ue=>I(u)},{default:o(()=>t[88]||(t[88]=[x(" 删除 ")])),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"]))])])])]),_:1}),n(Ue,{label:"OpenAI配置",name:"openai-config"},{default:o(()=>[d("div",Va,[d("div",Da,[t[91]||(t[91]=d("h2",{class:"section-title"},"AI模型服务配置",-1)),d("div",Ea,[n(te,null,{default:o(()=>[n(h,{type:"primary",onClick:ns},{default:o(()=>[n(k,null,{default:o(()=>[n(B(dt))]),_:1}),t[89]||(t[89]=x(" 添加服务商 "))]),_:1}),n(h,{onClick:wl,loading:B(e).isLoading},{default:o(()=>[n(k,null,{default:o(()=>[n(B(ct))]),_:1}),t[90]||(t[90]=x(" 刷新 "))]),_:1},8,["loading"])]),_:1})])]),d("div",Na,[B(e).providers.length===0?(j(),ee("div",Ia,[n(_t,{description:"暂无配置的服务商"}),n(h,{type:"primary",onClick:ns,class:"mt-3"},{default:o(()=>t[92]||(t[92]=[x("添加服务商")])),_:1})])):(j(),ee("div",Sa,[n(Tl,{modelValue:kt.value,"onUpdate:modelValue":t[5]||(t[5]=u=>kt.value=u)},{default:o(()=>[(j(!0),ee(tt,null,Mt(B(e).providers,(u,ue)=>(j(),Fe(xl,{key:u.id,name:u.id},{title:o(()=>[d("div",Ua,[d("span",Ba,ne(u.name),1)])]),default:o(()=>[d("div",$a,[n(de,{"label-width":"120px",size:"default"},{default:o(()=>[n(L,{label:"服务商名称"},{default:o(()=>[n(p,{modelValue:u.name,"onUpdate:modelValue":J=>u.name=J,placeholder:"例如: OpenAI, Claude, 智谱AI等"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),n(L,{label:"API基础URL"},{default:o(()=>[n(p,{modelValue:u.baseUrl,"onUpdate:modelValue":J=>u.baseUrl=J,placeholder:"例如: https://api.openai.com/v1"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),n(L,{class:"providers-info"}),d("div",La,[d("div",Ga,[t[95]||(t[95]=d("h3",null,"模型管理",-1)),d("div",null,[n(h,{type:"primary",size:"small",onClick:J=>is(u.id)},{default:o(()=>[n(k,null,{default:o(()=>[n(B(dt))]),_:1}),t[93]||(t[93]=x(" 添加模型 "))]),_:2},1032,["onClick"]),n(h,{type:"success",size:"small",onClick:J=>os(u.id)},{default:o(()=>[n(k,null,{default:o(()=>[n(B(ct))]),_:1}),t[94]||(t[94]=x(" 获取可用模型 "))]),_:2},1032,["onClick"])])]),d("div",Aa,[n(be,{data:u.models,style:{width:"100%"},border:""},{default:o(()=>[n(Y,{label:"模型ID","min-width":"180"},{default:o(({row:J})=>[n(p,{modelValue:J.id,"onUpdate:modelValue":pe=>J.id=pe,placeholder:"模型ID",size:"small",onBlur:pe=>vl(u.id,J)},null,8,["modelValue","onUpdate:modelValue","onBlur"])]),_:2},1024),n(Y,{label:"显示别名","min-width":"150"},{default:o(({row:J})=>[n(p,{modelValue:J.name,"onUpdate:modelValue":pe=>J.name=pe,placeholder:"显示别名",size:"small"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),n(Y,{label:"启用",width:"80",align:"center"},{default:o(({row:J})=>[n(he,{modelValue:J.available,"onUpdate:modelValue":pe=>J.available=pe,onChange:pe=>hl(J)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),n(Y,{label:"参数配置","min-width":"120",align:"center"},{default:o(({row:J})=>[n(h,{type:"primary",size:"small",onClick:pe=>kl(u.id,J)},{default:o(()=>t[96]||(t[96]=[x(" 配置参数 ")])),_:2},1032,["onClick"])]),_:2},1024),n(Y,{label:"操作",width:"100",align:"center"},{default:o(({$index:J})=>[n(h,{type:"danger",size:"small",onClick:pe=>gl(u.id,J)},{default:o(()=>t[97]||(t[97]=[x(" 删除 ")])),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1032,["data"]),u.models.length===0?(j(),ee("div",Pa,[n(_t,{description:"暂无配置的模型","image-size":60}),d("div",Oa,[n(h,{type:"primary",size:"small",onClick:J=>is(u.id),class:"mt-2 mr-2"},{default:o(()=>t[98]||(t[98]=[x(" 手动添加 ")])),_:2},1032,["onClick"]),n(h,{type:"success",size:"small",onClick:J=>os(u.id),class:"mt-2"},{default:o(()=>t[99]||(t[99]=[x(" 获取可用模型 ")])),_:2},1032,["onClick"])])])):xe("",!0)])]),d("div",za,[d("div",Ma,[t[101]||(t[101]=d("h3",null,"API密钥管理",-1)),n(h,{type:"primary",size:"small",onClick:J=>as(u.id)},{default:o(()=>[n(k,null,{default:o(()=>[n(B(dt))]),_:1}),t[100]||(t[100]=x(" 添加密钥 "))]),_:2},1032,["onClick"])]),d("div",Fa,[n(be,{data:u.apiKeys,style:{width:"100%"},border:""},{default:o(()=>[n(Y,{label:"密钥","min-width":"200"},{default:o(({row:J})=>[n(p,{modelValue:J.key,"onUpdate:modelValue":pe=>J.key=pe,placeholder:"API密钥","show-password":""},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),n(Y,{label:"权重",width:"100"},{default:o(({row:J})=>[n(Qe,{modelValue:J.weight,"onUpdate:modelValue":pe=>J.weight=pe,min:1,max:100,"controls-position":"right",size:"small"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),n(Y,{label:"状态",width:"100",align:"center"},{default:o(({row:J})=>[n(se,{type:J.status==="active"?"success":"danger"},{default:o(()=>[x(ne(J.status==="active"?"正常":"异常"),1)]),_:2},1032,["type"])]),_:1}),n(Y,{label:"操作",width:"150",align:"center"},{default:o(({row:J,$index:pe})=>[n(te,null,{default:o(()=>[n(h,{type:"primary",size:"small",onClick:Bl=>ml(u.id,J.id)},{default:o(()=>t[102]||(t[102]=[x(" 测试 ")])),_:2},1032,["onClick"]),n(h,{type:"danger",size:"small",onClick:Bl=>pl(u.id,pe)},{default:o(()=>t[103]||(t[103]=[x(" 删除 ")])),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["data"]),u.apiKeys.length===0?(j(),ee("div",Ra,[n(_t,{description:"暂无配置的API密钥","image-size":60}),n(h,{type:"primary",size:"small",onClick:J=>as(u.id),class:"mt-2"},{default:o(()=>t[104]||(t[104]=[x(" 添加密钥 ")])),_:2},1032,["onClick"])])):xe("",!0)])]),d("div",Ha,[n(te,null,{default:o(()=>[n(h,{type:"danger",onClick:J=>fl(ue)},{default:o(()=>t[105]||(t[105]=[x(" 删除服务商 ")])),_:2},1032,["onClick"]),n(h,{type:"primary",onClick:bl,loading:B(e).isLoading},{default:o(()=>t[106]||(t[106]=[x(" 保存配置 ")])),_:1},8,["loading"])]),_:2},1024)])]),_:2},1024)])]),_:2},1032,["name"]))),128))]),_:1},8,["modelValue"])]))])])]),_:1}),n(Ue,{label:"飞书配置",name:"feishu-config"},{default:o(()=>[d("div",Ja,[t[109]||(t[109]=d("div",{class:"section-header"},[d("h2",{class:"section-title"},"飞书配置")],-1)),d("div",ja,[d("div",Wa,[n(de,{ref_key:"feishuFormRef",ref:Js,model:De.value,"label-width":"150px"},{default:o(()=>[n(L,{label:"App ID",prop:"app_id"},{default:o(()=>[n(p,{modelValue:De.value.app_id,"onUpdate:modelValue":t[6]||(t[6]=u=>De.value.app_id=u),placeholder:"请输入飞书应用的 App ID","show-password":""},null,8,["modelValue"])]),_:1}),n(L,{label:"App Secret",prop:"app_secret"},{default:o(()=>[n(p,{modelValue:De.value.app_secret,"onUpdate:modelValue":t[7]||(t[7]=u=>De.value.app_secret=u),placeholder:"请输入飞书应用的 App Secret","show-password":""},null,8,["modelValue"])]),_:1}),n(L,{label:"Encrypt Key",prop:"encrypt_key"},{default:o(()=>[n(p,{modelValue:De.value.encrypt_key,"onUpdate:modelValue":t[8]||(t[8]=u=>De.value.encrypt_key=u),placeholder:"请输入加密密钥（可选）","show-password":""},null,8,["modelValue"])]),_:1}),n(L,{label:"Verification Token",prop:"verification_token"},{default:o(()=>[n(p,{modelValue:De.value.verification_token,"onUpdate:modelValue":t[9]||(t[9]=u=>De.value.verification_token=u),placeholder:"请输入验证令牌（可选）","show-password":""},null,8,["modelValue"])]),_:1}),n(L,null,{default:o(()=>[n(h,{type:"primary",onClick:ce},{default:o(()=>t[107]||(t[107]=[x("保存配置")])),_:1}),n(h,{onClick:Z},{default:o(()=>t[108]||(t[108]=[x("重置")])),_:1})]),_:1})]),_:1},8,["model"])])])])]),_:1}),n(Ue,{label:"备份配置",name:"backup-config"},{default:o(()=>[d("div",qa,[d("div",Xa,[t[111]||(t[111]=d("h2",{class:"section-title"},"备份设置",-1)),d("div",Za,[n(h,{type:"primary",onClick:Te},{default:o(()=>[n(k,null,{default:o(()=>[n(B(Ot))]),_:1}),t[110]||(t[110]=x(" 立即备份 "))]),_:1})])]),d("div",Qa,[d("div",Ka,[d("div",Ya,[n(de,{model:F.value,"label-width":"100px",size:"small"},{default:o(()=>[n(L,{label:"自动备份",class:"mb-2"},{default:o(()=>[n(he,{modelValue:F.value.autoBackup,"onUpdate:modelValue":t[10]||(t[10]=u=>F.value.autoBackup=u),onChange:ve},null,8,["modelValue"])]),_:1}),F.value.autoBackup?(j(),ee(tt,{key:0},[n(L,{label:"备份间隔",class:"mb-2"},{default:o(()=>[d("div",ei,[n(Qe,{modelValue:F.value.backupInterval,"onUpdate:modelValue":t[11]||(t[11]=u=>F.value.backupInterval=u),min:1,max:1440,step:1,onChange:ze,"controls-position":"right",size:"small"},null,8,["modelValue"]),t[112]||(t[112]=d("span",{class:"ml-2"},"分钟",-1)),n(Ke,{content:"设置自动备份的时间间隔（1-1440分钟）",placement:"top"},{default:o(()=>[n(k,{class:"ml-1"},{default:o(()=>[n(B(Le))]),_:1})]),_:1})])]),_:1}),n(L,{label:"保留数量",class:"mb-2"},{default:o(()=>[d("div",ti,[n(Qe,{modelValue:F.value.keepBackups,"onUpdate:modelValue":t[12]||(t[12]=u=>F.value.keepBackups=u),min:1,max:100,step:1,onChange:qe,"controls-position":"right",size:"small"},null,8,["modelValue"]),n(Ke,{content:"设置要保留的最新备份数量",placement:"top"},{default:o(()=>[n(k,{class:"ml-1"},{default:o(()=>[n(B(Le))]),_:1})]),_:1})])]),_:1})],64)):xe("",!0),n(L,{label:"压缩备份",class:"mb-2"},{default:o(()=>[n(Ke,{content:"启用后将使用ZIP压缩格式进行备份，节省空间但可能增加处理时间",placement:"top"},{default:o(()=>[n(he,{modelValue:F.value.useZip,"onUpdate:modelValue":t[13]||(t[13]=u=>F.value.useZip=u),onChange:Xs},null,8,["modelValue"])]),_:1})]),_:1}),n(L,{label:"备份源目录",class:"mb-2"},{default:o(()=>[d("div",si,[n(p,{modelValue:F.value.backupDir,"onUpdate:modelValue":t[14]||(t[14]=u=>F.value.backupDir=u),placeholder:"建议用软件的backup目录",size:"small"},null,8,["modelValue"]),n(h,{type:"primary",size:"small",onClick:t[15]||(t[15]=u=>D("backupDir"))},{default:o(()=>[n(k,null,{default:o(()=>[n(B(je))]),_:1})]),_:1})])]),_:1}),n(L,{label:"备份目录",class:"mb-2"},{default:o(()=>[d("div",li,[n(p,{modelValue:F.value.targetDir,"onUpdate:modelValue":t[16]||(t[16]=u=>F.value.targetDir=u),placeholder:"备份存储目录",size:"small"},null,8,["modelValue"]),n(h,{type:"primary",size:"small",onClick:t[17]||(t[17]=u=>D("targetDir"))},{default:o(()=>[n(k,null,{default:o(()=>[n(B(je))]),_:1})]),_:1})])]),_:1})]),_:1},8,["model"])]),d("div",ni,[d("div",ai,[t[114]||(t[114]=d("h3",null,"备份历史",-1)),n(te,null,{default:o(()=>[n(h,{type:"primary",size:"small",onClick:Te},{default:o(()=>[n(k,null,{default:o(()=>[n(B(Ot))]),_:1}),t[113]||(t[113]=x(" 立即备份 "))]),_:1}),n(h,{size:"small",onClick:S},{default:o(()=>[n(k,null,{default:o(()=>[n(B(ct))]),_:1})]),_:1})]),_:1})]),d("div",ii,[n(be,{data:Se.value,style:{width:"100%"},border:"",size:"small",height:"100%"},{default:o(()=>[n(Y,{label:"备份时间","min-width":"160"},{default:o(({row:u})=>[x(ne(Ve(u.time))+" ",1),n(se,{type:u.type==="auto"?"success":"primary",size:"small",class:"ml-2"},{default:o(()=>[x(ne(u.type==="auto"?"自动":"手动"),1)]),_:2},1032,["type"])]),_:1}),n(Y,{label:"大小",width:"100"},{default:o(({row:u})=>[x(ne(He(u.size)),1)]),_:1}),n(Y,{label:"路径","min-width":"200","show-overflow-tooltip":""},{default:o(({row:u})=>[x(ne(u.path),1)]),_:1}),n(Y,{label:"操作",width:"200",align:"center",fixed:"right"},{default:o(({row:u})=>[n(te,null,{default:o(()=>[n(h,{type:"primary",size:"small",onClick:ue=>Zs(u)},{default:o(()=>t[115]||(t[115]=[x(" 恢复 ")])),_:2},1032,["onClick"]),n(h,{type:"danger",size:"small",onClick:ue=>W(u)},{default:o(()=>t[116]||(t[116]=[x(" 删除 ")])),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])])])])])])]),_:1}),n(Ue,{label:"聊天设置",name:"chat-settings"},{default:o(()=>[d("div",oi,[d("div",ri,[t[119]||(t[119]=d("h2",{class:"section-title"},"AI聊天界面设置",-1)),d("div",ui,[n(h,{type:"primary",onClick:js},{default:o(()=>[n(k,null,{default:o(()=>[n(B(vs))]),_:1}),t[117]||(t[117]=x(" 保存设置 "))]),_:1}),n(h,{onClick:Ws},{default:o(()=>[n(k,null,{default:o(()=>[n(B(hs))]),_:1}),t[118]||(t[118]=x(" 重置 "))]),_:1})])]),d("div",di,[d("div",ci,[n(de,{"label-width":"140px"},{default:o(()=>[t[125]||(t[125]=d("h3",{class:"subsection-title"},"界面样式",-1)),n(L,{label:"字体大小"},{default:o(()=>[d("div",fi,[n(Ye,{modelValue:ye.value.fontSize,"onUpdate:modelValue":t[18]||(t[18]=u=>ye.value.fontSize=u),min:12,max:20,step:1,"show-input":"","show-input-controls":!1,style:{width:"300px"}},null,8,["modelValue"]),t[120]||(t[120]=d("span",{class:"unit-label"},"像素",-1))])]),_:1}),n(L,{label:"字体"},{default:o(()=>[n(rs,{modelValue:ye.value.fontFamily,"onUpdate:modelValue":t[19]||(t[19]=u=>ye.value.fontFamily=u),style:{width:"300px"}},{default:o(()=>[n(Ce,{label:"微软雅黑",value:"微软雅黑, sans-serif"}),n(Ce,{label:"宋体",value:"宋体, serif"}),n(Ce,{label:"黑体",value:"黑体, sans-serif"}),n(Ce,{label:"楷体",value:"楷体, serif"}),n(Ce,{label:"汉仪旗黑",value:"汉仪旗黑, sans-serif"}),n(Ce,{label:"Arial",value:"Arial, sans-serif"})]),_:1},8,["modelValue"])]),_:1}),t[126]||(t[126]=d("h3",{class:"subsection-title"},"代码块样式",-1)),n(L,{label:"代码块主题"},{default:o(()=>[n(us,{modelValue:ye.value.codeBlockTheme,"onUpdate:modelValue":t[20]||(t[20]=u=>ye.value.codeBlockTheme=u)},{default:o(()=>[n(ot,{value:"auto"},{default:o(()=>t[121]||(t[121]=[x("跟随系统")])),_:1}),n(ot,{value:"light"},{default:o(()=>t[122]||(t[122]=[x("亮色主题")])),_:1}),n(ot,{value:"dark"},{default:o(()=>t[123]||(t[123]=[x("暗色主题")])),_:1})]),_:1},8,["modelValue"])]),_:1}),n(L,{label:"代码块字体大小"},{default:o(()=>[d("div",pi,[n(Ye,{modelValue:ye.value.codeBlockFontSize,"onUpdate:modelValue":t[21]||(t[21]=u=>ye.value.codeBlockFontSize=u),min:12,max:20,step:1,"show-input":"","show-input-controls":!1,style:{width:"300px"}},null,8,["modelValue"]),t[124]||(t[124]=d("span",{class:"unit-label"},"像素",-1))])]),_:1}),n(L,{label:"代码高亮样式"},{default:o(()=>[n(rs,{modelValue:ye.value.codeHighlightStyle,"onUpdate:modelValue":t[22]||(t[22]=u=>ye.value.codeHighlightStyle=u),style:{width:"300px"}},{default:o(()=>[n(Ce,{label:"Tomorrow (亮色)",value:"tomorrow"}),n(Ce,{label:"Tomorrow Night (暗色)",value:"tomorrow-night"}),n(Ce,{label:"Atom One Light",value:"atom-one-light"}),n(Ce,{label:"Atom One Dark",value:"atom-one-dark"}),n(Ce,{label:"Github",value:"github"}),n(Ce,{label:"VS Code",value:"vs2015"})]),_:1},8,["modelValue"])]),_:1})]),_:1})])])])]),_:1}),n(Ue,{label:"Git备份",name:"git-backup"},{default:o(()=>[d("div",mi,[d("div",gi,[t[137]||(t[137]=d("h2",{class:"section-title"},"Git备份配置",-1)),d("div",vi,[n(te,null,{default:o(()=>[n(h,{type:"primary",onClick:Ks},{default:o(()=>[n(k,null,{default:o(()=>[n(B(vs))]),_:1}),t[127]||(t[127]=x(" 保存配置 "))]),_:1}),n(h,{type:"success",onClick:es},{default:o(()=>[n(k,null,{default:o(()=>[n(B(gs))]),_:1}),t[128]||(t[128]=x(" 检测Git "))]),_:1}),n(h,{type:"success",class:"init-button",onClick:il,disabled:!Ze.value||!nt.value},{default:o(()=>[n(k,null,{default:o(()=>[n(B(on))]),_:1}),t[129]||(t[129]=x(" 初始化仓库 "))]),_:1},8,["disabled"]),n(h,{type:"success",onClick:al,disabled:!Ze.value||!nt.value},{default:o(()=>[n(k,null,{default:o(()=>[n(B(Ot))]),_:1}),t[130]||(t[130]=x(" 立即备份 "))]),_:1},8,["disabled"])]),_:1}),n(Dl,{onCommand:ol},{dropdown:o(()=>[n(Vl,null,{default:o(()=>[n(rt,{command:"checkStatus"},{default:o(()=>t[132]||(t[132]=[x("检查仓库状态")])),_:1}),n(rt,{command:"cleanStaged"},{default:o(()=>t[133]||(t[133]=[x("清空暂存区")])),_:1}),n(rt,{command:"resetSoft",divided:""},{default:o(()=>t[134]||(t[134]=[x("撤销上次提交(软)")])),_:1}),n(rt,{command:"resetMixed"},{default:o(()=>t[135]||(t[135]=[x("重置并清空暂存区")])),_:1}),n(rt,{command:"resetHard"},{default:o(()=>t[136]||(t[136]=[d("span",{style:{color:"#F56C6C"}},"强制重置(危险)",-1)])),_:1})]),_:1})]),default:o(()=>[n(h,{type:"success",disabled:!Ze.value||!nt.value},{default:o(()=>[n(k,null,{default:o(()=>[n(B(un))]),_:1}),t[131]||(t[131]=x(" 更多操作 ")),n(k,{class:"el-icon--right"},{default:o(()=>[n(B(dn))]),_:1})]),_:1},8,["disabled"])]),_:1})])]),d("div",hi,[d("div",bi,[d("div",yi,[!bt.value&&!Pe.value?.installed?(j(),ee("div",wi,[n(ut,{title:"未检测到Git安装",type:"warning",description:"请先安装Git客户端，然后点击'检测Git'按钮重新检测。","show-icon":"",closable:!1})])):bt.value?(j(),ee("div",ki,[n(ut,{title:"正在检测Git安装...",type:"info","show-icon":"",closable:!1})])):Pe.value?.installed?(j(),ee("div",_i,[n(ut,{title:`Git已安装: ${Pe.value.version}`,type:"success","show-icon":"",closable:!1},null,8,["title"])])):xe("",!0),d("div",Ci,[n(de,{"label-width":"140px",model:v.value},{default:o(()=>[t[150]||(t[150]=d("h3",{class:"subsection-title"},"基本设置",-1)),n(L,{label:"Git仓库URL",required:""},{default:o(()=>[n(p,{modelValue:v.value.repoUrl,"onUpdate:modelValue":t[23]||(t[23]=u=>v.value.repoUrl=u),placeholder:"例如: https://e.coding.net/aiwork/author/pvv_backup.git"},null,8,["modelValue"])]),_:1}),n(L,{label:"认证方式"},{default:o(()=>[n(us,{modelValue:v.value.authType,"onUpdate:modelValue":t[24]||(t[24]=u=>v.value.authType=u)},{default:o(()=>[n(ot,{value:"password"},{default:o(()=>t[138]||(t[138]=[x("用户名和密码")])),_:1}),n(ot,{value:"token"},{default:o(()=>t[139]||(t[139]=[x("访问令牌")])),_:1})]),_:1},8,["modelValue"]),d("div",xi,[n(k,null,{default:o(()=>[n(B(Le))]),_:1}),t[140]||(t[140]=x(" 推荐使用访问令牌，可以限制只访问特定仓库 "))])]),_:1}),v.value.authType==="password"?(j(),ee(tt,{key:0},[n(L,{label:"Git用户名",required:""},{default:o(()=>[n(p,{modelValue:v.value.username,"onUpdate:modelValue":t[25]||(t[25]=u=>v.value.username=u),placeholder:"Git用户名"},null,8,["modelValue"])]),_:1}),n(L,{label:"Git密码",required:""},{default:o(()=>[d("div",Ti,[n(p,{modelValue:v.value.password,"onUpdate:modelValue":t[26]||(t[26]=u=>v.value.password=u),placeholder:"Git密码","show-password":""},null,8,["modelValue"]),n(h,{type:"primary",onClick:ts,disabled:!v.value.repoUrl||!v.value.username||!v.value.password},{default:o(()=>[n(k,null,{default:o(()=>[n(B(bs))]),_:1}),t[141]||(t[141]=x(" 测试连接 "))]),_:1},8,["disabled"])]),d("div",Vi,[n(k,null,{default:o(()=>[n(B($s))]),_:1}),t[142]||(t[142]=x(" 警告：使用密码方式会授予对所有仓库的访问权限 "))])]),_:1})],64)):(j(),ee(tt,{key:1},[n(L,{label:"Git用户名",required:""},{default:o(()=>[n(p,{modelValue:v.value.tokenUsername,"onUpdate:modelValue":t[27]||(t[27]=u=>v.value.tokenUsername=u),placeholder:"Git用户名（用于访问令牌认证）"},null,8,["modelValue"]),d("div",Di,[n(k,null,{default:o(()=>[n(B(Le))]),_:1}),t[143]||(t[143]=x(" 对于Coding平台，此处需填写您的用户名 "))])]),_:1}),n(L,{label:"访问令牌",required:""},{default:o(()=>[d("div",Ei,[n(p,{modelValue:v.value.token,"onUpdate:modelValue":t[28]||(t[28]=u=>v.value.token=u),placeholder:"Git个人访问令牌","show-password":""},null,8,["modelValue"]),n(h,{type:"primary",onClick:ts,disabled:!v.value.repoUrl||!v.value.token||St.value&&!v.value.tokenUsername,class:"test-connection-btn"},{default:o(()=>[n(k,null,{default:o(()=>[n(B(bs))]),_:1}),t[144]||(t[144]=x(" 测试连接 "))]),_:1},8,["disabled"])]),d("div",Ni,[n(k,null,{default:o(()=>[n(B(Le))]),_:1}),t[146]||(t[146]=x(" 访问令牌可以限制只访问特定仓库，提高安全性 ")),n(El,{type:"primary",onClick:ll,class:"ml-2"},{default:o(()=>t[145]||(t[145]=[x("如何创建?")])),_:1})])]),_:1})],64)),n(L,{label:"备份内容目录",required:""},{default:o(()=>[d("div",Ii,[n(p,{modelValue:v.value.backupDir,"onUpdate:modelValue":t[29]||(t[29]=u=>v.value.backupDir=u),placeholder:"选择需要备份的目录"},null,8,["modelValue"]),n(h,{type:"primary",onClick:t[30]||(t[30]=u=>D("gitBackupDir"))},{default:o(()=>[n(k,null,{default:o(()=>[n(B(je))]),_:1}),t[147]||(t[147]=x(" 选择目录 "))]),_:1})]),t[148]||(t[148]=d("div",{class:"form-item-tip"}," 指定需要备份到Git的内容目录，通常是项目数据所在目录 ",-1))]),_:1}),n(L,{label:"自动备份"},{default:o(()=>[n(he,{modelValue:v.value.autoBackup,"onUpdate:modelValue":t[31]||(t[31]=u=>v.value.autoBackup=u),onChange:tl},null,8,["modelValue"]),v.value.autoBackup?(j(),ee("div",Si," 系统将按照设定的时间间隔自动进行Git备份 ")):xe("",!0)]),_:1}),v.value.autoBackup?(j(),Fe(L,{key:2,label:"备份间隔"},{default:o(()=>[d("div",Ui,[n(Qe,{modelValue:v.value.backupInterval,"onUpdate:modelValue":t[32]||(t[32]=u=>v.value.backupInterval=u),min:10,max:1440,step:10,"controls-position":"right"},null,8,["modelValue"]),t[149]||(t[149]=d("span",{class:"ml-2"},"分钟",-1)),n(Ke,{content:"设置自动备份的时间间隔（10-1440分钟）",placement:"top"},{default:o(()=>[n(k,{class:"ml-1"},{default:o(()=>[n(B(Le))]),_:1})]),_:1})])]),_:1})):xe("",!0)]),_:1},8,["model"])])]),d("div",Bi,[d("div",$i,[t[152]||(t[152]=d("h3",{class:"history-title"},"备份历史记录",-1)),n(h,{onClick:at,loading:E.value,class:"refresh-button",type:"primary",plain:"",size:"small"},{default:o(()=>[n(k,null,{default:o(()=>[n(B(ct))]),_:1}),t[151]||(t[151]=x(" 刷新历史 "))]),_:1},8,["loading"])]),_.value?(j(),ee("div",Li,[n(ut,{title:_.value,type:"error","show-icon":"",closable:!1},null,8,["title"])])):Oe.value.length>0?(j(),Fe(Il,{key:1,class:"custom-timeline"},{default:o(()=>[(j(!0),ee(tt,null,Mt(Oe.value,u=>(j(),Fe(Nl,{key:u.hash,timestamp:u.date,color:u.isHead?"var(--el-color-success)":"var(--el-color-primary)",class:"timeline-item"},{default:o(()=>[d("div",Gi,[d("div",Ai,[d("span",{class:Us(["commit-badge",{"current-badge":u.isHead}])},ne(u.isHead?"当前版本":"历史版本"),3),d("span",Pi,ne(cl(u.date)),1)]),d("h4",Oi,ne(u.message),1),d("div",zi,[d("div",Mi,[n(se,{size:"small",effect:"dark",class:"hash-tag"},{default:o(()=>[x(ne(u.hash.substring(0,7)),1)]),_:2},1024),d("span",Fi,ne(u.author),1)]),d("div",Ri,[n(Ke,{content:"查看详情",placement:"top",enterable:!1},{default:o(()=>[n(h,{size:"small",circle:"",onClick:ue=>rl(u)},{default:o(()=>[n(k,null,{default:o(()=>[n(B(ys))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024),n(Ke,{content:"恢复到此版本",placement:"top",enterable:!1},{default:o(()=>[n(h,{size:"small",circle:"",type:"warning",onClick:ue=>ul(u),disabled:u.isHead},{default:o(()=>[n(k,null,{default:o(()=>[n(B(hs))]),_:1})]),_:2},1032,["onClick","disabled"])]),_:2},1024)])])])]),_:2},1032,["timestamp","color"]))),128))]),_:1})):E.value?xe("",!0):(j(),ee("div",Hi,[n(_t,{description:"暂无备份历史"})]))])])])])]),_:1})]),_:1},8,["modelValue"])])),[[cs,B(s).isLoading||B(e).isLoading,void 0,{fullscreen:!0,lock:!0}]]),n(Be,{modelValue:H.value.visible,"onUpdate:modelValue":t[43]||(t[43]=u=>H.value.visible=u),title:H.value.isEdit?"编辑用户数据目录":"添加用户数据目录",width:"600px"},{footer:o(()=>[d("span",Wi,[n(h,{onClick:t[42]||(t[42]=u=>H.value.visible=!1)},{default:o(()=>t[155]||(t[155]=[x("取消")])),_:1}),n(h,{type:"primary",onClick:P},{default:o(()=>t[156]||(t[156]=[x("确定")])),_:1})])]),default:o(()=>[n(de,{ref_key:"userDataDirFormRef",ref:_e,model:H.value.form,rules:Me,"label-width":"120px"},{default:o(()=>[n(L,{label:"名称",prop:"name"},{default:o(()=>[n(p,{modelValue:H.value.form.name,"onUpdate:modelValue":t[34]||(t[34]=u=>H.value.form.name=u),placeholder:"请输入名称"},null,8,["modelValue"])]),_:1}),n(L,{label:"路径",prop:"path"},{default:o(()=>[d("div",Ji,[n(p,{modelValue:H.value.form.path,"onUpdate:modelValue":t[35]||(t[35]=u=>H.value.form.path=u),placeholder:"请选择路径"},null,8,["modelValue"]),n(h,{type:"primary",onClick:t[36]||(t[36]=u=>D("userDataPath"))},{default:o(()=>[n(k,null,{default:o(()=>[n(B(je))]),_:1}),t[153]||(t[153]=x(" 选择路径 "))]),_:1})])]),_:1}),n(L,{label:"端口",prop:"port"},{default:o(()=>[n(Qe,{modelValue:H.value.form.port,"onUpdate:modelValue":t[37]||(t[37]=u=>H.value.form.port=u),min:1024,max:65535},null,8,["modelValue"])]),_:1}),n(L,{label:"设为默认"},{default:o(()=>[n(he,{modelValue:H.value.form.isDefault,"onUpdate:modelValue":t[38]||(t[38]=u=>H.value.form.isDefault=u)},null,8,["modelValue"])]),_:1}),n(L,{label:"启用扩展"},{default:o(()=>[n(he,{modelValue:H.value.form.enableExtensions,"onUpdate:modelValue":t[39]||(t[39]=u=>H.value.form.enableExtensions=u)},null,8,["modelValue"])]),_:1}),H.value.form.enableExtensions?(j(),Fe(L,{key:0,label:"扩展路径",prop:"extensionsPath"},{default:o(()=>[d("div",ji,[n(p,{modelValue:H.value.form.extensionsPath,"onUpdate:modelValue":t[40]||(t[40]=u=>H.value.form.extensionsPath=u),placeholder:"请选择扩展路径"},null,8,["modelValue"]),n(h,{type:"primary",onClick:t[41]||(t[41]=u=>D("extensionsPath"))},{default:o(()=>[n(k,null,{default:o(()=>[n(B(je))]),_:1}),t[154]||(t[154]=x(" 选择路径 "))]),_:1})])]),_:1})):xe("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),n(Be,{modelValue:q.value.visible,"onUpdate:modelValue":t[48]||(t[48]=u=>q.value.visible=u),title:q.value.title,width:"500px","destroy-on-close":""},{footer:o(()=>[d("div",qi,[n(h,{onClick:t[47]||(t[47]=u=>q.value.visible=!1)},{default:o(()=>t[157]||(t[157]=[x("取消")])),_:1}),n(h,{type:"primary",onClick:T},{default:o(()=>t[158]||(t[158]=[x("保存")])),_:1})])]),default:o(()=>[n(de,{model:q.value.form,"label-width":"100px"},{default:o(()=>[n(L,{label:"角色名称",required:""},{default:o(()=>[n(p,{modelValue:q.value.form.name,"onUpdate:modelValue":t[44]||(t[44]=u=>q.value.form.name=u),placeholder:"请输入角色名称"},null,8,["modelValue"])]),_:1}),n(L,{label:"提示词",required:""},{default:o(()=>[n(p,{modelValue:q.value.form.prompt,"onUpdate:modelValue":t[45]||(t[45]=u=>q.value.form.prompt=u),type:"textarea",rows:8,placeholder:"请输入角色提示词"},null,8,["modelValue"])]),_:1}),n(L,{label:"启用"},{default:o(()=>[n(he,{modelValue:q.value.form.isEnabled,"onUpdate:modelValue":t[46]||(t[46]=u=>q.value.form.isEnabled=u)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),n(Be,{modelValue:Xe.value.visible,"onUpdate:modelValue":t[51]||(t[51]=u=>Xe.value.visible=u),title:"导入AI角色",width:"600px","destroy-on-close":""},{footer:o(()=>[d("div",Xi,[n(h,{onClick:t[50]||(t[50]=u=>Xe.value.visible=!1)},{default:o(()=>t[159]||(t[159]=[x("取消")])),_:1}),n(h,{type:"primary",onClick:a.importAIRoles},{default:o(()=>t[160]||(t[160]=[x("导入")])),_:1},8,["onClick"])])]),default:o(()=>[t[161]||(t[161]=d("div",{class:"import-tip mb-3"}," 请粘贴符合格式的JSON数据，可以粘贴单个角色或角色数组 ",-1)),n(p,{modelValue:Xe.value.content,"onUpdate:modelValue":t[49]||(t[49]=u=>Xe.value.content=u),type:"textarea",rows:10,placeholder:"粘贴JSON数据"},null,8,["modelValue"])]),_:1},8,["modelValue"]),n(Be,{modelValue:K.value.visible,"onUpdate:modelValue":t[59]||(t[59]=u=>K.value.visible=u),title:`配置模型参数 - ${K.value.modelName}`,width:"600px","destroy-on-close":""},{footer:o(()=>[d("div",to,[n(h,{onClick:t[58]||(t[58]=u=>K.value.visible=!1)},{default:o(()=>t[168]||(t[168]=[x("取消")])),_:1}),n(h,{type:"primary",onClick:_l},{default:o(()=>t[169]||(t[169]=[x("保存配置")])),_:1}),n(h,{onClick:Cl},{default:o(()=>t[170]||(t[170]=[x("重置为默认")])),_:1})])]),default:o(()=>[n(de,{model:K.value.form,"label-width":"120px"},{default:o(()=>[n(L,{label:"温度 (Temperature)"},{default:o(()=>[d("div",Zi,[n(Ye,{modelValue:K.value.form.temperature,"onUpdate:modelValue":t[52]||(t[52]=u=>K.value.form.temperature=u),min:0,max:2,step:.1,"show-input":"","show-input-controls":!1,style:{width:"300px"}},null,8,["modelValue"]),t[162]||(t[162]=d("div",{class:"param-tip"},"控制输出的随机性，值越高越随机",-1))])]),_:1}),n(L,{label:"最大Token数"},{default:o(()=>[d("div",Qi,[n(Qe,{modelValue:K.value.form.max_tokens,"onUpdate:modelValue":t[53]||(t[53]=u=>K.value.form.max_tokens=u),min:1,max:131072,step:256,"controls-position":"right",style:{width:"200px"}},null,8,["modelValue"]),t[163]||(t[163]=d("div",{class:"param-tip"},"限制生成文本的最大长度",-1))])]),_:1}),n(L,{label:"Top P"},{default:o(()=>[d("div",Ki,[n(Ye,{modelValue:K.value.form.top_p,"onUpdate:modelValue":t[54]||(t[54]=u=>K.value.form.top_p=u),min:0,max:1,step:.1,"show-input":"","show-input-controls":!1,style:{width:"300px"}},null,8,["modelValue"]),t[164]||(t[164]=d("div",{class:"param-tip"},"控制词汇选择的多样性",-1))])]),_:1}),n(L,{label:"频率惩罚"},{default:o(()=>[d("div",Yi,[n(Ye,{modelValue:K.value.form.frequency_penalty,"onUpdate:modelValue":t[55]||(t[55]=u=>K.value.form.frequency_penalty=u),min:-2,max:2,step:.1,"show-input":"","show-input-controls":!1,style:{width:"300px"}},null,8,["modelValue"]),t[165]||(t[165]=d("div",{class:"param-tip"},"减少重复词汇的出现频率",-1))])]),_:1}),n(L,{label:"存在惩罚"},{default:o(()=>[d("div",eo,[n(Ye,{modelValue:K.value.form.presence_penalty,"onUpdate:modelValue":t[56]||(t[56]=u=>K.value.form.presence_penalty=u),min:-2,max:2,step:.1,"show-input":"","show-input-controls":!1,style:{width:"300px"}},null,8,["modelValue"]),t[166]||(t[166]=d("div",{class:"param-tip"},"鼓励谈论新话题",-1))])]),_:1}),n(L,{label:"流式输出"},{default:o(()=>[n(he,{modelValue:K.value.form.stream,"onUpdate:modelValue":t[57]||(t[57]=u=>K.value.form.stream=u)},null,8,["modelValue"]),t[167]||(t[167]=d("div",{class:"param-tip"},"启用实时流式输出",-1))]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),n(Be,{modelValue:R.value.visible,"onUpdate:modelValue":t[61]||(t[61]=u=>R.value.visible=u),title:"备份进度",width:"500px","close-on-click-modal":!1,"close-on-press-escape":!1,"show-close":R.value.status!=="normal"},ms({default:o(()=>[d("div",so,[n(ds,{percentage:R.value.percent,status:R.value.status},null,8,["percentage","status"]),d("div",lo,ne(R.value.message),1)])]),_:2},[R.value.status!=="normal"||R.value.percent===100?{name:"footer",fn:o(()=>[R.value.status==="success"?(j(),ee("div",no,[t[171]||(t[171]=d("p",null,"备份完成！文件已保存至：",-1)),d("div",ao,ne(R.value.backupPath),1)])):xe("",!0),d("div",io,[n(h,{onClick:t[60]||(t[60]=u=>R.value.visible=!1)},{default:o(()=>t[172]||(t[172]=[x("关闭")])),_:1}),R.value.status==="exception"?(j(),Fe(h,{key:0,type:"primary",onClick:qs},{default:o(()=>t[173]||(t[173]=[x("重试")])),_:1})):xe("",!0)])]),key:"0"}:void 0]),1032,["modelValue","show-close"]),n(Be,{modelValue:Ee.value.visible,"onUpdate:modelValue":t[63]||(t[63]=u=>Ee.value.visible=u),title:"提交差异",width:"700px"},{footer:o(()=>[d("span",uo,[n(h,{onClick:t[62]||(t[62]=u=>Ee.value.visible=!1)},{default:o(()=>t[174]||(t[174]=[x("关闭")])),_:1})])]),default:o(()=>[ps((j(),ee("div",oo,[d("pre",ro,ne(Ee.value.content),1)])),[[cs,Ee.value.loading]])]),_:1},8,["modelValue"]),n(Be,{modelValue:Ne.value.visible,"onUpdate:modelValue":t[65]||(t[65]=u=>Ne.value.visible=u),title:"Git备份进度",width:"500px","close-on-click-modal":!1,"close-on-press-escape":!1,"show-close":Ne.value.status!=="normal"},ms({default:o(()=>[d("div",co,[n(ds,{percentage:Ne.value.percent,status:Ne.value.status},null,8,["percentage","status"]),d("div",fo,ne(Ne.value.message),1)])]),_:2},[Ne.value.status!=="normal"||Ne.value.percent===100?{name:"footer",fn:o(()=>[d("div",po,[n(h,{onClick:t[64]||(t[64]=u=>Ne.value.visible=!1)},{default:o(()=>t[175]||(t[175]=[x("关闭")])),_:1}),Ne.value.status==="exception"?(j(),Fe(h,{key:0,type:"primary",onClick:sl},{default:o(()=>t[176]||(t[176]=[x("重试")])),_:1})):xe("",!0)])]),key:"0"}:void 0]),1032,["modelValue","show-close"]),n(Be,{modelValue:Ut.value,"onUpdate:modelValue":t[66]||(t[66]=u=>Ut.value=u),title:"如何创建Git访问令牌",width:"650px"},{default:o(()=>[d("div",mo,[t[177]||(t[177]=d("h3",null,"GitHub访问令牌创建步骤",-1)),t[178]||(t[178]=d("ol",null,[d("li",null,"登录您的GitHub账户"),d("li",null,"点击右上角头像 → Settings → Developer settings → Personal access tokens → Tokens (classic)"),d("li",null,'点击"Generate new token" → "Generate new token (classic)"'),d("li",null,'填写令牌描述，例如"PVV备份"'),d("li",null,"设置适当的过期时间"),d("li",null,'权限选择：建议只勾选"repo"权限（这只会授予对仓库的访问权限）'),d("li",null,'点击"Generate token"'),d("li",null,"复制生成的令牌（注意：令牌只会显示一次）")],-1)),t[179]||(t[179]=d("h3",null,"Gitee(码云)访问令牌创建步骤",-1)),t[180]||(t[180]=d("ol",null,[d("li",null,"登录您的Gitee账户"),d("li",null,"点击右上角头像 → 设置 → 私人令牌"),d("li",null,'点击"生成新令牌"'),d("li",null,"填写令牌描述"),d("li",null,'权限选择：建议只勾选"projects"权限'),d("li",null,'点击"提交"'),d("li",null,"复制生成的令牌")],-1)),t[181]||(t[181]=d("h3",null,"Coding 访问令牌创建与使用说明",-1)),t[182]||(t[182]=d("ol",null,[d("li",null,"登录您的 Coding 账户"),d("li",null,"点击右上角头像 → 个人设置"),d("li",null,'在左侧菜单栏选择"访问令牌"'),d("li",null,'点击"新建令牌"按钮'),d("li",null,'填写令牌名称，如"PVV备份"'),d("li",null,"设置过期时间（可选择永不过期或特定日期）"),d("li",null,'权限选择：勾选"项目"相关权限，建议选择"代码库 - 读写"权限'),d("li",null,'单击"新建"按钮'),d("li",null,"复制生成的令牌（注意：令牌只会显示一次）"),d("li",null,[d("strong",null,"使用方式"),x('：当选择访问令牌认证方式时，需要在"用户名"字段填写您的Coding用户名，在"令牌"字段填写获取的访问令牌')])],-1)),t[183]||(t[183]=d("h3",null,"其他Git平台",-1)),t[184]||(t[184]=d("p",null,"大多数Git平台都支持个人访问令牌功能，请参考相应平台的文档进行创建。",-1)),d("div",go,[n(ut,{title:"安全提示",type:"warning",description:"访问令牌与密码一样重要，请妥善保管，不要泄露给他人。建议设置合理的过期时间并定期更新。","show-icon":"",closable:!1})])])]),_:1},8,["modelValue"]),n(Be,{modelValue:Je.value,"onUpdate:modelValue":t[71]||(t[71]=u=>Je.value=u),title:"提交备份",width:"500px","close-on-click-modal":!1},{footer:o(()=>[d("span",wo,[n(h,{onClick:t[70]||(t[70]=u=>Je.value=!1)},{default:o(()=>t[190]||(t[190]=[x("取消")])),_:1}),n(h,{type:"primary",onClick:ss,loading:$t.value},{default:o(()=>t[191]||(t[191]=[x(" 提交备份 ")])),_:1},8,["loading"])])]),default:o(()=>[n(de,{model:we,"label-width":"100px"},{default:o(()=>[n(L,{label:"提交说明",required:""},{default:o(()=>[n(p,{modelValue:we.message,"onUpdate:modelValue":t[67]||(t[67]=u=>we.message=u),type:"textarea",rows:4,placeholder:"请输入本次备份的简要说明"},null,8,["modelValue"]),d("div",vo,[n(k,null,{default:o(()=>[n(B(Le))]),_:1}),t[185]||(t[185]=x(" 建议使用英文描述，避免中文字符造成编码问题 "))])]),_:1}),n(L,null,{default:o(()=>[n(Ul,{modelValue:we.force,"onUpdate:modelValue":t[68]||(t[68]=u=>we.force=u)},{default:o(()=>t[186]||(t[186]=[x(" 强制备份（即使没有检测到变更） ")])),_:1},8,["modelValue"]),d("div",ho,[n(k,null,{default:o(()=>[n(B(Le))]),_:1}),t[187]||(t[187]=x(" 选择后将忽略文件变更检查，强制执行备份操作 "))])]),_:1}),d("div",bo,[n(h,{type:"primary",plain:"",onClick:dl,loading:wt.value},{default:o(()=>[n(k,null,{default:o(()=>[n(B(ys))]),_:1}),t[188]||(t[188]=x(" 查看变更内容 "))]),_:1},8,["loading"])]),n(L,{label:"标签名称"},{default:o(()=>[n(p,{modelValue:we.tagName,"onUpdate:modelValue":t[69]||(t[69]=u=>we.tagName=u),placeholder:"例如: v1.0.0 或 完成第一章"},null,8,["modelValue"]),d("div",yo,[n(k,null,{default:o(()=>[n(B(Le))]),_:1}),t[189]||(t[189]=x(" 为此备份创建一个有意义的标签名，方便后续查找和恢复 "))])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),n(Be,{modelValue:yt.value,"onUpdate:modelValue":t[73]||(t[73]=u=>yt.value=u),title:"变更内容预览",width:"80%",top:"5vh","close-on-click-modal":!1,fullscreen:""},{footer:o(()=>[d("span",_o,[n(h,{onClick:t[72]||(t[72]=u=>yt.value=!1)},{default:o(()=>t[192]||(t[192]=[x("关闭")])),_:1}),n(h,{type:"primary",onClick:ss},{default:o(()=>t[193]||(t[193]=[x(" 确认提交 ")])),_:1})])]),default:o(()=>[d("div",ko,[n(da,{"diff-text":Gt.value,"changed-files":At.value,loading:wt.value,error:it.value},null,8,["diff-text","changed-files","loading","error"])])]),_:1},8,["modelValue"])])}}},Jo=Ss(Co,[["__scopeId","data-v-d3c250ff"]]);export{Jo as default};
