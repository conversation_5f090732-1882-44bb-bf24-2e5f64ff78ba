var bt=Object.defineProperty;var mt=(i,e,t)=>e in i?bt(i,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[e]=t;var T=(i,e,t)=>mt(i,typeof e!="symbol"?e+"":e,t);import{p as W}from"./purify-es-DhD2mIk-.js";import{_ as Ze,Z as I,r as V,c as j,o as qe,$ as Ke,b as N,m as R,e as E,F as Z,n as F,p as B,v as kt,cx as wt,W as We,E as Le,w as _t,d as H,g as X,C as ae,aS as yt,B as xt,aN as vt,N as Et,h as St,s as Tt,R as At,S as Rt,M as Nt,bk as Ie,Y as $e}from"./entry-DxFfH4M0.js";/* empty css                 */function Ee(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let Q=Ee();function Ve(i){Q=i}const ce={exec:()=>null};function S(i,e=""){let t=typeof i=="string"?i:i.source;const n={replace:(s,a)=>{let r=typeof a=="string"?a:a.source;return r=r.replace(O.caret,"$1"),t=t.replace(s,r),n},getRegex:()=>new RegExp(t,e)};return n}const O={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:i=>new RegExp(`^( {0,3}${i})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:i=>new RegExp(`^ {0,${Math.min(3,i-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:i=>new RegExp(`^ {0,${Math.min(3,i-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:i=>new RegExp(`^ {0,${Math.min(3,i-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:i=>new RegExp(`^ {0,${Math.min(3,i-1)}}#`),htmlBeginRegex:i=>new RegExp(`^ {0,${Math.min(3,i-1)}}<(?:[a-z].*>|!--)`,"i")},Ct=/^(?:[ \t]*(?:\n|$))+/,Mt=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,Ot=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,de=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,Lt=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,Se=/(?:[*+-]|\d{1,9}[.)])/,je=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,Ye=S(je).replace(/bull/g,Se).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),It=S(je).replace(/bull/g,Se).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),Te=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,$t=/^[^\n]+/,Ae=/(?!\s*\])(?:\\.|[^\[\]\\])+/,Bt=S(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",Ae).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),zt=S(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,Se).getRegex(),we="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Re=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,Dt=S("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",Re).replace("tag",we).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Qe=S(Te).replace("hr",de).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",we).getRegex(),Pt=S(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Qe).getRegex(),Ne={blockquote:Pt,code:Mt,def:Bt,fences:Ot,heading:Lt,hr:de,html:Dt,lheading:Ye,list:zt,newline:Ct,paragraph:Qe,table:ce,text:$t},Be=S("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",de).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",we).getRegex(),Ut={...Ne,lheading:It,table:Be,paragraph:S(Te).replace("hr",de).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",Be).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",we).getRegex()},Ht={...Ne,html:S(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Re).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:ce,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:S(Te).replace("hr",de).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Ye).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},Gt=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,Ft=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,Xe=/^( {2,}|\\)\n(?!\s*$)/,Zt=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,_e=/[\p{P}\p{S}]/u,Ce=/[\s\p{P}\p{S}]/u,Je=/[^\s\p{P}\p{S}]/u,qt=S(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,Ce).getRegex(),et=/(?!~)[\p{P}\p{S}]/u,Kt=/(?!~)[\s\p{P}\p{S}]/u,Wt=/(?:[^\s\p{P}\p{S}]|~)/u,Vt=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,tt=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,jt=S(tt,"u").replace(/punct/g,_e).getRegex(),Yt=S(tt,"u").replace(/punct/g,et).getRegex(),nt="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",Qt=S(nt,"gu").replace(/notPunctSpace/g,Je).replace(/punctSpace/g,Ce).replace(/punct/g,_e).getRegex(),Xt=S(nt,"gu").replace(/notPunctSpace/g,Wt).replace(/punctSpace/g,Kt).replace(/punct/g,et).getRegex(),Jt=S("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,Je).replace(/punctSpace/g,Ce).replace(/punct/g,_e).getRegex(),en=S(/\\(punct)/,"gu").replace(/punct/g,_e).getRegex(),tn=S(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),nn=S(Re).replace("(?:-->|$)","-->").getRegex(),sn=S("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",nn).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),fe=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,rn=S(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",fe).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),st=S(/^!?\[(label)\]\[(ref)\]/).replace("label",fe).replace("ref",Ae).getRegex(),rt=S(/^!?\[(ref)\](?:\[\])?/).replace("ref",Ae).getRegex(),an=S("reflink|nolink(?!\\()","g").replace("reflink",st).replace("nolink",rt).getRegex(),Me={_backpedal:ce,anyPunctuation:en,autolink:tn,blockSkip:Vt,br:Xe,code:Ft,del:ce,emStrongLDelim:jt,emStrongRDelimAst:Qt,emStrongRDelimUnd:Jt,escape:Gt,link:rn,nolink:rt,punctuation:qt,reflink:st,reflinkSearch:an,tag:sn,text:Zt,url:ce},on={...Me,link:S(/^!?\[(label)\]\((.*?)\)/).replace("label",fe).getRegex(),reflink:S(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",fe).getRegex()},ve={...Me,emStrongRDelimAst:Xt,emStrongLDelim:Yt,url:S(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},ln={...ve,br:S(Xe).replace("{2,}","*").getRegex(),text:S(ve.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},pe={normal:Ne,gfm:Ut,pedantic:Ht},oe={normal:Me,gfm:ve,breaks:ln,pedantic:on},cn={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},ze=i=>cn[i];function G(i,e){if(e){if(O.escapeTest.test(i))return i.replace(O.escapeReplace,ze)}else if(O.escapeTestNoEncode.test(i))return i.replace(O.escapeReplaceNoEncode,ze);return i}function De(i){try{i=encodeURI(i).replace(O.percentDecode,"%")}catch{return null}return i}function Pe(i,e){const t=i.replace(O.findPipe,(a,r,o)=>{let l=!1,c=r;for(;--c>=0&&o[c]==="\\";)l=!l;return l?"|":" |"}),n=t.split(O.splitPipe);let s=0;if(n[0].trim()||n.shift(),n.length>0&&!n.at(-1)?.trim()&&n.pop(),e)if(n.length>e)n.splice(e);else for(;n.length<e;)n.push("");for(;s<n.length;s++)n[s]=n[s].trim().replace(O.slashPipe,"|");return n}function le(i,e,t){const n=i.length;if(n===0)return"";let s=0;for(;s<n&&i.charAt(n-s-1)===e;)s++;return i.slice(0,n-s)}function un(i,e){if(i.indexOf(e[1])===-1)return-1;let t=0;for(let n=0;n<i.length;n++)if(i[n]==="\\")n++;else if(i[n]===e[0])t++;else if(i[n]===e[1]&&(t--,t<0))return n;return-1}function Ue(i,e,t,n,s){const a=e.href,r=e.title||null,o=i[1].replace(s.other.outputLinkReplace,"$1");if(i[0].charAt(0)!=="!"){n.state.inLink=!0;const l={type:"link",raw:t,href:a,title:r,text:o,tokens:n.inlineTokens(o)};return n.state.inLink=!1,l}return{type:"image",raw:t,href:a,title:r,text:o}}function dn(i,e,t){const n=i.match(t.other.indentCodeCompensation);if(n===null)return e;const s=n[1];return e.split(`
`).map(a=>{const r=a.match(t.other.beginningSpace);if(r===null)return a;const[o]=r;return o.length>=s.length?a.slice(s.length):a}).join(`
`)}class be{constructor(e){T(this,"options");T(this,"rules");T(this,"lexer");this.options=e||Q}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const n=t[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?n:le(n,`
`)}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const n=t[0],s=dn(n,t[3]||"",this.rules);return{type:"code",raw:n,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:s}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let n=t[2].trim();if(this.rules.other.endingHash.test(n)){const s=le(n,"#");(this.options.pedantic||!s||this.rules.other.endingSpaceChar.test(s))&&(n=s.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:n,tokens:this.lexer.inline(n)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:le(t[0],`
`)}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){let n=le(t[0],`
`).split(`
`),s="",a="";const r=[];for(;n.length>0;){let o=!1;const l=[];let c;for(c=0;c<n.length;c++)if(this.rules.other.blockquoteStart.test(n[c]))l.push(n[c]),o=!0;else if(!o)l.push(n[c]);else break;n=n.slice(c);const p=l.join(`
`),f=p.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");s=s?`${s}
${p}`:p,a=a?`${a}
${f}`:f;const b=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(f,r,!0),this.lexer.state.top=b,n.length===0)break;const m=r.at(-1);if(m?.type==="code")break;if(m?.type==="blockquote"){const _=m,k=_.raw+`
`+n.join(`
`),u=this.blockquote(k);r[r.length-1]=u,s=s.substring(0,s.length-_.raw.length)+u.raw,a=a.substring(0,a.length-_.text.length)+u.text;break}else if(m?.type==="list"){const _=m,k=_.raw+`
`+n.join(`
`),u=this.list(k);r[r.length-1]=u,s=s.substring(0,s.length-m.raw.length)+u.raw,a=a.substring(0,a.length-_.raw.length)+u.raw,n=k.substring(r.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:s,tokens:r,text:a}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim();const s=n.length>1,a={type:"list",raw:"",ordered:s,start:s?+n.slice(0,-1):"",loose:!1,items:[]};n=s?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=s?n:"[*+-]");const r=this.rules.other.listItemRegex(n);let o=!1;for(;e;){let c=!1,p="",f="";if(!(t=r.exec(e))||this.rules.block.hr.test(e))break;p=t[0],e=e.substring(p.length);let b=t[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,h=>" ".repeat(3*h.length)),m=e.split(`
`,1)[0],_=!b.trim(),k=0;if(this.options.pedantic?(k=2,f=b.trimStart()):_?k=t[1].length+1:(k=t[2].search(this.rules.other.nonSpaceChar),k=k>4?1:k,f=b.slice(k),k+=t[1].length),_&&this.rules.other.blankLine.test(m)&&(p+=m+`
`,e=e.substring(m.length+1),c=!0),!c){const h=this.rules.other.nextBulletRegex(k),g=this.rules.other.hrRegex(k),x=this.rules.other.fencesBeginRegex(k),v=this.rules.other.headingBeginRegex(k),w=this.rules.other.htmlBeginRegex(k);for(;e;){const C=e.split(`
`,1)[0];let M;if(m=C,this.options.pedantic?(m=m.replace(this.rules.other.listReplaceNesting,"  "),M=m):M=m.replace(this.rules.other.tabCharGlobal,"    "),x.test(m)||v.test(m)||w.test(m)||h.test(m)||g.test(m))break;if(M.search(this.rules.other.nonSpaceChar)>=k||!m.trim())f+=`
`+M.slice(k);else{if(_||b.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||x.test(b)||v.test(b)||g.test(b))break;f+=`
`+m}!_&&!m.trim()&&(_=!0),p+=C+`
`,e=e.substring(C.length+1),b=M.slice(k)}}a.loose||(o?a.loose=!0:this.rules.other.doubleBlankLine.test(p)&&(o=!0));let u=null,d;this.options.gfm&&(u=this.rules.other.listIsTask.exec(f),u&&(d=u[0]!=="[ ] ",f=f.replace(this.rules.other.listReplaceTask,""))),a.items.push({type:"list_item",raw:p,task:!!u,checked:d,loose:!1,text:f,tokens:[]}),a.raw+=p}const l=a.items.at(-1);if(l)l.raw=l.raw.trimEnd(),l.text=l.text.trimEnd();else return;a.raw=a.raw.trimEnd();for(let c=0;c<a.items.length;c++)if(this.lexer.state.top=!1,a.items[c].tokens=this.lexer.blockTokens(a.items[c].text,[]),!a.loose){const p=a.items[c].tokens.filter(b=>b.type==="space"),f=p.length>0&&p.some(b=>this.rules.other.anyLine.test(b.raw));a.loose=f}if(a.loose)for(let c=0;c<a.items.length;c++)a.items[c].loose=!0;return a}}html(e){const t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:t[1]==="pre"||t[1]==="script"||t[1]==="style",text:t[0]}}def(e){const t=this.rules.block.def.exec(e);if(t){const n=t[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),s=t[2]?t[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",a=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:n,raw:t[0],href:s,title:a}}}table(e){const t=this.rules.block.table.exec(e);if(!t||!this.rules.other.tableDelimiter.test(t[2]))return;const n=Pe(t[1]),s=t[2].replace(this.rules.other.tableAlignChars,"").split("|"),a=t[3]?.trim()?t[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],r={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length===s.length){for(const o of s)this.rules.other.tableAlignRight.test(o)?r.align.push("right"):this.rules.other.tableAlignCenter.test(o)?r.align.push("center"):this.rules.other.tableAlignLeft.test(o)?r.align.push("left"):r.align.push(null);for(let o=0;o<n.length;o++)r.header.push({text:n[o],tokens:this.lexer.inline(n[o]),header:!0,align:r.align[o]});for(const o of a)r.rows.push(Pe(o,r.header.length).map((l,c)=>({text:l,tokens:this.lexer.inline(l),header:!1,align:r.align[c]})));return r}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const n=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:n,tokens:this.lexer.inline(n)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:t[1]}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&this.rules.other.startATag.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const n=t[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(n)){if(!this.rules.other.endAngleBracket.test(n))return;const r=le(n.slice(0,-1),"\\");if((n.length-r.length)%2===0)return}else{const r=un(t[2],"()");if(r>-1){const l=(t[0].indexOf("!")===0?5:4)+t[1].length+r;t[2]=t[2].substring(0,r),t[0]=t[0].substring(0,l).trim(),t[3]=""}}let s=t[2],a="";if(this.options.pedantic){const r=this.rules.other.pedanticHrefTitle.exec(s);r&&(s=r[1],a=r[3])}else a=t[3]?t[3].slice(1,-1):"";return s=s.trim(),this.rules.other.startAngleBracket.test(s)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(n)?s=s.slice(1):s=s.slice(1,-1)),Ue(t,{href:s&&s.replace(this.rules.inline.anyPunctuation,"$1"),title:a&&a.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer,this.rules)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){const s=(n[2]||n[1]).replace(this.rules.other.multipleSpaceGlobal," "),a=t[s.toLowerCase()];if(!a){const r=n[0].charAt(0);return{type:"text",raw:r,text:r}}return Ue(n,a,n[0],this.lexer,this.rules)}}emStrong(e,t,n=""){let s=this.rules.inline.emStrongLDelim.exec(e);if(!s||s[3]&&n.match(this.rules.other.unicodeAlphaNumeric))return;if(!(s[1]||s[2]||"")||!n||this.rules.inline.punctuation.exec(n)){const r=[...s[0]].length-1;let o,l,c=r,p=0;const f=s[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(f.lastIndex=0,t=t.slice(-1*e.length+r);(s=f.exec(t))!=null;){if(o=s[1]||s[2]||s[3]||s[4]||s[5]||s[6],!o)continue;if(l=[...o].length,s[3]||s[4]){c+=l;continue}else if((s[5]||s[6])&&r%3&&!((r+l)%3)){p+=l;continue}if(c-=l,c>0)continue;l=Math.min(l,l+c+p);const b=[...s[0]][0].length,m=e.slice(0,r+s.index+b+l);if(Math.min(r,l)%2){const k=m.slice(1,-1);return{type:"em",raw:m,text:k,tokens:this.lexer.inlineTokens(k)}}const _=m.slice(2,-2);return{type:"strong",raw:m,text:_,tokens:this.lexer.inlineTokens(_)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let n=t[2].replace(this.rules.other.newLineCharGlobal," ");const s=this.rules.other.nonSpaceChar.test(n),a=this.rules.other.startingSpaceChar.test(n)&&this.rules.other.endingSpaceChar.test(n);return s&&a&&(n=n.substring(1,n.length-1)),{type:"codespan",raw:t[0],text:n}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){const t=this.rules.inline.autolink.exec(e);if(t){let n,s;return t[2]==="@"?(n=t[1],s="mailto:"+n):(n=t[1],s=n),{type:"link",raw:t[0],text:n,href:s,tokens:[{type:"text",raw:n,text:n}]}}}url(e){let t;if(t=this.rules.inline.url.exec(e)){let n,s;if(t[2]==="@")n=t[0],s="mailto:"+n;else{let a;do a=t[0],t[0]=this.rules.inline._backpedal.exec(t[0])?.[0]??"";while(a!==t[0]);n=t[0],t[1]==="www."?s="http://"+t[0]:s=t[0]}return{type:"link",raw:t[0],text:n,href:s,tokens:[{type:"text",raw:n,text:n}]}}}inlineText(e){const t=this.rules.inline.text.exec(e);if(t){const n=this.lexer.state.inRawBlock;return{type:"text",raw:t[0],text:t[0],escaped:n}}}}class z{constructor(e){T(this,"tokens");T(this,"options");T(this,"state");T(this,"tokenizer");T(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||Q,this.options.tokenizer=this.options.tokenizer||new be,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={other:O,block:pe.normal,inline:oe.normal};this.options.pedantic?(t.block=pe.pedantic,t.inline=oe.pedantic):this.options.gfm&&(t.block=pe.gfm,this.options.breaks?t.inline=oe.breaks:t.inline=oe.gfm),this.tokenizer.rules=t}static get rules(){return{block:pe,inline:oe}}static lex(e,t){return new z(t).lex(e)}static lexInline(e,t){return new z(t).inlineTokens(e)}lex(e){e=e.replace(O.carriageReturn,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){const n=this.inlineQueue[t];this.inlineTokens(n.src,n.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[],n=!1){for(this.options.pedantic&&(e=e.replace(O.tabCharGlobal,"    ").replace(O.spaceLine,""));e;){let s;if(this.options.extensions?.block?.some(r=>(s=r.call({lexer:this},e,t))?(e=e.substring(s.raw.length),t.push(s),!0):!1))continue;if(s=this.tokenizer.space(e)){e=e.substring(s.raw.length);const r=t.at(-1);s.raw.length===1&&r!==void 0?r.raw+=`
`:t.push(s);continue}if(s=this.tokenizer.code(e)){e=e.substring(s.raw.length);const r=t.at(-1);r?.type==="paragraph"||r?.type==="text"?(r.raw+=`
`+s.raw,r.text+=`
`+s.text,this.inlineQueue.at(-1).src=r.text):t.push(s);continue}if(s=this.tokenizer.fences(e)){e=e.substring(s.raw.length),t.push(s);continue}if(s=this.tokenizer.heading(e)){e=e.substring(s.raw.length),t.push(s);continue}if(s=this.tokenizer.hr(e)){e=e.substring(s.raw.length),t.push(s);continue}if(s=this.tokenizer.blockquote(e)){e=e.substring(s.raw.length),t.push(s);continue}if(s=this.tokenizer.list(e)){e=e.substring(s.raw.length),t.push(s);continue}if(s=this.tokenizer.html(e)){e=e.substring(s.raw.length),t.push(s);continue}if(s=this.tokenizer.def(e)){e=e.substring(s.raw.length);const r=t.at(-1);r?.type==="paragraph"||r?.type==="text"?(r.raw+=`
`+s.raw,r.text+=`
`+s.raw,this.inlineQueue.at(-1).src=r.text):this.tokens.links[s.tag]||(this.tokens.links[s.tag]={href:s.href,title:s.title});continue}if(s=this.tokenizer.table(e)){e=e.substring(s.raw.length),t.push(s);continue}if(s=this.tokenizer.lheading(e)){e=e.substring(s.raw.length),t.push(s);continue}let a=e;if(this.options.extensions?.startBlock){let r=1/0;const o=e.slice(1);let l;this.options.extensions.startBlock.forEach(c=>{l=c.call({lexer:this},o),typeof l=="number"&&l>=0&&(r=Math.min(r,l))}),r<1/0&&r>=0&&(a=e.substring(0,r+1))}if(this.state.top&&(s=this.tokenizer.paragraph(a))){const r=t.at(-1);n&&r?.type==="paragraph"?(r.raw+=`
`+s.raw,r.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=r.text):t.push(s),n=a.length!==e.length,e=e.substring(s.raw.length);continue}if(s=this.tokenizer.text(e)){e=e.substring(s.raw.length);const r=t.at(-1);r?.type==="text"?(r.raw+=`
`+s.raw,r.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=r.text):t.push(s);continue}if(e){const r="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(r);break}else throw new Error(r)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let n=e,s=null;if(this.tokens.links){const o=Object.keys(this.tokens.links);if(o.length>0)for(;(s=this.tokenizer.rules.inline.reflinkSearch.exec(n))!=null;)o.includes(s[0].slice(s[0].lastIndexOf("[")+1,-1))&&(n=n.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(s=this.tokenizer.rules.inline.blockSkip.exec(n))!=null;)n=n.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(s=this.tokenizer.rules.inline.anyPunctuation.exec(n))!=null;)n=n.slice(0,s.index)+"++"+n.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);let a=!1,r="";for(;e;){a||(r=""),a=!1;let o;if(this.options.extensions?.inline?.some(c=>(o=c.call({lexer:this},e,t))?(e=e.substring(o.raw.length),t.push(o),!0):!1))continue;if(o=this.tokenizer.escape(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.tag(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.link(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(o.raw.length);const c=t.at(-1);o.type==="text"&&c?.type==="text"?(c.raw+=o.raw,c.text+=o.text):t.push(o);continue}if(o=this.tokenizer.emStrong(e,n,r)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.codespan(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.br(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.del(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.autolink(e)){e=e.substring(o.raw.length),t.push(o);continue}if(!this.state.inLink&&(o=this.tokenizer.url(e))){e=e.substring(o.raw.length),t.push(o);continue}let l=e;if(this.options.extensions?.startInline){let c=1/0;const p=e.slice(1);let f;this.options.extensions.startInline.forEach(b=>{f=b.call({lexer:this},p),typeof f=="number"&&f>=0&&(c=Math.min(c,f))}),c<1/0&&c>=0&&(l=e.substring(0,c+1))}if(o=this.tokenizer.inlineText(l)){e=e.substring(o.raw.length),o.raw.slice(-1)!=="_"&&(r=o.raw.slice(-1)),a=!0;const c=t.at(-1);c?.type==="text"?(c.raw+=o.raw,c.text+=o.text):t.push(o);continue}if(e){const c="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(c);break}else throw new Error(c)}}return t}}class me{constructor(e){T(this,"options");T(this,"parser");this.options=e||Q}space(e){return""}code({text:e,lang:t,escaped:n}){const s=(t||"").match(O.notSpaceStart)?.[0],a=e.replace(O.endingNewline,"")+`
`;return s?'<pre><code class="language-'+G(s)+'">'+(n?a:G(a,!0))+`</code></pre>
`:"<pre><code>"+(n?a:G(a,!0))+`</code></pre>
`}blockquote({tokens:e}){return`<blockquote>
${this.parser.parse(e)}</blockquote>
`}html({text:e}){return e}heading({tokens:e,depth:t}){return`<h${t}>${this.parser.parseInline(e)}</h${t}>
`}hr(e){return`<hr>
`}list(e){const t=e.ordered,n=e.start;let s="";for(let o=0;o<e.items.length;o++){const l=e.items[o];s+=this.listitem(l)}const a=t?"ol":"ul",r=t&&n!==1?' start="'+n+'"':"";return"<"+a+r+`>
`+s+"</"+a+`>
`}listitem(e){let t="";if(e.task){const n=this.checkbox({checked:!!e.checked});e.loose?e.tokens[0]?.type==="paragraph"?(e.tokens[0].text=n+" "+e.tokens[0].text,e.tokens[0].tokens&&e.tokens[0].tokens.length>0&&e.tokens[0].tokens[0].type==="text"&&(e.tokens[0].tokens[0].text=n+" "+G(e.tokens[0].tokens[0].text),e.tokens[0].tokens[0].escaped=!0)):e.tokens.unshift({type:"text",raw:n+" ",text:n+" ",escaped:!0}):t+=n+" "}return t+=this.parser.parse(e.tokens,!!e.loose),`<li>${t}</li>
`}checkbox({checked:e}){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:e}){return`<p>${this.parser.parseInline(e)}</p>
`}table(e){let t="",n="";for(let a=0;a<e.header.length;a++)n+=this.tablecell(e.header[a]);t+=this.tablerow({text:n});let s="";for(let a=0;a<e.rows.length;a++){const r=e.rows[a];n="";for(let o=0;o<r.length;o++)n+=this.tablecell(r[o]);s+=this.tablerow({text:n})}return s&&(s=`<tbody>${s}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+s+`</table>
`}tablerow({text:e}){return`<tr>
${e}</tr>
`}tablecell(e){const t=this.parser.parseInline(e.tokens),n=e.header?"th":"td";return(e.align?`<${n} align="${e.align}">`:`<${n}>`)+t+`</${n}>
`}strong({tokens:e}){return`<strong>${this.parser.parseInline(e)}</strong>`}em({tokens:e}){return`<em>${this.parser.parseInline(e)}</em>`}codespan({text:e}){return`<code>${G(e,!0)}</code>`}br(e){return"<br>"}del({tokens:e}){return`<del>${this.parser.parseInline(e)}</del>`}link({href:e,title:t,tokens:n}){const s=this.parser.parseInline(n),a=De(e);if(a===null)return s;e=a;let r='<a href="'+e+'"';return t&&(r+=' title="'+G(t)+'"'),r+=">"+s+"</a>",r}image({href:e,title:t,text:n}){const s=De(e);if(s===null)return G(n);e=s;let a=`<img src="${e}" alt="${n}"`;return t&&(a+=` title="${G(t)}"`),a+=">",a}text(e){return"tokens"in e&&e.tokens?this.parser.parseInline(e.tokens):"escaped"in e&&e.escaped?e.text:G(e.text)}}class Oe{strong({text:e}){return e}em({text:e}){return e}codespan({text:e}){return e}del({text:e}){return e}html({text:e}){return e}text({text:e}){return e}link({text:e}){return""+e}image({text:e}){return""+e}br(){return""}}class D{constructor(e){T(this,"options");T(this,"renderer");T(this,"textRenderer");this.options=e||Q,this.options.renderer=this.options.renderer||new me,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new Oe}static parse(e,t){return new D(t).parse(e)}static parseInline(e,t){return new D(t).parseInline(e)}parse(e,t=!0){let n="";for(let s=0;s<e.length;s++){const a=e[s];if(this.options.extensions?.renderers?.[a.type]){const o=a,l=this.options.extensions.renderers[o.type].call({parser:this},o);if(l!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(o.type)){n+=l||"";continue}}const r=a;switch(r.type){case"space":{n+=this.renderer.space(r);continue}case"hr":{n+=this.renderer.hr(r);continue}case"heading":{n+=this.renderer.heading(r);continue}case"code":{n+=this.renderer.code(r);continue}case"table":{n+=this.renderer.table(r);continue}case"blockquote":{n+=this.renderer.blockquote(r);continue}case"list":{n+=this.renderer.list(r);continue}case"html":{n+=this.renderer.html(r);continue}case"paragraph":{n+=this.renderer.paragraph(r);continue}case"text":{let o=r,l=this.renderer.text(o);for(;s+1<e.length&&e[s+1].type==="text";)o=e[++s],l+=`
`+this.renderer.text(o);t?n+=this.renderer.paragraph({type:"paragraph",raw:l,text:l,tokens:[{type:"text",raw:l,text:l,escaped:!0}]}):n+=l;continue}default:{const o='Token with "'+r.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}parseInline(e,t=this.renderer){let n="";for(let s=0;s<e.length;s++){const a=e[s];if(this.options.extensions?.renderers?.[a.type]){const o=this.options.extensions.renderers[a.type].call({parser:this},a);if(o!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(a.type)){n+=o||"";continue}}const r=a;switch(r.type){case"escape":{n+=t.text(r);break}case"html":{n+=t.html(r);break}case"link":{n+=t.link(r);break}case"image":{n+=t.image(r);break}case"strong":{n+=t.strong(r);break}case"em":{n+=t.em(r);break}case"codespan":{n+=t.codespan(r);break}case"br":{n+=t.br(r);break}case"del":{n+=t.del(r);break}case"text":{n+=t.text(r);break}default:{const o='Token with "'+r.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}}class ue{constructor(e){T(this,"options");T(this,"block");this.options=e||Q}preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}provideLexer(){return this.block?z.lex:z.lexInline}provideParser(){return this.block?D.parse:D.parseInline}}T(ue,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));class pn{constructor(...e){T(this,"defaults",Ee());T(this,"options",this.setOptions);T(this,"parse",this.parseMarkdown(!0));T(this,"parseInline",this.parseMarkdown(!1));T(this,"Parser",D);T(this,"Renderer",me);T(this,"TextRenderer",Oe);T(this,"Lexer",z);T(this,"Tokenizer",be);T(this,"Hooks",ue);this.use(...e)}walkTokens(e,t){let n=[];for(const s of e)switch(n=n.concat(t.call(this,s)),s.type){case"table":{const a=s;for(const r of a.header)n=n.concat(this.walkTokens(r.tokens,t));for(const r of a.rows)for(const o of r)n=n.concat(this.walkTokens(o.tokens,t));break}case"list":{const a=s;n=n.concat(this.walkTokens(a.items,t));break}default:{const a=s;this.defaults.extensions?.childTokens?.[a.type]?this.defaults.extensions.childTokens[a.type].forEach(r=>{const o=a[r].flat(1/0);n=n.concat(this.walkTokens(o,t))}):a.tokens&&(n=n.concat(this.walkTokens(a.tokens,t)))}}return n}use(...e){const t=this.defaults.extensions||{renderers:{},childTokens:{}};return e.forEach(n=>{const s={...n};if(s.async=this.defaults.async||s.async||!1,n.extensions&&(n.extensions.forEach(a=>{if(!a.name)throw new Error("extension name required");if("renderer"in a){const r=t.renderers[a.name];r?t.renderers[a.name]=function(...o){let l=a.renderer.apply(this,o);return l===!1&&(l=r.apply(this,o)),l}:t.renderers[a.name]=a.renderer}if("tokenizer"in a){if(!a.level||a.level!=="block"&&a.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const r=t[a.level];r?r.unshift(a.tokenizer):t[a.level]=[a.tokenizer],a.start&&(a.level==="block"?t.startBlock?t.startBlock.push(a.start):t.startBlock=[a.start]:a.level==="inline"&&(t.startInline?t.startInline.push(a.start):t.startInline=[a.start]))}"childTokens"in a&&a.childTokens&&(t.childTokens[a.name]=a.childTokens)}),s.extensions=t),n.renderer){const a=this.defaults.renderer||new me(this.defaults);for(const r in n.renderer){if(!(r in a))throw new Error(`renderer '${r}' does not exist`);if(["options","parser"].includes(r))continue;const o=r,l=n.renderer[o],c=a[o];a[o]=(...p)=>{let f=l.apply(a,p);return f===!1&&(f=c.apply(a,p)),f||""}}s.renderer=a}if(n.tokenizer){const a=this.defaults.tokenizer||new be(this.defaults);for(const r in n.tokenizer){if(!(r in a))throw new Error(`tokenizer '${r}' does not exist`);if(["options","rules","lexer"].includes(r))continue;const o=r,l=n.tokenizer[o],c=a[o];a[o]=(...p)=>{let f=l.apply(a,p);return f===!1&&(f=c.apply(a,p)),f}}s.tokenizer=a}if(n.hooks){const a=this.defaults.hooks||new ue;for(const r in n.hooks){if(!(r in a))throw new Error(`hook '${r}' does not exist`);if(["options","block"].includes(r))continue;const o=r,l=n.hooks[o],c=a[o];ue.passThroughHooks.has(r)?a[o]=p=>{if(this.defaults.async)return Promise.resolve(l.call(a,p)).then(b=>c.call(a,b));const f=l.call(a,p);return c.call(a,f)}:a[o]=(...p)=>{let f=l.apply(a,p);return f===!1&&(f=c.apply(a,p)),f}}s.hooks=a}if(n.walkTokens){const a=this.defaults.walkTokens,r=n.walkTokens;s.walkTokens=function(o){let l=[];return l.push(r.call(this,o)),a&&(l=l.concat(a.call(this,o))),l}}this.defaults={...this.defaults,...s}}),this}setOptions(e){return this.defaults={...this.defaults,...e},this}lexer(e,t){return z.lex(e,t??this.defaults)}parser(e,t){return D.parse(e,t??this.defaults)}parseMarkdown(e){return(n,s)=>{const a={...s},r={...this.defaults,...a},o=this.onError(!!r.silent,!!r.async);if(this.defaults.async===!0&&a.async===!1)return o(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof n>"u"||n===null)return o(new Error("marked(): input parameter is undefined or null"));if(typeof n!="string")return o(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(n)+", string expected"));r.hooks&&(r.hooks.options=r,r.hooks.block=e);const l=r.hooks?r.hooks.provideLexer():e?z.lex:z.lexInline,c=r.hooks?r.hooks.provideParser():e?D.parse:D.parseInline;if(r.async)return Promise.resolve(r.hooks?r.hooks.preprocess(n):n).then(p=>l(p,r)).then(p=>r.hooks?r.hooks.processAllTokens(p):p).then(p=>r.walkTokens?Promise.all(this.walkTokens(p,r.walkTokens)).then(()=>p):p).then(p=>c(p,r)).then(p=>r.hooks?r.hooks.postprocess(p):p).catch(o);try{r.hooks&&(n=r.hooks.preprocess(n));let p=l(n,r);r.hooks&&(p=r.hooks.processAllTokens(p)),r.walkTokens&&this.walkTokens(p,r.walkTokens);let f=c(p,r);return r.hooks&&(f=r.hooks.postprocess(f)),f}catch(p){return o(p)}}}onError(e,t){return n=>{if(n.message+=`
Please report this to https://github.com/markedjs/marked.`,e){const s="<p>An error occurred:</p><pre>"+G(n.message+"",!0)+"</pre>";return t?Promise.resolve(s):s}if(t)return Promise.reject(n);throw n}}}const Y=new pn;function y(i,e){return Y.parse(i,e)}y.options=y.setOptions=function(i){return Y.setOptions(i),y.defaults=Y.defaults,Ve(y.defaults),y};y.getDefaults=Ee;y.defaults=Q;y.use=function(...i){return Y.use(...i),y.defaults=Y.defaults,Ve(y.defaults),y};y.walkTokens=function(i,e){return Y.walkTokens(i,e)};y.parseInline=Y.parseInline;y.Parser=D;y.parser=D.parse;y.Renderer=me;y.TextRenderer=Oe;y.Lexer=z;y.lexer=z.lex;y.Tokenizer=be;y.Hooks=ue;y.parse=y;y.options;y.setOptions;y.use;y.walkTokens;y.parseInline;D.parse;z.lex;const He="[A-Za-z$_][0-9A-Za-z$_]*",gn=["as","in","of","if","for","while","finally","var","new","function","do","return","void","else","break","catch","instanceof","with","throw","case","default","try","switch","continue","typeof","delete","let","yield","const","class","debugger","async","await","static","import","from","export","extends","using"],hn=["true","false","null","undefined","NaN","Infinity"],it=["Object","Function","Boolean","Symbol","Math","Date","Number","BigInt","String","RegExp","Array","Float32Array","Float64Array","Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Int32Array","Uint16Array","Uint32Array","BigInt64Array","BigUint64Array","Set","Map","WeakSet","WeakMap","ArrayBuffer","SharedArrayBuffer","Atomics","DataView","JSON","Promise","Generator","GeneratorFunction","AsyncFunction","Reflect","Proxy","Intl","WebAssembly"],at=["Error","EvalError","InternalError","RangeError","ReferenceError","SyntaxError","TypeError","URIError"],ot=["setInterval","setTimeout","clearInterval","clearTimeout","require","exports","eval","isFinite","isNaN","parseFloat","parseInt","decodeURI","decodeURIComponent","encodeURI","encodeURIComponent","escape","unescape"],fn=["arguments","this","super","console","window","document","localStorage","sessionStorage","module","global"],bn=[].concat(ot,it,at);function mn(i){const e=i.regex,t=(A,{after:L})=>{const P="</"+A[0].slice(1);return A.input.indexOf(P,L)!==-1},n=He,s={begin:"<>",end:"</>"},a=/<[A-Za-z0-9\\._:-]+\s*\/>/,r={begin:/<[A-Za-z0-9\\._:-]+/,end:/\/[A-Za-z0-9\\._:-]+>|\/>/,isTrulyOpeningTag:(A,L)=>{const P=A[0].length+A.index,q=A.input[P];if(q==="<"||q===","){L.ignoreMatch();return}q===">"&&(t(A,{after:P})||L.ignoreMatch());let K;const ie=A.input.substring(P);if(K=ie.match(/^\s*=/)){L.ignoreMatch();return}if((K=ie.match(/^\s+extends\s+/))&&K.index===0){L.ignoreMatch();return}}},o={$pattern:He,keyword:gn,literal:hn,built_in:bn,"variable.language":fn},l="[0-9](_?[0-9])*",c=`\\.(${l})`,p="0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*",f={className:"number",variants:[{begin:`(\\b(${p})((${c})|\\.)?|(${c}))[eE][+-]?(${l})\\b`},{begin:`\\b(${p})\\b((${c})\\b|\\.)?|(${c})\\b`},{begin:"\\b(0|[1-9](_?[0-9])*)n\\b"},{begin:"\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\b"},{begin:"\\b0[bB][0-1](_?[0-1])*n?\\b"},{begin:"\\b0[oO][0-7](_?[0-7])*n?\\b"},{begin:"\\b0[0-7]+n?\\b"}],relevance:0},b={className:"subst",begin:"\\$\\{",end:"\\}",keywords:o,contains:[]},m={begin:".?html`",end:"",starts:{end:"`",returnEnd:!1,contains:[i.BACKSLASH_ESCAPE,b],subLanguage:"xml"}},_={begin:".?css`",end:"",starts:{end:"`",returnEnd:!1,contains:[i.BACKSLASH_ESCAPE,b],subLanguage:"css"}},k={begin:".?gql`",end:"",starts:{end:"`",returnEnd:!1,contains:[i.BACKSLASH_ESCAPE,b],subLanguage:"graphql"}},u={className:"string",begin:"`",end:"`",contains:[i.BACKSLASH_ESCAPE,b]},h={className:"comment",variants:[i.COMMENT(/\/\*\*(?!\/)/,"\\*/",{relevance:0,contains:[{begin:"(?=@[A-Za-z]+)",relevance:0,contains:[{className:"doctag",begin:"@[A-Za-z]+"},{className:"type",begin:"\\{",end:"\\}",excludeEnd:!0,excludeBegin:!0,relevance:0},{className:"variable",begin:n+"(?=\\s*(-)|$)",endsParent:!0,relevance:0},{begin:/(?=[^\n])\s/,relevance:0}]}]}),i.C_BLOCK_COMMENT_MODE,i.C_LINE_COMMENT_MODE]},g=[i.APOS_STRING_MODE,i.QUOTE_STRING_MODE,m,_,k,u,{match:/\$\d+/},f];b.contains=g.concat({begin:/\{/,end:/\}/,keywords:o,contains:["self"].concat(g)});const x=[].concat(h,b.contains),v=x.concat([{begin:/(\s*)\(/,end:/\)/,keywords:o,contains:["self"].concat(x)}]),w={className:"params",begin:/(\s*)\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:o,contains:v},C={variants:[{match:[/class/,/\s+/,n,/\s+/,/extends/,/\s+/,e.concat(n,"(",e.concat(/\./,n),")*")],scope:{1:"keyword",3:"title.class",5:"keyword",7:"title.class.inherited"}},{match:[/class/,/\s+/,n],scope:{1:"keyword",3:"title.class"}}]},M={relevance:0,match:e.either(/\bJSON/,/\b[A-Z][a-z]+([A-Z][a-z]*|\d)*/,/\b[A-Z]{2,}([A-Z][a-z]+|\d)+([A-Z][a-z]*)*/,/\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\d)*([A-Z][a-z]*)*/),className:"title.class",keywords:{_:[...it,...at]}},$={label:"use_strict",className:"meta",relevance:10,begin:/^\s*['"]use (strict|asm)['"]/},ee={variants:[{match:[/function/,/\s+/,n,/(?=\s*\()/]},{match:[/function/,/\s*(?=\()/]}],className:{1:"keyword",3:"title.function"},label:"func.def",contains:[w],illegal:/%/},te={relevance:0,match:/\b[A-Z][A-Z_0-9]+\b/,className:"variable.constant"};function ne(A){return e.concat("(?!",A.join("|"),")")}const U={match:e.concat(/\b/,ne([...ot,"super","import"].map(A=>`${A}\\s*\\(`)),n,e.lookahead(/\s*\(/)),className:"title.function",relevance:0},se={begin:e.concat(/\./,e.lookahead(e.concat(n,/(?![0-9A-Za-z$_(])/))),end:n,excludeBegin:!0,keywords:"prototype",className:"property",relevance:0},ye={match:[/get|set/,/\s+/,n,/(?=\()/],className:{1:"keyword",3:"title.function"},contains:[{begin:/\(\)/},w]},re="(\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)|"+i.UNDERSCORE_IDENT_RE+")\\s*=>",xe={match:[/const|var|let/,/\s+/,n,/\s*/,/=\s*/,/(async\s*)?/,e.lookahead(re)],keywords:"async",className:{1:"keyword",3:"title.function"},contains:[w]};return{name:"JavaScript",aliases:["js","jsx","mjs","cjs"],keywords:o,exports:{PARAMS_CONTAINS:v,CLASS_REFERENCE:M},illegal:/#(?![$_A-z])/,contains:[i.SHEBANG({label:"shebang",binary:"node",relevance:5}),$,i.APOS_STRING_MODE,i.QUOTE_STRING_MODE,m,_,k,u,h,{match:/\$\d+/},f,M,{scope:"attr",match:n+e.lookahead(":"),relevance:0},xe,{begin:"("+i.RE_STARTERS_RE+"|\\b(case|return|throw)\\b)\\s*",keywords:"return throw case",relevance:0,contains:[h,i.REGEXP_MODE,{className:"function",begin:re,returnBegin:!0,end:"\\s*=>",contains:[{className:"params",variants:[{begin:i.UNDERSCORE_IDENT_RE,relevance:0},{className:null,begin:/\(\s*\)/,skip:!0},{begin:/(\s*)\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:o,contains:v}]}]},{begin:/,/,relevance:0},{match:/\s+/,relevance:0},{variants:[{begin:s.begin,end:s.end},{match:a},{begin:r.begin,"on:begin":r.isTrulyOpeningTag,end:r.end}],subLanguage:"xml",contains:[{begin:r.begin,end:r.end,skip:!0,contains:["self"]}]}]},ee,{beginKeywords:"while if switch catch for"},{begin:"\\b(?!function)"+i.UNDERSCORE_IDENT_RE+"\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)\\s*\\{",returnBegin:!0,label:"func.def",contains:[w,i.inherit(i.TITLE_MODE,{begin:n,className:"title.function"})]},{match:/\.\.\./,relevance:0},se,{match:"\\$"+n,relevance:0},{match:[/\bconstructor(?=\s*\()/],className:{1:"title.function"},contains:[w]},U,te,C,ye,{match:/\$[(.]/}]}}var J="[0-9](_*[0-9])*",ge=`\\.(${J})`,he="[0-9a-fA-F](_*[0-9a-fA-F])*",Ge={className:"number",variants:[{begin:`(\\b(${J})((${ge})|\\.)?|(${ge}))[eE][+-]?(${J})[fFdD]?\\b`},{begin:`\\b(${J})((${ge})[fFdD]?\\b|\\.([fFdD]\\b)?)`},{begin:`(${ge})[fFdD]?\\b`},{begin:`\\b(${J})[fFdD]\\b`},{begin:`\\b0[xX]((${he})\\.?|(${he})?\\.(${he}))[pP][+-]?(${J})[fFdD]?\\b`},{begin:"\\b(0|[1-9](_*[0-9])*)[lL]?\\b"},{begin:`\\b0[xX](${he})[lL]?\\b`},{begin:"\\b0(_*[0-7])*[lL]?\\b"},{begin:"\\b0[bB][01](_*[01])*[lL]?\\b"}],relevance:0};function lt(i,e,t){return t===-1?"":i.replace(e,n=>lt(i,e,t-1))}function kn(i){const e=i.regex,t="[À-ʸa-zA-Z_$][À-ʸa-zA-Z_$0-9]*",n=t+lt("(?:<"+t+"~~~(?:\\s*,\\s*"+t+"~~~)*>)?",/~~~/g,2),l={keyword:["synchronized","abstract","private","var","static","if","const ","for","while","strictfp","finally","protected","import","native","final","void","enum","else","break","transient","catch","instanceof","volatile","case","assert","package","default","public","try","switch","continue","throws","protected","public","private","module","requires","exports","do","sealed","yield","permits","goto","when"],literal:["false","true","null"],type:["char","boolean","long","float","int","byte","short","double"],built_in:["super","this"]},c={className:"meta",begin:"@"+t,contains:[{begin:/\(/,end:/\)/,contains:["self"]}]},p={className:"params",begin:/\(/,end:/\)/,keywords:l,relevance:0,contains:[i.C_BLOCK_COMMENT_MODE],endsParent:!0};return{name:"Java",aliases:["jsp"],keywords:l,illegal:/<\/|#/,contains:[i.COMMENT("/\\*\\*","\\*/",{relevance:0,contains:[{begin:/\w+@/,relevance:0},{className:"doctag",begin:"@[A-Za-z]+"}]}),{begin:/import java\.[a-z]+\./,keywords:"import",relevance:2},i.C_LINE_COMMENT_MODE,i.C_BLOCK_COMMENT_MODE,{begin:/"""/,end:/"""/,className:"string",contains:[i.BACKSLASH_ESCAPE]},i.APOS_STRING_MODE,i.QUOTE_STRING_MODE,{match:[/\b(?:class|interface|enum|extends|implements|new)/,/\s+/,t],className:{1:"keyword",3:"title.class"}},{match:/non-sealed/,scope:"keyword"},{begin:[e.concat(/(?!else)/,t),/\s+/,t,/\s+/,/=(?!=)/],className:{1:"type",3:"variable",5:"operator"}},{begin:[/record/,/\s+/,t],className:{1:"keyword",3:"title.class"},contains:[p,i.C_LINE_COMMENT_MODE,i.C_BLOCK_COMMENT_MODE]},{beginKeywords:"new throw return else",relevance:0},{begin:["(?:"+n+"\\s+)",i.UNDERSCORE_IDENT_RE,/\s*(?=\()/],className:{2:"title.function"},keywords:l,contains:[{className:"params",begin:/\(/,end:/\)/,keywords:l,relevance:0,contains:[c,i.APOS_STRING_MODE,i.QUOTE_STRING_MODE,Ge,i.C_BLOCK_COMMENT_MODE]},i.C_LINE_COMMENT_MODE,i.C_BLOCK_COMMENT_MODE]},Ge,c]}}function wn(i){const e=i.regex,t=/[\p{XID_Start}_]\p{XID_Continue}*/u,n=["and","as","assert","async","await","break","case","class","continue","def","del","elif","else","except","finally","for","from","global","if","import","in","is","lambda","match","nonlocal|10","not","or","pass","raise","return","try","while","with","yield"],o={$pattern:/[A-Za-z]\w+|__\w+__/,keyword:n,built_in:["__import__","abs","all","any","ascii","bin","bool","breakpoint","bytearray","bytes","callable","chr","classmethod","compile","complex","delattr","dict","dir","divmod","enumerate","eval","exec","filter","float","format","frozenset","getattr","globals","hasattr","hash","help","hex","id","input","int","isinstance","issubclass","iter","len","list","locals","map","max","memoryview","min","next","object","oct","open","ord","pow","print","property","range","repr","reversed","round","set","setattr","slice","sorted","staticmethod","str","sum","super","tuple","type","vars","zip"],literal:["__debug__","Ellipsis","False","None","NotImplemented","True"],type:["Any","Callable","Coroutine","Dict","List","Literal","Generic","Optional","Sequence","Set","Tuple","Type","Union"]},l={className:"meta",begin:/^(>>>|\.\.\.) /},c={className:"subst",begin:/\{/,end:/\}/,keywords:o,illegal:/#/},p={begin:/\{\{/,relevance:0},f={className:"string",contains:[i.BACKSLASH_ESCAPE],variants:[{begin:/([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?'''/,end:/'''/,contains:[i.BACKSLASH_ESCAPE,l],relevance:10},{begin:/([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?"""/,end:/"""/,contains:[i.BACKSLASH_ESCAPE,l],relevance:10},{begin:/([fF][rR]|[rR][fF]|[fF])'''/,end:/'''/,contains:[i.BACKSLASH_ESCAPE,l,p,c]},{begin:/([fF][rR]|[rR][fF]|[fF])"""/,end:/"""/,contains:[i.BACKSLASH_ESCAPE,l,p,c]},{begin:/([uU]|[rR])'/,end:/'/,relevance:10},{begin:/([uU]|[rR])"/,end:/"/,relevance:10},{begin:/([bB]|[bB][rR]|[rR][bB])'/,end:/'/},{begin:/([bB]|[bB][rR]|[rR][bB])"/,end:/"/},{begin:/([fF][rR]|[rR][fF]|[fF])'/,end:/'/,contains:[i.BACKSLASH_ESCAPE,p,c]},{begin:/([fF][rR]|[rR][fF]|[fF])"/,end:/"/,contains:[i.BACKSLASH_ESCAPE,p,c]},i.APOS_STRING_MODE,i.QUOTE_STRING_MODE]},b="[0-9](_?[0-9])*",m=`(\\b(${b}))?\\.(${b})|\\b(${b})\\.`,_=`\\b|${n.join("|")}`,k={className:"number",relevance:0,variants:[{begin:`(\\b(${b})|(${m}))[eE][+-]?(${b})[jJ]?(?=${_})`},{begin:`(${m})[jJ]?`},{begin:`\\b([1-9](_?[0-9])*|0+(_?0)*)[lLjJ]?(?=${_})`},{begin:`\\b0[bB](_?[01])+[lL]?(?=${_})`},{begin:`\\b0[oO](_?[0-7])+[lL]?(?=${_})`},{begin:`\\b0[xX](_?[0-9a-fA-F])+[lL]?(?=${_})`},{begin:`\\b(${b})[jJ](?=${_})`}]},u={className:"comment",begin:e.lookahead(/# type:/),end:/$/,keywords:o,contains:[{begin:/# type:/},{begin:/#/,end:/\b\B/,endsWithParent:!0}]},d={className:"params",variants:[{className:"",begin:/\(\s*\)/,skip:!0},{begin:/\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:o,contains:["self",l,k,f,i.HASH_COMMENT_MODE]}]};return c.contains=[f,k,l],{name:"Python",aliases:["py","gyp","ipython"],unicodeRegex:!0,keywords:o,illegal:/(<\/|\?)|=>/,contains:[l,k,{scope:"variable.language",match:/\bself\b/},{beginKeywords:"if",relevance:0},{match:/\bor\b/,scope:"keyword"},f,u,i.HASH_COMMENT_MODE,{match:[/\bdef/,/\s+/,t],scope:{1:"keyword",3:"title.function"},contains:[d]},{variants:[{match:[/\bclass/,/\s+/,t,/\s*/,/\(\s*/,t,/\s*\)/]},{match:[/\bclass/,/\s+/,t]}],scope:{1:"keyword",3:"title.class",6:"title.class.inherited"}},{className:"meta",begin:/^[\t ]*@/,end:/(?=#)|$/,contains:[k,d,f]}]}}function Fe(i){const e=i.regex,t=e.concat(/[\p{L}_]/u,e.optional(/[\p{L}0-9_.-]*:/u),/[\p{L}0-9_.-]*/u),n=/[\p{L}0-9._:-]+/u,s={className:"symbol",begin:/&[a-z]+;|&#[0-9]+;|&#x[a-f0-9]+;/},a={begin:/\s/,contains:[{className:"keyword",begin:/#?[a-z_][a-z1-9_-]+/,illegal:/\n/}]},r=i.inherit(a,{begin:/\(/,end:/\)/}),o=i.inherit(i.APOS_STRING_MODE,{className:"string"}),l=i.inherit(i.QUOTE_STRING_MODE,{className:"string"}),c={endsWithParent:!0,illegal:/</,relevance:0,contains:[{className:"attr",begin:n,relevance:0},{begin:/=\s*/,relevance:0,contains:[{className:"string",endsParent:!0,variants:[{begin:/"/,end:/"/,contains:[s]},{begin:/'/,end:/'/,contains:[s]},{begin:/[^\s"'=<>`]+/}]}]}]};return{name:"HTML, XML",aliases:["html","xhtml","rss","atom","xjb","xsd","xsl","plist","wsf","svg"],case_insensitive:!0,unicodeRegex:!0,contains:[{className:"meta",begin:/<![a-z]/,end:/>/,relevance:10,contains:[a,l,o,r,{begin:/\[/,end:/\]/,contains:[{className:"meta",begin:/<![a-z]/,end:/>/,contains:[a,r,l,o]}]}]},i.COMMENT(/<!--/,/-->/,{relevance:10}),{begin:/<!\[CDATA\[/,end:/\]\]>/,relevance:10},s,{className:"meta",end:/\?>/,variants:[{begin:/<\?xml/,relevance:10,contains:[l]},{begin:/<\?[a-z][a-z0-9]+/}]},{className:"tag",begin:/<style(?=\s|>)/,end:/>/,keywords:{name:"style"},contains:[c],starts:{end:/<\/style>/,returnEnd:!0,subLanguage:["css","xml"]}},{className:"tag",begin:/<script(?=\s|>)/,end:/>/,keywords:{name:"script"},contains:[c],starts:{end:/<\/script>/,returnEnd:!0,subLanguage:["javascript","handlebars","xml"]}},{className:"tag",begin:/<>|<\/>/},{className:"tag",begin:e.concat(/</,e.lookahead(e.concat(t,e.either(/\/>/,/>/,/\s/)))),end:/\/?>/,contains:[{className:"name",begin:t,relevance:0,starts:c}]},{className:"tag",begin:e.concat(/<\//,e.lookahead(e.concat(t,/>/))),contains:[{className:"name",begin:t,relevance:0},{begin:/>/,relevance:0,endsParent:!0}]}]}}function _n(i){const e={className:"attr",begin:/"(\\.|[^\\"\r\n])*"(?=\s*:)/,relevance:1.01},t={match:/[{}[\],:]/,className:"punctuation",relevance:0},n=["true","false","null"],s={scope:"literal",beginKeywords:n.join(" ")};return{name:"JSON",aliases:["jsonc"],keywords:{literal:n},contains:[e,t,i.QUOTE_STRING_MODE,s,i.C_NUMBER_MODE,i.C_LINE_COMMENT_MODE,i.C_BLOCK_COMMENT_MODE],illegal:"\\S"}}function yn(i){const e=i.regex,t={},n={begin:/\$\{/,end:/\}/,contains:["self",{begin:/:-/,contains:[t]}]};Object.assign(t,{className:"variable",variants:[{begin:e.concat(/\$[\w\d#@][\w\d_]*/,"(?![\\w\\d])(?![$])")},n]});const s={className:"subst",begin:/\$\(/,end:/\)/,contains:[i.BACKSLASH_ESCAPE]},a=i.inherit(i.COMMENT(),{match:[/(^|\s)/,/#.*$/],scope:{2:"comment"}}),r={begin:/<<-?\s*(?=\w+)/,starts:{contains:[i.END_SAME_AS_BEGIN({begin:/(\w+)/,end:/(\w+)/,className:"string"})]}},o={className:"string",begin:/"/,end:/"/,contains:[i.BACKSLASH_ESCAPE,t,s]};s.contains.push(o);const l={match:/\\"/},c={className:"string",begin:/'/,end:/'/},p={match:/\\'/},f={begin:/\$?\(\(/,end:/\)\)/,contains:[{begin:/\d+#[0-9a-f]+/,className:"number"},i.NUMBER_MODE,t]},b=["fish","bash","zsh","sh","csh","ksh","tcsh","dash","scsh"],m=i.SHEBANG({binary:`(${b.join("|")})`,relevance:10}),_={className:"function",begin:/\w[\w\d_]*\s*\(\s*\)\s*\{/,returnBegin:!0,contains:[i.inherit(i.TITLE_MODE,{begin:/\w[\w\d_]*/})],relevance:0},k=["if","then","else","elif","fi","time","for","while","until","in","do","done","case","esac","coproc","function","select"],u=["true","false"],d={match:/(\/[a-z._-]+)+/},h=["break","cd","continue","eval","exec","exit","export","getopts","hash","pwd","readonly","return","shift","test","times","trap","umask","unset"],g=["alias","bind","builtin","caller","command","declare","echo","enable","help","let","local","logout","mapfile","printf","read","readarray","source","sudo","type","typeset","ulimit","unalias"],x=["autoload","bg","bindkey","bye","cap","chdir","clone","comparguments","compcall","compctl","compdescribe","compfiles","compgroups","compquote","comptags","comptry","compvalues","dirs","disable","disown","echotc","echoti","emulate","fc","fg","float","functions","getcap","getln","history","integer","jobs","kill","limit","log","noglob","popd","print","pushd","pushln","rehash","sched","setcap","setopt","stat","suspend","ttyctl","unfunction","unhash","unlimit","unsetopt","vared","wait","whence","where","which","zcompile","zformat","zftp","zle","zmodload","zparseopts","zprof","zpty","zregexparse","zsocket","zstyle","ztcp"],v=["chcon","chgrp","chown","chmod","cp","dd","df","dir","dircolors","ln","ls","mkdir","mkfifo","mknod","mktemp","mv","realpath","rm","rmdir","shred","sync","touch","truncate","vdir","b2sum","base32","base64","cat","cksum","comm","csplit","cut","expand","fmt","fold","head","join","md5sum","nl","numfmt","od","paste","ptx","pr","sha1sum","sha224sum","sha256sum","sha384sum","sha512sum","shuf","sort","split","sum","tac","tail","tr","tsort","unexpand","uniq","wc","arch","basename","chroot","date","dirname","du","echo","env","expr","factor","groups","hostid","id","link","logname","nice","nohup","nproc","pathchk","pinky","printenv","printf","pwd","readlink","runcon","seq","sleep","stat","stdbuf","stty","tee","test","timeout","tty","uname","unlink","uptime","users","who","whoami","yes"];return{name:"Bash",aliases:["sh","zsh"],keywords:{$pattern:/\b[a-z][a-z0-9._-]+\b/,keyword:k,literal:u,built_in:[...h,...g,"set","shopt",...x,...v]},contains:[m,i.SHEBANG(),_,f,a,r,d,o,l,c,p,t]}}function xn(i){const e=i.regex,t=i.COMMENT("--","$"),n={scope:"string",variants:[{begin:/'/,end:/'/,contains:[{match:/''/}]}]},s={begin:/"/,end:/"/,contains:[{match:/""/}]},a=["true","false","unknown"],r=["double precision","large object","with timezone","without timezone"],o=["bigint","binary","blob","boolean","char","character","clob","date","dec","decfloat","decimal","float","int","integer","interval","nchar","nclob","national","numeric","real","row","smallint","time","timestamp","varchar","varying","varbinary"],l=["add","asc","collation","desc","final","first","last","view"],c=["abs","acos","all","allocate","alter","and","any","are","array","array_agg","array_max_cardinality","as","asensitive","asin","asymmetric","at","atan","atomic","authorization","avg","begin","begin_frame","begin_partition","between","bigint","binary","blob","boolean","both","by","call","called","cardinality","cascaded","case","cast","ceil","ceiling","char","char_length","character","character_length","check","classifier","clob","close","coalesce","collate","collect","column","commit","condition","connect","constraint","contains","convert","copy","corr","corresponding","cos","cosh","count","covar_pop","covar_samp","create","cross","cube","cume_dist","current","current_catalog","current_date","current_default_transform_group","current_path","current_role","current_row","current_schema","current_time","current_timestamp","current_path","current_role","current_transform_group_for_type","current_user","cursor","cycle","date","day","deallocate","dec","decimal","decfloat","declare","default","define","delete","dense_rank","deref","describe","deterministic","disconnect","distinct","double","drop","dynamic","each","element","else","empty","end","end_frame","end_partition","end-exec","equals","escape","every","except","exec","execute","exists","exp","external","extract","false","fetch","filter","first_value","float","floor","for","foreign","frame_row","free","from","full","function","fusion","get","global","grant","group","grouping","groups","having","hold","hour","identity","in","indicator","initial","inner","inout","insensitive","insert","int","integer","intersect","intersection","interval","into","is","join","json_array","json_arrayagg","json_exists","json_object","json_objectagg","json_query","json_table","json_table_primitive","json_value","lag","language","large","last_value","lateral","lead","leading","left","like","like_regex","listagg","ln","local","localtime","localtimestamp","log","log10","lower","match","match_number","match_recognize","matches","max","member","merge","method","min","minute","mod","modifies","module","month","multiset","national","natural","nchar","nclob","new","no","none","normalize","not","nth_value","ntile","null","nullif","numeric","octet_length","occurrences_regex","of","offset","old","omit","on","one","only","open","or","order","out","outer","over","overlaps","overlay","parameter","partition","pattern","per","percent","percent_rank","percentile_cont","percentile_disc","period","portion","position","position_regex","power","precedes","precision","prepare","primary","procedure","ptf","range","rank","reads","real","recursive","ref","references","referencing","regr_avgx","regr_avgy","regr_count","regr_intercept","regr_r2","regr_slope","regr_sxx","regr_sxy","regr_syy","release","result","return","returns","revoke","right","rollback","rollup","row","row_number","rows","running","savepoint","scope","scroll","search","second","seek","select","sensitive","session_user","set","show","similar","sin","sinh","skip","smallint","some","specific","specifictype","sql","sqlexception","sqlstate","sqlwarning","sqrt","start","static","stddev_pop","stddev_samp","submultiset","subset","substring","substring_regex","succeeds","sum","symmetric","system","system_time","system_user","table","tablesample","tan","tanh","then","time","timestamp","timezone_hour","timezone_minute","to","trailing","translate","translate_regex","translation","treat","trigger","trim","trim_array","true","truncate","uescape","union","unique","unknown","unnest","update","upper","user","using","value","values","value_of","var_pop","var_samp","varbinary","varchar","varying","versioning","when","whenever","where","width_bucket","window","with","within","without","year"],p=["abs","acos","array_agg","asin","atan","avg","cast","ceil","ceiling","coalesce","corr","cos","cosh","count","covar_pop","covar_samp","cume_dist","dense_rank","deref","element","exp","extract","first_value","floor","json_array","json_arrayagg","json_exists","json_object","json_objectagg","json_query","json_table","json_table_primitive","json_value","lag","last_value","lead","listagg","ln","log","log10","lower","max","min","mod","nth_value","ntile","nullif","percent_rank","percentile_cont","percentile_disc","position","position_regex","power","rank","regr_avgx","regr_avgy","regr_count","regr_intercept","regr_r2","regr_slope","regr_sxx","regr_sxy","regr_syy","row_number","sin","sinh","sqrt","stddev_pop","stddev_samp","substring","substring_regex","sum","tan","tanh","translate","translate_regex","treat","trim","trim_array","unnest","upper","value_of","var_pop","var_samp","width_bucket"],f=["current_catalog","current_date","current_default_transform_group","current_path","current_role","current_schema","current_transform_group_for_type","current_user","session_user","system_time","system_user","current_time","localtime","current_timestamp","localtimestamp"],b=["create table","insert into","primary key","foreign key","not null","alter table","add constraint","grouping sets","on overflow","character set","respect nulls","ignore nulls","nulls first","nulls last","depth first","breadth first"],m=p,_=[...c,...l].filter(v=>!p.includes(v)),k={scope:"variable",match:/@[a-z0-9][a-z0-9_]*/},u={scope:"operator",match:/[-+*/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?/,relevance:0},d={match:e.concat(/\b/,e.either(...m),/\s*\(/),relevance:0,keywords:{built_in:m}};function h(v){return e.concat(/\b/,e.either(...v.map(w=>w.replace(/\s+/,"\\s+"))),/\b/)}const g={scope:"keyword",match:h(b),relevance:0};function x(v,{exceptions:w,when:C}={}){const M=C;return w=w||[],v.map($=>$.match(/\|\d+$/)||w.includes($)?$:M($)?`${$}|0`:$)}return{name:"SQL",case_insensitive:!0,illegal:/[{}]|<\//,keywords:{$pattern:/\b[\w\.]+/,keyword:x(_,{when:v=>v.length<3}),literal:a,type:o,built_in:f},contains:[{scope:"type",match:h(r)},g,d,k,n,s,i.C_NUMBER_MODE,i.C_BLOCK_COMMENT_MODE,t,u]}}const ke="[A-Za-z$_][0-9A-Za-z$_]*",ct=["as","in","of","if","for","while","finally","var","new","function","do","return","void","else","break","catch","instanceof","with","throw","case","default","try","switch","continue","typeof","delete","let","yield","const","class","debugger","async","await","static","import","from","export","extends","using"],ut=["true","false","null","undefined","NaN","Infinity"],dt=["Object","Function","Boolean","Symbol","Math","Date","Number","BigInt","String","RegExp","Array","Float32Array","Float64Array","Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Int32Array","Uint16Array","Uint32Array","BigInt64Array","BigUint64Array","Set","Map","WeakSet","WeakMap","ArrayBuffer","SharedArrayBuffer","Atomics","DataView","JSON","Promise","Generator","GeneratorFunction","AsyncFunction","Reflect","Proxy","Intl","WebAssembly"],pt=["Error","EvalError","InternalError","RangeError","ReferenceError","SyntaxError","TypeError","URIError"],gt=["setInterval","setTimeout","clearInterval","clearTimeout","require","exports","eval","isFinite","isNaN","parseFloat","parseInt","decodeURI","decodeURIComponent","encodeURI","encodeURIComponent","escape","unescape"],ht=["arguments","this","super","console","window","document","localStorage","sessionStorage","module","global"],ft=[].concat(gt,dt,pt);function vn(i){const e=i.regex,t=(A,{after:L})=>{const P="</"+A[0].slice(1);return A.input.indexOf(P,L)!==-1},n=ke,s={begin:"<>",end:"</>"},a=/<[A-Za-z0-9\\._:-]+\s*\/>/,r={begin:/<[A-Za-z0-9\\._:-]+/,end:/\/[A-Za-z0-9\\._:-]+>|\/>/,isTrulyOpeningTag:(A,L)=>{const P=A[0].length+A.index,q=A.input[P];if(q==="<"||q===","){L.ignoreMatch();return}q===">"&&(t(A,{after:P})||L.ignoreMatch());let K;const ie=A.input.substring(P);if(K=ie.match(/^\s*=/)){L.ignoreMatch();return}if((K=ie.match(/^\s+extends\s+/))&&K.index===0){L.ignoreMatch();return}}},o={$pattern:ke,keyword:ct,literal:ut,built_in:ft,"variable.language":ht},l="[0-9](_?[0-9])*",c=`\\.(${l})`,p="0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*",f={className:"number",variants:[{begin:`(\\b(${p})((${c})|\\.)?|(${c}))[eE][+-]?(${l})\\b`},{begin:`\\b(${p})\\b((${c})\\b|\\.)?|(${c})\\b`},{begin:"\\b(0|[1-9](_?[0-9])*)n\\b"},{begin:"\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\b"},{begin:"\\b0[bB][0-1](_?[0-1])*n?\\b"},{begin:"\\b0[oO][0-7](_?[0-7])*n?\\b"},{begin:"\\b0[0-7]+n?\\b"}],relevance:0},b={className:"subst",begin:"\\$\\{",end:"\\}",keywords:o,contains:[]},m={begin:".?html`",end:"",starts:{end:"`",returnEnd:!1,contains:[i.BACKSLASH_ESCAPE,b],subLanguage:"xml"}},_={begin:".?css`",end:"",starts:{end:"`",returnEnd:!1,contains:[i.BACKSLASH_ESCAPE,b],subLanguage:"css"}},k={begin:".?gql`",end:"",starts:{end:"`",returnEnd:!1,contains:[i.BACKSLASH_ESCAPE,b],subLanguage:"graphql"}},u={className:"string",begin:"`",end:"`",contains:[i.BACKSLASH_ESCAPE,b]},h={className:"comment",variants:[i.COMMENT(/\/\*\*(?!\/)/,"\\*/",{relevance:0,contains:[{begin:"(?=@[A-Za-z]+)",relevance:0,contains:[{className:"doctag",begin:"@[A-Za-z]+"},{className:"type",begin:"\\{",end:"\\}",excludeEnd:!0,excludeBegin:!0,relevance:0},{className:"variable",begin:n+"(?=\\s*(-)|$)",endsParent:!0,relevance:0},{begin:/(?=[^\n])\s/,relevance:0}]}]}),i.C_BLOCK_COMMENT_MODE,i.C_LINE_COMMENT_MODE]},g=[i.APOS_STRING_MODE,i.QUOTE_STRING_MODE,m,_,k,u,{match:/\$\d+/},f];b.contains=g.concat({begin:/\{/,end:/\}/,keywords:o,contains:["self"].concat(g)});const x=[].concat(h,b.contains),v=x.concat([{begin:/(\s*)\(/,end:/\)/,keywords:o,contains:["self"].concat(x)}]),w={className:"params",begin:/(\s*)\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:o,contains:v},C={variants:[{match:[/class/,/\s+/,n,/\s+/,/extends/,/\s+/,e.concat(n,"(",e.concat(/\./,n),")*")],scope:{1:"keyword",3:"title.class",5:"keyword",7:"title.class.inherited"}},{match:[/class/,/\s+/,n],scope:{1:"keyword",3:"title.class"}}]},M={relevance:0,match:e.either(/\bJSON/,/\b[A-Z][a-z]+([A-Z][a-z]*|\d)*/,/\b[A-Z]{2,}([A-Z][a-z]+|\d)+([A-Z][a-z]*)*/,/\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\d)*([A-Z][a-z]*)*/),className:"title.class",keywords:{_:[...dt,...pt]}},$={label:"use_strict",className:"meta",relevance:10,begin:/^\s*['"]use (strict|asm)['"]/},ee={variants:[{match:[/function/,/\s+/,n,/(?=\s*\()/]},{match:[/function/,/\s*(?=\()/]}],className:{1:"keyword",3:"title.function"},label:"func.def",contains:[w],illegal:/%/},te={relevance:0,match:/\b[A-Z][A-Z_0-9]+\b/,className:"variable.constant"};function ne(A){return e.concat("(?!",A.join("|"),")")}const U={match:e.concat(/\b/,ne([...gt,"super","import"].map(A=>`${A}\\s*\\(`)),n,e.lookahead(/\s*\(/)),className:"title.function",relevance:0},se={begin:e.concat(/\./,e.lookahead(e.concat(n,/(?![0-9A-Za-z$_(])/))),end:n,excludeBegin:!0,keywords:"prototype",className:"property",relevance:0},ye={match:[/get|set/,/\s+/,n,/(?=\()/],className:{1:"keyword",3:"title.function"},contains:[{begin:/\(\)/},w]},re="(\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)|"+i.UNDERSCORE_IDENT_RE+")\\s*=>",xe={match:[/const|var|let/,/\s+/,n,/\s*/,/=\s*/,/(async\s*)?/,e.lookahead(re)],keywords:"async",className:{1:"keyword",3:"title.function"},contains:[w]};return{name:"JavaScript",aliases:["js","jsx","mjs","cjs"],keywords:o,exports:{PARAMS_CONTAINS:v,CLASS_REFERENCE:M},illegal:/#(?![$_A-z])/,contains:[i.SHEBANG({label:"shebang",binary:"node",relevance:5}),$,i.APOS_STRING_MODE,i.QUOTE_STRING_MODE,m,_,k,u,h,{match:/\$\d+/},f,M,{scope:"attr",match:n+e.lookahead(":"),relevance:0},xe,{begin:"("+i.RE_STARTERS_RE+"|\\b(case|return|throw)\\b)\\s*",keywords:"return throw case",relevance:0,contains:[h,i.REGEXP_MODE,{className:"function",begin:re,returnBegin:!0,end:"\\s*=>",contains:[{className:"params",variants:[{begin:i.UNDERSCORE_IDENT_RE,relevance:0},{className:null,begin:/\(\s*\)/,skip:!0},{begin:/(\s*)\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:o,contains:v}]}]},{begin:/,/,relevance:0},{match:/\s+/,relevance:0},{variants:[{begin:s.begin,end:s.end},{match:a},{begin:r.begin,"on:begin":r.isTrulyOpeningTag,end:r.end}],subLanguage:"xml",contains:[{begin:r.begin,end:r.end,skip:!0,contains:["self"]}]}]},ee,{beginKeywords:"while if switch catch for"},{begin:"\\b(?!function)"+i.UNDERSCORE_IDENT_RE+"\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)\\s*\\{",returnBegin:!0,label:"func.def",contains:[w,i.inherit(i.TITLE_MODE,{begin:n,className:"title.function"})]},{match:/\.\.\./,relevance:0},se,{match:"\\$"+n,relevance:0},{match:[/\bconstructor(?=\s*\()/],className:{1:"title.function"},contains:[w]},U,te,C,ye,{match:/\$[(.]/}]}}function En(i){const e=i.regex,t=vn(i),n=ke,s=["any","void","number","boolean","string","object","never","symbol","bigint","unknown"],a={begin:[/namespace/,/\s+/,i.IDENT_RE],beginScope:{1:"keyword",3:"title.class"}},r={beginKeywords:"interface",end:/\{/,excludeEnd:!0,keywords:{keyword:"interface extends",built_in:s},contains:[t.exports.CLASS_REFERENCE]},o={className:"meta",relevance:10,begin:/^\s*['"]use strict['"]/},l=["type","interface","public","private","protected","implements","declare","abstract","readonly","enum","override","satisfies"],c={$pattern:ke,keyword:ct.concat(l),literal:ut,built_in:ft.concat(s),"variable.language":ht},p={className:"meta",begin:"@"+n},f=(k,u,d)=>{const h=k.contains.findIndex(g=>g.label===u);if(h===-1)throw new Error("can not find mode to replace");k.contains.splice(h,1,d)};Object.assign(t.keywords,c),t.exports.PARAMS_CONTAINS.push(p);const b=t.contains.find(k=>k.scope==="attr"),m=Object.assign({},b,{match:e.concat(n,e.lookahead(/\s*\?:/))});t.exports.PARAMS_CONTAINS.push([t.exports.CLASS_REFERENCE,b,m]),t.contains=t.contains.concat([p,a,r,m]),f(t,"shebang",i.SHEBANG()),f(t,"use_strict",o);const _=t.contains.find(k=>k.label==="func.def");return _.relevance=0,Object.assign(t,{name:"TypeScript",aliases:["ts","tsx","mts","cts"]}),t}const Sn=i=>({IMPORTANT:{scope:"meta",begin:"!important"},BLOCK_COMMENT:i.C_BLOCK_COMMENT_MODE,HEXCOLOR:{scope:"number",begin:/#(([0-9a-fA-F]{3,4})|(([0-9a-fA-F]{2}){3,4}))\b/},FUNCTION_DISPATCH:{className:"built_in",begin:/[\w-]+(?=\()/},ATTRIBUTE_SELECTOR_MODE:{scope:"selector-attr",begin:/\[/,end:/\]/,illegal:"$",contains:[i.APOS_STRING_MODE,i.QUOTE_STRING_MODE]},CSS_NUMBER_MODE:{scope:"number",begin:i.NUMBER_RE+"(%|em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc|px|deg|grad|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)?",relevance:0},CSS_VARIABLE:{className:"attr",begin:/--[A-Za-z_][A-Za-z0-9_-]*/}}),Tn=["a","abbr","address","article","aside","audio","b","blockquote","body","button","canvas","caption","cite","code","dd","del","details","dfn","div","dl","dt","em","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","html","i","iframe","img","input","ins","kbd","label","legend","li","main","mark","menu","nav","object","ol","optgroup","option","p","picture","q","quote","samp","section","select","source","span","strong","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","ul","var","video"],An=["defs","g","marker","mask","pattern","svg","switch","symbol","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feFlood","feGaussianBlur","feImage","feMerge","feMorphology","feOffset","feSpecularLighting","feTile","feTurbulence","linearGradient","radialGradient","stop","circle","ellipse","image","line","path","polygon","polyline","rect","text","use","textPath","tspan","foreignObject","clipPath"],Rn=[...Tn,...An],Nn=["any-hover","any-pointer","aspect-ratio","color","color-gamut","color-index","device-aspect-ratio","device-height","device-width","display-mode","forced-colors","grid","height","hover","inverted-colors","monochrome","orientation","overflow-block","overflow-inline","pointer","prefers-color-scheme","prefers-contrast","prefers-reduced-motion","prefers-reduced-transparency","resolution","scan","scripting","update","width","min-width","max-width","min-height","max-height"].sort().reverse(),Cn=["active","any-link","blank","checked","current","default","defined","dir","disabled","drop","empty","enabled","first","first-child","first-of-type","fullscreen","future","focus","focus-visible","focus-within","has","host","host-context","hover","indeterminate","in-range","invalid","is","lang","last-child","last-of-type","left","link","local-link","not","nth-child","nth-col","nth-last-child","nth-last-col","nth-last-of-type","nth-of-type","only-child","only-of-type","optional","out-of-range","past","placeholder-shown","read-only","read-write","required","right","root","scope","target","target-within","user-invalid","valid","visited","where"].sort().reverse(),Mn=["after","backdrop","before","cue","cue-region","first-letter","first-line","grammar-error","marker","part","placeholder","selection","slotted","spelling-error"].sort().reverse(),On=["accent-color","align-content","align-items","align-self","alignment-baseline","all","anchor-name","animation","animation-composition","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-range","animation-range-end","animation-range-start","animation-timeline","animation-timing-function","appearance","aspect-ratio","backdrop-filter","backface-visibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-position-x","background-position-y","background-repeat","background-size","baseline-shift","block-size","border","border-block","border-block-color","border-block-end","border-block-end-color","border-block-end-style","border-block-end-width","border-block-start","border-block-start-color","border-block-start-style","border-block-start-width","border-block-style","border-block-width","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-end-end-radius","border-end-start-radius","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-inline","border-inline-color","border-inline-end","border-inline-end-color","border-inline-end-style","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-style","border-inline-start-width","border-inline-style","border-inline-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-start-end-radius","border-start-start-radius","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-align","box-decoration-break","box-direction","box-flex","box-flex-group","box-lines","box-ordinal-group","box-orient","box-pack","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","caret-color","clear","clip","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","color-scheme","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","contain","contain-intrinsic-block-size","contain-intrinsic-height","contain-intrinsic-inline-size","contain-intrinsic-size","contain-intrinsic-width","container","container-name","container-type","content","content-visibility","counter-increment","counter-reset","counter-set","cue","cue-after","cue-before","cursor","cx","cy","direction","display","dominant-baseline","empty-cells","enable-background","field-sizing","fill","fill-opacity","fill-rule","filter","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","flood-color","flood-opacity","flow","font","font-display","font-family","font-feature-settings","font-kerning","font-language-override","font-optical-sizing","font-palette","font-size","font-size-adjust","font-smooth","font-smoothing","font-stretch","font-style","font-synthesis","font-synthesis-position","font-synthesis-small-caps","font-synthesis-style","font-synthesis-weight","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-emoji","font-variant-ligatures","font-variant-numeric","font-variant-position","font-variation-settings","font-weight","forced-color-adjust","gap","glyph-orientation-horizontal","glyph-orientation-vertical","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-start","grid-gap","grid-row","grid-row-end","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","hanging-punctuation","height","hyphenate-character","hyphenate-limit-chars","hyphens","icon","image-orientation","image-rendering","image-resolution","ime-mode","initial-letter","initial-letter-align","inline-size","inset","inset-area","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","isolation","justify-content","justify-items","justify-self","kerning","left","letter-spacing","lighting-color","line-break","line-height","line-height-step","list-style","list-style-image","list-style-position","list-style-type","margin","margin-block","margin-block-end","margin-block-start","margin-bottom","margin-inline","margin-inline-end","margin-inline-start","margin-left","margin-right","margin-top","margin-trim","marker","marker-end","marker-mid","marker-start","marks","mask","mask-border","mask-border-mode","mask-border-outset","mask-border-repeat","mask-border-slice","mask-border-source","mask-border-width","mask-clip","mask-composite","mask-image","mask-mode","mask-origin","mask-position","mask-repeat","mask-size","mask-type","masonry-auto-flow","math-depth","math-shift","math-style","max-block-size","max-height","max-inline-size","max-width","min-block-size","min-height","min-inline-size","min-width","mix-blend-mode","nav-down","nav-index","nav-left","nav-right","nav-up","none","normal","object-fit","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-anchor","overflow-block","overflow-clip-margin","overflow-inline","overflow-wrap","overflow-x","overflow-y","overlay","overscroll-behavior","overscroll-behavior-block","overscroll-behavior-inline","overscroll-behavior-x","overscroll-behavior-y","padding","padding-block","padding-block-end","padding-block-start","padding-bottom","padding-inline","padding-inline-end","padding-inline-start","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","paint-order","pause","pause-after","pause-before","perspective","perspective-origin","place-content","place-items","place-self","pointer-events","position","position-anchor","position-visibility","print-color-adjust","quotes","r","resize","rest","rest-after","rest-before","right","rotate","row-gap","ruby-align","ruby-position","scale","scroll-behavior","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-align","scroll-snap-stop","scroll-snap-type","scroll-timeline","scroll-timeline-axis","scroll-timeline-name","scrollbar-color","scrollbar-gutter","scrollbar-width","shape-image-threshold","shape-margin","shape-outside","shape-rendering","speak","speak-as","src","stop-color","stop-opacity","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","table-layout","text-align","text-align-all","text-align-last","text-anchor","text-combine-upright","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-skip-ink","text-decoration-style","text-decoration-thickness","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-indent","text-justify","text-orientation","text-overflow","text-rendering","text-shadow","text-size-adjust","text-transform","text-underline-offset","text-underline-position","text-wrap","text-wrap-mode","text-wrap-style","timeline-scope","top","touch-action","transform","transform-box","transform-origin","transform-style","transition","transition-behavior","transition-delay","transition-duration","transition-property","transition-timing-function","translate","unicode-bidi","user-modify","user-select","vector-effect","vertical-align","view-timeline","view-timeline-axis","view-timeline-inset","view-timeline-name","view-transition-name","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","white-space","white-space-collapse","widows","width","will-change","word-break","word-spacing","word-wrap","writing-mode","x","y","z-index","zoom"].sort().reverse();function Ln(i){const e=i.regex,t=Sn(i),n={begin:/-(webkit|moz|ms|o)-(?=[a-z])/},s="and or not only",a=/@-?\w[\w]*(-\w+)*/,r="[a-zA-Z-][a-zA-Z0-9_-]*",o=[i.APOS_STRING_MODE,i.QUOTE_STRING_MODE];return{name:"CSS",case_insensitive:!0,illegal:/[=|'\$]/,keywords:{keyframePosition:"from to"},classNameAliases:{keyframePosition:"selector-tag"},contains:[t.BLOCK_COMMENT,n,t.CSS_NUMBER_MODE,{className:"selector-id",begin:/#[A-Za-z0-9_-]+/,relevance:0},{className:"selector-class",begin:"\\."+r,relevance:0},t.ATTRIBUTE_SELECTOR_MODE,{className:"selector-pseudo",variants:[{begin:":("+Cn.join("|")+")"},{begin:":(:)?("+Mn.join("|")+")"}]},t.CSS_VARIABLE,{className:"attribute",begin:"\\b("+On.join("|")+")\\b"},{begin:/:/,end:/[;}{]/,contains:[t.BLOCK_COMMENT,t.HEXCOLOR,t.IMPORTANT,t.CSS_NUMBER_MODE,...o,{begin:/(url|data-uri)\(/,end:/\)/,relevance:0,keywords:{built_in:"url data-uri"},contains:[...o,{className:"string",begin:/[^)]/,endsWithParent:!0,excludeEnd:!0}]},t.FUNCTION_DISPATCH]},{begin:e.lookahead(/@/),end:"[{;]",relevance:0,illegal:/:/,contains:[{className:"keyword",begin:a},{begin:/\s/,endsWithParent:!0,excludeEnd:!0,relevance:0,keywords:{$pattern:/[a-z-]+/,keyword:s,attribute:Nn.join(" ")},contains:[{begin:/[a-z-]+(?=:)/,className:"attribute"},...o,t.CSS_NUMBER_MODE]}]},{className:"selector-tag",begin:"\\b("+Rn.join("|")+")\\b"}]}}const In={style:{"user-select":"none","-webkit-user-select":"none","-moz-user-select":"none","-ms-user-select":"none"}},$n={class:"message-header",style:{"user-select":"none","-webkit-user-select":"none","-moz-user-select":"none","-ms-user-select":"none"}},Bn={class:"sender-name"},zn={class:"message-time"},Dn={key:0,class:"reasoning-container"},Pn={class:"reasoning-title"},Un={class:"reasoning-toggle"},Hn=["innerHTML"],Gn=["innerHTML"],Fn={class:"message-actions"},Zn=["disabled"],qn=["disabled"],Kn={__name:"MessageBubble",props:{content:{type:String,required:!0},isUser:{type:Boolean,default:!1},isError:{type:Boolean,default:!1},timestamp:{type:Number,default:()=>Date.now()},selectedModel:{type:String,default:"AI助手"},reasoning:{type:String,default:""},reasoningTime:{type:String,default:""},disabled:{type:Boolean,default:!1},hasEditor:{type:Boolean,default:!1}},emits:["resend","insertCode","insert","copy-full-message"],setup(i,{emit:e}){Array.prototype.at||(Array.prototype.at=function(u){return u>=0?this[u]:this[this.length+u]}),String.prototype.at||(String.prototype.at=function(u){return u>=0?this[u]:this[this.length+u]}),I.registerLanguage("javascript",mn),I.registerLanguage("java",kn),I.registerLanguage("python",wn),I.registerLanguage("xml",Fe),I.registerLanguage("html",Fe),I.registerLanguage("json",_n),I.registerLanguage("bash",yn),I.registerLanguage("sql",xn),I.registerLanguage("typescript",En),I.registerLanguage("css",Ln);const t=i,n=e,s=V(!0),a=()=>{s.value=!s.value},r=V({}),o=j(()=>({fontSize:`${l?.fontSize||16}px`,fontFamily:l?.fontFamily||"sans-serif"})),l=(()=>{try{return window.configStore&&window.configStore.chat?window.configStore.chat:{fontSize:16,fontFamily:"微软雅黑, sans-serif"}}catch(u){return console.error("获取配置失败:",u),{fontSize:16,fontFamily:"微软雅黑, sans-serif"}}})(),c=u=>{if(!u)return"plaintext";if(u.trim().startsWith("{")&&u.trim().endsWith("}"))try{return JSON.parse(u),"json"}catch{}return u.includes("public class")||u.includes("private class")||u.includes("import java.")||u.includes("@Override")?"java":u.includes("func ")&&u.includes("package main")?"go":u.includes("def ")&&u.includes("import ")||u.includes("print(")||u.includes("import os")?"python":u.includes("<template>")&&u.includes("<script>")?"html":u.includes("const ")&&u.includes("=>")||u.includes("function(")||u.includes("export default")?"javascript":u.includes("<div")||u.includes("<span")||u.includes("<html")||u.includes("</body>")?"html":u.includes("SELECT ")&&u.includes(" FROM ")||u.includes("INSERT INTO")?"sql":"plaintext"},p=new y.Renderer;p.code=(u,d)=>{let h=d,g=u;if(g&&typeof g=="object")if(g.type==="code")if(g.text)h=g.lang||h||"plaintext",g=g.text;else if(g.raw){const U=g.raw.split(`
`,1);h=U[0]?.trim()||g.lang||h||"plaintext",g=g.raw.substring(U[0].length+1)}else g=JSON.stringify(g,null,2),h="json";else g=JSON.stringify(g,null,2),h="json";if(g===null||typeof g>"u")g="";else if(typeof g!="string")try{g=String(g)}catch(U){console.error("Code content to string conversion error:",U),g="/* Error converting code to string */"}const x=h?h.toLowerCase().trim():c(g),v=I.getLanguage(x)?x:"plaintext",w=`code-mb-${Date.now()}-${Math.floor(Math.random()*1e5)}`;r.value[w]=g;let C;try{C=I.highlight(g,{language:v,ignoreIllegals:!0}).value}catch(U){console.error("代码高亮错误:",U,"Lang:",v,"Original lang:",d);try{C=I.highlight(g,{language:"plaintext",ignoreIllegals:!0}).value}catch(se){console.error("纯文本高亮错误:",se),C=g.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;")}}const M=v==="plaintext"?"text":v,$=`
    <button class="hljs-code-action-btn hljs-code-copy-specific" onclick="window.copySpecificCodeBlock_MB('${w}', this)">
      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect><path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path></svg>
      <span>Copy Code</span>
    </button>
  `,ee=`
    <button class="hljs-code-action-btn hljs-code-collapse-toggle" onclick="window.toggleCodeBlockCollapse_MB('${w}', this)">
      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="18 15 12 9 6 15"></polyline></svg>
      <span>Collapse</span>
    </button>
  `,te=`
    <button class="hljs-code-action-btn hljs-code-wrap-toggle" onclick="window.toggleCodeWrap_MB('${w}', this)">
      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="7 7 17 7 17 17"></polyline></svg>
      <span>Wrap</span>
    </button>
  `,ne=t.hasEditor?`
    <button class="hljs-code-action-btn hljs-code-insert-specific" onclick="window.emitInsertCodeEvent_MB('${w}', this)">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><polyline points="19 12 12 19 5 12"></polyline></svg>
      <span>Insert Code</span>
    </button>
  `:"";return`
    <div class="hljs-code-wrapper">
      <div class="hljs-code-header">
        <span class="hljs-code-lang">${M}</span>
        <div class="hljs-code-actions">
          ${$}
          ${ee}
          ${te}
          ${ne}
        </div>
      </div>
      <pre class="hljs"><code id="${w}" class="language-${v}">${C}</code></pre>
    </div>
  `},y.setOptions({renderer:p,gfm:!0,breaks:!0,sanitize:!1,smartypants:!1});const f=j(()=>{if(!t.content)return"";if(t.isError)return`<div class="error-message">${W.sanitize(t.content)}</div>`;try{if(t.content.trim().startsWith("{")&&t.content.trim().endsWith("}"))try{const u=JSON.parse(t.content),d=JSON.stringify(u,null,2),h=y("```json\n"+d+"\n```");return W.sanitize(h,{ADD_ATTR:["class","onclick"],ADD_TAGS:["button","pre","code"]})}catch{}return W.sanitize(y(t.content),{ADD_ATTR:["class","onclick"],ADD_TAGS:["button","pre","code"]})}catch(u){return console.error("渲染Markdown失败:",u),`<pre>${W.sanitize(t.content)}</pre>`}}),b=j(()=>t.reasoning?W.sanitize(y(t.reasoning)):""),m=u=>{if(!u)return"";const d=new Date(u),h=d.getFullYear(),g=(d.getMonth()+1).toString().padStart(2,"0"),x=d.getDate().toString().padStart(2,"0"),v=d.getHours().toString().padStart(2,"0"),w=d.getMinutes().toString().padStart(2,"0");return`${h}/${g}/${x} ${v}:${w}`},_=V(null),k=()=>{const d=window.getSelection().toString()||t.content;window.pywebview.api.copy_to_clipboard(d).then(()=>{Le.success("复制成功")}).catch(h=>{Le.error("复制失败")})};return qe(()=>{W.addHook("uponSanitizeElement",(u,d)=>{if(d.tagName==="button"||d.tagName==="pre"||d.tagName==="code")return u}),W.addHook("uponSanitizeAttribute",(u,d)=>{d.attrName==="onclick"&&(d.attrValue.includes("copyCodeToClipboard")||d.attrValue.includes("toggleCodeExpansion"))&&(d.forceKeepAttr=!0),d.attrName==="class"&&(d.forceKeepAttr=!0)}),window.copySpecificCodeBlock_MB=(u,d)=>{const h=r.value[u];h&&window.pywebview.api.copy_to_clipboard(h).then(()=>{const g=d.innerHTML;d.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 6L9 17l-5-5"></path></svg><span>Copied</span>',d.classList.add("copied"),setTimeout(()=>{d.innerHTML=g,d.classList.remove("copied")},2e3)}).catch(g=>{console.error("Copy specific code failed (MB):",g);const x=d.innerHTML;d.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line></svg><span>Error</span>',setTimeout(()=>{d.innerHTML=x},2e3)})},window.toggleCodeWrap_MB=(u,d)=>{const h=document.getElementById(u),g=h?h.closest("pre.hljs"):null;if(g&&h&&d){const x=g.classList.toggle("wrapped");h.classList.toggle("wrapped",x),x?(d.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect></svg><span>Unwrap</span>',d.classList.add("active")):(d.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="7 7 17 7 17 17"></polyline></svg><span>Wrap</span>',d.classList.remove("active"))}},window.emitInsertCodeEvent_MB=(u,d)=>{const h=r.value[u];if(h&&(n("insertCode",h),d)){const g=d.innerHTML;d.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 6L9 17l-5-5"></path></svg><span>Requested</span>',d.classList.add("copied"),setTimeout(()=>{d.innerHTML=g,d.classList.remove("copied")},1500)}},window.toggleCodeBlockCollapse_MB=(u,d)=>{const h=document.getElementById(u),g=h?h.closest("pre.hljs"):null;g&&d&&(g.classList.toggle("content-collapsed")?(d.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg><span>Expand</span>',d.classList.add("active")):(d.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="18 15 12 9 6 15"></polyline></svg><span>Collapse</span>',d.classList.remove("active")))}}),Ke(()=>{delete window.copySpecificCodeBlock_MB,delete window.toggleCodeWrap_MB,delete window.emitInsertCodeEvent_MB,delete window.toggleCodeBlockCollapse_MB}),(u,d)=>(R(),N("div",{class:F(["message-bubble-container",{"from-user":i.isUser}])},[E("div",{class:F(["avatar",{"user-avatar":i.isUser,"ai-avatar":!i.isUser}])},[E("span",In,Z(i.isUser?"我":"AI"),1)],2),E("div",{class:"message-content",style:We(o.value)},[E("div",$n,[E("div",Bn,Z(i.isUser?"您":i.selectedModel),1),E("div",zn,Z(m(i.timestamp)),1)]),i.reasoning&&!i.isUser?(R(),N("div",Dn,[E("div",{class:"reasoning-header",onClick:a},[d[3]||(d[3]=E("div",{class:"reasoning-icon"},[E("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[E("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"})])],-1)),E("div",Pn,[d[1]||(d[1]=kt(" 思考过程 ")),E("span",{class:F(["reasoning-time",{thinking:i.reasoningTime==="思考中..."}])},Z(i.reasoningTime),3)]),E("div",Un,[(R(),N("svg",{class:F(["toggle-icon",{"is-active":!s.value}]),xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},d[2]||(d[2]=[E("polyline",{points:"6 9 12 15 18 9"},null,-1)]),2))])]),E("div",{class:F(["reasoning-content",{collapsed:s.value}])},[E("div",{class:"markdown-body",innerHTML:b.value},null,8,Hn)],2)])):B("",!0),E("div",{class:F(["message-bubble",{"user-bubble":i.isUser,"ai-bubble":!i.isUser,"error-bubble":i.isError}]),style:{"user-select":"text !important"}},[E("div",{class:"markdown-body",innerHTML:f.value,style:{"user-select":"text !important"}},null,8,Gn)],2),E("div",Fn,[i.isUser?(R(),N("button",{key:0,class:"action-button resend-button",onClick:d[0]||(d[0]=h=>u.$emit("resend")),disabled:i.disabled},d[4]||(d[4]=[wt('<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-v-2b9114b6><path d="M21 2v6h-6" data-v-2b9114b6></path><path d="M3 12a9 9 0 0 1 15-6.7L21 8" data-v-2b9114b6></path><path d="M3 22v-6h6" data-v-2b9114b6></path><path d="M21 12a9 9 0 0 1-15 6.7L3 16" data-v-2b9114b6></path></svg><span data-v-2b9114b6>重发</span>',2)]),8,Zn)):B("",!0),E("button",{ref_key:"copyButtonRef",ref:_,class:F(["action-button copy-button",`copy-button-${i.isUser?"user":"ai"}`]),onClick:k,disabled:i.disabled},d[5]||(d[5]=[E("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[E("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2"}),E("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"})],-1),E("span",null,"复制",-1)]),10,qn)])],4)],2))}},us=Ze(Kn,[["__scopeId","data-v-2b9114b6"]]),Wn={class:"selected-text"},Vn={key:0,class:"dropdown-header"},jn={class:"header-title"},Yn={key:1,class:"search-box"},Qn=["onClick"],Xn={key:0,class:"option-checkbox"},Jn={class:"option-content"},es={class:"option-label"},ts={key:0,class:"option-description"},ns={key:1,class:"option-provider"},ss={key:1,class:"selected-indicator"},rs={key:0,class:"no-options"},is={__name:"UniversalSelector",props:{modelValue:{type:[String,Array],default:()=>[]},options:{type:Array,default:()=>[]},multiple:{type:Boolean,default:!1},placeholder:{type:String,default:"请选择"},showHeader:{type:Boolean,default:!0},headerTitle:{type:String,default:"请选择"},searchable:{type:Boolean,default:!1},maxHeight:{type:String,default:"300px"},width:{type:String,default:"100%"},clearable:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1}},emits:["update:modelValue","change","clear"],setup(i,{emit:e}){const t=i,n=e,s=V(!1),a=V(""),r=V(null),o=j(()=>t.multiple?Array.isArray(t.modelValue)?t.modelValue:[]:t.modelValue?[t.modelValue]:[]),l=j(()=>{if(!a.value)return t.options;const h=a.value.toLowerCase();return t.options.filter(g=>g.label.toLowerCase().includes(h)||g.description&&g.description.toLowerCase().includes(h)||g.provider&&g.provider.toLowerCase().includes(h))}),c=j(()=>{if(o.value.length===0)return t.placeholder;if(t.multiple){if(o.value.length===1){const h=t.options.find(g=>g.value===o.value[0]);return h?h.label:o.value[0]}return`已选择 ${o.value.length} 项`}else{const h=t.options.find(g=>g.value===t.modelValue);return h?h.label:t.modelValue}}),p=j(()=>({maxHeight:t.maxHeight,width:t.width})),f=()=>{t.disabled||(s.value=!s.value,s.value&&$e(()=>{k()}))},b=h=>{if(!h.disabled)if(t.multiple){const g=[...o.value],x=g.indexOf(h.value);x>-1?g.splice(x,1):g.push(h.value),n("update:modelValue",g),n("change",g,h)}else n("update:modelValue",h.value),n("change",h.value,h),s.value=!1},m=h=>o.value.includes(h),_=()=>{const h=t.multiple?[]:"";n("update:modelValue",h),n("clear"),n("change",h)},k=()=>{if(!r.value||o.value.length===0)return;const h=r.value.querySelector(".option-item.is-selected");h&&h.scrollIntoView({block:"nearest"})},u=V(null),d=h=>{u.value&&!u.value.contains(h.target)&&(s.value=!1,a.value="")};return qe(()=>{document.addEventListener("click",d)}),Ke(()=>{document.removeEventListener("click",d)}),_t(a,()=>{$e(()=>{k()})}),(h,g)=>{const x=xt,v=Tt;return R(),N("div",{class:F(["universal-selector",{"is-open":s.value}]),ref_key:"selectorRef",ref:u},[E("div",{class:"selector-trigger",onClick:f},[E("span",Wn,Z(c.value),1),H(x,{class:F(["dropdown-icon",{"is-active":s.value}])},{default:X(()=>[H(ae(yt))]),_:1},8,["class"])]),s.value?(R(),N("div",{key:0,class:"selector-dropdown",style:We(p.value)},[i.showHeader?(R(),N("div",Vn,[E("span",jn,Z(i.headerTitle),1),i.multiple&&o.value.length>0?(R(),N("button",{key:0,class:"clear-btn",onClick:_,title:"清空选择"},[H(x,null,{default:X(()=>[H(ae(vt))]),_:1})])):B("",!0)])):B("",!0),i.searchable?(R(),N("div",Yn,[H(v,{modelValue:a.value,"onUpdate:modelValue":g[0]||(g[0]=w=>a.value=w),placeholder:"搜索...",size:"small",clearable:"",onClick:g[1]||(g[1]=St(()=>{},["stop"]))},{prefix:X(()=>[H(x,null,{default:X(()=>[H(ae(Et))]),_:1})]),_:1},8,["modelValue"])])):B("",!0),E("div",{class:"options-container",ref_key:"optionsContainer",ref:r},[(R(!0),N(At,null,Rt(l.value,w=>(R(),N("div",{key:w.value,class:F(["option-item",{"is-selected":m(w.value),"is-disabled":w.disabled}]),onClick:C=>b(w)},[i.multiple?(R(),N("div",Xn,[m(w.value)?(R(),Nt(x,{key:0,class:"check-icon"},{default:X(()=>[H(ae(Ie))]),_:1})):B("",!0)])):B("",!0),E("div",Jn,[E("div",es,Z(w.label),1),w.description?(R(),N("div",ts,Z(w.description),1)):B("",!0),w.provider?(R(),N("div",ns,Z(w.provider),1)):B("",!0)]),!i.multiple&&m(w.value)?(R(),N("div",ss,[H(x,null,{default:X(()=>[H(ae(Ie))]),_:1})])):B("",!0)],10,Qn))),128)),l.value.length===0?(R(),N("div",rs,Z(a.value?"未找到匹配项":"暂无选项"),1)):B("",!0)],512)],4)):B("",!0)],2)}}},ds=Ze(is,[["__scopeId","data-v-1a5ec864"]]);export{us as M,ds as U,_n as a,Ln as c,mn as j,y as m,wn as p,Fe as x};
