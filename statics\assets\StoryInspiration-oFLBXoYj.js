import{r as x,bK as Ge,bL as Qe,bt as j,c as N,_ as Xe,a as Ye,w as ye,o as We,E as d,b as h,M as z,d as l,C as y,e as o,g as n,F as E,B as Ke,t as et,bb as tt,k as lt,m as r,aX as ot,bM as nt,v as g,bc as st,R as D,S as q,aD as at,aE as it,aA as rt,bN as ut,y as ct,p as A,aK as dt,G as se,a$ as ge,aw as pt,au as ae,aV as mt,bO as ft,bP as vt,aq as _t,J as ht,n as ie,bl as yt,aP as gt,aQ as bt,bh as kt,a9 as be,bk as Ct,bj as xt,bn as wt,az as Et,ax as Vt,s as St,bJ as Lt,Y as Pt,ah as ke}from"./entry-DxFfH4M0.js";/* empty css                        *//* empty css                    *//* empty css                   *//* empty css               *//* empty css                 *//* empty css                    *//* empty css                         *//* empty css                *//* empty css                          *//* empty css                  */const Ce={config:{version:"1.0.0",description:"通过替换不同剧情元素来优化故事效果",commonCheckPoints:["替换是否符合故事发展？","替换是否能带来更好的效果？","替换是否符合修真世界设定？","替换是否为后续发展埋下伏笔？"]},perspectiveSwap:{description:"通过切换不同视角来优化剧情效果",elements:[{type:"叙事视角",options:[{name:"第一人称",description:'从"我"的视角讲述故事',effect:"增加代入感，让读者更容易感同身受",examples:["我感受到体内灵力涌动，每一缕灵气都在经脉中欢快地流转"]},{name:"第三人称全知",description:"上帝视角，可以看到所有角色的想法",effect:"可以展示更多信息，增加剧情的复杂性",examples:["李青山暗自盘算，而对面的王掌门却早已洞悉了他的心思"]},{name:"第三人称限知",description:"跟随特定角色，只知道该角色所知道的信息",effect:"制造悬疑感，增加故事的不确定性",examples:["他看着对方高深莫测的表情，猜不透对方究竟在想什么"]}]},{type:"人物视角",options:[{name:"主角视角",description:"从主角角度展现故事",effect:"让读者更容易理解主角的想法和感受",examples:["修炼到瓶颈时的那种无助感，只有经历过的人才能理解"]},{name:"配角视角",description:"从配角的角度观察主角",effect:"展现主角的另一面，增加人物的立体感",examples:["作为师兄，我看着这个天赋异禀的小师弟一步步成长"]},{name:"对手视角",description:"从对手的角度看待冲突",effect:"增加对立面的可信度，使冲突更有说服力",examples:["我必须承认，这个年轻人的修炼速度远超我的预期"]}]}],checkPoints:["视角转换是否自然？","新视角是否带来新信息？","是否保持叙事连贯性？","是否控制好信息量？","是否有效推进了主线？","是否埋下了后续伏笔？","是否展现了新的信息？","是否深化了人物性格？","是否展示了人物关系？","是否有人物成长？","是否营造了特定氛围？","氛围是否配合剧情？","是否有画面感？","与上一场景是否有合理过渡？","是否为下一场景做好铺垫？","场景切换是否流畅？"]},sceneSwap:{description:"通过替换场景要素来强化剧情效果",elements:[{type:"时间要素",options:[{name:"黄昏",description:"日落时分，阴阳交替之际",effect:"营造神秘感，增加紧张气氛"},{name:"破晓",description:"黎明时分，新的开始",effect:"象征希望，展现新生"}]},{type:"空间要素",options:[{name:"古战场",description:"充满历史感的古代战场",effect:"增加历史感，展现世界观"},{name:"密室",description:"隐秘的修炼密室",effect:"制造神秘感，增加紧张气氛"}]},{type:"氛围要素",options:[{name:"黄昏",description:"日落时分，阴阳交替之际",effect:"营造神秘感，增加紧张气氛"},{name:"破晓",description:"黎明时分，新的开始",effect:"象征希望，展现新生"}]},{type:"场景目的",options:[{name:"黄昏",description:"日落时分，阴阳交替之际",effect:"营造神秘感，增加紧张气氛"},{name:"破晓",description:"黎明时分，新的开始",effect:"象征希望，展现新生"}]},{type:"场景卖点",options:[{name:"期待感",description:"场景卖点",effect:"增加期待感，吸引读者"},{name:"悬念",description:"场景卖点",effect:"制造悬念，增加紧张气氛"}]}],checkPoints:["场景替换是否合理？","新场景是否能更好地服务剧情？","是否与人物行为相协调？","是否增强了故事氛围？"]},conflictSwap:{description:"通过替换冲突类型来深化剧情",elements:[{type:"内心冲突",options:[{name:"道德困境",description:"在两个都正确的选择之间做决定",effect:"增加人物的深度，展现价值观的碰撞",examples:["为了变强而使用禁术，还是坚持正道修炼？"]},{name:"理想与现实",description:"理想与现实的差距带来的挣扎",effect:"展现人物的成长过程",examples:["修仙之路与凡人亲情的抉择"]},{name:"欲望与责任",description:"个人欲望与责任义务的矛盾",effect:"展现人物的选择与成长",examples:["是追求个人实力，还是履行宗门职责？"]}]},{type:"外部冲突",options:[{name:"势力之争",description:"不同修仙势力之间的对抗",effect:"展现世界观，推动故事发展",examples:["正道与魔道的千年对立"]},{name:"资源争夺",description:"为了有限的修炼资源而产生的冲突",effect:"突出修仙世界的残酷性",examples:["多个宗门为了一处上古遗迹而争斗"]},{name:"理念之争",description:"不同修炼理念之间的冲突",effect:"深化世界观，展现不同立场",examples:["守护凡人还是追求长生？"]}]}],checkPoints:["冲突升级是否合理？","新冲突是否更有深度？","是否符合人物性格？","是否为后续埋下伏笔？"]},actionSwap:{description:"通过替换行动要素来强化剧情效果",elements:[{type:"行动要素",options:[{name:"行动要素1",description:"描述行动要素1",effect:"增加紧张感，推动剧情发展"},{name:"行动要素2",description:"描述行动要素2",effect:"展现人物的决心和勇气"},{name:"行动要素3",description:"描述行动要素3",effect:"制造悬念，增加期待感"}]},{type:"身份转变",options:[{name:"坏变好",description:"描述行动要素1",effect:"增加人物的深度，展现成长"}]}],checkPoints:["行动替换是否合理？","新行动是否更有魅力？","是否符合人物性格？","是否为后续埋下伏笔？"]},eventSwap:{description:"通过替换事件要素来强化剧情效果",elements:[{type:"事件要素",options:[{name:"事件要素1",description:"描述事件要素1",effect:"增加紧张感，推动剧情发展"},{name:"事件要素2",description:"描述事件要素2",effect:"展现人物的决心和勇气"},{name:"事件要素3",description:"描述事件要素3",effect:"制造悬念，增加期待感"}]}],checkPoints:["事件替换是否合理？","新事件是否更有魅力？","是否符合人物性格？","是否为后续埋下伏笔？"]},characterSwap:{description:"通过替换人物要素来丰富角色",elements:[{type:"人物动机",options:[{name:"追求长生",description:"渴望突破生命限制",effect:"体现修仙者的终极追求",examples:["他立志要超越凡人的界限，追寻永恒的生命"]},{name:"复仇",description:"为了报仇而修炼",effect:"增加人物的动力和戏剧性",examples:["为了给家族报仇，他忍受着修炼的痛苦"]},{name:"守护",description:"为了保护重要的人或物而变强",effect:"增加人物的情感深度",examples:["只有变得更强，才能保护身边的人"]}]},{type:"性格特征",options:[{name:"谨慎稳重",description:"做事深思熟虑",effect:"增加可信度，适合智谋型角色",examples:["每一步都要算计清楚，这是他的修炼之道"]},{name:"狂傲自信",description:"对自己充满信心",effect:"增加戏剧性，适合天才型角色",examples:["在修炼一道，他从不相信有人能超越自己"]},{name:"温和谦逊",description:"待人温和，不骄不躁",effect:"增加反差感，适合隐藏实力的角色",examples:["表面上是个普通的杂役弟子，实则暗藏惊人的天赋"]}]}],checkPoints:["人物改变是否合理？","新特征是否更有魅力？","是否符合人物成长？","是否为后续留下空间？"]},relationshipSwap:{description:"通过替换人物关系来制造反转",elements:[{type:"正派位",options:[{name:"姐妹",description:"通过普通关系来展现人物关系",effect:"增加情感深度，展现人物关系"},{name:"特殊关系",description:"通过特殊关系来展现人物关系",effect:"增加戏剧性，展现人物关系"},{name:"宿命关系",description:"通过宿命关系来展现人物关系",effect:"增加宿命感，展现人物关系"}]},{type:"反派位",options:[{name:"无名反派",description:"通过当前关系来展现人物关系",effect:"增加反差感，展现人物关系"},{name:"小反派",description:"通过宿命关系来展现人物关系",effect:"增加宿命感，展现人物关系"},{name:"明星反派",description:"通过宿世关系来展现人物关系",effect:"增加宿命感，展现人物关系"}]}],checkPoints:["关系转变是否合理？","新关系是否更有深度？","是否符合人物背景？","是否为后续留下伏笔？"]}};function $t(){const f=x(!1);let k=null;const C=(b="加载中...")=>{k&&T(),f.value=!0,k=Ge.service({lock:!0,text:b,background:"rgba(0, 0, 0, 0.7)"})},T=()=>{k&&(k.close(),k=null),f.value=!1};return{isLoading:f,startLoading:C,stopLoading:T}}const Ot=Qe("inspiration",()=>{const f=j({data:{categories:{},theme:[],volume:[],keyPoint:[],technique:[]},isLoaded:!1}),k=x(!1),C=x(null),T=N(()=>f.data),b=N(()=>f.isLoaded),V=N(()=>f.data.categories||{}),P=N(()=>k.value),L=N(()=>!!C.value),B=p=>f.data&&f.data[p]&&Array.isArray(f.data[p])?f.data[p]:[];async function w(){try{if(k.value=!0,C.value=null,console.log("开始请求故事灵感数据..."),!window.pywebview?.api?.book_controller?.get_story_inspiration)throw console.error("API不存在: book_controller.get_story_inspiration"),new Error("API方法不存在，无法加载灵感卡池数据");const p=await window.pywebview.api.book_controller.get_story_inspiration();console.log("API原始响应:",p);let u=null;if(p==null?(console.warn("API返回了null或undefined，将创建默认数据"),u={success:!0,message:"已创建默认灵感卡池数据",data:U()}):u=typeof p=="string"?JSON.parse(p):p,console.log("处理后的响应:",u),u&&u.success&&u.data)return f.data=u.data,f.isLoaded=!0,console.log("数据加载成功:",f.data),u.data;if(u&&u.status==="success"&&u.data)return f.data=u.data,f.isLoaded=!0,console.log("数据加载成功(status格式):",f.data),u.data;{console.warn("响应没有包含有效数据，将使用默认数据");const m=U();f.data=m,f.isLoaded=!0;const v=u?.message||"加载灵感卡池失败: 服务器未返回有效数据，已使用默认数据";return C.value=v,console.error("加载灵感卡池警告:",v,u),m}}catch(p){console.error("加载灵感卡池出错:",p);const u=U();return f.data=u,f.isLoaded=!0,C.value=p.message||"加载灵感卡池出错: 请检查网络连接",u}finally{k.value=!1}}function U(){return{categories:{theme:{name:"主题层",description:"故事的核心主题与情感基调",icon:"Sunrise",color:"primary",defaultCount:2,maxCount:5},volume:{name:"卷级结构",description:"故事的大纲架构与发展脉络",icon:"Connection",color:"success",defaultCount:4,maxCount:8},keyPoint:{name:"关键点",description:"故事中的重要转折与关键节点",icon:"Key",color:"warning",defaultCount:5,maxCount:8},technique:{name:"技法卡",description:"用于优化剧情的各种写作技巧",icon:"TrendCharts",color:"danger",defaultCount:3,maxCount:5}},theme:[],volume:[],keyPoint:[],technique:[]}}async function te(p){try{k.value=!0,C.value=null;const u=await window.pywebview.api.book_controller.save_story_inspiration(p),m=typeof u=="string"?JSON.parse(u):u;if(m&&m.success)return f.data=p,m.data;{const v=m?.message||"保存灵感卡池失败";throw C.value=v,new Error(v)}}catch(u){throw C.value=u.message||"保存灵感卡池失败",console.error("保存灵感卡池失败:",u),u}finally{k.value=!1}}async function le(p,u){try{k.value=!0,C.value=null,console.log(`开始保存灵感类别 ${p}，共 ${u.length} 个元素`);const m=await window.pywebview.api.book_controller.save_inspiration_category(p,u);console.log("保存灵感类别API响应:",m);let v;try{v=typeof m=="string"?JSON.parse(m):m}catch(I){console.warn("解析响应失败，使用原始响应",I),v=m}if(v&&v.success===!0||v&&v.status==="success"||typeof v=="string"&&v.includes("成功"))return f.data[p]=u,console.log(`灵感类别 ${p} 保存成功`),v.data||{[p]:u};{const I=v?.message||"保存灵感类别失败";throw C.value=I,console.error("保存灵感类别失败:",I),new Error(I)}}catch(m){throw C.value=m.message||`保存灵感类别 ${p} 失败`,console.error(`保存灵感类别 ${p} 失败:`,m),m}finally{k.value=!1}}function X(){C.value=null}return{state:f,loading:k,error:C,inspirationData:T,isLoaded:b,categories:V,isLoading:P,hasError:L,getElementsByCategory:B,loadInspirationData:w,saveInspirationData:te,saveInspirationCategory:le,clearError:X}}),Dt={class:"story-inspiration"},It={key:0,class:"data-loading-overlay"},zt={key:1,class:"data-error-overlay"},qt={class:"error-message"},Bt={class:"error-actions"},Nt={class:"archetype-container"},Ut={class:"selection-section"},Tt={class:"selection-area"},Jt={class:"column-header"},Mt={class:"header-content"},At={class:"control-area"},jt={class:"action-buttons"},Ft={key:0,class:"fixed-elements-indicator"},Rt={class:"fixed-count"},Zt={class:"button-group"},Ht={class:"button-group"},Gt={class:"result-section"},Qt={class:"result-header"},Xt={class:"result-header-left"},Yt={class:"interaction-tips"},Wt={style:{"text-align":"left"}},Kt={class:"result-content"},el={class:"result-grid"},tl={class:"section-header"},ll={class:"element-count"},ol={class:"tag-group"},nl={key:1,class:"empty-result"},sl={class:"optimize-container"},al={key:0,class:"system-content"},il={class:"options-grid"},rl={class:"option-header"},ul={class:"option-content"},cl={key:0,class:"effect"},dl={class:"editor-container"},pl={class:"editor-toolbar"},ml={class:"examples-editor"},fl={class:"import-content"},vl={class:"format-hint-title"},_l={class:"import-options"},hl={class:"dialog-footer"},yl={class:"detail-header"},gl={class:"detail-title"},bl={key:0,class:"detail-content-wrapper"},kl={class:"detail-content"},Cl={class:"detail-section"},xl={class:"detail-description"},wl={key:0,class:"detail-section"},El={class:"detail-examples-list"},Vl={class:"detail-footer"},Sl={__name:"StoryInspiration",setup(f){Ye();const{startLoading:k,stopLoading:C}=$t(),T=x("archetype"),b=Ot();x({categories:{},theme:[],volume:[],keyPoint:[],technique:[]});const V=N(()=>b.categories),P=j({theme:[],volume:[],keyPoint:[],technique:[]}),L=j({theme:{},volume:{},keyPoint:{},technique:{}}),B=x(null);ye(P,e=>{Object.values(e).some(s=>s.length>0)&&(B.value=JSON.parse(JSON.stringify(e)))},{deep:!0});const w=j({theme:2,volume:4,keyPoint:5,technique:3});ye(()=>b.categories,e=>{e&&(e.theme?.defaultCount&&(w.theme=e.theme.defaultCount),e.volume?.defaultCount&&(w.volume=e.volume.defaultCount),e.keyPoint?.defaultCount&&(w.keyPoint=e.keyPoint.defaultCount),e.technique?.defaultCount&&(w.technique=e.technique.defaultCount))},{immediate:!0});const U=e=>b.getElementsByCategory(e),te=()=>{if(!b.isLoaded){d.warning("数据正在加载中，请稍候...");return}Object.keys(V.value).forEach(e=>{const t=U(e),s=Object.keys(L[e]).filter(O=>L[e][O]),c=w[e]-s.length;if(c<=0)P[e]=s.slice(0,w[e]);else{const O=t.filter(G=>!L[e][G.title]),H=le(O,c);P[e]=[...s,...H.map(G=>G.title)]}}),X()},le=(e,t)=>[...e].sort(()=>.5-Math.random()).slice(0,t),X=()=>{if(!b.isLoaded){d.warning("数据正在加载中，请稍候...");return}if(!Object.keys(V.value).every(s=>P[s].length>0)){d.warning("请确保每个类型都至少选择了一个元素");return}const t=Object.keys(V.value).flatMap(s=>[`【${V.value[s].name}】`,...p(P[s],V.value[s].name),""]).join(`
`);window.pywebview.api.copy_to_clipboard(t).then(()=>{d.success({message:"组合已生成并复制到剪贴板",duration:2e3})}).catch(()=>{d.warning({message:"复制到剪贴板失败，请手动复制",duration:2e3})}),B.value=JSON.parse(JSON.stringify(P)),console.log("组合结果已生成:",B.value)},p=(e,t)=>e.map((s,c)=>`${c+1}. ${s}`),u=()=>{Object.keys(V.value).forEach(e=>{P[e]=[],L[e]={}}),B.value=null},m=x(null),v=x(null),re=(e,t)=>{if(m.value!==null&&(clearTimeout(m.value),m.value=null,v.value&&v.value.category===e&&v.value.elementTitle===t)){v.value=null;return}v.value={category:e,elementTitle:t},m.value=setTimeout(()=>{v.value&&(L[e][t]?L[e][t]=!1:L[e][t]=!0,v.value=null,m.value=null)},300)},I=x(""),ue=x([]),ce=j({}),de=N(()=>I.value?Ce[I.value]:null),xe=(e,t)=>{ce[e]=t,d.success(`已选择: ${t.name}`)},we=(e,t)=>ce[e]?.name===t.name,Y=x(!1),J=x(null),S=x([]),M=j({theme:null,volume:null,keyPoint:null,technique:null}),Ee=()=>V.value[J.value]?.name?`编辑${V.value[J.value].name}元素`:"编辑元素",Ve=e=>{J.value=e,Y.value=!0;const t=d({message:"正在准备编辑器数据...",type:"info",duration:0});Pt(()=>{try{if(M[e]){console.log("使用缓存数据"),S.value=M[e],t.close();return}setTimeout(()=>{let s=U(e);Array.isArray(s)?(S.value=s.map(c=>({...c})),M[e]=[...S.value]):S.value=[],t.close()},100)}catch(s){console.error("加载编辑器数据失败:",s),t.close(),d.error("加载数据失败，请重试")}})},Se=async()=>{try{k("正在保存灵感元素...");const e=J.value;if(!e){d.warning("没有选择灵感类别");return}console.log(`保存${V.value[e]?.name||e}元素`,S.value),await b.saveInspirationCategory(e,S.value),d({type:"success",message:`灵感类别 ${e} 保存成功`,duration:2e3}),M[e]=[...S.value],Y.value=!1}catch(e){console.error("保存自定义元素失败:",e),d.error(`保存失败: ${e.message}`)}finally{C()}},Le=()=>{S.value.push({title:"新元素",description:"请输入描述",emotion:"↑",examples:["请添加示例"]})},Pe=e=>{ke.confirm("确定要删除这个元素吗？","确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{S.value.splice(e,1),d.success("元素已删除")}).catch(()=>{})},$e=e=>{e.examples||(e.examples=[]),e.examples.push("")},Oe=(e,t)=>{e.examples.splice(t,1)},F=x(!1),R=x(""),De=()=>{F.value=!0,R.value=""},Ie=async()=>{try{if(!R.value.trim()){d.warning("请输入JSON数据");return}let e;try{e=JSON.parse(R.value)}catch{d.error("JSON格式无效，请检查您的输入");return}if(!Array.isArray(e)){d.error("导入失败：数据必须是数组格式");return}for(const t of e)if(!t.title||!t.description){d.error("导入失败：数据中有元素缺少标题或描述字段");return}S.value=e,J.value&&(M[J.value]=[...e]),F.value=!1,d.success(`成功导入 ${e.length} 个元素`),console.log("导入的元素数量:",e.length)}catch(e){console.error("导入失败",e),d.error("导入失败："+e.message)}},ze=()=>{try{const e=JSON.stringify(S.value,null,2);window.pywebview.api.copy_to_clipboard(e).then(()=>{d.success(`已复制 ${S.value.length} 个${V.value[J.value]?.name||""}元素到剪贴板`)}).catch(t=>{console.error("复制到剪贴板失败:",t),d.error("复制到剪贴板失败，请检查浏览器权限");try{const s=document.createElement("textarea");s.value=e,s.style.position="fixed",s.style.opacity="0",document.body.appendChild(s),s.select();const c=document.execCommand("copy");document.body.removeChild(s),c?d.success("使用备用方法复制成功"):d.error("复制失败，请手动复制")}catch(s){console.error("备用复制方法失败:",s),d.error("复制失败，请手动复制")}}),console.log("导出的元素数量:",S.value.length)}catch(e){console.error("导出失败",e),d.error("导出失败："+e.message)}};We(async()=>{try{k("正在加载灵感卡池..."),await b.loadInspirationData(),W.value=!0;const e=b.categories;e&&(e.theme?.defaultCount&&(w.theme=e.theme.defaultCount),e.volume?.defaultCount&&(w.volume=e.volume.defaultCount),e.keyPoint?.defaultCount&&(w.keyPoint=e.keyPoint.defaultCount),e.technique?.defaultCount&&(w.technique=e.technique.defaultCount))}catch(e){console.error("加载灵感卡池失败:",e),d.error("加载灵感卡池出错，请刷新页面重试")}finally{C()}setTimeout(()=>{["theme","volume","keyPoint","technique"].forEach(e=>{if(!M[e]){let t=b.getElementsByCategory(e);Array.isArray(t)&&(M[e]=t.map(s=>({...s})))}}),console.log("编辑器数据预加载完成")},1e3)});const Z=x(!1),oe=x(""),$=x(null),qe=e=>{switch(e){case"↑":return"success";case"↓":return"danger";case"↓ | ↑":return"info";default:return"info"}},Be=e=>{switch(e){case"↑":return"上升";case"↓":return"下降";case"↓ | ↑":return"波动";default:return"未知"}},Ne=(e,t)=>{m.value!==null&&(clearTimeout(m.value),m.value=null,v.value=null),oe.value=e;const c=U(e).find(O=>O.title===t);c?($.value=c,Z.value=!0):($.value={title:t},Z.value=!0,console.warn(`未找到元素完整信息: ${t}`))},Ue=N(()=>Object.values(L).some(e=>Object.values(e).some(Boolean))),Te=N(()=>Object.values(L).reduce((e,t)=>e+Object.values(t).filter(Boolean).length,0)),W=x(!1),Je=async()=>{try{k("正在重新加载灵感卡池..."),await b.loadInspirationData(),W.value=!0;const e=b.categories;e&&(e.theme?.defaultCount&&(w.theme=e.theme.defaultCount),e.volume?.defaultCount&&(w.volume=e.volume.defaultCount),e.keyPoint?.defaultCount&&(w.keyPoint=e.keyPoint.defaultCount),e.technique?.defaultCount&&(w.technique=e.technique.defaultCount)),d.success("数据加载成功")}catch(e){console.error("重新加载灵感卡池失败:",e),d.error("重新加载失败，请检查网络连接")}finally{C()}},Me=()=>{ke.alert(`错误信息: ${b.error}

    API状态: ${window.pywebview?"可用":"不可用"}

    图书控制器: ${window.pywebview?.api?.book_controller?"可用":"不可用"}`,"诊断信息",{type:"warning"})},Ae=async()=>{try{if(!confirm("确定要重置灵感卡池数据吗？这将删除所有自定义内容。"))return;k("正在重置灵感卡池数据...");const e={categories:{theme:{name:"主题层",description:"故事的核心主题与情感基调",icon:"Sunrise",color:"primary",defaultCount:2,maxCount:5},volume:{name:"卷级结构",description:"故事的大纲架构与发展脉络",icon:"Connection",color:"success",defaultCount:4,maxCount:8},keyPoint:{name:"关键点",description:"故事中的重要转折与关键节点",icon:"Key",color:"warning",defaultCount:5,maxCount:8},technique:{name:"技法卡",description:"用于优化剧情的各种写作技巧",icon:"TrendCharts",color:"danger",defaultCount:3,maxCount:5}},theme:[],volume:[],keyPoint:[],technique:[]};await b.saveInspirationData(e),await b.loadInspirationData(),W.value=!0,d.success("灵感卡池数据已重置")}catch(e){console.error("重置数据失败:",e),d.error("重置数据失败: "+e.message)}finally{C()}};return(e,t)=>{const s=Ke,c=et,O=it,H=at,G=ct,je=ut,pe=rt,me=dt,fe=ht,ve=st,Fe=kt,_e=bt,he=gt,Re=tt,K=St,Q=Vt,Ze=Lt,He=Et,ne=lt;return r(),h("div",Dt,[!W.value&&y(b).isLoading?(r(),h("div",It,[l(s,{class:"loading-icon"},{default:n(()=>[l(y(ot))]),_:1}),t[9]||(t[9]=o("p",null,"正在加载灵感卡池数据...",-1))])):y(b).hasError?(r(),h("div",zt,[l(s,{class:"error-icon"},{default:n(()=>[l(y(nt))]),_:1}),t[13]||(t[13]=o("p",null,"加载灵感卡池数据失败",-1)),o("p",qt,E(y(b).error),1),o("div",Bt,[l(c,{type:"primary",onClick:Je},{default:n(()=>t[10]||(t[10]=[g("重试")])),_:1}),l(c,{type:"info",onClick:Me},{default:n(()=>t[11]||(t[11]=[g("诊断信息")])),_:1}),l(c,{type:"danger",onClick:Ae},{default:n(()=>t[12]||(t[12]=[g("重置数据")])),_:1})])])):(r(),z(Re,{key:2,modelValue:T.value,"onUpdate:modelValue":t[2]||(t[2]=a=>T.value=a),class:"inspiration-tabs"},{default:n(()=>[l(ve,{label:"原型组合",name:"archetype"},{default:n(()=>[o("div",Nt,[o("div",Ut,[o("div",Tt,[(r(!0),h(D,null,q(V.value,(a,i)=>(r(),h("div",{key:i,class:"selection-column"},[o("div",Jt,[o("div",Mt,[o("span",null,E(a.name),1)]),l(H,{modelValue:w[i],"onUpdate:modelValue":_=>w[i]=_,class:"count-select",size:"small",placeholder:"选择个数"},{default:n(()=>[(r(!0),h(D,null,q(a.maxCount,_=>(r(),z(O,{key:_,label:_+"个",value:_},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"]),l(c,{class:"edit-button",size:"small",type:"primary",onClick:_=>Ve(i),icon:"Edit",circle:""},null,8,["onClick"])]),l(pe,{height:"calc(100% - 42px)"},{default:n(()=>[l(je,{modelValue:P[i],"onUpdate:modelValue":_=>P[i]=_,class:"checkbox-list"},{default:n(()=>[(r(!0),h(D,null,q(U(i),_=>(r(),z(G,{key:_.title,value:_.title},{default:n(()=>[g(E(_.title),1)]),_:2},1032,["value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024)]))),128))]),o("div",At,[o("div",jt,[Ue.value?(r(),h("div",Ft,[o("span",Rt,"已固定: "+E(Te.value)+"个",1),l(me,{content:"点击元素可固定/解除固定，固定的元素在随机时将被保留"},{default:n(()=>[l(s,{class:"info-icon"},{default:n(()=>[l(y(se))]),_:1})]),_:1})])):A("",!0),o("div",Zt,[l(c,{type:"primary",size:"default",onClick:X,class:"action-button generate-button"},{default:n(()=>[l(s,{class:"button-icon"},{default:n(()=>[l(y(ge))]),_:1}),t[14]||(t[14]=g(" 生成组合 "))]),_:1})]),o("div",Ht,[l(c,{type:"success",size:"default",onClick:te,class:"action-button random-button"},{default:n(()=>[l(s,{class:"button-icon"},{default:n(()=>[l(y(pt))]),_:1}),t[15]||(t[15]=g(" 随机灵感 "))]),_:1})]),l(c,{type:"info",size:"default",onClick:u,class:"action-button reset-button"},{default:n(()=>[l(s,{class:"button-icon"},{default:n(()=>[l(y(ae))]),_:1}),t[16]||(t[16]=g(" 重置选择 "))]),_:1})])])]),o("div",Gt,[B.value?(r(),h(D,{key:0},[o("div",Qt,[o("div",Xt,[l(s,{class:"result-icon"},{default:n(()=>[l(y(mt))]),_:1}),t[17]||(t[17]=o("h2",null,"灵感组合结果",-1))]),o("div",Yt,[l(me,{placement:"top",effect:"light"},{content:n(()=>[o("div",Wt,[o("div",null,[l(s,null,{default:n(()=>[l(y(ft))]),_:1}),t[18]||(t[18]=g()),t[19]||(t[19]=o("b",null,"单击",-1)),t[20]||(t[20]=g(": 固定/解除固定元素"))]),o("div",null,[l(s,null,{default:n(()=>[l(y(vt))]),_:1}),t[21]||(t[21]=g()),t[22]||(t[22]=o("b",null,"双击",-1)),t[23]||(t[23]=g(": 查看元素详情"))])])]),default:n(()=>[l(s,{class:"tips-icon",size:20},{default:n(()=>[l(y(se))]),_:1})]),_:1})])]),o("div",Kt,[o("div",el,[(r(!0),h(D,null,q(V.value,(a,i)=>(r(),h("div",{key:i,class:"result-section-card"},[o("div",tl,[l(s,null,{default:n(()=>[(r(),z(_t(a.icon)))]),_:2},1024),o("span",null,E(a.name),1),o("div",ll,E(B.value[i].length)+"个元素",1)]),o("div",ol,[(r(!0),h(D,null,q(B.value[i],_=>(r(),z(fe,{key:_,class:ie(["result-tag",{"fixed-element":L[i][_]}]),effect:"light",type:a.color,onClick:ee=>re(i,_),onDblclick:ee=>Ne(i,_)},{default:n(()=>[L[i][_]?(r(),z(s,{key:0,class:"fixed-icon"},{default:n(()=>[l(y(yt))]),_:1})):A("",!0),g(" "+E(_),1)]),_:2},1032,["class","type","onClick","onDblclick"]))),128))])]))),128))]),t[24]||(t[24]=o("div",{class:"tech-floater"},[o("div",{class:"tech-floater-content"},[o("div",{class:"tech-circle"}),o("div",{class:"tech-lines"}),o("div",{class:"tech-dots"})])],-1))])],64)):(r(),h("div",nl,[l(s,{class:"empty-icon"},{default:n(()=>[l(y(ge))]),_:1}),t[25]||(t[25]=o("p",null,'请从上方选择元素并点击"生成组合"或"随机灵感"按钮',-1))]))])])]),_:1}),l(ve,{label:"剧情优化",name:"optimize"},{default:n(()=>[o("div",sl,[l(H,{modelValue:I.value,"onUpdate:modelValue":t[0]||(t[0]=a=>I.value=a),placeholder:"选择替换系统",class:"system-select"},{default:n(()=>[(r(!0),h(D,null,q(y(Ce),(a,i)=>(r(),z(O,{key:i,label:a.description,value:i},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),de.value?(r(),h("div",al,[l(he,{modelValue:ue.value,"onUpdate:modelValue":t[1]||(t[1]=a=>ue.value=a)},{default:n(()=>[(r(!0),h(D,null,q(de.value.elements,a=>(r(),z(_e,{key:a.type,title:a.type,name:a.type},{default:n(()=>[o("div",il,[(r(!0),h(D,null,q(a.options,i=>(r(),z(Fe,{key:i.name,class:ie(["option-card",{selected:we(a.type,i)}]),onClick:_=>xe(a.type,i)},{header:n(()=>[o("div",rl,E(i.name),1)]),default:n(()=>[o("div",ul,[o("p",null,E(i.description),1),i.effect?(r(),h("p",cl,"效果: "+E(i.effect),1)):A("",!0)])]),_:2},1032,["class","onClick"]))),128))])]),_:2},1032,["title","name"]))),128))]),_:1},8,["modelValue"])])):A("",!0)])]),_:1})]),_:1},8,["modelValue"])),l(ne,{modelValue:Y.value,"onUpdate:modelValue":t[3]||(t[3]=a=>Y.value=a),title:Ee(),width:"70%",class:"element-editor-dialog","destroy-on-close":"false","append-to-body":!0},{default:n(()=>[o("div",dl,[o("div",pl,[l(c,{type:"primary",size:"default",onClick:Le},{default:n(()=>[l(s,null,{default:n(()=>[l(y(be))]),_:1}),t[26]||(t[26]=g(" 添加新元素 "))]),_:1}),l(c,{type:"success",size:"default",onClick:Se},{default:n(()=>[l(s,null,{default:n(()=>[l(y(Ct))]),_:1}),t[27]||(t[27]=g(" 保存修改 "))]),_:1}),l(c,{size:"default",onClick:ze},{default:n(()=>[l(s,null,{default:n(()=>[l(y(xt))]),_:1}),t[28]||(t[28]=g(" 导出配置 "))]),_:1}),l(c,{type:"warning",size:"default",onClick:De},{default:n(()=>[l(s,null,{default:n(()=>[l(y(wt))]),_:1}),t[29]||(t[29]=g(" 导入配置 "))]),_:1})]),l(He,{data:S.value,style:{width:"100%"},"max-height":"450px",border:""},{default:n(()=>[l(Q,{label:"标题",width:"180"},{default:n(({row:a})=>[l(K,{modelValue:a.title,"onUpdate:modelValue":i=>a.title=i,placeholder:"输入标题",size:"default"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(Q,{label:"描述"},{default:n(({row:a})=>[l(K,{modelValue:a.description,"onUpdate:modelValue":i=>a.description=i,type:"textarea",placeholder:"输入描述",rows:2,size:"default"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(Q,{label:"情感走向",width:"120"},{default:n(({row:a})=>[l(H,{modelValue:a.emotion,"onUpdate:modelValue":i=>a.emotion=i,placeholder:"选择走向",size:"default"},{default:n(()=>[l(O,{label:"上升 ↑",value:"↑"}),l(O,{label:"下降 ↓",value:"↓"}),l(O,{label:"波动 ↓|↑",value:"↓ | ↑"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),l(Q,{label:"示例",width:"140"},{default:n(({row:a})=>[l(Ze,{placement:"right",width:350,trigger:"click"},{reference:n(()=>[l(c,{size:"default"},{default:n(()=>[g("编辑 ("+E(a.examples?.length||0)+")",1)]),_:2},1024)]),default:n(()=>[o("div",ml,[(r(!0),h(D,null,q(a.examples||[],(i,_)=>(r(),h("div",{key:_,class:"example-item"},[l(K,{modelValue:a.examples[_],"onUpdate:modelValue":ee=>a.examples[_]=ee,placeholder:"输入示例",size:"default"},null,8,["modelValue","onUpdate:modelValue"]),l(c,{type:"danger",onClick:ee=>Oe(a,_),size:"small",circle:""},{default:n(()=>[l(s,null,{default:n(()=>[l(y(ae))]),_:1})]),_:2},1032,["onClick"])]))),128)),l(c,{type:"primary",onClick:i=>$e(a),size:"default"},{default:n(()=>[l(s,null,{default:n(()=>[l(y(be))]),_:1}),t[30]||(t[30]=g(" 添加示例 "))]),_:2},1032,["onClick"])])]),_:2},1024)]),_:1}),l(Q,{label:"操作",width:"80"},{default:n(({$index:a})=>[l(c,{type:"danger",onClick:i=>Pe(a),size:"default",circle:""},{default:n(()=>[l(s,null,{default:n(()=>[l(y(ae))]),_:1})]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])])]),_:1},8,["modelValue","title"]),l(ne,{modelValue:F.value,"onUpdate:modelValue":t[6]||(t[6]=a=>F.value=a),title:"导入配置",width:"800px",class:"import-config-dialog","close-on-click-modal":!1,"show-close":!0,"append-to-body":!0,top:"5vh"},{footer:n(()=>[o("div",hl,[l(c,{onClick:t[5]||(t[5]=a=>F.value=!1)},{default:n(()=>t[33]||(t[33]=[g("取消")])),_:1}),l(c,{type:"primary",onClick:Ie},{default:n(()=>t[34]||(t[34]=[g("确认导入")])),_:1})])]),default:n(()=>[o("div",fl,[l(he,null,{default:n(()=>[l(_e,null,{title:n(()=>[o("div",vl,[l(s,null,{default:n(()=>[l(y(se))]),_:1}),t[31]||(t[31]=o("span",null,"查看JSON格式示例",-1))])]),default:n(()=>[t[32]||(t[32]=o("div",{class:"format-hint-content"},[o("pre",null,`[
  {
    "title": "元素标题",
    "description": "元素描述",
    "emotion": "↑",
    "examples": ["示例1", "示例2"]
  },
  {
    "title": "另一个元素",
    "description": "另一个描述",
    "emotion": "↓ | ↑",
    "examples": ["示例1"]
  }
]`)],-1))]),_:1})]),_:1}),o("div",_l,[l(K,{modelValue:R.value,"onUpdate:modelValue":t[4]||(t[4]=a=>R.value=a),type:"textarea",rows:12,placeholder:"请粘贴有效的JSON数据",class:"import-input"},null,8,["modelValue"])])])]),_:1},8,["modelValue"]),l(ne,{modelValue:Z.value,"onUpdate:modelValue":t[8]||(t[8]=a=>Z.value=a),width:"550px","close-on-click-modal":!0,"close-on-press-escape":!0,"append-to-body":!0,center:!0,"lock-scroll":!0,class:"element-detail-dialog tech-card","destroy-on-close":""},{header:n(()=>[o("div",yl,[t[35]||(t[35]=o("div",{class:"tech-lines"},null,-1)),o("div",gl,[o("span",{class:ie(["detail-category-badge",oe.value])},E(V.value[oe.value]?.name||"元素"),3),o("h3",null,E($.value?.title),1)]),$.value?.emotion?(r(),z(fe,{key:0,class:"emotion-tag",type:qe($.value.emotion)},{default:n(()=>[g(E(Be($.value.emotion)),1)]),_:1},8,["type"])):A("",!0)])]),footer:n(()=>[o("div",Vl,[t[41]||(t[41]=o("div",{class:"tech-pulse"},null,-1)),l(c,{onClick:t[7]||(t[7]=a=>Z.value=!1),class:"tech-button",round:""},{default:n(()=>t[40]||(t[40]=[g(" 关闭 ")])),_:1})])]),default:n(()=>[$.value?(r(),h("div",bl,[l(pe,{height:"auto","max-height":"450px",class:"detail-scrollbar"},{default:n(()=>[o("div",kl,[o("div",Cl,[t[36]||(t[36]=o("h4",{class:"detail-section-title"},[o("span",{class:"icon-container"},[o("svg",{viewBox:"0 0 24 24",class:"tech-icon"},[o("path",{d:"M4 5h16v2H4zm0 6h16v2H4zm0 6h16v2H4z"})])]),g(" 描述 ")],-1)),o("p",xl,E($.value.description),1)]),$.value.examples&&$.value.examples.length>0?(r(),h("div",wl,[t[38]||(t[38]=o("h4",{class:"detail-section-title"},[o("span",{class:"icon-container"},[o("svg",{viewBox:"0 0 24 24",class:"tech-icon"},[o("path",{d:"M13 10h5l-6 6-6-6h5V4h2v6z"})])]),g(" 示例 ")],-1)),o("ul",El,[(r(!0),h(D,null,q($.value.examples,(a,i)=>(r(),h("li",{key:i},[t[37]||(t[37]=o("div",{class:"example-bullet"},null,-1)),o("span",null,E(a),1)]))),128))])])):A("",!0),t[39]||(t[39]=o("div",{class:"tech-decoration"},[o("svg",{viewBox:"0 0 100 100",class:"corner-decoration top-left"},[o("path",{d:"M0 0 L40 0 L40 5 L5 5 L5 40 L0 40 Z"})]),o("svg",{viewBox:"0 0 100 100",class:"corner-decoration top-right"},[o("path",{d:"M100 0 L60 0 L60 5 L95 5 L95 40 L100 40 Z"})]),o("svg",{viewBox:"0 0 100 100",class:"corner-decoration bottom-left"},[o("path",{d:"M0 100 L40 100 L40 95 L5 95 L5 60 L0 60 Z"})]),o("svg",{viewBox:"0 0 100 100",class:"corner-decoration bottom-right"},[o("path",{d:"M100 100 L60 100 L60 95 L95 95 L95 60 L100 60 Z"})])],-1))])]),_:1})])):A("",!0)]),_:1},8,["modelValue"])])}}},Tl=Xe(Sl,[["__scopeId","data-v-b496034d"]]);export{Tl as default};
