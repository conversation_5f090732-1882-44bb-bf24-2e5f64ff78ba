import{ak as <PERSON>,bp as Gi,bq as Xt,br as <PERSON>,w as <PERSON>,c as <PERSON>e,b as we,m as re,ar as Ve,W as Ge,C as V,n as mt,al as Me,bs as Sa,$ as Bo,d as Z,g as ge,e as U,an as cn,bt as ka,bu as Sn,r as me,bv as Na,p as Pe,M as We,bw as Wi,R as Ze,bx as ze,by as Ca,o as ut,bz as $a,S as Rn,Y as Le,ao as Ui,bA as Ta,bB as Mn,F as Je,bC as Vo,bD as Ma,aj as Ia,aq as Ot,v as Xe,bE as Ht,aR as Zi,bF as Te,ai as jt,bG as Da,_ as Ln,a8 as Aa,t as Ki,bo as Pa,bH as xt,bI as St,q as za,s as qi,aG as Oa,aF as Fa,b9 as Ba,aK as Va,bJ as <PERSON>,aS as <PERSON>,j as <PERSON>,h as lo,ba as <PERSON>,ap as Ga,K as <PERSON>,E as Ie,ah as Pt,l as Xa,bK as ni,b2 as Wa,k as Ua,i as Za,bj as Ka,U as qa,bn as Ja,B as Qa,bk as ja,a0 as es,G as ts}from"./entry-DxFfH4M0.js";/* empty css                 *//* empty css                 *//* empty css                  *//* empty css                        *//* empty css                *//* empty css                   *//* empty css                        *//* empty css                  */import{n as et}from"./index-browser-OxPLOBIU.js";function Yn(e){return Gi()?(Sn(e),!0):!1}function ht(e){return typeof e=="function"?e():V(e)}const ns=typeof window<"u"&&typeof document<"u",os=e=>typeof e<"u",is=Object.prototype.toString,rs=e=>is.call(e)==="[object Object]",as=()=>{};function ss(e,t){function n(...o){return new Promise((i,r)=>{Promise.resolve(e(()=>t.apply(this,o),{fn:t,thisArg:this,args:o})).then(i).catch(r)})}return n}const Qi=e=>e();function ls(e=Qi){const t=me(!0);function n(){t.value=!1}function o(){t.value=!0}const i=(...r)=>{t.value&&e(...r)};return{isActive:Da(t),pause:n,resume:o,eventFilter:i}}function oi(e,t=!1,n="Timeout"){return new Promise((o,i)=>{setTimeout(t?()=>i(n):o,e)})}function us(e,t,n={}){const{eventFilter:o=Qi,...i}=n;return De(e,ss(o,t),i)}function zt(e,t,n={}){const{eventFilter:o,...i}=n,{eventFilter:r,pause:a,resume:s,isActive:u}=ls(o);return{stop:us(e,t,{...i,eventFilter:r}),pause:a,resume:s,isActive:u}}function cs(e,t={}){if(!Vo(e))return Ma(e);const n=Array.isArray(e.value)?Array.from({length:e.value.length}):{};for(const o in e.value)n[o]=Ia(()=>({get(){return e.value[o]},set(i){var r;if((r=ht(t.replaceRef))!=null?r:!0)if(Array.isArray(e.value)){const s=[...e.value];s[o]=i,e.value=s}else{const s={...e.value,[o]:i};Object.setPrototypeOf(s,Object.getPrototypeOf(e.value)),e.value=s}else e.value[o]=i}}));return n}function Eo(e,t=!1){function n(h,{flush:v="sync",deep:y=!1,timeout:M,throwOnTimeout:S}={}){let N=null;const I=[new Promise(g=>{N=De(e,T=>{h(T)!==t&&(N?.(),g(T))},{flush:v,deep:y,immediate:!0})})];return M!=null&&I.push(oi(M,S).then(()=>ht(e)).finally(()=>N?.())),Promise.race(I)}function o(h,v){if(!Vo(h))return n(T=>T===h,v);const{flush:y="sync",deep:M=!1,timeout:S,throwOnTimeout:N}=v??{};let $=null;const g=[new Promise(T=>{$=De([e,h],([Y,D])=>{t!==(Y===D)&&($?.(),T(Y))},{flush:y,deep:M,immediate:!0})})];return S!=null&&g.push(oi(S,N).then(()=>ht(e)).finally(()=>($?.(),ht(e)))),Promise.race(g)}function i(h){return n(v=>!!v,h)}function r(h){return o(null,h)}function a(h){return o(void 0,h)}function s(h){return n(Number.isNaN,h)}function u(h,v){return n(y=>{const M=Array.from(y);return M.includes(h)||M.includes(ht(h))},v)}function l(h){return c(1,h)}function c(h=1,v){let y=-1;return n(()=>(y+=1,y>=h),v)}return Array.isArray(ht(e))?{toMatch:n,toContains:u,changed:l,changedTimes:c,get not(){return Eo(e,!t)}}:{toMatch:n,toBe:o,toBeTruthy:i,toBeNull:r,toBeNaN:s,toBeUndefined:a,changed:l,changedTimes:c,get not(){return Eo(e,!t)}}}function xo(e){return Eo(e)}function ds(e){var t;const n=ht(e);return(t=n?.$el)!=null?t:n}const ji=ns?window:void 0;function er(...e){let t,n,o,i;if(typeof e[0]=="string"||Array.isArray(e[0])?([n,o,i]=e,t=ji):[t,n,o,i]=e,!t)return as;Array.isArray(n)||(n=[n]),Array.isArray(o)||(o=[o]);const r=[],a=()=>{r.forEach(c=>c()),r.length=0},s=(c,h,v,y)=>(c.addEventListener(h,v,y),()=>c.removeEventListener(h,v,y)),u=De(()=>[ds(t),ht(i)],([c,h])=>{if(a(),!c)return;const v=rs(h)?{...h}:h;r.push(...n.flatMap(y=>o.map(M=>s(c,y,M,v))))},{immediate:!0,flush:"post"}),l=()=>{u(),a()};return Yn(l),l}function fs(e){return typeof e=="function"?e:typeof e=="string"?t=>t.key===e:Array.isArray(e)?t=>e.includes(t.key):()=>!0}function ii(...e){let t,n,o={};e.length===3?(t=e[0],n=e[1],o=e[2]):e.length===2?typeof e[1]=="object"?(t=!0,n=e[0],o=e[1]):(t=e[0],n=e[1]):(t=!0,n=e[0]);const{target:i=ji,eventName:r="keydown",passive:a=!1,dedupe:s=!1}=o,u=fs(t);return er(i,r,c=>{c.repeat&&ht(s)||u(c)&&n(c)},a)}function hs(e){return JSON.parse(JSON.stringify(e))}function uo(e,t,n,o={}){var i,r,a;const{clone:s=!1,passive:u=!1,eventName:l,deep:c=!1,defaultValue:h,shouldEmit:v}=o,y=cn(),M=n||y?.emit||((i=y?.$emit)==null?void 0:i.bind(y))||((a=(r=y?.proxy)==null?void 0:r.$emit)==null?void 0:a.bind(y?.proxy));let S=l;t||(t="modelValue"),S=S||`update:${t.toString()}`;const N=g=>s?typeof s=="function"?s(g):hs(g):g,$=()=>os(e[t])?N(e[t]):h,I=g=>{v?v(g)&&M(S,g):M(S,g)};if(u){const g=$(),T=me(g);let Y=!1;return De(()=>e[t],D=>{Y||(Y=!0,T.value=N(D),Le(()=>Y=!1))}),De(T,D=>{!Y&&(D!==e[t]||c)&&I(D)},{deep:c}),T}else return Ee({get(){return $()},set(g){I(g)}})}var ps={value:()=>{}};function Gn(){for(var e=0,t=arguments.length,n={},o;e<t;++e){if(!(o=arguments[e]+"")||o in n||/[\s.]/.test(o))throw new Error("illegal type: "+o);n[o]=[]}return new kn(n)}function kn(e){this._=e}function gs(e,t){return e.trim().split(/^|\s+/).map(function(n){var o="",i=n.indexOf(".");if(i>=0&&(o=n.slice(i+1),n=n.slice(0,i)),n&&!t.hasOwnProperty(n))throw new Error("unknown type: "+n);return{type:n,name:o}})}kn.prototype=Gn.prototype={constructor:kn,on:function(e,t){var n=this._,o=gs(e+"",n),i,r=-1,a=o.length;if(arguments.length<2){for(;++r<a;)if((i=(e=o[r]).type)&&(i=ms(n[i],e.name)))return i;return}if(t!=null&&typeof t!="function")throw new Error("invalid callback: "+t);for(;++r<a;)if(i=(e=o[r]).type)n[i]=ri(n[i],e.name,t);else if(t==null)for(i in n)n[i]=ri(n[i],e.name,null);return this},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new kn(e)},call:function(e,t){if((i=arguments.length-2)>0)for(var n=new Array(i),o=0,i,r;o<i;++o)n[o]=arguments[o+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(r=this._[e],o=0,i=r.length;o<i;++o)r[o].value.apply(t,n)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var o=this._[e],i=0,r=o.length;i<r;++i)o[i].value.apply(t,n)}};function ms(e,t){for(var n=0,o=e.length,i;n<o;++n)if((i=e[n]).name===t)return i.value}function ri(e,t,n){for(var o=0,i=e.length;o<i;++o)if(e[o].name===t){e[o]=ps,e=e.slice(0,o).concat(e.slice(o+1));break}return n!=null&&e.push({name:t,value:n}),e}var So="http://www.w3.org/1999/xhtml";const ai={svg:"http://www.w3.org/2000/svg",xhtml:So,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function Xn(e){var t=e+="",n=t.indexOf(":");return n>=0&&(t=e.slice(0,n))!=="xmlns"&&(e=e.slice(n+1)),ai.hasOwnProperty(t)?{space:ai[t],local:e}:e}function vs(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===So&&t.documentElement.namespaceURI===So?t.createElement(e):t.createElementNS(n,e)}}function ys(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function tr(e){var t=Xn(e);return(t.local?ys:vs)(t)}function ws(){}function Ho(e){return e==null?ws:function(){return this.querySelector(e)}}function _s(e){typeof e!="function"&&(e=Ho(e));for(var t=this._groups,n=t.length,o=new Array(n),i=0;i<n;++i)for(var r=t[i],a=r.length,s=o[i]=new Array(a),u,l,c=0;c<a;++c)(u=r[c])&&(l=e.call(u,u.__data__,c,r))&&("__data__"in u&&(l.__data__=u.__data__),s[c]=l);return new Qe(o,this._parents)}function bs(e){return e==null?[]:Array.isArray(e)?e:Array.from(e)}function Es(){return[]}function nr(e){return e==null?Es:function(){return this.querySelectorAll(e)}}function xs(e){return function(){return bs(e.apply(this,arguments))}}function Ss(e){typeof e=="function"?e=xs(e):e=nr(e);for(var t=this._groups,n=t.length,o=[],i=[],r=0;r<n;++r)for(var a=t[r],s=a.length,u,l=0;l<s;++l)(u=a[l])&&(o.push(e.call(u,u.__data__,l,a)),i.push(u));return new Qe(o,i)}function or(e){return function(){return this.matches(e)}}function ir(e){return function(t){return t.matches(e)}}var ks=Array.prototype.find;function Ns(e){return function(){return ks.call(this.children,e)}}function Cs(){return this.firstElementChild}function $s(e){return this.select(e==null?Cs:Ns(typeof e=="function"?e:ir(e)))}var Ts=Array.prototype.filter;function Ms(){return Array.from(this.children)}function Is(e){return function(){return Ts.call(this.children,e)}}function Ds(e){return this.selectAll(e==null?Ms:Is(typeof e=="function"?e:ir(e)))}function As(e){typeof e!="function"&&(e=or(e));for(var t=this._groups,n=t.length,o=new Array(n),i=0;i<n;++i)for(var r=t[i],a=r.length,s=o[i]=[],u,l=0;l<a;++l)(u=r[l])&&e.call(u,u.__data__,l,r)&&s.push(u);return new Qe(o,this._parents)}function rr(e){return new Array(e.length)}function Ps(){return new Qe(this._enter||this._groups.map(rr),this._parents)}function In(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}In.prototype={constructor:In,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};function zs(e){return function(){return e}}function Os(e,t,n,o,i,r){for(var a=0,s,u=t.length,l=r.length;a<l;++a)(s=t[a])?(s.__data__=r[a],o[a]=s):n[a]=new In(e,r[a]);for(;a<u;++a)(s=t[a])&&(i[a]=s)}function Fs(e,t,n,o,i,r,a){var s,u,l=new Map,c=t.length,h=r.length,v=new Array(c),y;for(s=0;s<c;++s)(u=t[s])&&(v[s]=y=a.call(u,u.__data__,s,t)+"",l.has(y)?i[s]=u:l.set(y,u));for(s=0;s<h;++s)y=a.call(e,r[s],s,r)+"",(u=l.get(y))?(o[s]=u,u.__data__=r[s],l.delete(y)):n[s]=new In(e,r[s]);for(s=0;s<c;++s)(u=t[s])&&l.get(v[s])===u&&(i[s]=u)}function Bs(e){return e.__data__}function Vs(e,t){if(!arguments.length)return Array.from(this,Bs);var n=t?Fs:Os,o=this._parents,i=this._groups;typeof e!="function"&&(e=zs(e));for(var r=i.length,a=new Array(r),s=new Array(r),u=new Array(r),l=0;l<r;++l){var c=o[l],h=i[l],v=h.length,y=Hs(e.call(c,c&&c.__data__,l,o)),M=y.length,S=s[l]=new Array(M),N=a[l]=new Array(M),$=u[l]=new Array(v);n(c,h,S,N,$,y,t);for(var I=0,g=0,T,Y;I<M;++I)if(T=S[I]){for(I>=g&&(g=I+1);!(Y=N[g])&&++g<M;);T._next=Y||null}}return a=new Qe(a,o),a._enter=s,a._exit=u,a}function Hs(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function Rs(){return new Qe(this._exit||this._groups.map(rr),this._parents)}function Ls(e,t,n){var o=this.enter(),i=this,r=this.exit();return typeof e=="function"?(o=e(o),o&&(o=o.selection())):o=o.append(e+""),t!=null&&(i=t(i),i&&(i=i.selection())),n==null?r.remove():n(r),o&&i?o.merge(i).order():i}function Ys(e){for(var t=e.selection?e.selection():e,n=this._groups,o=t._groups,i=n.length,r=o.length,a=Math.min(i,r),s=new Array(i),u=0;u<a;++u)for(var l=n[u],c=o[u],h=l.length,v=s[u]=new Array(h),y,M=0;M<h;++M)(y=l[M]||c[M])&&(v[M]=y);for(;u<i;++u)s[u]=n[u];return new Qe(s,this._parents)}function Gs(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var o=e[t],i=o.length-1,r=o[i],a;--i>=0;)(a=o[i])&&(r&&a.compareDocumentPosition(r)^4&&r.parentNode.insertBefore(a,r),r=a);return this}function Xs(e){e||(e=Ws);function t(h,v){return h&&v?e(h.__data__,v.__data__):!h-!v}for(var n=this._groups,o=n.length,i=new Array(o),r=0;r<o;++r){for(var a=n[r],s=a.length,u=i[r]=new Array(s),l,c=0;c<s;++c)(l=a[c])&&(u[c]=l);u.sort(t)}return new Qe(i,this._parents).order()}function Ws(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function Us(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this}function Zs(){return Array.from(this)}function Ks(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var o=e[t],i=0,r=o.length;i<r;++i){var a=o[i];if(a)return a}return null}function qs(){let e=0;for(const t of this)++e;return e}function Js(){return!this.node()}function Qs(e){for(var t=this._groups,n=0,o=t.length;n<o;++n)for(var i=t[n],r=0,a=i.length,s;r<a;++r)(s=i[r])&&e.call(s,s.__data__,r,i);return this}function js(e){return function(){this.removeAttribute(e)}}function el(e){return function(){this.removeAttributeNS(e.space,e.local)}}function tl(e,t){return function(){this.setAttribute(e,t)}}function nl(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function ol(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttribute(e):this.setAttribute(e,n)}}function il(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function rl(e,t){var n=Xn(e);if(arguments.length<2){var o=this.node();return n.local?o.getAttributeNS(n.space,n.local):o.getAttribute(n)}return this.each((t==null?n.local?el:js:typeof t=="function"?n.local?il:ol:n.local?nl:tl)(n,t))}function ar(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function al(e){return function(){this.style.removeProperty(e)}}function sl(e,t,n){return function(){this.style.setProperty(e,t,n)}}function ll(e,t,n){return function(){var o=t.apply(this,arguments);o==null?this.style.removeProperty(e):this.style.setProperty(e,o,n)}}function ul(e,t,n){return arguments.length>1?this.each((t==null?al:typeof t=="function"?ll:sl)(e,t,n??"")):Rt(this.node(),e)}function Rt(e,t){return e.style.getPropertyValue(t)||ar(e).getComputedStyle(e,null).getPropertyValue(t)}function cl(e){return function(){delete this[e]}}function dl(e,t){return function(){this[e]=t}}function fl(e,t){return function(){var n=t.apply(this,arguments);n==null?delete this[e]:this[e]=n}}function hl(e,t){return arguments.length>1?this.each((t==null?cl:typeof t=="function"?fl:dl)(e,t)):this.node()[e]}function sr(e){return e.trim().split(/^|\s+/)}function Ro(e){return e.classList||new lr(e)}function lr(e){this._node=e,this._names=sr(e.getAttribute("class")||"")}lr.prototype={add:function(e){var t=this._names.indexOf(e);t<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};function ur(e,t){for(var n=Ro(e),o=-1,i=t.length;++o<i;)n.add(t[o])}function cr(e,t){for(var n=Ro(e),o=-1,i=t.length;++o<i;)n.remove(t[o])}function pl(e){return function(){ur(this,e)}}function gl(e){return function(){cr(this,e)}}function ml(e,t){return function(){(t.apply(this,arguments)?ur:cr)(this,e)}}function vl(e,t){var n=sr(e+"");if(arguments.length<2){for(var o=Ro(this.node()),i=-1,r=n.length;++i<r;)if(!o.contains(n[i]))return!1;return!0}return this.each((typeof t=="function"?ml:t?pl:gl)(n,t))}function yl(){this.textContent=""}function wl(e){return function(){this.textContent=e}}function _l(e){return function(){var t=e.apply(this,arguments);this.textContent=t??""}}function bl(e){return arguments.length?this.each(e==null?yl:(typeof e=="function"?_l:wl)(e)):this.node().textContent}function El(){this.innerHTML=""}function xl(e){return function(){this.innerHTML=e}}function Sl(e){return function(){var t=e.apply(this,arguments);this.innerHTML=t??""}}function kl(e){return arguments.length?this.each(e==null?El:(typeof e=="function"?Sl:xl)(e)):this.node().innerHTML}function Nl(){this.nextSibling&&this.parentNode.appendChild(this)}function Cl(){return this.each(Nl)}function $l(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Tl(){return this.each($l)}function Ml(e){var t=typeof e=="function"?e:tr(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})}function Il(){return null}function Dl(e,t){var n=typeof e=="function"?e:tr(e),o=t==null?Il:typeof t=="function"?t:Ho(t);return this.select(function(){return this.insertBefore(n.apply(this,arguments),o.apply(this,arguments)||null)})}function Al(){var e=this.parentNode;e&&e.removeChild(this)}function Pl(){return this.each(Al)}function zl(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Ol(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Fl(e){return this.select(e?Ol:zl)}function Bl(e){return arguments.length?this.property("__data__",e):this.node().__data__}function Vl(e){return function(t){e.call(this,t,this.__data__)}}function Hl(e){return e.trim().split(/^|\s+/).map(function(t){var n="",o=t.indexOf(".");return o>=0&&(n=t.slice(o+1),t=t.slice(0,o)),{type:t,name:n}})}function Rl(e){return function(){var t=this.__on;if(t){for(var n=0,o=-1,i=t.length,r;n<i;++n)r=t[n],(!e.type||r.type===e.type)&&r.name===e.name?this.removeEventListener(r.type,r.listener,r.options):t[++o]=r;++o?t.length=o:delete this.__on}}}function Ll(e,t,n){return function(){var o=this.__on,i,r=Vl(t);if(o){for(var a=0,s=o.length;a<s;++a)if((i=o[a]).type===e.type&&i.name===e.name){this.removeEventListener(i.type,i.listener,i.options),this.addEventListener(i.type,i.listener=r,i.options=n),i.value=t;return}}this.addEventListener(e.type,r,n),i={type:e.type,name:e.name,value:t,listener:r,options:n},o?o.push(i):this.__on=[i]}}function Yl(e,t,n){var o=Hl(e+""),i,r=o.length,a;if(arguments.length<2){var s=this.node().__on;if(s){for(var u=0,l=s.length,c;u<l;++u)for(i=0,c=s[u];i<r;++i)if((a=o[i]).type===c.type&&a.name===c.name)return c.value}return}for(s=t?Ll:Rl,i=0;i<r;++i)this.each(s(o[i],t,n));return this}function dr(e,t,n){var o=ar(e),i=o.CustomEvent;typeof i=="function"?i=new i(t,n):(i=o.document.createEvent("Event"),n?(i.initEvent(t,n.bubbles,n.cancelable),i.detail=n.detail):i.initEvent(t,!1,!1)),e.dispatchEvent(i)}function Gl(e,t){return function(){return dr(this,e,t)}}function Xl(e,t){return function(){return dr(this,e,t.apply(this,arguments))}}function Wl(e,t){return this.each((typeof t=="function"?Xl:Gl)(e,t))}function*Ul(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var o=e[t],i=0,r=o.length,a;i<r;++i)(a=o[i])&&(yield a)}var fr=[null];function Qe(e,t){this._groups=e,this._parents=t}function dn(){return new Qe([[document.documentElement]],fr)}function Zl(){return this}Qe.prototype=dn.prototype={constructor:Qe,select:_s,selectAll:Ss,selectChild:$s,selectChildren:Ds,filter:As,data:Vs,enter:Ps,exit:Rs,join:Ls,merge:Ys,selection:Zl,order:Gs,sort:Xs,call:Us,nodes:Zs,node:Ks,size:qs,empty:Js,each:Qs,attr:rl,style:ul,property:hl,classed:vl,text:bl,html:kl,raise:Cl,lower:Tl,append:Ml,insert:Dl,remove:Pl,clone:Fl,datum:Bl,on:Yl,dispatch:Wl,[Symbol.iterator]:Ul};function tt(e){return typeof e=="string"?new Qe([[document.querySelector(e)]],[document.documentElement]):new Qe([[e]],fr)}function Kl(e){let t;for(;t=e.sourceEvent;)e=t;return e}function rt(e,t){if(e=Kl(e),t===void 0&&(t=e.currentTarget),t){var n=t.ownerSVGElement||t;if(n.createSVGPoint){var o=n.createSVGPoint();return o.x=e.clientX,o.y=e.clientY,o=o.matrixTransform(t.getScreenCTM().inverse()),[o.x,o.y]}if(t.getBoundingClientRect){var i=t.getBoundingClientRect();return[e.clientX-i.left-t.clientLeft,e.clientY-i.top-t.clientTop]}}return[e.pageX,e.pageY]}const ql={passive:!1},on={capture:!0,passive:!1};function co(e){e.stopImmediatePropagation()}function Ft(e){e.preventDefault(),e.stopImmediatePropagation()}function hr(e){var t=e.document.documentElement,n=tt(e).on("dragstart.drag",Ft,on);"onselectstart"in t?n.on("selectstart.drag",Ft,on):(t.__noselect=t.style.MozUserSelect,t.style.MozUserSelect="none")}function pr(e,t){var n=e.document.documentElement,o=tt(e).on("dragstart.drag",null);t&&(o.on("click.drag",Ft,on),setTimeout(function(){o.on("click.drag",null)},0)),"onselectstart"in n?o.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}const gn=e=>()=>e;function ko(e,{sourceEvent:t,subject:n,target:o,identifier:i,active:r,x:a,y:s,dx:u,dy:l,dispatch:c}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:o,enumerable:!0,configurable:!0},identifier:{value:i,enumerable:!0,configurable:!0},active:{value:r,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:s,enumerable:!0,configurable:!0},dx:{value:u,enumerable:!0,configurable:!0},dy:{value:l,enumerable:!0,configurable:!0},_:{value:c}})}ko.prototype.on=function(){var e=this._.on.apply(this._,arguments);return e===this._?this:e};function Jl(e){return!e.ctrlKey&&!e.button}function Ql(){return this.parentNode}function jl(e,t){return t??{x:e.x,y:e.y}}function eu(){return navigator.maxTouchPoints||"ontouchstart"in this}function tu(){var e=Jl,t=Ql,n=jl,o=eu,i={},r=Gn("start","drag","end"),a=0,s,u,l,c,h=0;function v(T){T.on("mousedown.drag",y).filter(o).on("touchstart.drag",N).on("touchmove.drag",$,ql).on("touchend.drag touchcancel.drag",I).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function y(T,Y){if(!(c||!e.call(this,T,Y))){var D=g(this,t.call(this,T,Y),T,Y,"mouse");D&&(tt(T.view).on("mousemove.drag",M,on).on("mouseup.drag",S,on),hr(T.view),co(T),l=!1,s=T.clientX,u=T.clientY,D("start",T))}}function M(T){if(Ft(T),!l){var Y=T.clientX-s,D=T.clientY-u;l=Y*Y+D*D>h}i.mouse("drag",T)}function S(T){tt(T.view).on("mousemove.drag mouseup.drag",null),pr(T.view,l),Ft(T),i.mouse("end",T)}function N(T,Y){if(e.call(this,T,Y)){var D=T.changedTouches,A=t.call(this,T,Y),O=D.length,R,G;for(R=0;R<O;++R)(G=g(this,A,T,Y,D[R].identifier,D[R]))&&(co(T),G("start",T,D[R]))}}function $(T){var Y=T.changedTouches,D=Y.length,A,O;for(A=0;A<D;++A)(O=i[Y[A].identifier])&&(Ft(T),O("drag",T,Y[A]))}function I(T){var Y=T.changedTouches,D=Y.length,A,O;for(c&&clearTimeout(c),c=setTimeout(function(){c=null},500),A=0;A<D;++A)(O=i[Y[A].identifier])&&(co(T),O("end",T,Y[A]))}function g(T,Y,D,A,O,R){var G=r.copy(),F=rt(R||D,Y),x,X,E;if((E=n.call(T,new ko("beforestart",{sourceEvent:D,target:v,identifier:O,active:a,x:F[0],y:F[1],dx:0,dy:0,dispatch:G}),A))!=null)return x=E.x-F[0]||0,X=E.y-F[1]||0,function z(k,H,L){var J=F,W;switch(k){case"start":i[O]=z,W=a++;break;case"end":delete i[O],--a;case"drag":F=rt(L||H,Y),W=a;break}G.call(k,T,new ko(k,{sourceEvent:H,subject:E,target:v,identifier:O,active:W,x:F[0]+x,y:F[1]+X,dx:F[0]-J[0],dy:F[1]-J[1],dispatch:G}),A)}}return v.filter=function(T){return arguments.length?(e=typeof T=="function"?T:gn(!!T),v):e},v.container=function(T){return arguments.length?(t=typeof T=="function"?T:gn(T),v):t},v.subject=function(T){return arguments.length?(n=typeof T=="function"?T:gn(T),v):n},v.touchable=function(T){return arguments.length?(o=typeof T=="function"?T:gn(!!T),v):o},v.on=function(){var T=r.on.apply(r,arguments);return T===r?v:T},v.clickDistance=function(T){return arguments.length?(h=(T=+T)*T,v):Math.sqrt(h)},v}function Lo(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function gr(e,t){var n=Object.create(e.prototype);for(var o in t)n[o]=t[o];return n}function fn(){}var rn=.7,Dn=1/rn,Bt="\\s*([+-]?\\d+)\\s*",an="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",lt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",nu=/^#([0-9a-f]{3,8})$/,ou=new RegExp(`^rgb\\(${Bt},${Bt},${Bt}\\)$`),iu=new RegExp(`^rgb\\(${lt},${lt},${lt}\\)$`),ru=new RegExp(`^rgba\\(${Bt},${Bt},${Bt},${an}\\)$`),au=new RegExp(`^rgba\\(${lt},${lt},${lt},${an}\\)$`),su=new RegExp(`^hsl\\(${an},${lt},${lt}\\)$`),lu=new RegExp(`^hsla\\(${an},${lt},${lt},${an}\\)$`),si={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};Lo(fn,Mt,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:li,formatHex:li,formatHex8:uu,formatHsl:cu,formatRgb:ui,toString:ui});function li(){return this.rgb().formatHex()}function uu(){return this.rgb().formatHex8()}function cu(){return mr(this).formatHsl()}function ui(){return this.rgb().formatRgb()}function Mt(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=nu.exec(e))?(n=t[1].length,t=parseInt(t[1],16),n===6?ci(t):n===3?new qe(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):n===8?mn(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):n===4?mn(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=ou.exec(e))?new qe(t[1],t[2],t[3],1):(t=iu.exec(e))?new qe(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=ru.exec(e))?mn(t[1],t[2],t[3],t[4]):(t=au.exec(e))?mn(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=su.exec(e))?hi(t[1],t[2]/100,t[3]/100,1):(t=lu.exec(e))?hi(t[1],t[2]/100,t[3]/100,t[4]):si.hasOwnProperty(e)?ci(si[e]):e==="transparent"?new qe(NaN,NaN,NaN,0):null}function ci(e){return new qe(e>>16&255,e>>8&255,e&255,1)}function mn(e,t,n,o){return o<=0&&(e=t=n=NaN),new qe(e,t,n,o)}function du(e){return e instanceof fn||(e=Mt(e)),e?(e=e.rgb(),new qe(e.r,e.g,e.b,e.opacity)):new qe}function No(e,t,n,o){return arguments.length===1?du(e):new qe(e,t,n,o??1)}function qe(e,t,n,o){this.r=+e,this.g=+t,this.b=+n,this.opacity=+o}Lo(qe,No,gr(fn,{brighter(e){return e=e==null?Dn:Math.pow(Dn,e),new qe(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?rn:Math.pow(rn,e),new qe(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new qe($t(this.r),$t(this.g),$t(this.b),An(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:di,formatHex:di,formatHex8:fu,formatRgb:fi,toString:fi}));function di(){return`#${Ct(this.r)}${Ct(this.g)}${Ct(this.b)}`}function fu(){return`#${Ct(this.r)}${Ct(this.g)}${Ct(this.b)}${Ct((isNaN(this.opacity)?1:this.opacity)*255)}`}function fi(){const e=An(this.opacity);return`${e===1?"rgb(":"rgba("}${$t(this.r)}, ${$t(this.g)}, ${$t(this.b)}${e===1?")":`, ${e})`}`}function An(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function $t(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Ct(e){return e=$t(e),(e<16?"0":"")+e.toString(16)}function hi(e,t,n,o){return o<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new nt(e,t,n,o)}function mr(e){if(e instanceof nt)return new nt(e.h,e.s,e.l,e.opacity);if(e instanceof fn||(e=Mt(e)),!e)return new nt;if(e instanceof nt)return e;e=e.rgb();var t=e.r/255,n=e.g/255,o=e.b/255,i=Math.min(t,n,o),r=Math.max(t,n,o),a=NaN,s=r-i,u=(r+i)/2;return s?(t===r?a=(n-o)/s+(n<o)*6:n===r?a=(o-t)/s+2:a=(t-n)/s+4,s/=u<.5?r+i:2-r-i,a*=60):s=u>0&&u<1?0:a,new nt(a,s,u,e.opacity)}function hu(e,t,n,o){return arguments.length===1?mr(e):new nt(e,t,n,o??1)}function nt(e,t,n,o){this.h=+e,this.s=+t,this.l=+n,this.opacity=+o}Lo(nt,hu,gr(fn,{brighter(e){return e=e==null?Dn:Math.pow(Dn,e),new nt(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?rn:Math.pow(rn,e),new nt(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,o=n+(n<.5?n:1-n)*t,i=2*n-o;return new qe(fo(e>=240?e-240:e+120,i,o),fo(e,i,o),fo(e<120?e+240:e-120,i,o),this.opacity)},clamp(){return new nt(pi(this.h),vn(this.s),vn(this.l),An(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=An(this.opacity);return`${e===1?"hsl(":"hsla("}${pi(this.h)}, ${vn(this.s)*100}%, ${vn(this.l)*100}%${e===1?")":`, ${e})`}`}}));function pi(e){return e=(e||0)%360,e<0?e+360:e}function vn(e){return Math.max(0,Math.min(1,e||0))}function fo(e,t,n){return(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)*255}const Yo=e=>()=>e;function pu(e,t){return function(n){return e+n*t}}function gu(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(o){return Math.pow(e+o*t,n)}}function mu(e){return(e=+e)==1?vr:function(t,n){return n-t?gu(t,n,e):Yo(isNaN(t)?n:t)}}function vr(e,t){var n=t-e;return n?pu(e,n):Yo(isNaN(e)?t:e)}const Pn=function e(t){var n=mu(t);function o(i,r){var a=n((i=No(i)).r,(r=No(r)).r),s=n(i.g,r.g),u=n(i.b,r.b),l=vr(i.opacity,r.opacity);return function(c){return i.r=a(c),i.g=s(c),i.b=u(c),i.opacity=l(c),i+""}}return o.gamma=e,o}(1);function vu(e,t){t||(t=[]);var n=e?Math.min(t.length,e.length):0,o=t.slice(),i;return function(r){for(i=0;i<n;++i)o[i]=e[i]*(1-r)+t[i]*r;return o}}function yu(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function wu(e,t){var n=t?t.length:0,o=e?Math.min(n,e.length):0,i=new Array(o),r=new Array(n),a;for(a=0;a<o;++a)i[a]=en(e[a],t[a]);for(;a<n;++a)r[a]=t[a];return function(s){for(a=0;a<o;++a)r[a]=i[a](s);return r}}function _u(e,t){var n=new Date;return e=+e,t=+t,function(o){return n.setTime(e*(1-o)+t*o),n}}function at(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}function bu(e,t){var n={},o={},i;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(i in t)i in e?n[i]=en(e[i],t[i]):o[i]=t[i];return function(r){for(i in n)o[i]=n[i](r);return o}}var Co=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ho=new RegExp(Co.source,"g");function Eu(e){return function(){return e}}function xu(e){return function(t){return e(t)+""}}function yr(e,t){var n=Co.lastIndex=ho.lastIndex=0,o,i,r,a=-1,s=[],u=[];for(e=e+"",t=t+"";(o=Co.exec(e))&&(i=ho.exec(t));)(r=i.index)>n&&(r=t.slice(n,r),s[a]?s[a]+=r:s[++a]=r),(o=o[0])===(i=i[0])?s[a]?s[a]+=i:s[++a]=i:(s[++a]=null,u.push({i:a,x:at(o,i)})),n=ho.lastIndex;return n<t.length&&(r=t.slice(n),s[a]?s[a]+=r:s[++a]=r),s.length<2?u[0]?xu(u[0].x):Eu(t):(t=u.length,function(l){for(var c=0,h;c<t;++c)s[(h=u[c]).i]=h.x(l);return s.join("")})}function en(e,t){var n=typeof t,o;return t==null||n==="boolean"?Yo(t):(n==="number"?at:n==="string"?(o=Mt(t))?(t=o,Pn):yr:t instanceof Mt?Pn:t instanceof Date?_u:yu(t)?vu:Array.isArray(t)?wu:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?bu:at)(e,t)}var gi=180/Math.PI,$o={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function wr(e,t,n,o,i,r){var a,s,u;return(a=Math.sqrt(e*e+t*t))&&(e/=a,t/=a),(u=e*n+t*o)&&(n-=e*u,o-=t*u),(s=Math.sqrt(n*n+o*o))&&(n/=s,o/=s,u/=s),e*o<t*n&&(e=-e,t=-t,u=-u,a=-a),{translateX:i,translateY:r,rotate:Math.atan2(t,e)*gi,skewX:Math.atan(u)*gi,scaleX:a,scaleY:s}}var yn;function Su(e){const t=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?$o:wr(t.a,t.b,t.c,t.d,t.e,t.f)}function ku(e){return e==null||(yn||(yn=document.createElementNS("http://www.w3.org/2000/svg","g")),yn.setAttribute("transform",e),!(e=yn.transform.baseVal.consolidate()))?$o:(e=e.matrix,wr(e.a,e.b,e.c,e.d,e.e,e.f))}function _r(e,t,n,o){function i(l){return l.length?l.pop()+" ":""}function r(l,c,h,v,y,M){if(l!==h||c!==v){var S=y.push("translate(",null,t,null,n);M.push({i:S-4,x:at(l,h)},{i:S-2,x:at(c,v)})}else(h||v)&&y.push("translate("+h+t+v+n)}function a(l,c,h,v){l!==c?(l-c>180?c+=360:c-l>180&&(l+=360),v.push({i:h.push(i(h)+"rotate(",null,o)-2,x:at(l,c)})):c&&h.push(i(h)+"rotate("+c+o)}function s(l,c,h,v){l!==c?v.push({i:h.push(i(h)+"skewX(",null,o)-2,x:at(l,c)}):c&&h.push(i(h)+"skewX("+c+o)}function u(l,c,h,v,y,M){if(l!==h||c!==v){var S=y.push(i(y)+"scale(",null,",",null,")");M.push({i:S-4,x:at(l,h)},{i:S-2,x:at(c,v)})}else(h!==1||v!==1)&&y.push(i(y)+"scale("+h+","+v+")")}return function(l,c){var h=[],v=[];return l=e(l),c=e(c),r(l.translateX,l.translateY,c.translateX,c.translateY,h,v),a(l.rotate,c.rotate,h,v),s(l.skewX,c.skewX,h,v),u(l.scaleX,l.scaleY,c.scaleX,c.scaleY,h,v),l=c=null,function(y){for(var M=-1,S=v.length,N;++M<S;)h[(N=v[M]).i]=N.x(y);return h.join("")}}}var Nu=_r(Su,"px, ","px)","deg)"),Cu=_r(ku,", ",")",")"),$u=1e-12;function mi(e){return((e=Math.exp(e))+1/e)/2}function Tu(e){return((e=Math.exp(e))-1/e)/2}function Mu(e){return((e=Math.exp(2*e))-1)/(e+1)}const Nn=function e(t,n,o){function i(r,a){var s=r[0],u=r[1],l=r[2],c=a[0],h=a[1],v=a[2],y=c-s,M=h-u,S=y*y+M*M,N,$;if(S<$u)$=Math.log(v/l)/t,N=function(A){return[s+A*y,u+A*M,l*Math.exp(t*A*$)]};else{var I=Math.sqrt(S),g=(v*v-l*l+o*S)/(2*l*n*I),T=(v*v-l*l-o*S)/(2*v*n*I),Y=Math.log(Math.sqrt(g*g+1)-g),D=Math.log(Math.sqrt(T*T+1)-T);$=(D-Y)/t,N=function(A){var O=A*$,R=mi(Y),G=l/(n*I)*(R*Mu(t*O+Y)-Tu(Y));return[s+G*y,u+G*M,l*R/mi(t*O+Y)]}}return N.duration=$*1e3*t/Math.SQRT2,N}return i.rho=function(r){var a=Math.max(.001,+r),s=a*a,u=s*s;return e(a,s,u)},i}(Math.SQRT2,2,4);var Lt=0,qt=0,Zt=0,br=1e3,zn,Jt,On=0,It=0,Wn=0,sn=typeof performance=="object"&&performance.now?performance:Date,Er=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function Go(){return It||(Er(Iu),It=sn.now()+Wn)}function Iu(){It=0}function Fn(){this._call=this._time=this._next=null}Fn.prototype=xr.prototype={constructor:Fn,restart:function(e,t,n){if(typeof e!="function")throw new TypeError("callback is not a function");n=(n==null?Go():+n)+(t==null?0:+t),!this._next&&Jt!==this&&(Jt?Jt._next=this:zn=this,Jt=this),this._call=e,this._time=n,To()},stop:function(){this._call&&(this._call=null,this._time=1/0,To())}};function xr(e,t,n){var o=new Fn;return o.restart(e,t,n),o}function Du(){Go(),++Lt;for(var e=zn,t;e;)(t=It-e._time)>=0&&e._call.call(void 0,t),e=e._next;--Lt}function vi(){It=(On=sn.now())+Wn,Lt=qt=0;try{Du()}finally{Lt=0,Pu(),It=0}}function Au(){var e=sn.now(),t=e-On;t>br&&(Wn-=t,On=e)}function Pu(){for(var e,t=zn,n,o=1/0;t;)t._call?(o>t._time&&(o=t._time),e=t,t=t._next):(n=t._next,t._next=null,t=e?e._next=n:zn=n);Jt=e,To(o)}function To(e){if(!Lt){qt&&(qt=clearTimeout(qt));var t=e-It;t>24?(e<1/0&&(qt=setTimeout(vi,e-sn.now()-Wn)),Zt&&(Zt=clearInterval(Zt))):(Zt||(On=sn.now(),Zt=setInterval(Au,br)),Lt=1,Er(vi))}}function yi(e,t,n){var o=new Fn;return t=t==null?0:+t,o.restart(i=>{o.stop(),e(i+t)},t,n),o}var zu=Gn("start","end","cancel","interrupt"),Ou=[],Sr=0,wi=1,Mo=2,Cn=3,_i=4,Io=5,$n=6;function Un(e,t,n,o,i,r){var a=e.__transition;if(!a)e.__transition={};else if(n in a)return;Fu(e,n,{name:t,index:o,group:i,on:zu,tween:Ou,time:r.time,delay:r.delay,duration:r.duration,ease:r.ease,timer:null,state:Sr})}function Xo(e,t){var n=ot(e,t);if(n.state>Sr)throw new Error("too late; already scheduled");return n}function ct(e,t){var n=ot(e,t);if(n.state>Cn)throw new Error("too late; already running");return n}function ot(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function Fu(e,t,n){var o=e.__transition,i;o[t]=n,n.timer=xr(r,0,n.time);function r(l){n.state=wi,n.timer.restart(a,n.delay,n.time),n.delay<=l&&a(l-n.delay)}function a(l){var c,h,v,y;if(n.state!==wi)return u();for(c in o)if(y=o[c],y.name===n.name){if(y.state===Cn)return yi(a);y.state===_i?(y.state=$n,y.timer.stop(),y.on.call("interrupt",e,e.__data__,y.index,y.group),delete o[c]):+c<t&&(y.state=$n,y.timer.stop(),y.on.call("cancel",e,e.__data__,y.index,y.group),delete o[c])}if(yi(function(){n.state===Cn&&(n.state=_i,n.timer.restart(s,n.delay,n.time),s(l))}),n.state=Mo,n.on.call("start",e,e.__data__,n.index,n.group),n.state===Mo){for(n.state=Cn,i=new Array(v=n.tween.length),c=0,h=-1;c<v;++c)(y=n.tween[c].value.call(e,e.__data__,n.index,n.group))&&(i[++h]=y);i.length=h+1}}function s(l){for(var c=l<n.duration?n.ease.call(null,l/n.duration):(n.timer.restart(u),n.state=Io,1),h=-1,v=i.length;++h<v;)i[h].call(e,c);n.state===Io&&(n.on.call("end",e,e.__data__,n.index,n.group),u())}function u(){n.state=$n,n.timer.stop(),delete o[t];for(var l in o)return;delete e.__transition}}function Tn(e,t){var n=e.__transition,o,i,r=!0,a;if(n){t=t==null?null:t+"";for(a in n){if((o=n[a]).name!==t){r=!1;continue}i=o.state>Mo&&o.state<Io,o.state=$n,o.timer.stop(),o.on.call(i?"interrupt":"cancel",e,e.__data__,o.index,o.group),delete n[a]}r&&delete e.__transition}}function Bu(e){return this.each(function(){Tn(this,e)})}function Vu(e,t){var n,o;return function(){var i=ct(this,e),r=i.tween;if(r!==n){o=n=r;for(var a=0,s=o.length;a<s;++a)if(o[a].name===t){o=o.slice(),o.splice(a,1);break}}i.tween=o}}function Hu(e,t,n){var o,i;if(typeof n!="function")throw new Error;return function(){var r=ct(this,e),a=r.tween;if(a!==o){i=(o=a).slice();for(var s={name:t,value:n},u=0,l=i.length;u<l;++u)if(i[u].name===t){i[u]=s;break}u===l&&i.push(s)}r.tween=i}}function Ru(e,t){var n=this._id;if(e+="",arguments.length<2){for(var o=ot(this.node(),n).tween,i=0,r=o.length,a;i<r;++i)if((a=o[i]).name===e)return a.value;return null}return this.each((t==null?Vu:Hu)(n,e,t))}function Wo(e,t,n){var o=e._id;return e.each(function(){var i=ct(this,o);(i.value||(i.value={}))[t]=n.apply(this,arguments)}),function(i){return ot(i,o).value[t]}}function kr(e,t){var n;return(typeof t=="number"?at:t instanceof Mt?Pn:(n=Mt(t))?(t=n,Pn):yr)(e,t)}function Lu(e){return function(){this.removeAttribute(e)}}function Yu(e){return function(){this.removeAttributeNS(e.space,e.local)}}function Gu(e,t,n){var o,i=n+"",r;return function(){var a=this.getAttribute(e);return a===i?null:a===o?r:r=t(o=a,n)}}function Xu(e,t,n){var o,i=n+"",r;return function(){var a=this.getAttributeNS(e.space,e.local);return a===i?null:a===o?r:r=t(o=a,n)}}function Wu(e,t,n){var o,i,r;return function(){var a,s=n(this),u;return s==null?void this.removeAttribute(e):(a=this.getAttribute(e),u=s+"",a===u?null:a===o&&u===i?r:(i=u,r=t(o=a,s)))}}function Uu(e,t,n){var o,i,r;return function(){var a,s=n(this),u;return s==null?void this.removeAttributeNS(e.space,e.local):(a=this.getAttributeNS(e.space,e.local),u=s+"",a===u?null:a===o&&u===i?r:(i=u,r=t(o=a,s)))}}function Zu(e,t){var n=Xn(e),o=n==="transform"?Cu:kr;return this.attrTween(e,typeof t=="function"?(n.local?Uu:Wu)(n,o,Wo(this,"attr."+e,t)):t==null?(n.local?Yu:Lu)(n):(n.local?Xu:Gu)(n,o,t))}function Ku(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}function qu(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}function Ju(e,t){var n,o;function i(){var r=t.apply(this,arguments);return r!==o&&(n=(o=r)&&qu(e,r)),n}return i._value=t,i}function Qu(e,t){var n,o;function i(){var r=t.apply(this,arguments);return r!==o&&(n=(o=r)&&Ku(e,r)),n}return i._value=t,i}function ju(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(t==null)return this.tween(n,null);if(typeof t!="function")throw new Error;var o=Xn(e);return this.tween(n,(o.local?Ju:Qu)(o,t))}function ec(e,t){return function(){Xo(this,e).delay=+t.apply(this,arguments)}}function tc(e,t){return t=+t,function(){Xo(this,e).delay=t}}function nc(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?ec:tc)(t,e)):ot(this.node(),t).delay}function oc(e,t){return function(){ct(this,e).duration=+t.apply(this,arguments)}}function ic(e,t){return t=+t,function(){ct(this,e).duration=t}}function rc(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?oc:ic)(t,e)):ot(this.node(),t).duration}function ac(e,t){if(typeof t!="function")throw new Error;return function(){ct(this,e).ease=t}}function sc(e){var t=this._id;return arguments.length?this.each(ac(t,e)):ot(this.node(),t).ease}function lc(e,t){return function(){var n=t.apply(this,arguments);if(typeof n!="function")throw new Error;ct(this,e).ease=n}}function uc(e){if(typeof e!="function")throw new Error;return this.each(lc(this._id,e))}function cc(e){typeof e!="function"&&(e=or(e));for(var t=this._groups,n=t.length,o=new Array(n),i=0;i<n;++i)for(var r=t[i],a=r.length,s=o[i]=[],u,l=0;l<a;++l)(u=r[l])&&e.call(u,u.__data__,l,r)&&s.push(u);return new vt(o,this._parents,this._name,this._id)}function dc(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,o=t.length,i=n.length,r=Math.min(o,i),a=new Array(o),s=0;s<r;++s)for(var u=t[s],l=n[s],c=u.length,h=a[s]=new Array(c),v,y=0;y<c;++y)(v=u[y]||l[y])&&(h[y]=v);for(;s<o;++s)a[s]=t[s];return new vt(a,this._parents,this._name,this._id)}function fc(e){return(e+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||t==="start"})}function hc(e,t,n){var o,i,r=fc(t)?Xo:ct;return function(){var a=r(this,e),s=a.on;s!==o&&(i=(o=s).copy()).on(t,n),a.on=i}}function pc(e,t){var n=this._id;return arguments.length<2?ot(this.node(),n).on.on(e):this.each(hc(n,e,t))}function gc(e){return function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}}function mc(){return this.on("end.remove",gc(this._id))}function vc(e){var t=this._name,n=this._id;typeof e!="function"&&(e=Ho(e));for(var o=this._groups,i=o.length,r=new Array(i),a=0;a<i;++a)for(var s=o[a],u=s.length,l=r[a]=new Array(u),c,h,v=0;v<u;++v)(c=s[v])&&(h=e.call(c,c.__data__,v,s))&&("__data__"in c&&(h.__data__=c.__data__),l[v]=h,Un(l[v],t,n,v,l,ot(c,n)));return new vt(r,this._parents,t,n)}function yc(e){var t=this._name,n=this._id;typeof e!="function"&&(e=nr(e));for(var o=this._groups,i=o.length,r=[],a=[],s=0;s<i;++s)for(var u=o[s],l=u.length,c,h=0;h<l;++h)if(c=u[h]){for(var v=e.call(c,c.__data__,h,u),y,M=ot(c,n),S=0,N=v.length;S<N;++S)(y=v[S])&&Un(y,t,n,S,v,M);r.push(v),a.push(c)}return new vt(r,a,t,n)}var wc=dn.prototype.constructor;function _c(){return new wc(this._groups,this._parents)}function bc(e,t){var n,o,i;return function(){var r=Rt(this,e),a=(this.style.removeProperty(e),Rt(this,e));return r===a?null:r===n&&a===o?i:i=t(n=r,o=a)}}function Nr(e){return function(){this.style.removeProperty(e)}}function Ec(e,t,n){var o,i=n+"",r;return function(){var a=Rt(this,e);return a===i?null:a===o?r:r=t(o=a,n)}}function xc(e,t,n){var o,i,r;return function(){var a=Rt(this,e),s=n(this),u=s+"";return s==null&&(u=s=(this.style.removeProperty(e),Rt(this,e))),a===u?null:a===o&&u===i?r:(i=u,r=t(o=a,s))}}function Sc(e,t){var n,o,i,r="style."+t,a="end."+r,s;return function(){var u=ct(this,e),l=u.on,c=u.value[r]==null?s||(s=Nr(t)):void 0;(l!==n||i!==c)&&(o=(n=l).copy()).on(a,i=c),u.on=o}}function kc(e,t,n){var o=(e+="")=="transform"?Nu:kr;return t==null?this.styleTween(e,bc(e,o)).on("end.style."+e,Nr(e)):typeof t=="function"?this.styleTween(e,xc(e,o,Wo(this,"style."+e,t))).each(Sc(this._id,e)):this.styleTween(e,Ec(e,o,t),n).on("end.style."+e,null)}function Nc(e,t,n){return function(o){this.style.setProperty(e,t.call(this,o),n)}}function Cc(e,t,n){var o,i;function r(){var a=t.apply(this,arguments);return a!==i&&(o=(i=a)&&Nc(e,a,n)),o}return r._value=t,r}function $c(e,t,n){var o="style."+(e+="");if(arguments.length<2)return(o=this.tween(o))&&o._value;if(t==null)return this.tween(o,null);if(typeof t!="function")throw new Error;return this.tween(o,Cc(e,t,n??""))}function Tc(e){return function(){this.textContent=e}}function Mc(e){return function(){var t=e(this);this.textContent=t??""}}function Ic(e){return this.tween("text",typeof e=="function"?Mc(Wo(this,"text",e)):Tc(e==null?"":e+""))}function Dc(e){return function(t){this.textContent=e.call(this,t)}}function Ac(e){var t,n;function o(){var i=e.apply(this,arguments);return i!==n&&(t=(n=i)&&Dc(i)),t}return o._value=e,o}function Pc(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(e==null)return this.tween(t,null);if(typeof e!="function")throw new Error;return this.tween(t,Ac(e))}function zc(){for(var e=this._name,t=this._id,n=Cr(),o=this._groups,i=o.length,r=0;r<i;++r)for(var a=o[r],s=a.length,u,l=0;l<s;++l)if(u=a[l]){var c=ot(u,t);Un(u,e,n,l,a,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}return new vt(o,this._parents,e,n)}function Oc(){var e,t,n=this,o=n._id,i=n.size();return new Promise(function(r,a){var s={value:a},u={value:function(){--i===0&&r()}};n.each(function(){var l=ct(this,o),c=l.on;c!==e&&(t=(e=c).copy(),t._.cancel.push(s),t._.interrupt.push(s),t._.end.push(u)),l.on=t}),i===0&&r()})}var Fc=0;function vt(e,t,n,o){this._groups=e,this._parents=t,this._name=n,this._id=o}function Cr(){return++Fc}var ft=dn.prototype;vt.prototype={constructor:vt,select:vc,selectAll:yc,selectChild:ft.selectChild,selectChildren:ft.selectChildren,filter:cc,merge:dc,selection:_c,transition:zc,call:ft.call,nodes:ft.nodes,node:ft.node,size:ft.size,empty:ft.empty,each:ft.each,on:pc,attr:Zu,attrTween:ju,style:kc,styleTween:$c,text:Ic,textTween:Pc,remove:mc,tween:Ru,delay:nc,duration:rc,ease:sc,easeVarying:uc,end:Oc,[Symbol.iterator]:ft[Symbol.iterator]};function Bc(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}var Vc={time:null,delay:0,duration:250,ease:Bc};function Hc(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}function Rc(e){var t,n;e instanceof vt?(t=e._id,e=e._name):(t=Cr(),(n=Vc).time=Go(),e=e==null?null:e+"");for(var o=this._groups,i=o.length,r=0;r<i;++r)for(var a=o[r],s=a.length,u,l=0;l<s;++l)(u=a[l])&&Un(u,e,t,l,a,n||Hc(u,t));return new vt(o,this._parents,e,t)}dn.prototype.interrupt=Bu;dn.prototype.transition=Rc;const wn=e=>()=>e;function Lc(e,{sourceEvent:t,target:n,transform:o,dispatch:i}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:o,enumerable:!0,configurable:!0},_:{value:i}})}function pt(e,t,n){this.k=e,this.x=t,this.y=n}pt.prototype={constructor:pt,scale:function(e){return e===1?this:new pt(this.k*e,this.x,this.y)},translate:function(e,t){return e===0&t===0?this:new pt(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var Yt=new pt(1,0,0);pt.prototype;function po(e){e.stopImmediatePropagation()}function Kt(e){e.preventDefault(),e.stopImmediatePropagation()}function Yc(e){return(!e.ctrlKey||e.type==="wheel")&&!e.button}function Gc(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e,e.hasAttribute("viewBox")?(e=e.viewBox.baseVal,[[e.x,e.y],[e.x+e.width,e.y+e.height]]):[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]):[[0,0],[e.clientWidth,e.clientHeight]]}function bi(){return this.__zoom||Yt}function Xc(e){return-e.deltaY*(e.deltaMode===1?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function Wc(){return navigator.maxTouchPoints||"ontouchstart"in this}function Uc(e,t,n){var o=e.invertX(t[0][0])-n[0][0],i=e.invertX(t[1][0])-n[1][0],r=e.invertY(t[0][1])-n[0][1],a=e.invertY(t[1][1])-n[1][1];return e.translate(i>o?(o+i)/2:Math.min(0,o)||Math.max(0,i),a>r?(r+a)/2:Math.min(0,r)||Math.max(0,a))}function Zc(){var e=Yc,t=Gc,n=Uc,o=Xc,i=Wc,r=[0,1/0],a=[[-1/0,-1/0],[1/0,1/0]],s=250,u=Nn,l=Gn("start","zoom","end"),c,h,v,y=500,M=150,S=0,N=10;function $(E){E.property("__zoom",bi).on("wheel.zoom",O,{passive:!1}).on("mousedown.zoom",R).on("dblclick.zoom",G).filter(i).on("touchstart.zoom",F).on("touchmove.zoom",x).on("touchend.zoom touchcancel.zoom",X).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}$.transform=function(E,z,k,H){var L=E.selection?E.selection():E;L.property("__zoom",bi),E!==L?Y(E,z,k,H):L.interrupt().each(function(){D(this,arguments).event(H).start().zoom(null,typeof z=="function"?z.apply(this,arguments):z).end()})},$.scaleBy=function(E,z,k,H){$.scaleTo(E,function(){var L=this.__zoom.k,J=typeof z=="function"?z.apply(this,arguments):z;return L*J},k,H)},$.scaleTo=function(E,z,k,H){$.transform(E,function(){var L=t.apply(this,arguments),J=this.__zoom,W=k==null?T(L):typeof k=="function"?k.apply(this,arguments):k,se=J.invert(W),pe=typeof z=="function"?z.apply(this,arguments):z;return n(g(I(J,pe),W,se),L,a)},k,H)},$.translateBy=function(E,z,k,H){$.transform(E,function(){return n(this.__zoom.translate(typeof z=="function"?z.apply(this,arguments):z,typeof k=="function"?k.apply(this,arguments):k),t.apply(this,arguments),a)},null,H)},$.translateTo=function(E,z,k,H,L){$.transform(E,function(){var J=t.apply(this,arguments),W=this.__zoom,se=H==null?T(J):typeof H=="function"?H.apply(this,arguments):H;return n(Yt.translate(se[0],se[1]).scale(W.k).translate(typeof z=="function"?-z.apply(this,arguments):-z,typeof k=="function"?-k.apply(this,arguments):-k),J,a)},H,L)};function I(E,z){return z=Math.max(r[0],Math.min(r[1],z)),z===E.k?E:new pt(z,E.x,E.y)}function g(E,z,k){var H=z[0]-k[0]*E.k,L=z[1]-k[1]*E.k;return H===E.x&&L===E.y?E:new pt(E.k,H,L)}function T(E){return[(+E[0][0]+ +E[1][0])/2,(+E[0][1]+ +E[1][1])/2]}function Y(E,z,k,H){E.on("start.zoom",function(){D(this,arguments).event(H).start()}).on("interrupt.zoom end.zoom",function(){D(this,arguments).event(H).end()}).tween("zoom",function(){var L=this,J=arguments,W=D(L,J).event(H),se=t.apply(L,J),pe=k==null?T(se):typeof k=="function"?k.apply(L,J):k,fe=Math.max(se[1][0]-se[0][0],se[1][1]-se[0][1]),Q=L.__zoom,ae=typeof z=="function"?z.apply(L,J):z,ce=u(Q.invert(pe).concat(fe/Q.k),ae.invert(pe).concat(fe/ae.k));return function(Se){if(Se===1)Se=ae;else{var Ne=ce(Se),ve=fe/Ne[2];Se=new pt(ve,pe[0]-Ne[0]*ve,pe[1]-Ne[1]*ve)}W.zoom(null,Se)}})}function D(E,z,k){return!k&&E.__zooming||new A(E,z)}function A(E,z){this.that=E,this.args=z,this.active=0,this.sourceEvent=null,this.extent=t.apply(E,z),this.taps=0}A.prototype={event:function(E){return E&&(this.sourceEvent=E),this},start:function(){return++this.active===1&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(E,z){return this.mouse&&E!=="mouse"&&(this.mouse[1]=z.invert(this.mouse[0])),this.touch0&&E!=="touch"&&(this.touch0[1]=z.invert(this.touch0[0])),this.touch1&&E!=="touch"&&(this.touch1[1]=z.invert(this.touch1[0])),this.that.__zoom=z,this.emit("zoom"),this},end:function(){return--this.active===0&&(delete this.that.__zooming,this.emit("end")),this},emit:function(E){var z=tt(this.that).datum();l.call(E,this.that,new Lc(E,{sourceEvent:this.sourceEvent,target:$,transform:this.that.__zoom,dispatch:l}),z)}};function O(E,...z){if(!e.apply(this,arguments))return;var k=D(this,z).event(E),H=this.__zoom,L=Math.max(r[0],Math.min(r[1],H.k*Math.pow(2,o.apply(this,arguments)))),J=rt(E);if(k.wheel)(k.mouse[0][0]!==J[0]||k.mouse[0][1]!==J[1])&&(k.mouse[1]=H.invert(k.mouse[0]=J)),clearTimeout(k.wheel);else{if(H.k===L)return;k.mouse=[J,H.invert(J)],Tn(this),k.start()}Kt(E),k.wheel=setTimeout(W,M),k.zoom("mouse",n(g(I(H,L),k.mouse[0],k.mouse[1]),k.extent,a));function W(){k.wheel=null,k.end()}}function R(E,...z){if(v||!e.apply(this,arguments))return;var k=E.currentTarget,H=D(this,z,!0).event(E),L=tt(E.view).on("mousemove.zoom",pe,!0).on("mouseup.zoom",fe,!0),J=rt(E,k),W=E.clientX,se=E.clientY;hr(E.view),po(E),H.mouse=[J,this.__zoom.invert(J)],Tn(this),H.start();function pe(Q){if(Kt(Q),!H.moved){var ae=Q.clientX-W,ce=Q.clientY-se;H.moved=ae*ae+ce*ce>S}H.event(Q).zoom("mouse",n(g(H.that.__zoom,H.mouse[0]=rt(Q,k),H.mouse[1]),H.extent,a))}function fe(Q){L.on("mousemove.zoom mouseup.zoom",null),pr(Q.view,H.moved),Kt(Q),H.event(Q).end()}}function G(E,...z){if(e.apply(this,arguments)){var k=this.__zoom,H=rt(E.changedTouches?E.changedTouches[0]:E,this),L=k.invert(H),J=k.k*(E.shiftKey?.5:2),W=n(g(I(k,J),H,L),t.apply(this,z),a);Kt(E),s>0?tt(this).transition().duration(s).call(Y,W,H,E):tt(this).call($.transform,W,H,E)}}function F(E,...z){if(e.apply(this,arguments)){var k=E.touches,H=k.length,L=D(this,z,E.changedTouches.length===H).event(E),J,W,se,pe;for(po(E),W=0;W<H;++W)se=k[W],pe=rt(se,this),pe=[pe,this.__zoom.invert(pe),se.identifier],L.touch0?!L.touch1&&L.touch0[2]!==pe[2]&&(L.touch1=pe,L.taps=0):(L.touch0=pe,J=!0,L.taps=1+!!c);c&&(c=clearTimeout(c)),J&&(L.taps<2&&(h=pe[0],c=setTimeout(function(){c=null},y)),Tn(this),L.start())}}function x(E,...z){if(this.__zooming){var k=D(this,z).event(E),H=E.changedTouches,L=H.length,J,W,se,pe;for(Kt(E),J=0;J<L;++J)W=H[J],se=rt(W,this),k.touch0&&k.touch0[2]===W.identifier?k.touch0[0]=se:k.touch1&&k.touch1[2]===W.identifier&&(k.touch1[0]=se);if(W=k.that.__zoom,k.touch1){var fe=k.touch0[0],Q=k.touch0[1],ae=k.touch1[0],ce=k.touch1[1],Se=(Se=ae[0]-fe[0])*Se+(Se=ae[1]-fe[1])*Se,Ne=(Ne=ce[0]-Q[0])*Ne+(Ne=ce[1]-Q[1])*Ne;W=I(W,Math.sqrt(Se/Ne)),se=[(fe[0]+ae[0])/2,(fe[1]+ae[1])/2],pe=[(Q[0]+ce[0])/2,(Q[1]+ce[1])/2]}else if(k.touch0)se=k.touch0[0],pe=k.touch0[1];else return;k.zoom("touch",n(g(W,se,pe),k.extent,a))}}function X(E,...z){if(this.__zooming){var k=D(this,z).event(E),H=E.changedTouches,L=H.length,J,W;for(po(E),v&&clearTimeout(v),v=setTimeout(function(){v=null},y),J=0;J<L;++J)W=H[J],k.touch0&&k.touch0[2]===W.identifier?delete k.touch0:k.touch1&&k.touch1[2]===W.identifier&&delete k.touch1;if(k.touch1&&!k.touch0&&(k.touch0=k.touch1,delete k.touch1),k.touch0)k.touch0[1]=this.__zoom.invert(k.touch0[0]);else if(k.end(),k.taps===2&&(W=rt(W,this),Math.hypot(h[0]-W[0],h[1]-W[1])<N)){var se=tt(this).on("dblclick.zoom");se&&se.apply(this,arguments)}}}return $.wheelDelta=function(E){return arguments.length?(o=typeof E=="function"?E:wn(+E),$):o},$.filter=function(E){return arguments.length?(e=typeof E=="function"?E:wn(!!E),$):e},$.touchable=function(E){return arguments.length?(i=typeof E=="function"?E:wn(!!E),$):i},$.extent=function(E){return arguments.length?(t=typeof E=="function"?E:wn([[+E[0][0],+E[0][1]],[+E[1][0],+E[1][1]]]),$):t},$.scaleExtent=function(E){return arguments.length?(r[0]=+E[0],r[1]=+E[1],$):[r[0],r[1]]},$.translateExtent=function(E){return arguments.length?(a[0][0]=+E[0][0],a[1][0]=+E[1][0],a[0][1]=+E[0][1],a[1][1]=+E[1][1],$):[[a[0][0],a[0][1]],[a[1][0],a[1][1]]]},$.constrain=function(E){return arguments.length?(n=E,$):n},$.duration=function(E){return arguments.length?(s=+E,$):s},$.interpolate=function(E){return arguments.length?(u=E,$):u},$.on=function(){var E=l.on.apply(l,arguments);return E===l?$:E},$.clickDistance=function(E){return arguments.length?(S=(E=+E)*E,$):Math.sqrt(S)},$.tapDistance=function(E){return arguments.length?(N=+E,$):N},$}var te=(e=>(e.Left="left",e.Top="top",e.Right="right",e.Bottom="bottom",e))(te||{}),Uo=(e=>(e.Partial="partial",e.Full="full",e))(Uo||{}),kt=(e=>(e.Bezier="default",e.SimpleBezier="simple-bezier",e.Straight="straight",e.Step="step",e.SmoothStep="smoothstep",e))(kt||{}),bt=(e=>(e.Strict="strict",e.Loose="loose",e))(bt||{}),Do=(e=>(e.Arrow="arrow",e.ArrowClosed="arrowclosed",e))(Do||{}),tn=(e=>(e.Free="free",e.Vertical="vertical",e.Horizontal="horizontal",e))(tn||{}),$r=(e=>(e.TopLeft="top-left",e.TopCenter="top-center",e.TopRight="top-right",e.BottomLeft="bottom-left",e.BottomCenter="bottom-center",e.BottomRight="bottom-right",e))($r||{});const Kc=["INPUT","SELECT","TEXTAREA"],qc=typeof document<"u"?document:null;function Ao(e){var t,n;const o=((n=(t=e.composedPath)==null?void 0:t.call(e))==null?void 0:n[0])||e.target,i=typeof o?.hasAttribute=="function"?o.hasAttribute("contenteditable"):!1,r=typeof o?.closest=="function"?o.closest(".nokey"):null;return Kc.includes(o?.nodeName)||i||!!r}function Jc(e){return e.ctrlKey||e.metaKey||e.shiftKey||e.altKey}function Ei(e,t,n,o){const i=t.replace("+",`
`).replace(`

`,`
+`).split(`
`).map(a=>a.trim().toLowerCase());if(i.length===1)return e.toLowerCase()===t.toLowerCase();o||n.add(e.toLowerCase());const r=i.every((a,s)=>n.has(a)&&Array.from(n.values())[s]===i[s]);return o&&n.delete(e.toLowerCase()),r}function Qc(e,t){return n=>{if(!n.code&&!n.key)return!1;const o=jc(n.code,e);return Array.isArray(e)?e.some(i=>Ei(n[o],i,t,n.type==="keyup")):Ei(n[o],e,t,n.type==="keyup")}}function jc(e,t){return t.includes(e)?"code":"key"}function nn(e,t){const n=Ee(()=>Te(t?.target)??qc),o=Mn(Te(e)===!0);let i=!1;const r=new Set;let a=u(Te(e));De(()=>Te(e),(l,c)=>{typeof c=="boolean"&&typeof l!="boolean"&&s(),a=u(l)},{immediate:!0}),er(["blur","contextmenu"],s),ii((...l)=>a(...l),l=>{var c,h;const v=Te(t?.actInsideInputWithModifier)??!0,y=Te(t?.preventDefault)??!1;if(i=Jc(l),(!i||i&&!v)&&Ao(l))return;const S=((h=(c=l.composedPath)==null?void 0:c.call(l))==null?void 0:h[0])||l.target,N=S?.nodeName==="BUTTON"||S?.nodeName==="A";!y&&(i||!N)&&l.preventDefault(),o.value=!0},{eventName:"keydown",target:n}),ii((...l)=>a(...l),l=>{const c=Te(t?.actInsideInputWithModifier)??!0;if(o.value){if((!i||i&&!c)&&Ao(l))return;i=!1,o.value=!1}},{eventName:"keyup",target:n});function s(){i=!1,r.clear(),o.value=Te(e)===!0}function u(l){return l===null?(s(),()=>!1):typeof l=="boolean"?(s(),o.value=l,()=>!1):Array.isArray(l)||typeof l=="string"?Qc(l,r):l}return o}const Tr="vue-flow__node-desc",Mr="vue-flow__edge-desc",ed="vue-flow__aria-live",Ir=["Enter"," ","Escape"],Vt={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}};function Bn(e){return{...e.computedPosition||{x:0,y:0},width:e.dimensions.width||0,height:e.dimensions.height||0}}function Vn(e,t){const n=Math.max(0,Math.min(e.x+e.width,t.x+t.width)-Math.max(e.x,t.x)),o=Math.max(0,Math.min(e.y+e.height,t.y+t.height)-Math.max(e.y,t.y));return Math.ceil(n*o)}function Zn(e){return{width:e.offsetWidth,height:e.offsetHeight}}function Dt(e,t=0,n=1){return Math.min(Math.max(e,t),n)}function Dr(e,t){return{x:Dt(e.x,t[0][0],t[1][0]),y:Dt(e.y,t[0][1],t[1][1])}}function xi(e){const t=e.getRootNode();return"elementFromPoint"in t?t:window.document}function Et(e){return e&&typeof e=="object"&&"id"in e&&"source"in e&&"target"in e}function Tt(e){return e&&typeof e=="object"&&"id"in e&&"position"in e&&!Et(e)}function Qt(e){return Tt(e)&&"computedPosition"in e}function _n(e){return!Number.isNaN(e)&&Number.isFinite(e)}function td(e){return _n(e.width)&&_n(e.height)&&_n(e.x)&&_n(e.y)}function nd(e,t,n){const o={id:e.id.toString(),type:e.type??"default",dimensions:jt({width:0,height:0}),computedPosition:jt({z:0,...e.position}),handleBounds:{source:[],target:[]},draggable:void 0,selectable:void 0,connectable:void 0,focusable:void 0,selected:!1,dragging:!1,resizing:!1,initialized:!1,isParent:!1,position:{x:0,y:0},data:He(e.data)?e.data:{},events:jt(He(e.events)?e.events:{})};return Object.assign(t??o,e,{id:e.id.toString(),parentNode:n})}function Ar(e,t,n){var o,i;const r={id:e.id.toString(),type:e.type??t?.type??"default",source:e.source.toString(),target:e.target.toString(),sourceHandle:(o=e.sourceHandle)==null?void 0:o.toString(),targetHandle:(i=e.targetHandle)==null?void 0:i.toString(),updatable:e.updatable??n?.updatable,selectable:e.selectable??n?.selectable,focusable:e.focusable??n?.focusable,data:He(e.data)?e.data:{},events:jt(He(e.events)?e.events:{}),label:e.label??"",interactionWidth:e.interactionWidth??n?.interactionWidth,...n??{}};return Object.assign(t??r,e,{id:e.id.toString()})}function Pr(e,t,n,o){const i=typeof e=="string"?e:e.id,r=new Set,a=o==="source"?"target":"source";for(const s of n)s[a]===i&&r.add(s[o]);return t.filter(s=>r.has(s.id))}function od(...e){if(e.length===3){const[r,a,s]=e;return Pr(r,a,s,"target")}const[t,n]=e,o=typeof t=="string"?t:t.id;return n.filter(r=>Et(r)&&r.source===o).map(r=>n.find(a=>Tt(a)&&a.id===r.target))}function id(...e){if(e.length===3){const[r,a,s]=e;return Pr(r,a,s,"source")}const[t,n]=e,o=typeof t=="string"?t:t.id;return n.filter(r=>Et(r)&&r.target===o).map(r=>n.find(a=>Tt(a)&&a.id===r.source))}function zr({source:e,sourceHandle:t,target:n,targetHandle:o}){return`vueflow__edge-${e}${t??""}-${n}${o??""}`}function rd(e,t){return t.some(n=>Et(n)&&n.source===e.source&&n.target===e.target&&(n.sourceHandle===e.sourceHandle||!n.sourceHandle&&!e.sourceHandle)&&(n.targetHandle===e.targetHandle||!n.targetHandle&&!e.targetHandle))}function Po({x:e,y:t},{x:n,y:o,zoom:i}){return{x:e*i+n,y:t*i+o}}function ln({x:e,y:t},{x:n,y:o,zoom:i},r=!1,a=[1,1]){const s={x:(e-n)/i,y:(t-o)/i};return r?Kn(s,a):s}function ad(e,t){return{x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x2,t.x2),y2:Math.max(e.y2,t.y2)}}function Or({x:e,y:t,width:n,height:o}){return{x:e,y:t,x2:e+n,y2:t+o}}function sd({x:e,y:t,x2:n,y2:o}){return{x:e,y:t,width:n-e,height:o-t}}function Fr(e){let t={x:Number.POSITIVE_INFINITY,y:Number.POSITIVE_INFINITY,x2:Number.NEGATIVE_INFINITY,y2:Number.NEGATIVE_INFINITY};for(let n=0;n<e.length;n++){const o=e[n];t=ad(t,Or({...o.computedPosition,...o.dimensions}))}return sd(t)}function Br(e,t,n={x:0,y:0,zoom:1},o=!1,i=!1){const r={...ln(t,n),width:t.width/n.zoom,height:t.height/n.zoom},a=[];for(const s of e){const{dimensions:u,selectable:l=!0,hidden:c=!1}=s,h=u.width??s.width??null,v=u.height??s.height??null;if(i&&!l||c)continue;const y=Vn(r,Bn(s)),M=h===null||v===null,S=o&&y>0,N=(h??0)*(v??0);(M||S||y>=N||s.dragging)&&a.push(s)}return a}function Vr(e,t){const n=new Set;if(typeof e=="string")n.add(e);else if(e.length>=1)for(const o of e)n.add(o.id);return t.filter(o=>n.has(o.source)||n.has(o.target))}function Si(e,t,n,o,i,r=.1,a={x:0,y:0}){const s=t/(e.width*(1+r)),u=n/(e.height*(1+r)),l=Math.min(s,u),c=Dt(l,o,i),h=e.x+e.width/2,v=e.y+e.height/2,y=t/2-h*c+(a.x??0),M=n/2-v*c+(a.y??0);return{x:y,y:M,zoom:c}}function ld(e,t){return{x:t.x+e.x,y:t.y+e.y,z:(e.z>t.z?e.z:t.z)+1}}function Hr(e,t){if(!e.parentNode)return!1;const n=t(e.parentNode);return n?n.selected?!0:Hr(n,t):!1}function un(e,t){return typeof e>"u"?"":typeof e=="string"?e:`${t?`${t}__`:""}${Object.keys(e).sort().map(o=>`${o}=${e[o]}`).join("&")}`}function ki(e,t,n){return e<t?Dt(Math.abs(e-t),1,t)/t:e>n?-Dt(Math.abs(e-n),1,t)/t:0}function Rr(e,t,n=15,o=40){const i=ki(e.x,o,t.width-o)*n,r=ki(e.y,o,t.height-o)*n;return[i,r]}function go(e,t){if(t){const n=e.position.x+e.dimensions.width-t.dimensions.width,o=e.position.y+e.dimensions.height-t.dimensions.height;if(n>0||o>0||e.position.x<0||e.position.y<0){let i={};if(typeof t.style=="function"?i={...t.style(t)}:t.style&&(i={...t.style}),i.width=i.width??`${t.dimensions.width}px`,i.height=i.height??`${t.dimensions.height}px`,n>0)if(typeof i.width=="string"){const r=Number(i.width.replace("px",""));i.width=`${r+n}px`}else i.width+=n;if(o>0)if(typeof i.height=="string"){const r=Number(i.height.replace("px",""));i.height=`${r+o}px`}else i.height+=o;if(e.position.x<0){const r=Math.abs(e.position.x);if(t.position.x=t.position.x-r,typeof i.width=="string"){const a=Number(i.width.replace("px",""));i.width=`${a+r}px`}else i.width+=r;e.position.x=0}if(e.position.y<0){const r=Math.abs(e.position.y);if(t.position.y=t.position.y-r,typeof i.height=="string"){const a=Number(i.height.replace("px",""));i.height=`${a+r}px`}else i.height+=r;e.position.y=0}t.dimensions.width=Number(i.width.toString().replace("px","")),t.dimensions.height=Number(i.height.toString().replace("px","")),typeof t.style=="function"?t.style=r=>{const a=t.style;return{...a(r),...i}}:t.style={...t.style,...i}}}}function Ni(e,t){var n,o;const i=e.filter(a=>a.type==="add"||a.type==="remove");for(const a of i)if(a.type==="add")t.findIndex(u=>u.id===a.item.id)===-1&&t.push(a.item);else if(a.type==="remove"){const s=t.findIndex(u=>u.id===a.id);s!==-1&&t.splice(s,1)}const r=t.map(a=>a.id);for(const a of t)for(const s of e)if(s.id===a.id)switch(s.type){case"select":a.selected=s.selected;break;case"position":if(Qt(a)&&(typeof s.position<"u"&&(a.position=s.position),typeof s.dragging<"u"&&(a.dragging=s.dragging),a.expandParent&&a.parentNode)){const u=t[r.indexOf(a.parentNode)];u&&Qt(u)&&go(a,u)}break;case"dimensions":if(Qt(a)&&(typeof s.dimensions<"u"&&(a.dimensions=s.dimensions),typeof s.updateStyle<"u"&&s.updateStyle&&(a.style={...a.style||{},width:`${(n=s.dimensions)==null?void 0:n.width}px`,height:`${(o=s.dimensions)==null?void 0:o.height}px`}),typeof s.resizing<"u"&&(a.resizing=s.resizing),a.expandParent&&a.parentNode)){const u=t[r.indexOf(a.parentNode)];u&&Qt(u)&&(!!u.dimensions.width&&!!u.dimensions.height?go(a,u):Le(()=>{go(a,u)}))}break}return t}function wt(e,t){return{id:e,type:"select",selected:t}}function Ci(e){return{item:e,type:"add"}}function $i(e){return{id:e,type:"remove"}}function Ti(e,t,n,o,i){return{id:e,source:t,target:n,sourceHandle:o||null,targetHandle:i||null,type:"remove"}}function _t(e,t=new Set,n=!1){const o=[];for(const[i,r]of e){const a=t.has(i);!(r.selected===void 0&&!a)&&r.selected!==a&&(n&&(r.selected=a),o.push(wt(r.id,a)))}return o}function ee(e){const t=new Set;let n=!1;const o=()=>t.size>0;e&&(n=!0,t.add(e));const i=s=>{t.delete(s)};return{on:s=>{e&&n&&t.delete(e),t.add(s);const u=()=>{i(s),e&&n&&t.add(e)};return Yn(u),{off:u}},off:i,trigger:s=>Promise.all(Array.from(t).map(u=>u(s))),hasListeners:o,fns:t}}function Mi(e,t,n){let o=e;do{if(o&&o.matches(t))return!0;if(o===n)return!1;o=o.parentElement}while(o);return!1}function ud(e,t,n,o,i){var r,a;const s=[];for(const u of e)(u.selected||u.id===i)&&(!u.parentNode||!Hr(u,o))&&(u.draggable||t&&typeof u.draggable>"u")&&s.push(jt({id:u.id,position:u.position||{x:0,y:0},distance:{x:n.x-((r=u.computedPosition)==null?void 0:r.x)||0,y:n.y-((a=u.computedPosition)==null?void 0:a.y)||0},from:u.computedPosition,extent:u.extent,parentNode:u.parentNode,dimensions:u.dimensions,expandParent:u.expandParent}));return s}function mo({id:e,dragItems:t,findNode:n}){const o=[];for(const i of t){const r=n(i.id);r&&o.push(r)}return[e?o.find(i=>i.id===e):o[0],o]}function Lr(e){if(Array.isArray(e))switch(e.length){case 1:return[e[0],e[0],e[0],e[0]];case 2:return[e[0],e[1],e[0],e[1]];case 3:return[e[0],e[1],e[2],e[1]];case 4:return e;default:return[0,0,0,0]}return[e,e,e,e]}function cd(e,t,n){const[o,i,r,a]=typeof e!="string"?Lr(e.padding):[0,0,0,0];return n&&typeof n.computedPosition.x<"u"&&typeof n.computedPosition.y<"u"&&typeof n.dimensions.width<"u"&&typeof n.dimensions.height<"u"?[[n.computedPosition.x+a,n.computedPosition.y+o],[n.computedPosition.x+n.dimensions.width-i,n.computedPosition.y+n.dimensions.height-r]]:!1}function dd(e,t,n,o){let i=e.extent||n;if((i==="parent"||!Array.isArray(i)&&i?.range==="parent")&&!e.expandParent)if(e.parentNode&&o&&e.dimensions.width&&e.dimensions.height){const r=cd(i,e,o);r&&(i=r)}else t(new Ye(Re.NODE_EXTENT_INVALID,e.id)),i=n;else if(Array.isArray(i)){const r=o?.computedPosition.x||0,a=o?.computedPosition.y||0;i=[[i[0][0]+r,i[0][1]+a],[i[1][0]+r,i[1][1]+a]]}else if(i!=="parent"&&i?.range&&Array.isArray(i.range)){const[r,a,s,u]=Lr(i.padding),l=o?.computedPosition.x||0,c=o?.computedPosition.y||0;i=[[i.range[0][0]+l+u,i.range[0][1]+c+r],[i.range[1][0]+l-a,i.range[1][1]+c-s]]}return i==="parent"?[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]]:i}function fd({width:e,height:t},n){return[n[0],[n[1][0]-(e||0),n[1][1]-(t||0)]]}function Zo(e,t,n,o,i){const r=fd(e.dimensions,dd(e,n,o,i)),a=Dr(t,r);return{position:{x:a.x-(i?.computedPosition.x||0),y:a.y-(i?.computedPosition.y||0)},computedPosition:a}}function Gt(e,t,n=te.Left,o=!1){const i=(t?.x??0)+e.computedPosition.x,r=(t?.y??0)+e.computedPosition.y,{width:a,height:s}=t??md(e);if(o)return{x:i+a/2,y:r+s/2};switch(t?.position??n){case te.Top:return{x:i+a/2,y:r};case te.Right:return{x:i+a,y:r+s/2};case te.Bottom:return{x:i+a/2,y:r+s};case te.Left:return{x:i,y:r+s/2}}}function Ii(e,t){return e&&(t?e.find(n=>n.id===t):e[0])||null}function hd({sourcePos:e,targetPos:t,sourceWidth:n,sourceHeight:o,targetWidth:i,targetHeight:r,width:a,height:s,viewport:u}){const l={x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x+n,t.x+i),y2:Math.max(e.y+o,t.y+r)};l.x===l.x2&&(l.x2+=1),l.y===l.y2&&(l.y2+=1);const c=Or({x:(0-u.x)/u.zoom,y:(0-u.y)/u.zoom,width:a/u.zoom,height:s/u.zoom}),h=Math.max(0,Math.min(c.x2,l.x2)-Math.max(c.x,l.x)),v=Math.max(0,Math.min(c.y2,l.y2)-Math.max(c.y,l.y));return Math.ceil(h*v)>0}function pd(e,t,n=!1){const o=typeof e.zIndex=="number";let i=o?e.zIndex:0;const r=t(e.source),a=t(e.target);return!r||!a?0:(n&&(i=o?e.zIndex:Math.max(r.computedPosition.z||0,a.computedPosition.z||0)),i)}var Re=(e=>(e.MISSING_STYLES="MISSING_STYLES",e.MISSING_VIEWPORT_DIMENSIONS="MISSING_VIEWPORT_DIMENSIONS",e.NODE_INVALID="NODE_INVALID",e.NODE_NOT_FOUND="NODE_NOT_FOUND",e.NODE_MISSING_PARENT="NODE_MISSING_PARENT",e.NODE_TYPE_MISSING="NODE_TYPE_MISSING",e.NODE_EXTENT_INVALID="NODE_EXTENT_INVALID",e.EDGE_INVALID="EDGE_INVALID",e.EDGE_NOT_FOUND="EDGE_NOT_FOUND",e.EDGE_SOURCE_MISSING="EDGE_SOURCE_MISSING",e.EDGE_TARGET_MISSING="EDGE_TARGET_MISSING",e.EDGE_TYPE_MISSING="EDGE_TYPE_MISSING",e.EDGE_SOURCE_TARGET_SAME="EDGE_SOURCE_TARGET_SAME",e.EDGE_SOURCE_TARGET_MISSING="EDGE_SOURCE_TARGET_MISSING",e.EDGE_ORPHANED="EDGE_ORPHANED",e.USEVUEFLOW_OPTIONS="USEVUEFLOW_OPTIONS",e))(Re||{});const Di={MISSING_STYLES:()=>"It seems that you haven't loaded the necessary styles. Please import '@vue-flow/core/dist/style.css' to ensure that the graph is rendered correctly",MISSING_VIEWPORT_DIMENSIONS:()=>"The Vue Flow parent container needs a width and a height to render the graph",NODE_INVALID:e=>`Node is invalid
Node: ${e}`,NODE_NOT_FOUND:e=>`Node not found
Node: ${e}`,NODE_MISSING_PARENT:(e,t)=>`Node is missing a parent
Node: ${e}
Parent: ${t}`,NODE_TYPE_MISSING:e=>`Node type is missing
Type: ${e}`,NODE_EXTENT_INVALID:e=>`Only child nodes can use a parent extent
Node: ${e}`,EDGE_INVALID:e=>`An edge needs a source and a target
Edge: ${e}`,EDGE_SOURCE_MISSING:(e,t)=>`Edge source is missing
Edge: ${e} 
Source: ${t}`,EDGE_TARGET_MISSING:(e,t)=>`Edge target is missing
Edge: ${e} 
Target: ${t}`,EDGE_TYPE_MISSING:e=>`Edge type is missing
Type: ${e}`,EDGE_SOURCE_TARGET_SAME:(e,t,n)=>`Edge source and target are the same
Edge: ${e} 
Source: ${t} 
Target: ${n}`,EDGE_SOURCE_TARGET_MISSING:(e,t,n)=>`Edge source or target is missing
Edge: ${e} 
Source: ${t} 
Target: ${n}`,EDGE_ORPHANED:e=>`Edge was orphaned (suddenly missing source or target) and has been removed
Edge: ${e}`,EDGE_NOT_FOUND:e=>`Edge not found
Edge: ${e}`,USEVUEFLOW_OPTIONS:()=>"The options parameter is deprecated and will be removed in the next major version. Please use the id parameter instead"};class Ye extends Error{constructor(t,...n){var o;super((o=Di[t])==null?void 0:o.call(Di,...n)),this.name="VueFlowError",this.code=t,this.args=n}}function Ko(e){return"clientX"in e}function gd(e){return"sourceEvent"in e}function st(e,t){const n=Ko(e);let o,i;return n?(o=e.clientX,i=e.clientY):"touches"in e&&e.touches.length>0?(o=e.touches[0].clientX,i=e.touches[0].clientY):"changedTouches"in e&&e.changedTouches.length>0?(o=e.changedTouches[0].clientX,i=e.changedTouches[0].clientY):(o=0,i=0),{x:o-(t?.left??0),y:i-(t?.top??0)}}const Hn=()=>{var e;return typeof navigator<"u"&&((e=navigator?.userAgent)==null?void 0:e.indexOf("Mac"))>=0};function md(e){var t,n;return{width:((t=e.dimensions)==null?void 0:t.width)??e.width??0,height:((n=e.dimensions)==null?void 0:n.height)??e.height??0}}function Kn(e,t=[1,1]){return{x:t[0]*Math.round(e.x/t[0]),y:t[1]*Math.round(e.y/t[1])}}const vd=()=>!0;function vo(e){e?.classList.remove("valid","connecting","vue-flow__handle-valid","vue-flow__handle-connecting")}function yd(e,t,n){const o=[],i={x:e.x-n,y:e.y-n,width:n*2,height:n*2};for(const r of t.values())Vn(i,Bn(r))>0&&o.push(r);return o}const wd=250;function _d(e,t,n,o){var i,r;let a=[],s=Number.POSITIVE_INFINITY;const u=yd(e,n,t+wd);for(const l of u){const c=[...((i=l.handleBounds)==null?void 0:i.source)??[],...((r=l.handleBounds)==null?void 0:r.target)??[]];for(const h of c){if(o.nodeId===h.nodeId&&o.type===h.type&&o.id===h.id)continue;const{x:v,y}=Gt(l,h,h.position,!0),M=Math.sqrt((v-e.x)**2+(y-e.y)**2);M>t||(M<s?(a=[{...h,x:v,y}],s=M):M===s&&a.push({...h,x:v,y}))}}if(!a.length)return null;if(a.length>1){const l=o.type==="source"?"target":"source";return a.find(c=>c.type===l)??a[0]}return a[0]}function Ai(e,{handle:t,connectionMode:n,fromNodeId:o,fromHandleId:i,fromType:r,doc:a,lib:s,flowId:u,isValidConnection:l=vd},c,h,v){const y=r==="target",M=t?a.querySelector(`.${s}-flow__handle[data-id="${u}-${t?.nodeId}-${t?.id}-${t?.type}"]`):null,{x:S,y:N}=st(e),$=a.elementFromPoint(S,N),I=$?.classList.contains(`${s}-flow__handle`)?$:M,g={handleDomNode:I,isValid:!1,connection:null,toHandle:null};if(I){const T=Yr(void 0,I),Y=I.getAttribute("data-nodeid"),D=I.getAttribute("data-handleid"),A=I.classList.contains("connectable"),O=I.classList.contains("connectableend");if(!Y||!T)return g;const R={source:y?Y:o,sourceHandle:y?D:i,target:y?o:Y,targetHandle:y?i:D};g.connection=R;const F=A&&O&&(n===bt.Strict?y&&T==="source"||!y&&T==="target":Y!==o||D!==i);g.isValid=F&&l(R,{nodes:h,edges:c,sourceNode:v(o),targetNode:v(Y)}),g.toHandle=t}return g}function Yr(e,t){return e||(t?.classList.contains("target")?"target":t?.classList.contains("source")?"source":null)}function bd(e,t){let n=null;return t?n="valid":e&&!t&&(n="invalid"),n}function Ed(e,t){let n=null;return t?n=!0:e&&!t&&(n=!1),n}function xd(e,t,n,o,i,r=!1){var a,s,u;const l=o.get(e);if(!l)return null;const c=i===bt.Strict?(a=l.handleBounds)==null?void 0:a[t]:[...((s=l.handleBounds)==null?void 0:s.source)??[],...((u=l.handleBounds)==null?void 0:u.target)??[]],h=(n?c?.find(v=>v.id===n):c?.[0])??null;return h&&r?{...h,...Gt(l,h,h.position,!0)}:h}const zo={[te.Left]:te.Right,[te.Right]:te.Left,[te.Top]:te.Bottom,[te.Bottom]:te.Top},Sd=["production","prod"];function qn(e,...t){Gr()&&console.warn(`[Vue Flow]: ${e}`,...t)}function Gr(){return!Sd.includes("production")}function Pi(e,t,n,o,i){const r=t.querySelectorAll(`.vue-flow__handle.${e}`);return r?.length?Array.from(r).map(a=>{const s=a.getBoundingClientRect();return{id:a.getAttribute("data-handleid"),type:e,nodeId:i,position:a.getAttribute("data-handlepos"),x:(s.left-n.left)/o,y:(s.top-n.top)/o,...Zn(a)}}):null}function Oo(e,t,n,o,i,r=!1,a){i.value=!1,e.selected?(r||e.selected&&t)&&(o([e]),Le(()=>{a.blur()})):n([e])}function He(e){return typeof V(e)<"u"}function kd(e,t,n,o){if(!e||!e.source||!e.target)return n(new Ye(Re.EDGE_INVALID,e?.id??"[ID UNKNOWN]")),!1;let i;return Et(e)?i=e:i={...e,id:zr(e)},i=Ar(i,void 0,o),rd(i,t)?!1:i}function Nd(e,t,n,o,i){if(!t.source||!t.target)return i(new Ye(Re.EDGE_INVALID,e.id)),!1;if(!n)return i(new Ye(Re.EDGE_NOT_FOUND,e.id)),!1;const{id:r,...a}=e;return{...a,id:o?zr(t):r,source:t.source,target:t.target,sourceHandle:t.sourceHandle,targetHandle:t.targetHandle}}function zi(e,t,n){const o={},i=[];for(let r=0;r<e.length;++r){const a=e[r];if(!Tt(a)){n(new Ye(Re.NODE_INVALID,a?.id)||`[ID UNKNOWN|INDEX ${r}]`);continue}const s=nd(a,t(a.id),a.parentNode);a.parentNode&&(o[a.parentNode]=!0),i[r]=s}for(const r of i){const a=t(r.parentNode)||i.find(s=>s.id===r.parentNode);r.parentNode&&!a&&n(new Ye(Re.NODE_MISSING_PARENT,r.id,r.parentNode)),(r.parentNode||o[r.id])&&(o[r.id]&&(r.isParent=!0),a&&(a.isParent=!0))}return i}function Oi(e,t,n,o,i,r){let a=i;const s=o.get(a)||new Map;o.set(a,s.set(n,t)),a=`${i}-${e}`;const u=o.get(a)||new Map;if(o.set(a,u.set(n,t)),r){a=`${i}-${e}-${r}`;const l=o.get(a)||new Map;o.set(a,l.set(n,t))}}function yo(e,t,n){e.clear();for(const o of n){const{source:i,target:r,sourceHandle:a=null,targetHandle:s=null}=o,u={edgeId:o.id,source:i,target:r,sourceHandle:a,targetHandle:s},l=`${i}-${a}--${r}-${s}`,c=`${r}-${s}--${i}-${a}`;Oi("source",u,c,e,i,a),Oi("target",u,l,e,r,s)}}function Fi(e,t){if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0}function wo(e,t,n,o,i,r,a,s){const u=[];for(const l of e){const c=Et(l)?l:kd(l,s,i,r);if(!c)continue;const h=n(c.source),v=n(c.target);if(!h||!v){i(new Ye(Re.EDGE_SOURCE_TARGET_MISSING,c.id,c.source,c.target));continue}if(!h){i(new Ye(Re.EDGE_SOURCE_MISSING,c.id,c.source));continue}if(!v){i(new Ye(Re.EDGE_TARGET_MISSING,c.id,c.target));continue}if(t&&!t(c,{edges:s,nodes:a,sourceNode:h,targetNode:v})){i(new Ye(Re.EDGE_INVALID,c.id));continue}const y=o(c.id);u.push({...Ar(c,y,r),sourceNode:h,targetNode:v})}return u}const Bi=Symbol("vueFlow"),Xr=Symbol("nodeId"),Wr=Symbol("nodeRef"),Cd=Symbol("edgeId"),$d=Symbol("edgeRef"),Jn=Symbol("slots");function Ur(e){const{vueFlowRef:t,snapToGrid:n,snapGrid:o,noDragClassName:i,nodes:r,nodeExtent:a,nodeDragThreshold:s,viewport:u,autoPanOnNodeDrag:l,autoPanSpeed:c,nodesDraggable:h,panBy:v,findNode:y,multiSelectionActive:M,nodesSelectionActive:S,selectNodesOnDrag:N,removeSelectedElements:$,addSelectedNodes:I,updateNodePositions:g,emits:T}=Oe(),{onStart:Y,onDrag:D,onStop:A,onClick:O,el:R,disabled:G,id:F,selectable:x,dragHandle:X}=e,E=Mn(!1);let z=[],k,H=null,L={x:void 0,y:void 0},J={x:0,y:0},W=null,se=!1,pe=0,fe=!1;const Q=Id(),ae=({x:he,y:xe})=>{L={x:he,y:xe};let de=!1;if(z=z.map(m=>{const p={x:he-m.distance.x,y:xe-m.distance.y},{computedPosition:b}=Zo(m,n.value?Kn(p,o.value):p,T.error,a.value,m.parentNode?y(m.parentNode):void 0);return de=de||m.position.x!==b.x||m.position.y!==b.y,m.position=b,m}),!!de&&(g(z,!0,!0),E.value=!0,W)){const[m,p]=mo({id:F,dragItems:z,findNode:y});D({event:W,node:m,nodes:p})}},ce=()=>{if(!H)return;const[he,xe]=Rr(J,H,c.value);if(he!==0||xe!==0){const de={x:(L.x??0)-he/u.value.zoom,y:(L.y??0)-xe/u.value.zoom};v({x:he,y:xe})&&ae(de)}pe=requestAnimationFrame(ce)},Se=(he,xe)=>{se=!0;const de=y(F);!N.value&&!M.value&&de&&(de.selected||$()),de&&Te(x)&&N.value&&Oo(de,M.value,I,$,S,!1,xe);const m=Q(he.sourceEvent);if(L=m,z=ud(r.value,h.value,m,y,F),z.length){const[p,b]=mo({id:F,dragItems:z,findNode:y});Y({event:he.sourceEvent,node:p,nodes:b})}},Ne=(he,xe)=>{var de;he.sourceEvent.type==="touchmove"&&he.sourceEvent.touches.length>1||(s.value===0&&Se(he,xe),L=Q(he.sourceEvent),H=((de=t.value)==null?void 0:de.getBoundingClientRect())||null,J=st(he.sourceEvent,H))},ve=(he,xe)=>{const de=Q(he.sourceEvent);if(!fe&&se&&l.value&&(fe=!0,ce()),!se){const m=de.xSnapped-(L.x??0),p=de.ySnapped-(L.y??0);Math.sqrt(m*m+p*p)>s.value&&Se(he,xe)}(L.x!==de.xSnapped||L.y!==de.ySnapped)&&z.length&&se&&(W=he.sourceEvent,J=st(he.sourceEvent,H),ae(de))},$e=he=>{let xe=!1;if(!se&&!E.value&&!M.value){const de=he.sourceEvent,m=Q(de),p=m.xSnapped-(L.x??0),b=m.ySnapped-(L.y??0),w=Math.sqrt(p*p+b*b);w!==0&&w<=s.value&&(O?.(de),xe=!0)}if(z.length&&!xe){g(z,!1,!1);const[de,m]=mo({id:F,dragItems:z,findNode:y});A({event:he.sourceEvent,node:de,nodes:m})}z=[],E.value=!1,fe=!1,se=!1,L={x:void 0,y:void 0},cancelAnimationFrame(pe)};return De([()=>Te(G),R],([he,xe],de,m)=>{if(xe){const p=tt(xe);he||(k=tu().on("start",b=>Ne(b,xe)).on("drag",b=>ve(b,xe)).on("end",b=>$e(b)).filter(b=>{const w=b.target,P=Te(X);return!b.button&&(!i.value||!Mi(w,`.${i.value}`,xe)&&(!P||Mi(w,P,xe)))}),p.call(k)),m(()=>{p.on(".drag",null),k&&(k.on("start",null),k.on("drag",null),k.on("end",null))})}}),E}function Td(){return{doubleClick:ee(),click:ee(),mouseEnter:ee(),mouseMove:ee(),mouseLeave:ee(),contextMenu:ee(),updateStart:ee(),update:ee(),updateEnd:ee()}}function Md(e,t){const n=Td();return n.doubleClick.on(o=>{var i,r;t.edgeDoubleClick(o),(r=(i=e.events)==null?void 0:i.doubleClick)==null||r.call(i,o)}),n.click.on(o=>{var i,r;t.edgeClick(o),(r=(i=e.events)==null?void 0:i.click)==null||r.call(i,o)}),n.mouseEnter.on(o=>{var i,r;t.edgeMouseEnter(o),(r=(i=e.events)==null?void 0:i.mouseEnter)==null||r.call(i,o)}),n.mouseMove.on(o=>{var i,r;t.edgeMouseMove(o),(r=(i=e.events)==null?void 0:i.mouseMove)==null||r.call(i,o)}),n.mouseLeave.on(o=>{var i,r;t.edgeMouseLeave(o),(r=(i=e.events)==null?void 0:i.mouseLeave)==null||r.call(i,o)}),n.contextMenu.on(o=>{var i,r;t.edgeContextMenu(o),(r=(i=e.events)==null?void 0:i.contextMenu)==null||r.call(i,o)}),n.updateStart.on(o=>{var i,r;t.edgeUpdateStart(o),(r=(i=e.events)==null?void 0:i.updateStart)==null||r.call(i,o)}),n.update.on(o=>{var i,r;t.edgeUpdate(o),(r=(i=e.events)==null?void 0:i.update)==null||r.call(i,o)}),n.updateEnd.on(o=>{var i,r;t.edgeUpdateEnd(o),(r=(i=e.events)==null?void 0:i.updateEnd)==null||r.call(i,o)}),Object.entries(n).reduce((o,[i,r])=>(o.emit[i]=r.trigger,o.on[i]=r.on,o),{emit:{},on:{}})}function Id(){const{viewport:e,snapGrid:t,snapToGrid:n,vueFlowRef:o}=Oe();return i=>{var r;const a=((r=o.value)==null?void 0:r.getBoundingClientRect())??{left:0,top:0},s=gd(i)?i.sourceEvent:i,{x:u,y:l}=st(s,a),c=ln({x:u,y:l},e.value),{x:h,y:v}=n.value?Kn(c,t.value):c;return{xSnapped:h,ySnapped:v,...c}}}function bn(){return!0}function Zr({handleId:e,nodeId:t,type:n,isValidConnection:o,edgeUpdaterType:i,onEdgeUpdate:r,onEdgeUpdateEnd:a}){const{id:s,vueFlowRef:u,connectionMode:l,connectionRadius:c,connectOnClick:h,connectionClickStartHandle:v,nodesConnectable:y,autoPanOnConnect:M,autoPanSpeed:S,findNode:N,panBy:$,startConnection:I,updateConnection:g,endConnection:T,emits:Y,viewport:D,edges:A,nodes:O,isValidConnection:R,nodeLookup:G}=Oe();let F=null,x=!1,X=null;function E(k){var H;const L=Te(n)==="target",J=Ko(k),W=xi(k.target);if(J&&k.button===0||!J){let se=function(ye){m=st(ye,he),ae=_d(ln(m,D.value,!1,[1,1]),c.value,G.value,w),p||(b(),p=!0);const ke=Ai(ye,{handle:ae,connectionMode:l.value,fromNodeId:Te(t),fromHandleId:Te(e),fromType:L?"target":"source",isValidConnection:Q,doc:W,lib:"vue",flowId:s,nodeLookup:G.value},A.value,O.value,N);X=ke.handleDomNode,F=ke.connection,x=Ed(!!ae,ke.isValid);const Ce={...q,isValid:x,to:ke.toHandle&&x?Po({x:ke.toHandle.x,y:ke.toHandle.y},D.value):m,toHandle:ke.toHandle,toPosition:x&&ke.toHandle?ke.toHandle.position:zo[w.position],toNode:ke.toHandle?G.value.get(ke.toHandle.nodeId):null};if(!(x&&ae&&q?.toHandle&&Ce.toHandle&&q.toHandle.type===Ce.toHandle.type&&q.toHandle.nodeId===Ce.toHandle.nodeId&&q.toHandle.id===Ce.toHandle.id&&q.to.x===Ce.to.x&&q.to.y===Ce.to.y)){if(g(ae&&x?Po({x:ae.x,y:ae.y},D.value):m,ke.toHandle,bd(!!ae,x)),q=Ce,!ae&&!x&&!X)return vo(de);F&&F.source!==F.target&&X&&(vo(de),de=X,X.classList.add("connecting","vue-flow__handle-connecting"),X.classList.toggle("valid",!!x),X.classList.toggle("vue-flow__handle-valid",!!x))}},pe=function(ye){(ae||X)&&F&&x&&(r?r(ye,F):Y.connect(F)),Y.connectEnd(ye),i&&a?.(ye),vo(de),cancelAnimationFrame(ce),T(ye),p=!1,x=!1,F=null,X=null,W.removeEventListener("mousemove",se),W.removeEventListener("mouseup",pe),W.removeEventListener("touchmove",se),W.removeEventListener("touchend",pe)};const fe=N(Te(t));let Q=Te(o)||R.value||bn;!Q&&fe&&(Q=(L?fe.isValidSourcePos:fe.isValidTargetPos)||bn);let ae,ce=0;const{x:Se,y:Ne}=st(k),ve=W?.elementFromPoint(Se,Ne),$e=Yr(Te(i),ve),he=(H=u.value)==null?void 0:H.getBoundingClientRect();if(!he||!$e)return;const xe=xd(Te(t),$e,Te(e),G.value,l.value);if(!xe)return;let de,m=st(k,he),p=!1;const b=()=>{if(!M.value)return;const[ye,ke]=Rr(m,he,S.value);$({x:ye,y:ke}),ce=requestAnimationFrame(b)},w={...xe,nodeId:Te(t),type:$e,position:xe.position},P=G.value.get(Te(t)),oe={inProgress:!0,isValid:null,from:Gt(P,w,te.Left,!0),fromHandle:w,fromPosition:w.position,fromNode:P,to:m,toHandle:null,toPosition:zo[w.position],toNode:null};I({nodeId:Te(t),id:Te(e),type:$e,position:ve?.getAttribute("data-handlepos")||te.Top,...m},{x:Se-he.left,y:Ne-he.top}),Y.connectStart({event:k,nodeId:Te(t),handleId:Te(e),handleType:$e});let q=oe;W.addEventListener("mousemove",se),W.addEventListener("mouseup",pe),W.addEventListener("touchmove",se),W.addEventListener("touchend",pe)}}function z(k){var H,L;if(!h.value)return;const J=Te(n)==="target";if(!v.value){Y.clickConnectStart({event:k,nodeId:Te(t),handleId:Te(e)}),I({nodeId:Te(t),type:Te(n),id:Te(e),position:te.Top,...st(k)},void 0,!0);return}let W=Te(o)||R.value||bn;const se=N(Te(t));if(!W&&se&&(W=(J?se.isValidSourcePos:se.isValidTargetPos)||bn),se&&(typeof se.connectable>"u"?y.value:se.connectable)===!1)return;const pe=xi(k.target),fe=Ai(k,{handle:{nodeId:Te(t),id:Te(e),type:Te(n),position:te.Top,...st(k)},connectionMode:l.value,fromNodeId:v.value.nodeId,fromHandleId:v.value.id??null,fromType:v.value.type,isValidConnection:W,doc:pe,lib:"vue",flowId:s,nodeLookup:G.value},A.value,O.value,N),Q=((H=fe.connection)==null?void 0:H.source)===((L=fe.connection)==null?void 0:L.target);fe.isValid&&fe.connection&&!Q&&Y.connect(fe.connection),Y.clickConnectEnd(k),T(k,!0)}return{handlePointerDown:E,handleClick:z}}function Dd(){return Xt(Xr,"")}function Kr(e){const t=e??Dd()??"",n=Xt(Wr,me(null)),{findNode:o,edges:i,emits:r}=Oe(),a=o(t);return a||r.error(new Ye(Re.NODE_NOT_FOUND,t)),{id:t,nodeEl:n,node:a,parentNode:Ee(()=>o(a.parentNode)),connectedEdges:Ee(()=>Vr([a],i.value))}}function Ad(){return{doubleClick:ee(),click:ee(),mouseEnter:ee(),mouseMove:ee(),mouseLeave:ee(),contextMenu:ee(),dragStart:ee(),drag:ee(),dragStop:ee()}}function Pd(e,t){const n=Ad();return n.doubleClick.on(o=>{var i,r;t.nodeDoubleClick(o),(r=(i=e.events)==null?void 0:i.doubleClick)==null||r.call(i,o)}),n.click.on(o=>{var i,r;t.nodeClick(o),(r=(i=e.events)==null?void 0:i.click)==null||r.call(i,o)}),n.mouseEnter.on(o=>{var i,r;t.nodeMouseEnter(o),(r=(i=e.events)==null?void 0:i.mouseEnter)==null||r.call(i,o)}),n.mouseMove.on(o=>{var i,r;t.nodeMouseMove(o),(r=(i=e.events)==null?void 0:i.mouseMove)==null||r.call(i,o)}),n.mouseLeave.on(o=>{var i,r;t.nodeMouseLeave(o),(r=(i=e.events)==null?void 0:i.mouseLeave)==null||r.call(i,o)}),n.contextMenu.on(o=>{var i,r;t.nodeContextMenu(o),(r=(i=e.events)==null?void 0:i.contextMenu)==null||r.call(i,o)}),n.dragStart.on(o=>{var i,r;t.nodeDragStart(o),(r=(i=e.events)==null?void 0:i.dragStart)==null||r.call(i,o)}),n.drag.on(o=>{var i,r;t.nodeDrag(o),(r=(i=e.events)==null?void 0:i.drag)==null||r.call(i,o)}),n.dragStop.on(o=>{var i,r;t.nodeDragStop(o),(r=(i=e.events)==null?void 0:i.dragStop)==null||r.call(i,o)}),Object.entries(n).reduce((o,[i,r])=>(o.emit[i]=r.trigger,o.on[i]=r.on,o),{emit:{},on:{}})}function qr(){const{getSelectedNodes:e,nodeExtent:t,updateNodePositions:n,findNode:o,snapGrid:i,snapToGrid:r,nodesDraggable:a,emits:s}=Oe();return(u,l=!1)=>{const c=r.value?i.value[0]:5,h=r.value?i.value[1]:5,v=l?4:1,y=u.x*c*v,M=u.y*h*v,S=[];for(const N of e.value)if(N.draggable||a&&typeof N.draggable>"u"){const $={x:N.computedPosition.x+y,y:N.computedPosition.y+M},{computedPosition:I}=Zo(N,$,s.error,t.value,N.parentNode?o(N.parentNode):void 0);S.push({id:N.id,position:I,from:N.position,distance:{x:u.x,y:u.y},dimensions:N.dimensions})}n(S,!0,!1)}}const _o=.1,zd=e=>((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2;function yt(){return qn("Viewport not initialized yet."),Promise.resolve(!1)}const Od={zoomIn:yt,zoomOut:yt,zoomTo:yt,fitView:yt,setCenter:yt,fitBounds:yt,project:e=>e,screenToFlowCoordinate:e=>e,flowToScreenCoordinate:e=>e,setViewport:yt,setTransform:yt,getViewport:()=>({x:0,y:0,zoom:1}),getTransform:()=>({x:0,y:0,zoom:1}),viewportInitialized:!1};function Fd(e){function t(o,i){return new Promise(r=>{e.d3Selection&&e.d3Zoom?e.d3Zoom.interpolate(i?.interpolate==="linear"?en:Nn).scaleBy(bo(e.d3Selection,i?.duration,i?.ease,()=>{r(!0)}),o):r(!1)})}function n(o,i,r,a){return new Promise(s=>{var u;const{x:l,y:c}=Dr({x:-o,y:-i},e.translateExtent),h=Yt.translate(-l,-c).scale(r);e.d3Selection&&e.d3Zoom?(u=e.d3Zoom)==null||u.interpolate(a?.interpolate==="linear"?en:Nn).transform(bo(e.d3Selection,a?.duration,a?.ease,()=>{s(!0)}),h):s(!1)})}return Ee(()=>e.d3Zoom&&e.d3Selection&&e.dimensions.width&&e.dimensions.height?{viewportInitialized:!0,zoomIn:i=>t(1.2,i),zoomOut:i=>t(1/1.2,i),zoomTo:(i,r)=>new Promise(a=>{e.d3Selection&&e.d3Zoom?e.d3Zoom.interpolate(r?.interpolate==="linear"?en:Nn).scaleTo(bo(e.d3Selection,r?.duration,r?.ease,()=>{a(!0)}),i):a(!1)}),setViewport:(i,r)=>n(i.x,i.y,i.zoom,r),setTransform:(i,r)=>n(i.x,i.y,i.zoom,r),getViewport:()=>({x:e.viewport.x,y:e.viewport.y,zoom:e.viewport.zoom}),getTransform:()=>({x:e.viewport.x,y:e.viewport.y,zoom:e.viewport.zoom}),fitView:(i={padding:_o,includeHiddenNodes:!1,duration:0})=>{var r,a;const s=[];for(const v of e.nodes)v.dimensions.width&&v.dimensions.height&&(i?.includeHiddenNodes||!v.hidden)&&(!((r=i.nodes)!=null&&r.length)||(a=i.nodes)!=null&&a.length&&i.nodes.includes(v.id))&&s.push(v);if(!s.length)return Promise.resolve(!1);const u=Fr(s),{x:l,y:c,zoom:h}=Si(u,e.dimensions.width,e.dimensions.height,i.minZoom??e.minZoom,i.maxZoom??e.maxZoom,i.padding??_o,i.offset);return n(l,c,h,i)},setCenter:(i,r,a)=>{const s=typeof a?.zoom<"u"?a.zoom:e.maxZoom,u=e.dimensions.width/2-i*s,l=e.dimensions.height/2-r*s;return n(u,l,s,a)},fitBounds:(i,r={padding:_o})=>{const{x:a,y:s,zoom:u}=Si(i,e.dimensions.width,e.dimensions.height,e.minZoom,e.maxZoom,r.padding);return n(a,s,u,r)},project:i=>ln(i,e.viewport,e.snapToGrid,e.snapGrid),screenToFlowCoordinate:i=>{if(e.vueFlowRef){const{x:r,y:a}=e.vueFlowRef.getBoundingClientRect(),s={x:i.x-r,y:i.y-a};return ln(s,e.viewport,e.snapToGrid,e.snapGrid)}return{x:0,y:0}},flowToScreenCoordinate:i=>{if(e.vueFlowRef){const{x:r,y:a}=e.vueFlowRef.getBoundingClientRect(),s={x:i.x+r,y:i.y+a};return Po(s,e.viewport)}return{x:0,y:0}}}:Od)}function bo(e,t=0,n=zd,o=()=>{}){const i=typeof t=="number"&&t>0;return i||o(),i?e.transition().duration(t).ease(n).on("end",o):e}function Bd(e,t,n){const o=Xi(!0);return o.run(()=>{const i=()=>{o.run(()=>{let S,N,$=!!(n.nodes.value.length||n.edges.value.length);S=zt([e.modelValue,()=>{var I,g;return(g=(I=e.modelValue)==null?void 0:I.value)==null?void 0:g.length}],([I])=>{I&&Array.isArray(I)&&(N?.pause(),n.setElements(I),!N&&!$&&I.length?$=!0:N?.resume())}),N=zt([n.nodes,n.edges,()=>n.edges.value.length,()=>n.nodes.value.length],([I,g])=>{var T;(T=e.modelValue)!=null&&T.value&&Array.isArray(e.modelValue.value)&&(S?.pause(),e.modelValue.value=[...I,...g],Le(()=>{S?.resume()}))},{immediate:$}),Sn(()=>{S?.stop(),N?.stop()})})},r=()=>{o.run(()=>{let S,N,$=!!n.nodes.value.length;S=zt([e.nodes,()=>{var I,g;return(g=(I=e.nodes)==null?void 0:I.value)==null?void 0:g.length}],([I])=>{I&&Array.isArray(I)&&(N?.pause(),n.setNodes(I),!N&&!$&&I.length?$=!0:N?.resume())}),N=zt([n.nodes,()=>n.nodes.value.length],([I])=>{var g;(g=e.nodes)!=null&&g.value&&Array.isArray(e.nodes.value)&&(S?.pause(),e.nodes.value=[...I],Le(()=>{S?.resume()}))},{immediate:$}),Sn(()=>{S?.stop(),N?.stop()})})},a=()=>{o.run(()=>{let S,N,$=!!n.edges.value.length;S=zt([e.edges,()=>{var I,g;return(g=(I=e.edges)==null?void 0:I.value)==null?void 0:g.length}],([I])=>{I&&Array.isArray(I)&&(N?.pause(),n.setEdges(I),!N&&!$&&I.length?$=!0:N?.resume())}),N=zt([n.edges,()=>n.edges.value.length],([I])=>{var g;(g=e.edges)!=null&&g.value&&Array.isArray(e.edges.value)&&(S?.pause(),e.edges.value=[...I],Le(()=>{S?.resume()}))},{immediate:$}),Sn(()=>{S?.stop(),N?.stop()})})},s=()=>{o.run(()=>{De(()=>t.maxZoom,()=>{t.maxZoom&&He(t.maxZoom)&&n.setMaxZoom(t.maxZoom)},{immediate:!0})})},u=()=>{o.run(()=>{De(()=>t.minZoom,()=>{t.minZoom&&He(t.minZoom)&&n.setMinZoom(t.minZoom)},{immediate:!0})})},l=()=>{o.run(()=>{De(()=>t.translateExtent,()=>{t.translateExtent&&He(t.translateExtent)&&n.setTranslateExtent(t.translateExtent)},{immediate:!0})})},c=()=>{o.run(()=>{De(()=>t.nodeExtent,()=>{t.nodeExtent&&He(t.nodeExtent)&&n.setNodeExtent(t.nodeExtent)},{immediate:!0})})},h=()=>{o.run(()=>{De(()=>t.applyDefault,()=>{He(t.applyDefault)&&(n.applyDefault.value=t.applyDefault)},{immediate:!0})})},v=()=>{o.run(()=>{const S=async N=>{let $=N;typeof t.autoConnect=="function"&&($=await t.autoConnect(N)),$!==!1&&n.addEdges([$])};De(()=>t.autoConnect,()=>{He(t.autoConnect)&&(n.autoConnect.value=t.autoConnect)},{immediate:!0}),De(n.autoConnect,(N,$,I)=>{N?n.onConnect(S):n.hooks.value.connect.off(S),I(()=>{n.hooks.value.connect.off(S)})},{immediate:!0})})},y=()=>{const S=["id","modelValue","translateExtent","nodeExtent","edges","nodes","maxZoom","minZoom","applyDefault","autoConnect"];for(const N of Object.keys(t)){const $=N;if(!S.includes($)){const I=ze(()=>t[$]),g=n[$];Vo(g)&&o.run(()=>{De(I,T=>{He(T)&&(g.value=T)},{immediate:!0})})}}};(()=>{i(),r(),a(),u(),s(),l(),c(),h(),v(),y()})()}),()=>o.stop()}function Vd(){return{edgesChange:ee(),nodesChange:ee(),nodeDoubleClick:ee(),nodeClick:ee(),nodeMouseEnter:ee(),nodeMouseMove:ee(),nodeMouseLeave:ee(),nodeContextMenu:ee(),nodeDragStart:ee(),nodeDrag:ee(),nodeDragStop:ee(),nodesInitialized:ee(),miniMapNodeClick:ee(),miniMapNodeDoubleClick:ee(),miniMapNodeMouseEnter:ee(),miniMapNodeMouseMove:ee(),miniMapNodeMouseLeave:ee(),connect:ee(),connectStart:ee(),connectEnd:ee(),clickConnectStart:ee(),clickConnectEnd:ee(),paneReady:ee(),init:ee(),move:ee(),moveStart:ee(),moveEnd:ee(),selectionDragStart:ee(),selectionDrag:ee(),selectionDragStop:ee(),selectionContextMenu:ee(),selectionStart:ee(),selectionEnd:ee(),viewportChangeStart:ee(),viewportChange:ee(),viewportChangeEnd:ee(),paneScroll:ee(),paneClick:ee(),paneContextMenu:ee(),paneMouseEnter:ee(),paneMouseMove:ee(),paneMouseLeave:ee(),edgeContextMenu:ee(),edgeMouseEnter:ee(),edgeMouseMove:ee(),edgeMouseLeave:ee(),edgeDoubleClick:ee(),edgeClick:ee(),edgeUpdateStart:ee(),edgeUpdate:ee(),edgeUpdateEnd:ee(),updateNodeInternals:ee(),error:ee(e=>qn(e.message))}}function Hd(e,t){$a(()=>{for(const[n,o]of Object.entries(t.value)){const i=r=>{e(n,r)};o.fns.add(i),Yn(()=>{o.off(i)})}})}function Jr(){return{vueFlowRef:null,viewportRef:null,nodes:[],edges:[],connectionLookup:new Map,nodeTypes:{},edgeTypes:{},initialized:!1,dimensions:{width:0,height:0},viewport:{x:0,y:0,zoom:1},d3Zoom:null,d3Selection:null,d3ZoomHandler:null,minZoom:.5,maxZoom:2,translateExtent:[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],nodeExtent:[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],selectionMode:Uo.Full,paneDragging:!1,preventScrolling:!0,zoomOnScroll:!0,zoomOnPinch:!0,zoomOnDoubleClick:!0,panOnScroll:!1,panOnScrollSpeed:.5,panOnScrollMode:tn.Free,paneClickDistance:0,panOnDrag:!0,edgeUpdaterRadius:10,onlyRenderVisibleElements:!1,defaultViewport:{x:0,y:0,zoom:1},nodesSelectionActive:!1,userSelectionActive:!1,userSelectionRect:null,defaultMarkerColor:"#b1b1b7",connectionLineStyle:{},connectionLineType:null,connectionLineOptions:{type:kt.Bezier,style:{}},connectionMode:bt.Loose,connectionStartHandle:null,connectionEndHandle:null,connectionClickStartHandle:null,connectionPosition:{x:Number.NaN,y:Number.NaN},connectionRadius:20,connectOnClick:!0,connectionStatus:null,isValidConnection:null,snapGrid:[15,15],snapToGrid:!1,edgesUpdatable:!1,edgesFocusable:!0,nodesFocusable:!0,nodesConnectable:!0,nodesDraggable:!0,nodeDragThreshold:1,elementsSelectable:!0,selectNodesOnDrag:!0,multiSelectionActive:!1,selectionKeyCode:"Shift",multiSelectionKeyCode:Hn()?"Meta":"Control",zoomActivationKeyCode:Hn()?"Meta":"Control",deleteKeyCode:"Backspace",panActivationKeyCode:"Space",hooks:Vd(),applyDefault:!0,autoConnect:!1,fitViewOnInit:!1,fitViewOnInitDone:!1,noDragClassName:"nodrag",noWheelClassName:"nowheel",noPanClassName:"nopan",defaultEdgeOptions:void 0,elevateEdgesOnSelect:!1,elevateNodesOnSelect:!0,autoPanOnNodeDrag:!0,autoPanOnConnect:!0,autoPanSpeed:15,disableKeyboardA11y:!1,ariaLiveMessage:""}}const Rd=["id","vueFlowRef","viewportRef","initialized","modelValue","nodes","edges","maxZoom","minZoom","translateExtent","hooks","defaultEdgeOptions"];function Ld(e,t,n){const o=Fd(e),i=m=>{const p=m??[];e.hooks.updateNodeInternals.trigger(p)},r=m=>id(m,e.nodes,e.edges),a=m=>od(m,e.nodes,e.edges),s=m=>Vr(m,e.edges),u=({id:m,type:p,nodeId:b})=>{var w;return Array.from(((w=e.connectionLookup.get(`${b}-${p}-${m??null}`))==null?void 0:w.values())??[])},l=m=>{if(m)return t.value.get(m)},c=m=>{if(m)return n.value.get(m)},h=(m,p,b)=>{var w,P;const ne=[];for(const oe of m){const q={id:oe.id,type:"position",dragging:b,from:oe.from};if(p&&(q.position=oe.position,oe.parentNode)){const ye=l(oe.parentNode);q.position={x:q.position.x-(((w=ye?.computedPosition)==null?void 0:w.x)??0),y:q.position.y-(((P=ye?.computedPosition)==null?void 0:P.y)??0)}}ne.push(q)}ne?.length&&e.hooks.nodesChange.trigger(ne)},v=m=>{if(!e.vueFlowRef)return;const p=e.vueFlowRef.querySelector(".vue-flow__transformationpane");if(!p)return;const b=window.getComputedStyle(p),{m22:w}=new window.DOMMatrixReadOnly(b.transform),P=[];for(const ne of m){const oe=ne,q=l(oe.id);if(q){const ye=Zn(oe.nodeElement);if(!!(ye.width&&ye.height&&(q.dimensions.width!==ye.width||q.dimensions.height!==ye.height||oe.forceUpdate))){const Ce=oe.nodeElement.getBoundingClientRect();q.dimensions=ye,q.handleBounds.source=Pi("source",oe.nodeElement,Ce,w,q.id),q.handleBounds.target=Pi("target",oe.nodeElement,Ce,w,q.id),P.push({id:q.id,type:"dimensions",dimensions:ye})}}}!e.fitViewOnInitDone&&e.fitViewOnInit&&o.value.fitView().then(()=>{e.fitViewOnInitDone=!0}),P.length&&e.hooks.nodesChange.trigger(P)},y=(m,p)=>{const b=new Set,w=new Set;for(const oe of m)Tt(oe)?b.add(oe.id):Et(oe)&&w.add(oe.id);const P=_t(t.value,b,!0),ne=_t(n.value,w);if(e.multiSelectionActive){for(const oe of b)P.push(wt(oe,p));for(const oe of w)ne.push(wt(oe,p))}P.length&&e.hooks.nodesChange.trigger(P),ne.length&&e.hooks.edgesChange.trigger(ne)},M=m=>{if(e.multiSelectionActive){const p=m.map(b=>wt(b.id,!0));e.hooks.nodesChange.trigger(p);return}e.hooks.nodesChange.trigger(_t(t.value,new Set(m.map(p=>p.id)),!0)),e.hooks.edgesChange.trigger(_t(n.value))},S=m=>{if(e.multiSelectionActive){const p=m.map(b=>wt(b.id,!0));e.hooks.edgesChange.trigger(p);return}e.hooks.edgesChange.trigger(_t(n.value,new Set(m.map(p=>p.id)))),e.hooks.nodesChange.trigger(_t(t.value,new Set,!0))},N=m=>{y(m,!0)},$=m=>{const b=(m||e.nodes).map(w=>(w.selected=!1,wt(w.id,!1)));e.hooks.nodesChange.trigger(b)},I=m=>{const b=(m||e.edges).map(w=>(w.selected=!1,wt(w.id,!1)));e.hooks.edgesChange.trigger(b)},g=m=>{if(!m||!m.length)return y([],!1);const p=m.reduce((b,w)=>{const P=wt(w.id,!1);return Tt(w)?b.nodes.push(P):b.edges.push(P),b},{nodes:[],edges:[]});p.nodes.length&&e.hooks.nodesChange.trigger(p.nodes),p.edges.length&&e.hooks.edgesChange.trigger(p.edges)},T=m=>{var p;(p=e.d3Zoom)==null||p.scaleExtent([m,e.maxZoom]),e.minZoom=m},Y=m=>{var p;(p=e.d3Zoom)==null||p.scaleExtent([e.minZoom,m]),e.maxZoom=m},D=m=>{var p;(p=e.d3Zoom)==null||p.translateExtent(m),e.translateExtent=m},A=m=>{e.nodeExtent=m,i()},O=m=>{var p;(p=e.d3Zoom)==null||p.clickDistance(m)},R=m=>{e.nodesDraggable=m,e.nodesConnectable=m,e.elementsSelectable=m},G=m=>{const p=m instanceof Function?m(e.nodes):m;!e.initialized&&!p.length||(e.nodes=zi(p,l,e.hooks.error.trigger))},F=m=>{const p=m instanceof Function?m(e.edges):m;if(!e.initialized&&!p.length)return;const b=wo(p,e.isValidConnection,l,c,e.hooks.error.trigger,e.defaultEdgeOptions,e.nodes,e.edges);yo(e.connectionLookup,n.value,b),e.edges=b},x=m=>{const p=m instanceof Function?m([...e.nodes,...e.edges]):m;!e.initialized&&!p.length||(G(p.filter(Tt)),F(p.filter(Et)))},X=m=>{let p=m instanceof Function?m(e.nodes):m;p=Array.isArray(p)?p:[p];const b=zi(p,l,e.hooks.error.trigger),w=[];for(const P of b)w.push(Ci(P));w.length&&e.hooks.nodesChange.trigger(w)},E=m=>{let p=m instanceof Function?m(e.edges):m;p=Array.isArray(p)?p:[p];const b=wo(p,e.isValidConnection,l,c,e.hooks.error.trigger,e.defaultEdgeOptions,e.nodes,e.edges),w=[];for(const P of b)w.push(Ci(P));w.length&&e.hooks.edgesChange.trigger(w)},z=(m,p=!0,b=!1)=>{const w=m instanceof Function?m(e.nodes):m,P=Array.isArray(w)?w:[w],ne=[],oe=[];function q(ke){const Ce=s(ke);for(const Ae of Ce)(!He(Ae.deletable)||Ae.deletable)&&oe.push(Ti(Ae.id,Ae.source,Ae.target,Ae.sourceHandle,Ae.targetHandle))}function ye(ke){const Ce=[];for(const Ae of e.nodes)Ae.parentNode===ke&&Ce.push(Ae);if(Ce.length){for(const Ae of Ce)ne.push($i(Ae.id));p&&q(Ce);for(const Ae of Ce)ye(Ae.id)}}for(const ke of P){const Ce=typeof ke=="string"?l(ke):ke;Ce&&(He(Ce.deletable)&&!Ce.deletable||(ne.push($i(Ce.id)),p&&q([Ce]),b&&ye(Ce.id)))}oe.length&&e.hooks.edgesChange.trigger(oe),ne.length&&e.hooks.nodesChange.trigger(ne)},k=m=>{const p=m instanceof Function?m(e.edges):m,b=Array.isArray(p)?p:[p],w=[];for(const P of b){const ne=typeof P=="string"?c(P):P;ne&&(He(ne.deletable)&&!ne.deletable||w.push(Ti(typeof P=="string"?P:P.id,ne.source,ne.target,ne.sourceHandle,ne.targetHandle)))}e.hooks.edgesChange.trigger(w)},H=(m,p,b=!0)=>{const w=c(m.id);if(!w)return!1;const P=e.edges.indexOf(w),ne=Nd(m,p,w,b,e.hooks.error.trigger);if(ne){const[oe]=wo([ne],e.isValidConnection,l,c,e.hooks.error.trigger,e.defaultEdgeOptions,e.nodes,e.edges);return e.edges=e.edges.map((q,ye)=>ye===P?oe:q),yo(e.connectionLookup,n.value,[oe]),oe}return!1},L=(m,p,b={replace:!1})=>{const w=c(m);if(!w)return;const P=typeof p=="function"?p(w):p;w.data=b.replace?P:{...w.data,...P}},J=m=>Ni(m,e.nodes),W=m=>{const p=Ni(m,e.edges);return yo(e.connectionLookup,n.value,p),p},se=(m,p,b={replace:!1})=>{const w=l(m);if(!w)return;const P=typeof p=="function"?p(w):p;b.replace?e.nodes.splice(e.nodes.indexOf(w),1,P):Object.assign(w,P)},pe=(m,p,b={replace:!1})=>{const w=l(m);if(!w)return;const P=typeof p=="function"?p(w):p;w.data=b.replace?P:{...w.data,...P}},fe=(m,p,b=!1)=>{b?e.connectionClickStartHandle=m:e.connectionStartHandle=m,e.connectionEndHandle=null,e.connectionStatus=null,p&&(e.connectionPosition=p)},Q=(m,p=null,b=null)=>{e.connectionStartHandle&&(e.connectionPosition=m,e.connectionEndHandle=p,e.connectionStatus=b)},ae=(m,p)=>{e.connectionPosition={x:Number.NaN,y:Number.NaN},e.connectionEndHandle=null,e.connectionStatus=null,p?e.connectionClickStartHandle=null:e.connectionStartHandle=null},ce=m=>{const p=td(m),b=p?null:Qt(m)?m:l(m.id);return!p&&!b?[null,null,p]:[p?m:Bn(b),b,p]},Se=(m,p=!0,b=e.nodes)=>{const[w,P,ne]=ce(m);if(!w)return[];const oe=[];for(const q of b||e.nodes){if(!ne&&(q.id===P.id||!q.computedPosition))continue;const ye=Bn(q),ke=Vn(ye,w);(p&&ke>0||ke>=Number(w.width)*Number(w.height))&&oe.push(q)}return oe},Ne=(m,p,b=!0)=>{const[w]=ce(m);if(!w)return!1;const P=Vn(w,p);return b&&P>0||P>=Number(w.width)*Number(w.height)},ve=m=>{const{viewport:p,dimensions:b,d3Zoom:w,d3Selection:P,translateExtent:ne}=e;if(!w||!P||!m.x&&!m.y)return!1;const oe=Yt.translate(p.x+m.x,p.y+m.y).scale(p.zoom),q=[[0,0],[b.width,b.height]],ye=w.constrain()(oe,q,ne),ke=e.viewport.x!==ye.x||e.viewport.y!==ye.y||e.viewport.zoom!==ye.k;return w.transform(P,ye),ke},$e=m=>{const p=m instanceof Function?m(e):m,b=["d3Zoom","d3Selection","d3ZoomHandler","viewportRef","vueFlowRef","dimensions","hooks"];He(p.defaultEdgeOptions)&&(e.defaultEdgeOptions=p.defaultEdgeOptions);const w=p.modelValue||p.nodes||p.edges?[]:void 0;w&&(p.modelValue&&w.push(...p.modelValue),p.nodes&&w.push(...p.nodes),p.edges&&w.push(...p.edges),x(w));const P=()=>{He(p.maxZoom)&&Y(p.maxZoom),He(p.minZoom)&&T(p.minZoom),He(p.translateExtent)&&D(p.translateExtent)};for(const ne of Object.keys(p)){const oe=ne,q=p[oe];![...Rd,...b].includes(oe)&&He(q)&&(e[oe]=q)}xo(()=>e.d3Zoom).not.toBeNull().then(P),e.initialized||(e.initialized=!0)};return{updateNodePositions:h,updateNodeDimensions:v,setElements:x,setNodes:G,setEdges:F,addNodes:X,addEdges:E,removeNodes:z,removeEdges:k,findNode:l,findEdge:c,updateEdge:H,updateEdgeData:L,updateNode:se,updateNodeData:pe,applyEdgeChanges:W,applyNodeChanges:J,addSelectedElements:N,addSelectedNodes:M,addSelectedEdges:S,setMinZoom:T,setMaxZoom:Y,setTranslateExtent:D,setNodeExtent:A,setPaneClickDistance:O,removeSelectedElements:g,removeSelectedNodes:$,removeSelectedEdges:I,startConnection:fe,updateConnection:Q,endConnection:ae,setInteractive:R,setState:$e,getIntersectingNodes:Se,getIncomers:r,getOutgoers:a,getConnectedEdges:s,getHandleConnections:u,isNodeIntersecting:Ne,panBy:ve,fitView:m=>o.value.fitView(m),zoomIn:m=>o.value.zoomIn(m),zoomOut:m=>o.value.zoomOut(m),zoomTo:(m,p)=>o.value.zoomTo(m,p),setViewport:(m,p)=>o.value.setViewport(m,p),setTransform:(m,p)=>o.value.setTransform(m,p),getViewport:()=>o.value.getViewport(),getTransform:()=>o.value.getTransform(),setCenter:(m,p,b)=>o.value.setCenter(m,p,b),fitBounds:(m,p)=>o.value.fitBounds(m,p),project:m=>o.value.project(m),screenToFlowCoordinate:m=>o.value.screenToFlowCoordinate(m),flowToScreenCoordinate:m=>o.value.flowToScreenCoordinate(m),toObject:()=>{const m=[],p=[];for(const b of e.nodes){const{computedPosition:w,handleBounds:P,selected:ne,dimensions:oe,isParent:q,resizing:ye,dragging:ke,events:Ce,...Ae}=b;m.push(Ae)}for(const b of e.edges){const{selected:w,sourceNode:P,targetNode:ne,events:oe,...q}=b;p.push(q)}return JSON.parse(JSON.stringify({nodes:m,edges:p,position:[e.viewport.x,e.viewport.y],zoom:e.viewport.zoom,viewport:e.viewport}))},fromObject:m=>new Promise(p=>{const{nodes:b,edges:w,position:P,zoom:ne,viewport:oe}=m;if(b&&G(b),w&&F(w),oe?.x&&oe?.y||P){const q=oe?.x||P[0],ye=oe?.y||P[1],ke=oe?.zoom||ne||e.viewport.zoom;return xo(()=>o.value.viewportInitialized).toBe(!0).then(()=>{o.value.setViewport({x:q,y:ye,zoom:ke}).then(()=>{p(!0)})})}else p(!0)}),updateNodeInternals:i,viewportHelper:o,$reset:()=>{const m=Jr();if(e.edges=[],e.nodes=[],e.d3Zoom&&e.d3Selection){const p=Yt.translate(m.defaultViewport.x??0,m.defaultViewport.y??0).scale(Dt(m.defaultViewport.zoom??1,m.minZoom,m.maxZoom)),b=e.viewportRef.getBoundingClientRect(),w=[[0,0],[b.width,b.height]],P=e.d3Zoom.constrain()(p,w,m.translateExtent);e.d3Zoom.transform(e.d3Selection,P)}$e(m)},$destroy:()=>{}}}const Yd=["data-id","data-handleid","data-nodeid","data-handlepos"],Gd={name:"Handle",compatConfig:{MODE:3}},Ke=Fe({...Gd,props:{id:{default:null},type:{},position:{default:()=>te.Top},isValidConnection:{type:Function},connectable:{type:[Boolean,Number,String,Function],default:void 0},connectableStart:{type:Boolean,default:!0},connectableEnd:{type:Boolean,default:!0}},setup(e,{expose:t}){const n=Ca(e,["position","connectable","connectableStart","connectableEnd","id"]),o=ze(()=>n.type??"source"),i=ze(()=>n.isValidConnection??null),{id:r,connectionStartHandle:a,connectionClickStartHandle:s,connectionEndHandle:u,vueFlowRef:l,nodesConnectable:c,noDragClassName:h,noPanClassName:v}=Oe(),{id:y,node:M,nodeEl:S,connectedEdges:N}=Kr(),$=me(),I=ze(()=>typeof e.connectableStart<"u"?e.connectableStart:!0),g=ze(()=>typeof e.connectableEnd<"u"?e.connectableEnd:!0),T=ze(()=>{var F,x,X,E,z,k;return((F=a.value)==null?void 0:F.nodeId)===y&&((x=a.value)==null?void 0:x.id)===e.id&&((X=a.value)==null?void 0:X.type)===o.value||((E=u.value)==null?void 0:E.nodeId)===y&&((z=u.value)==null?void 0:z.id)===e.id&&((k=u.value)==null?void 0:k.type)===o.value}),Y=ze(()=>{var F,x,X;return((F=s.value)==null?void 0:F.nodeId)===y&&((x=s.value)==null?void 0:x.id)===e.id&&((X=s.value)==null?void 0:X.type)===o.value}),{handlePointerDown:D,handleClick:A}=Zr({nodeId:y,handleId:e.id,isValidConnection:i,type:o}),O=Ee(()=>typeof e.connectable=="string"&&e.connectable==="single"?!N.value.some(F=>{const x=F[`${o.value}Handle`];return F[o.value]!==y?!1:x?x===e.id:!0}):typeof e.connectable=="number"?N.value.filter(F=>{const x=F[`${o.value}Handle`];return F[o.value]!==y?!1:x?x===e.id:!0}).length<e.connectable:typeof e.connectable=="function"?e.connectable(M,N.value):He(e.connectable)?e.connectable:c.value);ut(()=>{var F;if(!M.dimensions.width||!M.dimensions.height)return;const x=(F=M.handleBounds[o.value])==null?void 0:F.find(J=>J.id===e.id);if(!l.value||x)return;const X=l.value.querySelector(".vue-flow__transformationpane");if(!S.value||!$.value||!X||!e.id)return;const E=S.value.getBoundingClientRect(),z=$.value.getBoundingClientRect(),k=window.getComputedStyle(X),{m22:H}=new window.DOMMatrixReadOnly(k.transform),L={id:e.id,position:e.position,x:(z.left-E.left)/H,y:(z.top-E.top)/H,type:o.value,nodeId:y,...Zn($.value)};M.handleBounds[o.value]=[...M.handleBounds[o.value]??[],L]});function R(F){const x=Ko(F);O.value&&I.value&&(x&&F.button===0||!x)&&D(F)}function G(F){!y||!s.value&&!I.value||O.value&&A(F)}return t({handleClick:A,handlePointerDown:D,onClick:G,onPointerDown:R}),(F,x)=>(re(),we("div",{ref_key:"handle",ref:$,"data-id":`${V(r)}-${V(y)}-${e.id}-${o.value}`,"data-handleid":e.id,"data-nodeid":V(y),"data-handlepos":F.position,class:mt(["vue-flow__handle",[`vue-flow__handle-${F.position}`,`vue-flow__handle-${e.id}`,V(h),V(v),o.value,{connectable:O.value,connecting:Y.value,connectablestart:I.value,connectableend:g.value,connectionindicator:O.value&&(I.value&&!T.value||g.value&&T.value)}]]),onMousedown:R,onTouchstartPassive:R,onClick:G},[Ve(F.$slots,"default",{id:F.id})],42,Yd))}}),Qn=function({sourcePosition:e=te.Bottom,targetPosition:t=te.Top,label:n,connectable:o=!0,isValidTargetPos:i,isValidSourcePos:r,data:a}){const s=a.label??n;return[Me(Ke,{type:"target",position:t,connectable:o,isValidConnection:i}),typeof s!="string"&&s?Me(s):Me(Ze,[s]),Me(Ke,{type:"source",position:e,connectable:o,isValidConnection:r})]};Qn.props=["sourcePosition","targetPosition","label","isValidTargetPos","isValidSourcePos","connectable","data"];Qn.inheritAttrs=!1;Qn.compatConfig={MODE:3};const Xd=Qn,jn=function({targetPosition:e=te.Top,label:t,connectable:n=!0,isValidTargetPos:o,data:i}){const r=i.label??t;return[Me(Ke,{type:"target",position:e,connectable:n,isValidConnection:o}),typeof r!="string"&&r?Me(r):Me(Ze,[r])]};jn.props=["targetPosition","label","isValidTargetPos","connectable","data"];jn.inheritAttrs=!1;jn.compatConfig={MODE:3};const Wd=jn,eo=function({sourcePosition:e=te.Bottom,label:t,connectable:n=!0,isValidSourcePos:o,data:i}){const r=i.label??t;return[typeof r!="string"&&r?Me(r):Me(Ze,[r]),Me(Ke,{type:"source",position:e,connectable:n,isValidConnection:o})]};eo.props=["sourcePosition","label","isValidSourcePos","connectable","data"];eo.inheritAttrs=!1;eo.compatConfig={MODE:3};const Ud=eo,Zd=["transform"],Kd=["width","height","x","y","rx","ry"],qd=["y"],Jd={name:"EdgeText",compatConfig:{MODE:3}},Qd=Fe({...Jd,props:{x:{},y:{},label:{},labelStyle:{default:()=>({})},labelShowBg:{type:Boolean,default:!0},labelBgStyle:{default:()=>({})},labelBgPadding:{default:()=>[2,4]},labelBgBorderRadius:{default:2}},setup(e){const t=me({x:0,y:0,width:0,height:0}),n=me(null),o=Ee(()=>`translate(${e.x-t.value.width/2} ${e.y-t.value.height/2})`);ut(i),De([()=>e.x,()=>e.y,n,()=>e.label],i);function i(){if(!n.value)return;const r=n.value.getBBox();(r.width!==t.value.width||r.height!==t.value.height)&&(t.value=r)}return(r,a)=>(re(),we("g",{transform:o.value,class:"vue-flow__edge-textwrapper"},[r.labelShowBg?(re(),we("rect",{key:0,class:"vue-flow__edge-textbg",width:`${t.value.width+2*r.labelBgPadding[0]}px`,height:`${t.value.height+2*r.labelBgPadding[1]}px`,x:-r.labelBgPadding[0],y:-r.labelBgPadding[1],style:Ge(r.labelBgStyle),rx:r.labelBgBorderRadius,ry:r.labelBgBorderRadius},null,12,Kd)):Pe("",!0),U("text",Wi(r.$attrs,{ref_key:"el",ref:n,class:"vue-flow__edge-text",y:t.value.height/2,dy:"0.3em",style:r.labelStyle}),[Ve(r.$slots,"default",{},()=>[typeof r.label!="string"?(re(),We(Ot(r.label),{key:0})):(re(),we(Ze,{key:1},[Xe(Je(r.label),1)],64))])],16,qd)],8,Zd))}}),jd=["id","d","marker-end","marker-start"],ef=["d","stroke-width"],tf={name:"BaseEdge",inheritAttrs:!1,compatConfig:{MODE:3}},to=Fe({...tf,props:{id:{},labelX:{},labelY:{},path:{},label:{},markerStart:{},markerEnd:{},interactionWidth:{default:20},labelStyle:{},labelShowBg:{type:Boolean},labelBgStyle:{},labelBgPadding:{},labelBgBorderRadius:{}},setup(e,{expose:t}){const n=me(null),o=me(null),i=me(null),r=Na();return t({pathEl:n,interactionEl:o,labelEl:i}),(a,s)=>(re(),we(Ze,null,[U("path",Wi(V(r),{id:a.id,ref_key:"pathEl",ref:n,d:a.path,class:"vue-flow__edge-path","marker-end":a.markerEnd,"marker-start":a.markerStart}),null,16,jd),a.interactionWidth?(re(),we("path",{key:0,ref_key:"interactionEl",ref:o,fill:"none",d:a.path,"stroke-width":a.interactionWidth,"stroke-opacity":0,class:"vue-flow__edge-interaction"},null,8,ef)):Pe("",!0),a.label&&a.labelX&&a.labelY?(re(),We(Qd,{key:1,ref_key:"labelEl",ref:i,x:a.labelX,y:a.labelY,label:a.label,"label-show-bg":a.labelShowBg,"label-bg-style":a.labelBgStyle,"label-bg-padding":a.labelBgPadding,"label-bg-border-radius":a.labelBgBorderRadius,"label-style":a.labelStyle},null,8,["x","y","label","label-show-bg","label-bg-style","label-bg-padding","label-bg-border-radius","label-style"])):Pe("",!0)],64))}});function Qr({sourceX:e,sourceY:t,targetX:n,targetY:o}){const i=Math.abs(n-e)/2,r=n<e?n+i:n-i,a=Math.abs(o-t)/2,s=o<t?o+a:o-a;return[r,s,i,a]}function jr({sourceX:e,sourceY:t,targetX:n,targetY:o,sourceControlX:i,sourceControlY:r,targetControlX:a,targetControlY:s}){const u=e*.125+i*.375+a*.375+n*.125,l=t*.125+r*.375+s*.375+o*.125,c=Math.abs(u-e),h=Math.abs(l-t);return[u,l,c,h]}function En(e,t){return e>=0?.5*e:t*25*Math.sqrt(-e)}function Vi({pos:e,x1:t,y1:n,x2:o,y2:i,c:r}){let a,s;switch(e){case te.Left:a=t-En(t-o,r),s=n;break;case te.Right:a=t+En(o-t,r),s=n;break;case te.Top:a=t,s=n-En(n-i,r);break;case te.Bottom:a=t,s=n+En(i-n,r);break}return[a,s]}function ea(e){const{sourceX:t,sourceY:n,sourcePosition:o=te.Bottom,targetX:i,targetY:r,targetPosition:a=te.Top,curvature:s=.25}=e,[u,l]=Vi({pos:o,x1:t,y1:n,x2:i,y2:r,c:s}),[c,h]=Vi({pos:a,x1:i,y1:r,x2:t,y2:n,c:s}),[v,y,M,S]=jr({sourceX:t,sourceY:n,targetX:i,targetY:r,sourceControlX:u,sourceControlY:l,targetControlX:c,targetControlY:h});return[`M${t},${n} C${u},${l} ${c},${h} ${i},${r}`,v,y,M,S]}function Hi({pos:e,x1:t,y1:n,x2:o,y2:i}){let r,a;switch(e){case te.Left:case te.Right:r=.5*(t+o),a=n;break;case te.Top:case te.Bottom:r=t,a=.5*(n+i);break}return[r,a]}function ta(e){const{sourceX:t,sourceY:n,sourcePosition:o=te.Bottom,targetX:i,targetY:r,targetPosition:a=te.Top}=e,[s,u]=Hi({pos:o,x1:t,y1:n,x2:i,y2:r}),[l,c]=Hi({pos:a,x1:i,y1:r,x2:t,y2:n}),[h,v,y,M]=jr({sourceX:t,sourceY:n,targetX:i,targetY:r,sourceControlX:s,sourceControlY:u,targetControlX:l,targetControlY:c});return[`M${t},${n} C${s},${u} ${l},${c} ${i},${r}`,h,v,y,M]}const Ri={[te.Left]:{x:-1,y:0},[te.Right]:{x:1,y:0},[te.Top]:{x:0,y:-1},[te.Bottom]:{x:0,y:1}};function nf({source:e,sourcePosition:t=te.Bottom,target:n}){return t===te.Left||t===te.Right?e.x<n.x?{x:1,y:0}:{x:-1,y:0}:e.y<n.y?{x:0,y:1}:{x:0,y:-1}}function Li(e,t){return Math.sqrt((t.x-e.x)**2+(t.y-e.y)**2)}function of({source:e,sourcePosition:t=te.Bottom,target:n,targetPosition:o=te.Top,center:i,offset:r}){const a=Ri[t],s=Ri[o],u={x:e.x+a.x*r,y:e.y+a.y*r},l={x:n.x+s.x*r,y:n.y+s.y*r},c=nf({source:u,sourcePosition:t,target:l}),h=c.x!==0?"x":"y",v=c[h];let y,M,S;const N={x:0,y:0},$={x:0,y:0},[I,g,T,Y]=Qr({sourceX:e.x,sourceY:e.y,targetX:n.x,targetY:n.y});if(a[h]*s[h]===-1){M=i.x??I,S=i.y??g;const A=[{x:M,y:u.y},{x:M,y:l.y}],O=[{x:u.x,y:S},{x:l.x,y:S}];a[h]===v?y=h==="x"?A:O:y=h==="x"?O:A}else{const A=[{x:u.x,y:l.y}],O=[{x:l.x,y:u.y}];if(h==="x"?y=a.x===v?O:A:y=a.y===v?A:O,t===o){const X=Math.abs(e[h]-n[h]);if(X<=r){const E=Math.min(r-1,r-X);a[h]===v?N[h]=(u[h]>e[h]?-1:1)*E:$[h]=(l[h]>n[h]?-1:1)*E}}if(t!==o){const X=h==="x"?"y":"x",E=a[h]===s[X],z=u[X]>l[X],k=u[X]<l[X];(a[h]===1&&(!E&&z||E&&k)||a[h]!==1&&(!E&&k||E&&z))&&(y=h==="x"?A:O)}const R={x:u.x+N.x,y:u.y+N.y},G={x:l.x+$.x,y:l.y+$.y},F=Math.max(Math.abs(R.x-y[0].x),Math.abs(G.x-y[0].x)),x=Math.max(Math.abs(R.y-y[0].y),Math.abs(G.y-y[0].y));F>=x?(M=(R.x+G.x)/2,S=y[0].y):(M=y[0].x,S=(R.y+G.y)/2)}return[[e,{x:u.x+N.x,y:u.y+N.y},...y,{x:l.x+$.x,y:l.y+$.y},n],M,S,T,Y]}function rf(e,t,n,o){const i=Math.min(Li(e,t)/2,Li(t,n)/2,o),{x:r,y:a}=t;if(e.x===r&&r===n.x||e.y===a&&a===n.y)return`L${r} ${a}`;if(e.y===a){const l=e.x<n.x?-1:1,c=e.y<n.y?1:-1;return`L ${r+i*l},${a}Q ${r},${a} ${r},${a+i*c}`}const s=e.x<n.x?1:-1,u=e.y<n.y?-1:1;return`L ${r},${a+i*u}Q ${r},${a} ${r+i*s},${a}`}function Fo(e){const{sourceX:t,sourceY:n,sourcePosition:o=te.Bottom,targetX:i,targetY:r,targetPosition:a=te.Top,borderRadius:s=5,centerX:u,centerY:l,offset:c=20}=e,[h,v,y,M,S]=of({source:{x:t,y:n},sourcePosition:o,target:{x:i,y:r},targetPosition:a,center:{x:u,y:l},offset:c});return[h.reduce(($,I,g)=>{let T;return g>0&&g<h.length-1?T=rf(h[g-1],I,h[g+1],s):T=`${g===0?"M":"L"}${I.x} ${I.y}`,$+=T,$},""),v,y,M,S]}function af(e){const{sourceX:t,sourceY:n,targetX:o,targetY:i}=e,[r,a,s,u]=Qr({sourceX:t,sourceY:n,targetX:o,targetY:i});return[`M ${t},${n}L ${o},${i}`,r,a,s,u]}const sf=Fe({name:"StraightEdge",props:["label","labelStyle","labelShowBg","labelBgStyle","labelBgPadding","labelBgBorderRadius","sourceY","sourceX","targetX","targetY","markerEnd","markerStart","interactionWidth"],compatConfig:{MODE:3},setup(e,{attrs:t}){return()=>{const[n,o,i]=af(e);return Me(to,{path:n,labelX:o,labelY:i,...t,...e})}}}),na=sf,lf=Fe({name:"SmoothStepEdge",props:["sourcePosition","targetPosition","label","labelStyle","labelShowBg","labelBgStyle","labelBgPadding","labelBgBorderRadius","sourceY","sourceX","targetX","targetY","borderRadius","markerEnd","markerStart","interactionWidth","offset"],compatConfig:{MODE:3},setup(e,{attrs:t}){return()=>{const[n,o,i]=Fo({...e,sourcePosition:e.sourcePosition??te.Bottom,targetPosition:e.targetPosition??te.Top});return Me(to,{path:n,labelX:o,labelY:i,...t,...e})}}}),qo=lf,uf=Fe({name:"StepEdge",props:["sourcePosition","targetPosition","label","labelStyle","labelShowBg","labelBgStyle","labelBgPadding","labelBgBorderRadius","sourceY","sourceX","targetX","targetY","markerEnd","markerStart","interactionWidth"],setup(e,{attrs:t}){return()=>Me(qo,{...e,...t,borderRadius:0})}}),oa=uf,cf=Fe({name:"BezierEdge",props:["sourcePosition","targetPosition","label","labelStyle","labelShowBg","labelBgStyle","labelBgPadding","labelBgBorderRadius","sourceY","sourceX","targetX","targetY","curvature","markerEnd","markerStart","interactionWidth"],compatConfig:{MODE:3},setup(e,{attrs:t}){return()=>{const[n,o,i]=ea({...e,sourcePosition:e.sourcePosition??te.Bottom,targetPosition:e.targetPosition??te.Top});return Me(to,{path:n,labelX:o,labelY:i,...t,...e})}}}),ia=cf,df=Fe({name:"SimpleBezierEdge",props:["sourcePosition","targetPosition","label","labelStyle","labelShowBg","labelBgStyle","labelBgPadding","labelBgBorderRadius","sourceY","sourceX","targetX","targetY","markerEnd","markerStart","interactionWidth"],compatConfig:{MODE:3},setup(e,{attrs:t}){return()=>{const[n,o,i]=ta({...e,sourcePosition:e.sourcePosition??te.Bottom,targetPosition:e.targetPosition??te.Top});return Me(to,{path:n,labelX:o,labelY:i,...t,...e})}}}),ra=df,ff={input:Ud,default:Xd,output:Wd},hf={default:ia,straight:na,step:oa,smoothstep:qo,simplebezier:ra};function pf(e,t,n){const o=Ee(()=>S=>t.value.get(S)),i=Ee(()=>S=>n.value.get(S)),r=Ee(()=>{const S={...hf,...e.edgeTypes},N=Object.keys(S);for(const $ of e.edges)$.type&&!N.includes($.type)&&(S[$.type]=$.type);return S}),a=Ee(()=>{const S={...ff,...e.nodeTypes},N=Object.keys(S);for(const $ of e.nodes)$.type&&!N.includes($.type)&&(S[$.type]=$.type);return S}),s=Ee(()=>e.onlyRenderVisibleElements?Br(e.nodes,{x:0,y:0,width:e.dimensions.width,height:e.dimensions.height},e.viewport,!0):e.nodes),u=Ee(()=>{if(e.onlyRenderVisibleElements){const S=[];for(const N of e.edges){const $=t.value.get(N.source),I=t.value.get(N.target);hd({sourcePos:$.computedPosition||{x:0,y:0},targetPos:I.computedPosition||{x:0,y:0},sourceWidth:$.dimensions.width,sourceHeight:$.dimensions.height,targetWidth:I.dimensions.width,targetHeight:I.dimensions.height,width:e.dimensions.width,height:e.dimensions.height,viewport:e.viewport})&&S.push(N)}return S}return e.edges}),l=Ee(()=>[...s.value,...u.value]),c=Ee(()=>{const S=[];for(const N of e.nodes)N.selected&&S.push(N);return S}),h=Ee(()=>{const S=[];for(const N of e.edges)N.selected&&S.push(N);return S}),v=Ee(()=>[...c.value,...h.value]),y=Ee(()=>{const S=[];for(const N of e.nodes)N.dimensions.width&&N.dimensions.height&&N.handleBounds!==void 0&&S.push(N);return S}),M=Ee(()=>s.value.length>0&&y.value.length===s.value.length);return{getNode:o,getEdge:i,getElements:l,getEdgeTypes:r,getNodeTypes:a,getEdges:u,getNodes:s,getSelectedElements:v,getSelectedNodes:c,getSelectedEdges:h,getNodesInitialized:y,areNodesInitialized:M}}class Nt{constructor(){this.currentId=0,this.flows=new Map}static getInstance(){var t;const n=(t=cn())==null?void 0:t.appContext.app,o=n?.config.globalProperties.$vueFlowStorage??Nt.instance;return Nt.instance=o??new Nt,n&&(n.config.globalProperties.$vueFlowStorage=Nt.instance),Nt.instance}set(t,n){return this.flows.set(t,n)}get(t){return this.flows.get(t)}remove(t){return this.flows.delete(t)}create(t,n){const o=Jr(),i=ka(o),r={};for(const[v,y]of Object.entries(i.hooks)){const M=`on${v.charAt(0).toUpperCase()+v.slice(1)}`;r[M]=y.on}const a={};for(const[v,y]of Object.entries(i.hooks))a[v]=y.trigger;const s=Ee(()=>{const v=new Map;for(const y of i.nodes)v.set(y.id,y);return v}),u=Ee(()=>{const v=new Map;for(const y of i.edges)v.set(y.id,y);return v}),l=pf(i,s,u),c=Ld(i,s,u);c.setState({...i,...n});const h={...r,...l,...c,...cs(i),nodeLookup:s,edgeLookup:u,emits:a,id:t,vueFlowVersion:"1.44.0",$destroy:()=>{this.remove(t)}};return this.set(t,h),h}getId(){return`vue-flow-${this.currentId++}`}}function Oe(e){const t=Nt.getInstance(),n=Gi(),o=typeof e=="object",i=o?e:{id:e},r=i.id,a=r??n?.vueFlowId;let s;if(n){const u=Xt(Bi,null);typeof u<"u"&&u!==null&&(!a||u.id===a)&&(s=u)}if(s||a&&(s=t.get(a)),!s||a&&s.id!==a){const u=r??t.getId(),l=t.create(u,i);s=l,(n??Xi(!0)).run(()=>{De(l.applyDefault,(h,v,y)=>{const M=N=>{l.applyNodeChanges(N)},S=N=>{l.applyEdgeChanges(N)};h?(l.onNodesChange(M),l.onEdgesChange(S)):(l.hooks.value.nodesChange.off(M),l.hooks.value.edgesChange.off(S)),y(()=>{l.hooks.value.nodesChange.off(M),l.hooks.value.edgesChange.off(S)})},{immediate:!0}),Yn(()=>{if(s){const h=t.get(s.id);h?h.$destroy():qn(`No store instance found for id ${s.id} in storage.`)}})})}else o&&s.setState(i);if(n&&(Ht(Bi,s),n.vueFlowId=s.id),o){const u=cn();u?.type.name!=="VueFlow"&&s.emits.error(new Ye(Re.USEVUEFLOW_OPTIONS))}return s}function gf(e){const{emits:t,dimensions:n}=Oe();let o;ut(()=>{const i=e.value,r=()=>{if(!i)return;const a=Zn(i);(a.width===0||a.height===0)&&t.error(new Ye(Re.MISSING_VIEWPORT_DIMENSIONS)),n.value={width:a.width||500,height:a.height||500}};r(),window.addEventListener("resize",r),i&&(o=new ResizeObserver(()=>r()),o.observe(i)),Ui(()=>{window.removeEventListener("resize",r),o&&i&&o.unobserve(i)})})}const mf={name:"UserSelection",compatConfig:{MODE:3}},vf=Fe({...mf,props:{userSelectionRect:{}},setup(e){return(t,n)=>(re(),we("div",{class:"vue-flow__selection vue-flow__container",style:Ge({width:`${t.userSelectionRect.width}px`,height:`${t.userSelectionRect.height}px`,transform:`translate(${t.userSelectionRect.x}px, ${t.userSelectionRect.y}px)`})},null,4))}}),yf=["tabIndex"],wf={name:"NodesSelection",compatConfig:{MODE:3}},_f=Fe({...wf,setup(e){const{emits:t,viewport:n,getSelectedNodes:o,noPanClassName:i,disableKeyboardA11y:r,userSelectionActive:a}=Oe(),s=qr(),u=me(null),l=Ur({el:u,onStart(M){t.selectionDragStart(M)},onDrag(M){t.selectionDrag(M)},onStop(M){t.selectionDragStop(M)}});ut(()=>{var M;r.value||(M=u.value)==null||M.focus({preventScroll:!0})});const c=Ee(()=>Fr(o.value)),h=Ee(()=>({width:`${c.value.width}px`,height:`${c.value.height}px`,top:`${c.value.y}px`,left:`${c.value.x}px`}));function v(M){t.selectionContextMenu({event:M,nodes:o.value})}function y(M){r||Vt[M.key]&&(M.preventDefault(),s({x:Vt[M.key].x,y:Vt[M.key].y},M.shiftKey))}return(M,S)=>!V(a)&&c.value.width&&c.value.height?(re(),we("div",{key:0,class:mt(["vue-flow__nodesselection vue-flow__container",V(i)]),style:Ge({transform:`translate(${V(n).x}px,${V(n).y}px) scale(${V(n).zoom})`})},[U("div",{ref_key:"el",ref:u,class:mt([{dragging:V(l)},"vue-flow__nodesselection-rect"]),style:Ge(h.value),tabIndex:V(r)?void 0:-1,onContextmenu:v,onKeydown:y},null,46,yf)],6)):Pe("",!0)}});function bf(e,t){return{x:e.clientX-t.left,y:e.clientY-t.top}}const Ef={name:"Pane",compatConfig:{MODE:3}},xf=Fe({...Ef,props:{isSelecting:{type:Boolean},selectionKeyPressed:{type:Boolean}},setup(e){const{vueFlowRef:t,nodes:n,viewport:o,emits:i,userSelectionActive:r,removeSelectedElements:a,userSelectionRect:s,elementsSelectable:u,nodesSelectionActive:l,getSelectedEdges:c,getSelectedNodes:h,removeNodes:v,removeEdges:y,selectionMode:M,deleteKeyCode:S,multiSelectionKeyCode:N,multiSelectionActive:$,edgeLookup:I,nodeLookup:g,connectionLookup:T,defaultEdgeOptions:Y,connectionStartHandle:D}=Oe(),A=me(null),O=me(new Set),R=me(new Set),G=me(),F=ze(()=>u.value&&(e.isSelecting||r.value)),x=ze(()=>D.value!==null);let X=!1,E=!1;const z=nn(S,{actInsideInputWithModifier:!1}),k=nn(N);De(z,Q=>{Q&&(v(h.value),y(c.value),l.value=!1)}),De(k,Q=>{$.value=Q});function H(Q,ae){return ce=>{ce.target===ae&&Q?.(ce)}}function L(Q){if(X||x.value){X=!1;return}i.paneClick(Q),a(),l.value=!1}function J(Q){Q.preventDefault(),Q.stopPropagation(),i.paneContextMenu(Q)}function W(Q){i.paneScroll(Q)}function se(Q){var ae,ce,Se;if(G.value=(ae=t.value)==null?void 0:ae.getBoundingClientRect(),!u.value||!e.isSelecting||Q.button!==0||Q.target!==A.value||!G.value)return;(Se=(ce=Q.target)==null?void 0:ce.setPointerCapture)==null||Se.call(ce,Q.pointerId);const{x:Ne,y:ve}=bf(Q,G.value);E=!0,X=!1,a(),s.value={width:0,height:0,startX:Ne,startY:ve,x:Ne,y:ve},i.selectionStart(Q)}function pe(Q){var ae;if(!G.value||!s.value)return;X=!0;const{x:ce,y:Se}=st(Q,G.value),{startX:Ne=0,startY:ve=0}=s.value,$e={startX:Ne,startY:ve,x:ce<Ne?ce:Ne,y:Se<ve?Se:ve,width:Math.abs(ce-Ne),height:Math.abs(Se-ve)},he=O.value,xe=R.value;O.value=new Set(Br(n.value,$e,o.value,M.value===Uo.Partial,!0).map(m=>m.id)),R.value=new Set;const de=((ae=Y.value)==null?void 0:ae.selectable)??!0;for(const m of O.value){const p=T.value.get(m);if(p)for(const{edgeId:b}of p.values()){const w=I.value.get(b);w&&(w.selectable??de)&&R.value.add(b)}}if(!Fi(he,O.value)){const m=_t(g.value,O.value,!0);i.nodesChange(m)}if(!Fi(xe,R.value)){const m=_t(I.value,R.value);i.edgesChange(m)}s.value=$e,r.value=!0,l.value=!1}function fe(Q){var ae;Q.button!==0||!E||((ae=Q.target)==null||ae.releasePointerCapture(Q.pointerId),!r.value&&s.value&&Q.target===A.value&&L(Q),r.value=!1,s.value=null,l.value=O.value.size>0,i.selectionEnd(Q),e.selectionKeyPressed&&(X=!1),E=!1)}return(Q,ae)=>(re(),we("div",{ref_key:"container",ref:A,class:mt(["vue-flow__pane vue-flow__container",{selection:Q.isSelecting}]),onClick:ae[0]||(ae[0]=ce=>F.value?void 0:H(L,A.value)(ce)),onContextmenu:ae[1]||(ae[1]=ce=>H(J,A.value)(ce)),onWheelPassive:ae[2]||(ae[2]=ce=>H(W,A.value)(ce)),onPointerenter:ae[3]||(ae[3]=ce=>F.value?void 0:V(i).paneMouseEnter(ce)),onPointerdown:ae[4]||(ae[4]=ce=>F.value?se(ce):V(i).paneMouseMove(ce)),onPointermove:ae[5]||(ae[5]=ce=>F.value?pe(ce):V(i).paneMouseMove(ce)),onPointerup:ae[6]||(ae[6]=ce=>F.value?fe(ce):void 0),onPointerleave:ae[7]||(ae[7]=ce=>V(i).paneMouseLeave(ce))},[Ve(Q.$slots,"default"),V(r)&&V(s)?(re(),We(vf,{key:0,"user-selection-rect":V(s)},null,8,["user-selection-rect"])):Pe("",!0),V(l)&&V(h).length?(re(),We(_f,{key:1})):Pe("",!0)],34))}}),Sf={name:"Transform",compatConfig:{MODE:3}},kf=Fe({...Sf,setup(e){const{viewport:t,fitViewOnInit:n,fitViewOnInitDone:o}=Oe(),i=Ee(()=>n.value?!o.value:!1),r=Ee(()=>`translate(${t.value.x}px,${t.value.y}px) scale(${t.value.zoom})`);return(a,s)=>(re(),we("div",{class:"vue-flow__transformationpane vue-flow__container",style:Ge({transform:r.value,opacity:i.value?0:void 0})},[Ve(a.$slots,"default")],4))}}),Nf={name:"Viewport",compatConfig:{MODE:3}},Cf=Fe({...Nf,setup(e){const{minZoom:t,maxZoom:n,defaultViewport:o,translateExtent:i,zoomActivationKeyCode:r,selectionKeyCode:a,panActivationKeyCode:s,panOnScroll:u,panOnScrollMode:l,panOnScrollSpeed:c,panOnDrag:h,zoomOnDoubleClick:v,zoomOnPinch:y,zoomOnScroll:M,preventScrolling:S,noWheelClassName:N,noPanClassName:$,emits:I,connectionStartHandle:g,userSelectionActive:T,paneDragging:Y,d3Zoom:D,d3Selection:A,d3ZoomHandler:O,viewport:R,viewportRef:G,paneClickDistance:F}=Oe();gf(G);const x=Mn(!1),X=Mn(!1);let E=null,z=!1,k=0,H={x:0,y:0,zoom:0};const L=nn(s),J=nn(a),W=nn(r),se=ze(()=>(!J.value||J.value&&a.value===!0)&&(L.value||h.value)),pe=ze(()=>L.value||u.value),fe=ze(()=>J.value||a.value===!0&&se.value!==!0);ut(()=>{if(!G.value){qn("Viewport element is missing");return}const ve=G.value,$e=ve.getBoundingClientRect(),he=Zc().clickDistance(F.value).scaleExtent([t.value,n.value]).translateExtent(i.value),xe=tt(ve).call(he),de=xe.on("wheel.zoom"),m=Yt.translate(o.value.x??0,o.value.y??0).scale(Dt(o.value.zoom??1,t.value,n.value)),p=[[0,0],[$e.width,$e.height]],b=he.constrain()(m,p,i.value);he.transform(xe,b),he.wheelDelta(ae),D.value=he,A.value=xe,O.value=de,R.value={x:b.x,y:b.y,zoom:b.k},he.on("start",w=>{var P;if(!w.sourceEvent)return null;k=w.sourceEvent.button,x.value=!0;const ne=Se(w.transform);((P=w.sourceEvent)==null?void 0:P.type)==="mousedown"&&(Y.value=!0),H=ne,I.viewportChangeStart(ne),I.moveStart({event:w,flowTransform:ne})}),he.on("end",w=>{if(!w.sourceEvent)return null;if(x.value=!1,Y.value=!1,Q(se.value,k??0)&&!z&&I.paneContextMenu(w.sourceEvent),z=!1,ce(H,w.transform)){const P=Se(w.transform);H=P,I.viewportChangeEnd(P),I.moveEnd({event:w,flowTransform:P})}}),he.filter(w=>{var P;const ne=W.value||M.value,oe=y.value&&w.ctrlKey,q=w.button;if(q===1&&w.type==="mousedown"&&(Ne(w,"vue-flow__node")||Ne(w,"vue-flow__edge")))return!0;if(!se.value&&!ne&&!pe.value&&!v.value&&!y.value||T.value||!v.value&&w.type==="dblclick"||Ne(w,N.value)&&w.type==="wheel"||Ne(w,$.value)&&(w.type!=="wheel"||pe.value&&w.type==="wheel"&&!W.value)||!y.value&&w.ctrlKey&&w.type==="wheel"||!ne&&!pe.value&&!oe&&w.type==="wheel")return!1;if(!y&&w.type==="touchstart"&&((P=w.touches)==null?void 0:P.length)>1)return w.preventDefault(),!1;if(!se.value&&(w.type==="mousedown"||w.type==="touchstart")||a.value===!0&&Array.isArray(h.value)&&h.value.includes(0)&&q===0||Array.isArray(h.value)&&!h.value.includes(q)&&(w.type==="mousedown"||w.type==="touchstart"))return!1;const ye=Array.isArray(h.value)&&h.value.includes(q)||a.value===!0&&Array.isArray(h.value)&&!h.value.includes(0)||!q||q<=1;return(!w.ctrlKey||L.value||w.type==="wheel")&&ye}),De([T,se],()=>{T.value&&!x.value?he.on("zoom",null):T.value||he.on("zoom",w=>{R.value={x:w.transform.x,y:w.transform.y,zoom:w.transform.k};const P=Se(w.transform);z=Q(se.value,k??0),I.viewportChange(P),I.move({event:w,flowTransform:P})})},{immediate:!0}),De([T,pe,l,W,y,S,N],()=>{pe.value&&!W.value&&!T.value?xe.on("wheel.zoom",w=>{if(Ne(w,N.value))return!1;const P=W.value||M.value,ne=y.value&&w.ctrlKey;if(!(!S.value||pe.value||P||ne))return!1;w.preventDefault(),w.stopImmediatePropagation();const q=xe.property("__zoom").k||1,ye=Hn();if(!L.value&&w.ctrlKey&&y.value&&ye){const dt=rt(w),no=ae(w),oo=q*2**no;he.scaleTo(xe,oo,dt,w);return}const ke=w.deltaMode===1?20:1;let Ce=l.value===tn.Vertical?0:w.deltaX*ke,Ae=l.value===tn.Horizontal?0:w.deltaY*ke;!ye&&w.shiftKey&&l.value!==tn.Vertical&&!Ce&&Ae&&(Ce=Ae,Ae=0),he.translateBy(xe,-(Ce/q)*c.value,-(Ae/q)*c.value);const je=Se(xe.property("__zoom"));E&&clearTimeout(E),X.value?(I.move({event:w,flowTransform:je}),I.viewportChange(je),E=setTimeout(()=>{I.moveEnd({event:w,flowTransform:je}),I.viewportChangeEnd(je),X.value=!1},150)):(X.value=!0,I.moveStart({event:w,flowTransform:je}),I.viewportChangeStart(je))},{passive:!1}):typeof de<"u"&&xe.on("wheel.zoom",function(w,P){const ne=!S.value&&w.type==="wheel"&&!w.ctrlKey,oe=W.value||M.value,q=y.value&&w.ctrlKey;if(!oe&&!u.value&&!q&&w.type==="wheel"||ne||Ne(w,N.value))return null;w.preventDefault(),de.call(this,w,P)},{passive:!1})},{immediate:!0})});function Q(ve,$e){return $e===2&&Array.isArray(ve)&&ve.includes(2)}function ae(ve){const $e=ve.ctrlKey&&Hn()?10:1;return-ve.deltaY*(ve.deltaMode===1?.05:ve.deltaMode?1:.002)*$e}function ce(ve,$e){return ve.x!==$e.x&&!Number.isNaN($e.x)||ve.y!==$e.y&&!Number.isNaN($e.y)||ve.zoom!==$e.k&&!Number.isNaN($e.k)}function Se(ve){return{x:ve.x,y:ve.y,zoom:ve.k}}function Ne(ve,$e){return ve.target.closest(`.${$e}`)}return(ve,$e)=>(re(),we("div",{ref_key:"viewportRef",ref:G,class:"vue-flow__viewport vue-flow__container"},[Z(xf,{"is-selecting":fe.value,"selection-key-pressed":V(J),class:mt({connecting:!!V(g),dragging:V(Y),draggable:V(h)===!0||Array.isArray(V(h))&&V(h).includes(0)})},{default:ge(()=>[Z(kf,null,{default:ge(()=>[Ve(ve.$slots,"default")]),_:3})]),_:3},8,["is-selecting","selection-key-pressed","class"])],512))}}),$f=["id"],Tf=["id"],Mf=["id"],If={name:"A11yDescriptions",compatConfig:{MODE:3}},Df=Fe({...If,setup(e){const{id:t,disableKeyboardA11y:n,ariaLiveMessage:o}=Oe();return(i,r)=>(re(),we(Ze,null,[U("div",{id:`${V(Tr)}-${V(t)}`,style:{display:"none"}}," Press enter or space to select a node. "+Je(V(n)?"":"You can then use the arrow keys to move the node around.")+" You can then use the arrow keys to move the node around, press delete to remove it and press escape to cancel. ",9,$f),U("div",{id:`${V(Mr)}-${V(t)}`,style:{display:"none"}}," Press enter or space to select an edge. You can then press delete to remove it or press escape to cancel. ",8,Tf),V(n)?Pe("",!0):(re(),we("div",{key:0,id:`${V(ed)}-${V(t)}`,"aria-live":"assertive","aria-atomic":"true",style:{position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0px, 0px, 0px, 0px)","clip-path":"inset(100%)"}},Je(V(o)),9,Mf))],64))}});function Af(){const e=Oe();De(()=>e.viewportHelper.value.viewportInitialized,t=>{t&&setTimeout(()=>{e.emits.init(e),e.emits.paneReady(e)},1)})}function Pf(e,t,n){return n===te.Left?e-t:n===te.Right?e+t:e}function zf(e,t,n){return n===te.Top?e-t:n===te.Bottom?e+t:e}const Jo=function({radius:e=10,centerX:t=0,centerY:n=0,position:o=te.Top,type:i}){return Me("circle",{class:`vue-flow__edgeupdater vue-flow__edgeupdater-${i}`,cx:Pf(t,e,o),cy:zf(n,e,o),r:e,stroke:"transparent",fill:"transparent"})};Jo.props=["radius","centerX","centerY","position","type"];Jo.compatConfig={MODE:3};const Yi=Jo,Of=Fe({name:"Edge",compatConfig:{MODE:3},props:["id"],setup(e){const{id:t,addSelectedEdges:n,connectionMode:o,edgeUpdaterRadius:i,emits:r,nodesSelectionActive:a,noPanClassName:s,getEdgeTypes:u,removeSelectedEdges:l,findEdge:c,findNode:h,isValidConnection:v,multiSelectionActive:y,disableKeyboardA11y:M,elementsSelectable:S,edgesUpdatable:N,edgesFocusable:$,hooks:I}=Oe(),g=Ee(()=>c(e.id)),{emit:T,on:Y}=Md(g.value,r),D=Xt(Jn),A=cn(),O=me(!1),R=me(!1),G=me(""),F=me(null),x=me("source"),X=me(null),E=ze(()=>typeof g.value.selectable>"u"?S.value:g.value.selectable),z=ze(()=>typeof g.value.updatable>"u"?N.value:g.value.updatable),k=ze(()=>typeof g.value.focusable>"u"?$.value:g.value.focusable);Ht(Cd,e.id),Ht($d,X);const H=Ee(()=>g.value.class instanceof Function?g.value.class(g.value):g.value.class),L=Ee(()=>g.value.style instanceof Function?g.value.style(g.value):g.value.style),J=Ee(()=>{const p=g.value.type||"default",b=D?.[`edge-${p}`];if(b)return b;let w=g.value.template??u.value[p];if(typeof w=="string"&&A){const P=Object.keys(A.appContext.components);P&&P.includes(p)&&(w=Zi(p,!1))}return w&&typeof w!="string"?w:(r.error(new Ye(Re.EDGE_TYPE_MISSING,w)),!1)}),{handlePointerDown:W}=Zr({nodeId:G,handleId:F,type:x,isValidConnection:v,edgeUpdaterType:x,onEdgeUpdate:fe,onEdgeUpdateEnd:Q});return()=>{const p=h(g.value.source),b=h(g.value.target),w="pathOptions"in g.value?g.value.pathOptions:{};if(!p&&!b)return r.error(new Ye(Re.EDGE_SOURCE_TARGET_MISSING,g.value.id,g.value.source,g.value.target)),null;if(!p)return r.error(new Ye(Re.EDGE_SOURCE_MISSING,g.value.id,g.value.source)),null;if(!b)return r.error(new Ye(Re.EDGE_TARGET_MISSING,g.value.id,g.value.target)),null;if(!g.value||g.value.hidden||p.hidden||b.hidden)return null;let P;o.value===bt.Strict?P=p.handleBounds.source:P=[...p.handleBounds.source||[],...p.handleBounds.target||[]];const ne=Ii(P,g.value.sourceHandle);let oe;o.value===bt.Strict?oe=b.handleBounds.target:oe=[...b.handleBounds.target||[],...b.handleBounds.source||[]];const q=Ii(oe,g.value.targetHandle),ye=ne?.position||te.Bottom,ke=q?.position||te.Top,{x:Ce,y:Ae}=Gt(p,ne,ye),{x:je,y:dt}=Gt(b,q,ke);return g.value.sourceX=Ce,g.value.sourceY=Ae,g.value.targetX=je,g.value.targetY=dt,Me("g",{ref:X,key:e.id,"data-id":e.id,class:["vue-flow__edge",`vue-flow__edge-${J.value===!1?"default":g.value.type||"default"}`,s.value,H.value,{updating:O.value,selected:g.value.selected,animated:g.value.animated,inactive:!E.value&&!I.value.edgeClick.hasListeners()}],onClick:ce,onContextmenu:Se,onDblclick:Ne,onMouseenter:ve,onMousemove:$e,onMouseleave:he,onKeyDown:k.value?m:void 0,tabIndex:k.value?0:void 0,"aria-label":g.value.ariaLabel===null?void 0:g.value.ariaLabel||`Edge from ${g.value.source} to ${g.value.target}`,"aria-describedby":k.value?`${Mr}-${t}`:void 0,role:k.value?"button":"img"},[R.value?null:Me(J.value===!1?u.value.default:J.value,{id:e.id,sourceNode:p,targetNode:b,source:g.value.source,target:g.value.target,type:g.value.type,updatable:z.value,selected:g.value.selected,animated:g.value.animated,label:g.value.label,labelStyle:g.value.labelStyle,labelShowBg:g.value.labelShowBg,labelBgStyle:g.value.labelBgStyle,labelBgPadding:g.value.labelBgPadding,labelBgBorderRadius:g.value.labelBgBorderRadius,data:g.value.data,events:{...g.value.events,...Y},style:L.value,markerStart:`url('#${un(g.value.markerStart,t)}')`,markerEnd:`url('#${un(g.value.markerEnd,t)}')`,sourcePosition:ye,targetPosition:ke,sourceX:Ce,sourceY:Ae,targetX:je,targetY:dt,sourceHandleId:g.value.sourceHandle,targetHandleId:g.value.targetHandle,interactionWidth:g.value.interactionWidth,...w}),[z.value==="source"||z.value===!0?[Me("g",{onMousedown:xe,onMouseenter:se,onMouseout:pe},Me(Yi,{position:ye,centerX:Ce,centerY:Ae,radius:i.value,type:"source","data-type":"source"}))]:null,z.value==="target"||z.value===!0?[Me("g",{onMousedown:de,onMouseenter:se,onMouseout:pe},Me(Yi,{position:ke,centerX:je,centerY:dt,radius:i.value,type:"target","data-type":"target"}))]:null]])};function se(){O.value=!0}function pe(){O.value=!1}function fe(p,b){T.update({event:p,edge:g.value,connection:b})}function Q(p){T.updateEnd({event:p,edge:g.value}),R.value=!1}function ae(p,b){p.button===0&&(R.value=!0,G.value=b?g.value.target:g.value.source,F.value=(b?g.value.targetHandle:g.value.sourceHandle)??null,x.value=b?"target":"source",T.updateStart({event:p,edge:g.value}),W(p))}function ce(p){var b;const w={event:p,edge:g.value};E.value&&(a.value=!1,g.value.selected&&y.value?(l([g.value]),(b=X.value)==null||b.blur()):n([g.value])),T.click(w)}function Se(p){T.contextMenu({event:p,edge:g.value})}function Ne(p){T.doubleClick({event:p,edge:g.value})}function ve(p){T.mouseEnter({event:p,edge:g.value})}function $e(p){T.mouseMove({event:p,edge:g.value})}function he(p){T.mouseLeave({event:p,edge:g.value})}function xe(p){ae(p,!0)}function de(p){ae(p,!1)}function m(p){var b;!M.value&&Ir.includes(p.key)&&E.value&&(p.key==="Escape"?((b=X.value)==null||b.blur(),l([c(e.id)])):n([c(e.id)]))}}}),Ff=Of,Bf=Fe({name:"ConnectionLine",compatConfig:{MODE:3},setup(){var e;const{id:t,connectionMode:n,connectionStartHandle:o,connectionEndHandle:i,connectionPosition:r,connectionLineType:a,connectionLineStyle:s,connectionLineOptions:u,connectionStatus:l,viewport:c,findNode:h}=Oe(),v=(e=Xt(Jn))==null?void 0:e["connection-line"],y=Ee(()=>{var I;return h((I=o.value)==null?void 0:I.nodeId)}),M=Ee(()=>{var I;return h((I=i.value)==null?void 0:I.nodeId)??null}),S=Ee(()=>({x:(r.value.x-c.value.x)/c.value.zoom,y:(r.value.y-c.value.y)/c.value.zoom})),N=Ee(()=>u.value.markerStart?`url(#${un(u.value.markerStart,t)})`:""),$=Ee(()=>u.value.markerEnd?`url(#${un(u.value.markerEnd,t)})`:"");return()=>{var I,g,T;if(!y.value||!o.value)return null;const Y=o.value.id,D=o.value.type,A=y.value.handleBounds;let O=A?.[D]??[];if(n.value===bt.Loose){const L=A?.[D==="source"?"target":"source"]??[];O=[...O,...L]}if(!O)return null;const R=(Y?O.find(L=>L.id===Y):O[0])??null,G=R?.position??te.Top,{x:F,y:x}=Gt(y.value,R,G);let X=null;M.value&&(n.value===bt.Strict?X=((I=M.value.handleBounds[D==="source"?"target":"source"])==null?void 0:I.find(L=>{var J;return L.id===((J=i.value)==null?void 0:J.id)}))||null:X=((g=[...M.value.handleBounds.source??[],...M.value.handleBounds.target??[]])==null?void 0:g.find(L=>{var J;return L.id===((J=i.value)==null?void 0:J.id)}))||null);const E=((T=i.value)==null?void 0:T.position)??(G?zo[G]:null);if(!G||!E)return null;const z=a.value??u.value.type??kt.Bezier;let k="";const H={sourceX:F,sourceY:x,sourcePosition:G,targetX:S.value.x,targetY:S.value.y,targetPosition:E};return z===kt.Bezier?[k]=ea(H):z===kt.Step?[k]=Fo({...H,borderRadius:0}):z===kt.SmoothStep?[k]=Fo(H):z===kt.SimpleBezier?[k]=ta(H):k=`M${F},${x} ${S.value.x},${S.value.y}`,Me("svg",{class:"vue-flow__edges vue-flow__connectionline vue-flow__container"},Me("g",{class:"vue-flow__connection"},v?Me(v,{sourceX:F,sourceY:x,sourcePosition:G,targetX:S.value.x,targetY:S.value.y,targetPosition:E,sourceNode:y.value,sourceHandle:R,targetNode:M.value,targetHandle:X,markerEnd:$.value,markerStart:N.value,connectionStatus:l.value}):Me("path",{d:k,class:[u.value.class,l,"vue-flow__connection-path"],style:{...s.value,...u.value.style},"marker-end":$.value,"marker-start":N.value})))}}}),Vf=Bf,Hf=["id","markerWidth","markerHeight","markerUnits","orient"],Rf={name:"MarkerType",compatConfig:{MODE:3}},Lf=Fe({...Rf,props:{id:{},type:{},color:{default:"none"},width:{default:12.5},height:{default:12.5},markerUnits:{default:"strokeWidth"},orient:{default:"auto-start-reverse"},strokeWidth:{default:1}},setup(e){return(t,n)=>(re(),we("marker",{id:t.id,class:"vue-flow__arrowhead",viewBox:"-10 -10 20 20",refX:"0",refY:"0",markerWidth:`${t.width}`,markerHeight:`${t.height}`,markerUnits:t.markerUnits,orient:t.orient},[t.type===V(Do).ArrowClosed?(re(),we("polyline",{key:0,style:Ge({stroke:t.color,fill:t.color,strokeWidth:t.strokeWidth}),"stroke-linecap":"round","stroke-linejoin":"round",points:"-5,-4 0,0 -5,4 -5,-4"},null,4)):Pe("",!0),t.type===V(Do).Arrow?(re(),we("polyline",{key:1,style:Ge({stroke:t.color,strokeWidth:t.strokeWidth}),"stroke-linecap":"round","stroke-linejoin":"round",fill:"none",points:"-5,-4 0,0 -5,4"},null,4)):Pe("",!0)],8,Hf))}}),Yf={class:"vue-flow__marker vue-flow__container","aria-hidden":"true"},Gf={name:"MarkerDefinitions",compatConfig:{MODE:3}},Xf=Fe({...Gf,setup(e){const{id:t,edges:n,connectionLineOptions:o,defaultMarkerColor:i}=Oe(),r=Ee(()=>{const a=new Set,s=[],u=l=>{if(l){const c=un(l,t);a.has(c)||(typeof l=="object"?s.push({...l,id:c,color:l.color||i.value}):s.push({id:c,color:i.value,type:l}),a.add(c))}};for(const l of[o.value.markerEnd,o.value.markerStart])u(l);for(const l of n.value)for(const c of[l.markerStart,l.markerEnd])u(c);return s.sort((l,c)=>l.id.localeCompare(c.id))});return(a,s)=>(re(),we("svg",Yf,[U("defs",null,[(re(!0),we(Ze,null,Rn(r.value,u=>(re(),We(Lf,{id:u.id,key:u.id,type:u.type,color:u.color,width:u.width,height:u.height,markerUnits:u.markerUnits,"stroke-width":u.strokeWidth,orient:u.orient},null,8,["id","type","color","width","height","markerUnits","stroke-width","orient"]))),128))])]))}}),Wf={name:"Edges",compatConfig:{MODE:3}},Uf=Fe({...Wf,setup(e){const{findNode:t,getEdges:n,elevateEdgesOnSelect:o}=Oe();return(i,r)=>(re(),we(Ze,null,[Z(Xf),(re(!0),we(Ze,null,Rn(V(n),a=>(re(),we("svg",{key:a.id,class:"vue-flow__edges vue-flow__container",style:Ge({zIndex:V(pd)(a,V(t),V(o))})},[Z(V(Ff),{id:a.id},null,8,["id"])],4))),128)),Z(V(Vf))],64))}}),Zf=Fe({name:"Node",compatConfig:{MODE:3},props:["id","resizeObserver"],setup(e){const{id:t,noPanClassName:n,selectNodesOnDrag:o,nodesSelectionActive:i,multiSelectionActive:r,emits:a,removeSelectedNodes:s,addSelectedNodes:u,updateNodeDimensions:l,onUpdateNodeInternals:c,getNodeTypes:h,nodeExtent:v,elevateNodesOnSelect:y,disableKeyboardA11y:M,ariaLiveMessage:S,snapToGrid:N,snapGrid:$,nodeDragThreshold:I,nodesDraggable:g,elementsSelectable:T,nodesConnectable:Y,nodesFocusable:D,hooks:A}=Oe(),O=me(null);Ht(Wr,O),Ht(Xr,e.id);const R=Xt(Jn),G=cn(),F=qr(),{node:x,parentNode:X}=Kr(e.id),{emit:E,on:z}=Pd(x,a),k=ze(()=>typeof x.draggable>"u"?g.value:x.draggable),H=ze(()=>typeof x.selectable>"u"?T.value:x.selectable),L=ze(()=>typeof x.connectable>"u"?Y.value:x.connectable),J=ze(()=>typeof x.focusable>"u"?D.value:x.focusable),W=Ee(()=>H.value||k.value||A.value.nodeClick.hasListeners()||A.value.nodeDoubleClick.hasListeners()||A.value.nodeMouseEnter.hasListeners()||A.value.nodeMouseMove.hasListeners()||A.value.nodeMouseLeave.hasListeners()),se=ze(()=>!!x.dimensions.width&&!!x.dimensions.height),pe=Ee(()=>{const b=x.type||"default",w=R?.[`node-${b}`];if(w)return w;let P=x.template||h.value[b];if(typeof P=="string"&&G){const ne=Object.keys(G.appContext.components);ne&&ne.includes(b)&&(P=Zi(b,!1))}return P&&typeof P!="string"?P:(a.error(new Ye(Re.NODE_TYPE_MISSING,P)),!1)}),fe=Ur({id:e.id,el:O,disabled:()=>!k.value,selectable:H,dragHandle:()=>x.dragHandle,onStart(b){E.dragStart(b)},onDrag(b){E.drag(b)},onStop(b){E.dragStop(b)},onClick(b){m(b)}}),Q=Ee(()=>x.class instanceof Function?x.class(x):x.class),ae=Ee(()=>{const b=(x.style instanceof Function?x.style(x):x.style)||{},w=x.width instanceof Function?x.width(x):x.width,P=x.height instanceof Function?x.height(x):x.height;return!b.width&&w&&(b.width=typeof w=="string"?w:`${w}px`),!b.height&&P&&(b.height=typeof P=="string"?P:`${P}px`),b}),ce=ze(()=>Number(x.zIndex??ae.value.zIndex??0));return c(b=>{(b.includes(e.id)||!b.length)&&Ne()}),ut(()=>{De(()=>x.hidden,(b=!1,w,P)=>{!b&&O.value&&(e.resizeObserver.observe(O.value),P(()=>{O.value&&e.resizeObserver.unobserve(O.value)}))},{immediate:!0,flush:"post"})}),De([()=>x.type,()=>x.sourcePosition,()=>x.targetPosition],()=>{Le(()=>{l([{id:e.id,nodeElement:O.value,forceUpdate:!0}])})}),De([()=>x.position.x,()=>x.position.y,()=>{var b;return(b=X.value)==null?void 0:b.computedPosition.x},()=>{var b;return(b=X.value)==null?void 0:b.computedPosition.y},()=>{var b;return(b=X.value)==null?void 0:b.computedPosition.z},ce,()=>x.selected,()=>x.dimensions.height,()=>x.dimensions.width,()=>{var b;return(b=X.value)==null?void 0:b.dimensions.height},()=>{var b;return(b=X.value)==null?void 0:b.dimensions.width}],([b,w,P,ne,oe,q])=>{const ye={x:b,y:w,z:q+(y.value&&x.selected?1e3:0)};typeof P<"u"&&typeof ne<"u"?x.computedPosition=ld({x:P,y:ne,z:oe},ye):x.computedPosition=ye},{flush:"post",immediate:!0}),De([()=>x.extent,v],([b,w],[P,ne])=>{(b!==P||w!==ne)&&Se()}),x.extent==="parent"||typeof x.extent=="object"&&"range"in x.extent&&x.extent.range==="parent"?xo(()=>se).toBe(!0).then(Se):Se(),()=>x.hidden?null:Me("div",{ref:O,"data-id":x.id,class:["vue-flow__node",`vue-flow__node-${pe.value===!1?"default":x.type||"default"}`,{[n.value]:k.value,dragging:fe?.value,draggable:k.value,selected:x.selected,selectable:H.value,parent:x.isParent},Q.value],style:{visibility:se.value?"visible":"hidden",zIndex:x.computedPosition.z??ce.value,transform:`translate(${x.computedPosition.x}px,${x.computedPosition.y}px)`,pointerEvents:W.value?"all":"none",...ae.value},tabIndex:J.value?0:void 0,role:J.value?"button":void 0,"aria-describedby":M.value?void 0:`${Tr}-${t}`,"aria-label":x.ariaLabel,onMouseenter:ve,onMousemove:$e,onMouseleave:he,onContextmenu:xe,onClick:m,onDblclick:de,onKeydown:p},[Me(pe.value===!1?h.value.default:pe.value,{id:x.id,type:x.type,data:x.data,events:{...x.events,...z},selected:x.selected,resizing:x.resizing,dragging:fe.value,connectable:L.value,position:x.computedPosition,dimensions:x.dimensions,isValidTargetPos:x.isValidTargetPos,isValidSourcePos:x.isValidSourcePos,parent:x.parentNode,parentNodeId:x.parentNode,zIndex:x.computedPosition.z??ce.value,targetPosition:x.targetPosition,sourcePosition:x.sourcePosition,label:x.label,dragHandle:x.dragHandle,onUpdateNodeInternals:Ne})]);function Se(){const b=x.computedPosition,{computedPosition:w,position:P}=Zo(x,N.value?Kn(b,$.value):b,a.error,v.value,X.value);(x.computedPosition.x!==w.x||x.computedPosition.y!==w.y)&&(x.computedPosition={...x.computedPosition,...w}),(x.position.x!==P.x||x.position.y!==P.y)&&(x.position=P)}function Ne(){O.value&&l([{id:e.id,nodeElement:O.value,forceUpdate:!0}])}function ve(b){fe?.value||E.mouseEnter({event:b,node:x})}function $e(b){fe?.value||E.mouseMove({event:b,node:x})}function he(b){fe?.value||E.mouseLeave({event:b,node:x})}function xe(b){return E.contextMenu({event:b,node:x})}function de(b){return E.doubleClick({event:b,node:x})}function m(b){H.value&&(!o.value||!k.value||I.value>0)&&Oo(x,r.value,u,s,i,!1,O.value),E.click({event:b,node:x})}function p(b){if(!(Ao(b)||M.value))if(Ir.includes(b.key)&&H.value){const w=b.key==="Escape";Oo(x,r.value,u,s,i,w,O.value)}else k.value&&x.selected&&Vt[b.key]&&(b.preventDefault(),S.value=`Moved selected node ${b.key.replace("Arrow","").toLowerCase()}. New position, x: ${~~x.position.x}, y: ${~~x.position.y}`,F({x:Vt[b.key].x,y:Vt[b.key].y},b.shiftKey))}}}),Kf=Zf;function qf(e={includeHiddenNodes:!1}){const{nodes:t}=Oe();return Ee(()=>{if(t.value.length===0)return!1;for(const n of t.value)if((e.includeHiddenNodes||!n.hidden)&&(n?.handleBounds===void 0||n.dimensions.width===0||n.dimensions.height===0))return!1;return!0})}const Jf={class:"vue-flow__nodes vue-flow__container"},Qf={name:"Nodes",compatConfig:{MODE:3}},jf=Fe({...Qf,setup(e){const{getNodes:t,updateNodeDimensions:n,emits:o}=Oe(),i=qf(),r=me();return De(i,a=>{a&&Le(()=>{o.nodesInitialized(t.value)})},{immediate:!0}),ut(()=>{r.value=new ResizeObserver(a=>{const s=a.map(u=>({id:u.target.getAttribute("data-id"),nodeElement:u.target,forceUpdate:!0}));Le(()=>n(s))})}),Ui(()=>{var a;return(a=r.value)==null?void 0:a.disconnect()}),(a,s)=>(re(),we("div",Jf,[r.value?(re(!0),we(Ze,{key:0},Rn(V(t),(u,l,c,h)=>{const v=[u.id];if(h&&h.key===u.id&&Ta(h,v))return h;const y=(re(),We(V(Kf),{id:u.id,key:u.id,"resize-observer":r.value},null,8,["id","resize-observer"]));return y.memo=v,y},s,0),128)):Pe("",!0)]))}});function eh(){const{emits:e}=Oe();ut(()=>{if(Gr()){const t=document.querySelector(".vue-flow__pane");t&&window.getComputedStyle(t).zIndex!=="1"&&e.error(new Ye(Re.MISSING_STYLES))}})}const th=U("div",{class:"vue-flow__edge-labels"},null,-1),nh={name:"VueFlow",compatConfig:{MODE:3}},oh=Fe({...nh,props:{id:{},modelValue:{},nodes:{},edges:{},edgeTypes:{},nodeTypes:{},connectionMode:{},connectionLineType:{},connectionLineStyle:{default:void 0},connectionLineOptions:{default:void 0},connectionRadius:{},isValidConnection:{type:[Function,null],default:void 0},deleteKeyCode:{default:void 0},selectionKeyCode:{type:[Boolean,null],default:void 0},multiSelectionKeyCode:{default:void 0},zoomActivationKeyCode:{default:void 0},panActivationKeyCode:{default:void 0},snapToGrid:{type:Boolean,default:void 0},snapGrid:{},onlyRenderVisibleElements:{type:Boolean,default:void 0},edgesUpdatable:{type:[Boolean,String],default:void 0},nodesDraggable:{type:Boolean,default:void 0},nodesConnectable:{type:Boolean,default:void 0},nodeDragThreshold:{},elementsSelectable:{type:Boolean,default:void 0},selectNodesOnDrag:{type:Boolean,default:void 0},panOnDrag:{type:[Boolean,Array],default:void 0},minZoom:{},maxZoom:{},defaultViewport:{},translateExtent:{},nodeExtent:{},defaultMarkerColor:{},zoomOnScroll:{type:Boolean,default:void 0},zoomOnPinch:{type:Boolean,default:void 0},panOnScroll:{type:Boolean,default:void 0},panOnScrollSpeed:{},panOnScrollMode:{},paneClickDistance:{},zoomOnDoubleClick:{type:Boolean,default:void 0},preventScrolling:{type:Boolean,default:void 0},selectionMode:{},edgeUpdaterRadius:{},fitViewOnInit:{type:Boolean,default:void 0},connectOnClick:{type:Boolean,default:void 0},applyDefault:{type:Boolean,default:void 0},autoConnect:{type:[Boolean,Function],default:void 0},noDragClassName:{},noWheelClassName:{},noPanClassName:{},defaultEdgeOptions:{},elevateEdgesOnSelect:{type:Boolean,default:void 0},elevateNodesOnSelect:{type:Boolean,default:void 0},disableKeyboardA11y:{type:Boolean,default:void 0},edgesFocusable:{type:Boolean,default:void 0},nodesFocusable:{type:Boolean,default:void 0},autoPanOnConnect:{type:Boolean,default:void 0},autoPanOnNodeDrag:{type:Boolean,default:void 0},autoPanSpeed:{}},emits:["nodesChange","edgesChange","nodesInitialized","paneReady","init","updateNodeInternals","error","connect","connectStart","connectEnd","clickConnectStart","clickConnectEnd","moveStart","move","moveEnd","selectionDragStart","selectionDrag","selectionDragStop","selectionContextMenu","selectionStart","selectionEnd","viewportChangeStart","viewportChange","viewportChangeEnd","paneScroll","paneClick","paneContextMenu","paneMouseEnter","paneMouseMove","paneMouseLeave","edgeUpdate","edgeContextMenu","edgeMouseEnter","edgeMouseMove","edgeMouseLeave","edgeDoubleClick","edgeClick","edgeUpdateStart","edgeUpdateEnd","nodeContextMenu","nodeMouseEnter","nodeMouseMove","nodeMouseLeave","nodeDoubleClick","nodeClick","nodeDragStart","nodeDrag","nodeDragStop","miniMapNodeClick","miniMapNodeDoubleClick","miniMapNodeMouseEnter","miniMapNodeMouseMove","miniMapNodeMouseLeave","update:modelValue","update:nodes","update:edges"],setup(e,{expose:t,emit:n}){const o=e,i=Sa(),r=uo(o,"modelValue",n),a=uo(o,"nodes",n),s=uo(o,"edges",n),u=Oe(o),l=Bd({modelValue:r,nodes:a,edges:s},o,u);return Hd(n,u.hooks),Af(),eh(),Ht(Jn,i),Bo(()=>{l()}),t(u),(c,h)=>(re(),we("div",{ref:V(u).vueFlowRef,class:"vue-flow"},[Z(Cf,null,{default:ge(()=>[Z(Uf),th,Z(jf),Ve(c.$slots,"zoom-pane")]),_:3}),Ve(c.$slots,"default"),Z(Df)],512))}}),ih={name:"Panel",compatConfig:{MODE:3}},aa=Fe({...ih,props:{position:{}},setup(e){const t=e,{userSelectionActive:n}=Oe(),o=Ee(()=>`${t.position}`.split("-"));return(i,r)=>(re(),we("div",{class:mt(["vue-flow__panel",o.value]),style:Ge({pointerEvents:V(n)?"none":"all"})},[Ve(i.$slots,"default")],6))}});var gt=(e=>(e.Lines="lines",e.Dots="dots",e))(gt||{});const sa=function({dimensions:e,size:t,color:n}){return Me("path",{stroke:n,"stroke-width":t,d:`M${e[0]/2} 0 V${e[1]} M0 ${e[1]/2} H${e[0]}`})},la=function({radius:e,color:t}){return Me("circle",{cx:e,cy:e,r:e,fill:t})};gt.Lines+"",gt.Dots+"";const rh={[gt.Dots]:"#81818a",[gt.Lines]:"#eee"},ah=["id","x","y","width","height","patternTransform"],sh={key:2,height:"100",width:"100"},lh=["fill"],uh=["x","y","fill"],ch={name:"Background",compatConfig:{MODE:3}},dh=Fe({...ch,props:{id:{},variant:{default:()=>gt.Dots},gap:{default:20},size:{default:1},lineWidth:{default:1},patternColor:{},color:{},bgColor:{},height:{default:100},width:{default:100},x:{default:0},y:{default:0},offset:{default:0}},setup(e){const{id:t,viewport:n}=Oe(),o=Ee(()=>{const a=n.value.zoom,[s,u]=Array.isArray(e.gap)?e.gap:[e.gap,e.gap],l=[s*a||1,u*a||1],c=e.size*a,[h,v]=Array.isArray(e.offset)?e.offset:[e.offset,e.offset],y=[h*a||1+l[0]/2,v*a||1+l[1]/2];return{scaledGap:l,offset:y,size:c}}),i=ze(()=>`pattern-${t}${e.id?`-${e.id}`:""}`),r=ze(()=>e.color||e.patternColor||rh[e.variant||gt.Dots]);return(a,s)=>(re(),we("svg",{class:"vue-flow__background vue-flow__container",style:Ge({height:`${a.height>100?100:a.height}%`,width:`${a.width>100?100:a.width}%`})},[Ve(a.$slots,"pattern-container",{id:i.value},()=>[U("pattern",{id:i.value,x:V(n).x%o.value.scaledGap[0],y:V(n).y%o.value.scaledGap[1],width:o.value.scaledGap[0],height:o.value.scaledGap[1],patternTransform:`translate(-${o.value.offset[0]},-${o.value.offset[1]})`,patternUnits:"userSpaceOnUse"},[Ve(a.$slots,"pattern",{},()=>[a.variant===V(gt).Lines?(re(),We(V(sa),{key:0,size:a.lineWidth,color:r.value,dimensions:o.value.scaledGap},null,8,["size","color","dimensions"])):a.variant===V(gt).Dots?(re(),We(V(la),{key:1,color:r.value,radius:o.value.size/2},null,8,["color","radius"])):Pe("",!0),a.bgColor?(re(),we("svg",sh,[U("rect",{width:"100%",height:"100%",fill:a.bgColor},null,8,lh)])):Pe("",!0)])],8,ah)]),U("rect",{x:a.x,y:a.y,width:"100%",height:"100%",fill:`url(#${i.value})`},null,8,uh),Ve(a.$slots,"default",{id:i.value})],4))}}),fh={name:"ControlButton",compatConfig:{MODE:3}},hh=(e,t)=>{const n=e.__vccOpts||e;for(const[o,i]of t)n[o]=i;return n},ph={class:"vue-flow__controls-button"};function gh(e,t,n,o,i,r){return re(),we("button",ph,[Ve(e.$slots,"default")])}const xn=hh(fh,[["render",gh]]),mh={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32"},vh=U("path",{d:"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"},null,-1),yh=[vh];function wh(e,t){return re(),we("svg",mh,yh)}const _h={render:wh},bh={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 5"},Eh=U("path",{d:"M0 0h32v4.2H0z"},null,-1),xh=[Eh];function Sh(e,t){return re(),we("svg",bh,xh)}const kh={render:Sh},Nh={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 30"},Ch=U("path",{d:"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0 0 27.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94a.919.919 0 0 1-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"},null,-1),$h=[Ch];function Th(e,t){return re(),we("svg",Nh,$h)}const Mh={render:Th},Ih={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},Dh=U("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 0 0 0 13.714v15.238A3.056 3.056 0 0 0 3.048 32h18.285a3.056 3.056 0 0 0 3.048-3.048V13.714a3.056 3.056 0 0 0-3.048-3.047zM12.19 24.533a3.056 3.056 0 0 1-3.047-3.047 3.056 3.056 0 0 1 3.047-3.048 3.056 3.056 0 0 1 3.048 3.048 3.056 3.056 0 0 1-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"},null,-1),Ah=[Dh];function Ph(e,t){return re(),we("svg",Ih,Ah)}const zh={render:Ph},Oh={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},Fh=U("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 0 0 0 13.714v15.238A3.056 3.056 0 0 0 3.048 32h18.285a3.056 3.056 0 0 0 3.048-3.048V13.714a3.056 3.056 0 0 0-3.048-3.047zM12.19 24.533a3.056 3.056 0 0 1-3.047-3.047 3.056 3.056 0 0 1 3.047-3.048 3.056 3.056 0 0 1 3.048 3.048 3.056 3.056 0 0 1-3.048 3.047z"},null,-1),Bh=[Fh];function Vh(e,t){return re(),we("svg",Oh,Bh)}const Hh={render:Vh},Rh={name:"Controls",compatConfig:{MODE:3}},Lh=Fe({...Rh,props:{showZoom:{type:Boolean,default:!0},showFitView:{type:Boolean,default:!0},showInteractive:{type:Boolean,default:!0},fitViewParams:{},position:{default:()=>$r.BottomLeft}},emits:["zoomIn","zoomOut","fitView","interactionChange"],setup(e,{emit:t}){const{nodesDraggable:n,nodesConnectable:o,elementsSelectable:i,setInteractive:r,zoomIn:a,zoomOut:s,fitView:u,viewport:l,minZoom:c,maxZoom:h}=Oe(),v=ze(()=>n.value||o.value||i.value),y=ze(()=>l.value.zoom<=c.value),M=ze(()=>l.value.zoom>=h.value);function S(){a(),t("zoomIn")}function N(){s(),t("zoomOut")}function $(){u(e.fitViewParams),t("fitView")}function I(){r(!v.value),t("interactionChange",!v.value)}return(g,T)=>(re(),We(V(aa),{class:"vue-flow__controls",position:g.position},{default:ge(()=>[Ve(g.$slots,"top"),g.showZoom?(re(),we(Ze,{key:0},[Ve(g.$slots,"control-zoom-in",{},()=>[Z(xn,{class:"vue-flow__controls-zoomin",disabled:M.value,onClick:S},{default:ge(()=>[Ve(g.$slots,"icon-zoom-in",{},()=>[(re(),We(Ot(V(_h))))])]),_:3},8,["disabled"])]),Ve(g.$slots,"control-zoom-out",{},()=>[Z(xn,{class:"vue-flow__controls-zoomout",disabled:y.value,onClick:N},{default:ge(()=>[Ve(g.$slots,"icon-zoom-out",{},()=>[(re(),We(Ot(V(kh))))])]),_:3},8,["disabled"])])],64)):Pe("",!0),g.showFitView?Ve(g.$slots,"control-fit-view",{key:1},()=>[Z(xn,{class:"vue-flow__controls-fitview",onClick:$},{default:ge(()=>[Ve(g.$slots,"icon-fit-view",{},()=>[(re(),We(Ot(V(Mh))))])]),_:3})]):Pe("",!0),g.showInteractive?Ve(g.$slots,"control-interactive",{key:2},()=>[g.showInteractive?(re(),We(xn,{key:0,class:"vue-flow__controls-interactive",onClick:I},{default:ge(()=>[v.value?Ve(g.$slots,"icon-unlock",{key:0},()=>[(re(),We(Ot(V(Hh))))]):Pe("",!0),v.value?Pe("",!0):Ve(g.$slots,"icon-lock",{key:1},()=>[(re(),We(Ot(V(zh))))])]),_:3})):Pe("",!0)]):Pe("",!0),Ve(g.$slots,"default")]),_:3},8,["position"]))}}),Yh={class:"main-event-content"},Gh={key:0,class:"event-date"},Xh={class:"event-title"},Wh={key:1,class:"event-desc"},Uh={class:"handles-container"},Zh={__name:"MainEventNode",props:{id:{type:String,required:!0},data:{type:Object,required:!0},selected:{type:Boolean,default:!1}},setup(e){const t=e,n=Ee(()=>{if(t.data.useCustomTime&&t.data.customTime)return t.data.customTime;const{year:o,month:i,day:r}=t.data;return o?`${o}/${i||""}/${r||""}`:""});return(o,i)=>(re(),we("div",{class:mt(["main-event-node",{selected:e.selected}])},[U("div",Yh,[n.value?(re(),we("div",Gh,Je(n.value),1)):Pe("",!0),U("div",Xh,Je(e.data.label),1),e.data.content?(re(),we("div",Wh,Je(e.data.content),1)):Pe("",!0)]),U("div",Uh,[Z(V(Ke),{id:"left",type:"source",position:V(te).Left,class:"main-handle left-handle"},null,8,["position"]),Z(V(Ke),{id:"left-target",type:"target",position:V(te).Left,class:"main-handle left-handle"},null,8,["position"]),Z(V(Ke),{id:"right",type:"source",position:V(te).Right,class:"main-handle right-handle"},null,8,["position"]),Z(V(Ke),{id:"right-target",type:"target",position:V(te).Right,class:"main-handle right-handle"},null,8,["position"]),Z(V(Ke),{id:"top",type:"target",position:V(te).Top,class:"main-handle top-handle"},null,8,["position"]),Z(V(Ke),{id:"bottom",type:"source",position:V(te).Bottom,class:"main-handle bottom-handle"},null,8,["position"])])],2))}},Kh=Ln(Zh,[["__scopeId","data-v-52d9f0fd"]]),qh={class:"branch-event-content"},Jh={key:0,class:"event-date"},Qh={class:"event-title"},jh={key:1,class:"event-desc"},ep={__name:"BranchEventNode",props:{id:{type:String,required:!0},data:{type:Object,required:!0},selected:{type:Boolean,default:!1}},setup(e){const t=e,n=Ee(()=>{if(t.data.useCustomTime&&t.data.customTime)return t.data.customTime;const{year:r,month:a,day:s}=t.data;return r?`${r}/${a||""}/${s||""}`:""}),o=Ee(()=>t.data.isLeftSide!==void 0?t.data.isLeftSide:!1),i=Ee(()=>({borderColor:t.data.color||"#e84393",backgroundColor:`${t.data.color||"#e84393"}22`}));return(r,a)=>(re(),we("div",{class:mt(["branch-event-node",{selected:e.selected,"left-side":o.value,"right-side":!o.value}]),style:Ge(i.value)},[Z(V(Ke),{id:"left",type:"source",position:V(te).Left,class:"branch-handle",style:Ge({background:e.data.color})},null,8,["position","style"]),U("div",qh,[n.value?(re(),we("div",Jh,Je(n.value),1)):Pe("",!0),U("div",Qh,Je(e.data.label),1),e.data.content?(re(),we("div",jh,Je(e.data.content),1)):Pe("",!0)]),Z(V(Ke),{id:"right",type:"source",position:V(te).Right,class:"branch-handle",style:Ge({background:e.data.color})},null,8,["position","style"]),Z(V(Ke),{id:"top",type:"source",position:V(te).Top,class:"branch-handle",style:Ge({background:e.data.color})},null,8,["position","style"]),Z(V(Ke),{id:"bottom",type:"source",position:V(te).Bottom,class:"branch-handle",style:Ge({background:e.data.color})},null,8,["position","style"])],6))}},tp=Ln(ep,[["__scopeId","data-v-87441d98"]]),np={class:"timeline-flow-wrapper"},op={class:"panel-buttons"},ip={class:"custom-dialog-body"},rp={class:"time-mode-switch"},ap={key:0,class:"date-inputs"},sp={key:1,class:"custom-time-inputs"},lp={class:"color-picker-container"},up={class:"color-meaning-hint"},cp={class:"color-meanings-popover"},dp=["onClick"],fp={class:"color-desc"},hp={class:"custom-dialog-footer"},pp={class:"custom-dialog-header"},gp={class:"custom-dialog-body"},mp={class:"custom-dialog-footer"},vp={class:"custom-dialog-header"},yp={class:"custom-dialog-body"},wp={class:"import-container"},_p={class:"custom-dialog-footer"},Be=400,bp={__name:"TimelineFlow",setup(e,{expose:t}){const n={Straight:"straight",SmoothStep:"smoothstep",SimpleBezier:"simplebezier",Bezier:"bezier",Step:"step"},o={Arrow:"arrow",ArrowClosed:"arrowclosed"},i=me([]),r=me([]),a=me(!1);me(!1),me(null);const s=me(null),u=me(!1),l=me({}),c=me(""),h=me(!1),v=me({top:"0px",left:"0px"}),y=me(""),M=me(""),S=me(""),N=me(""),$={type:n.SmoothStep,animated:!0,style:{cursor:"move",strokeWidth:2},updatable:!0,draggable:!0},I=Oe(),{findNode:g,getSelectedNodes:T,addNodes:Y,addEdges:D,removeNodes:A,removeEdges:O,updateEdge:R,updateNode:G,fitView:F}=I,x=()=>{try{const f=I.getNodes.value;return Array.isArray(f)?f:[]}catch(f){return console.error("获取节点失败:",f),[]}},X=()=>{try{const f=I.getEdges.value;return Array.isArray(f)?f:[]}catch(f){return console.error("获取边失败:",f),[]}},E=f=>{try{return X().find(_=>_.id===f)}catch(d){return console.error("查找边时出错:",d),null}},z=(f,d)=>{try{const _=g(f);if(!_){console.error(`找不到ID为${f}的节点`);return}_.position={...d},G(f,{..._,position:d})}catch(_){console.error("更新节点位置时出错:",_)}},k=[{id:"main-1",type:"main-event",position:{x:Be,y:100},data:{label:"故事开始",year:"2020",month:"1",day:"1",content:"主角踏上旅程",nodeType:"main",color:"#409EFF"}},{id:"main-2",type:"main-event",position:{x:Be,y:250},data:{label:"主角出发",year:"2021",month:"6",day:"15",content:"主角面临挑战",nodeType:"main",color:"#409EFF"}},{id:"main-3",type:"main-event",position:{x:Be,y:400},data:{label:"第一次危机",year:"2022",month:"3",day:"10",content:"主角遇到危机",nodeType:"main",color:"#409EFF"}},{id:"branch-1",type:"branch-event",position:{x:Be+250,y:100},data:{label:"初遇",year:"2020",month:"3",day:"15",content:"与女主角相遇",nodeType:"branch",color:"#e84393",parentId:"main-1",isLeftSide:!1},connectable:!0},{id:"branch-2",type:"branch-event",position:{x:Be-250,y:250},data:{label:"挫折",year:"2021",month:"5",day:"20",content:"遭遇挫折",nodeType:"branch",color:"#00b894",parentId:"main-2",isLeftSide:!0},connectable:!0}],H=[{id:"e-main1-main2",source:"main-1",target:"main-2",sourceHandle:"bottom",targetHandle:"top",type:n.SmoothStep,style:{strokeWidth:4,stroke:"#409EFF"},markerEnd:{type:o.ArrowClosed,color:"#409EFF"},updatable:!0,draggable:!0},{id:"e-main2-main3",source:"main-2",target:"main-3",sourceHandle:"bottom",targetHandle:"top",type:n.SmoothStep,style:{strokeWidth:4,stroke:"#409EFF"},markerEnd:{type:o.ArrowClosed,color:"#409EFF"},updatable:!0,draggable:!0},{id:"e-main1-branch1",source:"main-1",sourceHandle:"right",target:"branch-1",targetHandle:"left",type:n.SmoothStep,animated:!0,style:{strokeWidth:3,stroke:"#e84393"},markerEnd:{type:o.ArrowClosed,color:"#e84393"}},{id:"e-main2-branch2",source:"main-2",sourceHandle:"left",target:"branch-2",targetHandle:"right",type:n.SmoothStep,animated:!0,style:{strokeWidth:3,stroke:"#00b894"},markerEnd:{type:o.ArrowClosed,color:"#00b894"}}],L=me(!1),J=()=>{const f=document.querySelector(".timeline-flow-wrapper");document.fullscreenElement?(document.exitFullscreen?document.exitFullscreen():document.webkitExitFullscreen?document.webkitExitFullscreen():document.msExitFullscreen&&document.msExitFullscreen(),L.value=!1):(f.requestFullscreen?f.requestFullscreen():f.webkitRequestFullscreen?f.webkitRequestFullscreen():f.msRequestFullscreen&&f.msRequestFullscreen(),L.value=!0),Le(()=>{F()})};ut(()=>{i.value=k,r.value=H,document.addEventListener("click",()=>{h.value=!1}),document.addEventListener("keydown",ti),document.addEventListener("fullscreenchange",W),document.addEventListener("webkitfullscreenchange",W),document.addEventListener("mozfullscreenchange",W),document.addEventListener("MSFullscreenChange",W),console.log("TimelineFlow组件已挂载，节点数:",i.value.length,"边数:",r.value.length),Le(()=>{F()}),window.addEventListener("resize",se)}),Bo(()=>{window.removeEventListener("resize",se),document.removeEventListener("keydown",ti),document.removeEventListener("fullscreenchange",W),document.removeEventListener("webkitfullscreenchange",W),document.removeEventListener("mozfullscreenchange",W),document.removeEventListener("MSFullscreenChange",W)});const W=()=>{L.value=!!document.fullscreenElement||!!document.webkitFullscreenElement||!!document.mozFullScreenElement||!!document.msFullscreenElement,Le(()=>{F()})},se=()=>{pe.value&&clearTimeout(pe.value),pe.value=setTimeout(()=>{console.log("窗口大小变化，重新适应视图"),F()},200)},pe=me(null),fe=()=>{const f=["#e84393","#00b894","#e17055","#6c5ce7","#fdcb6e","#00cec9","#ff7675","#74b9ff"];return f[Math.floor(Math.random()*f.length)]},Q=f=>{At()},ae=f=>{console.log("连接变化:",f),At()},ce=f=>{try{const d=f.node;s.value=d,d.data.nodeType==="main"&&(d.position.x=Be,console.log(`主干节点 ${d.id} 拖动中, Y位置: ${d.position.y}`))}catch(d){console.error("节点拖动时出错:",d)}},Se=(f,d)=>{try{const _=g(f),B=g(d);if(!_||!B){console.error("无法找到节点:",f,d);return}const j=X().find(ue=>ue.source===d&&ue.target===f||ue.source===f&&ue.target===d);if(j){const ue={...j,style:{...j.style}};R(j,ue),console.log(`已刷新分支到主干连接: ${j.id}`)}else{const ue=_.data.isLeftSide,ie={id:`e-${d}-${f}-${Date.now()}`,source:d,sourceHandle:ue?"left":"right",target:f,targetHandle:ue?"right":"left",type:n.SmoothStep,animated:!0,style:{strokeWidth:3,stroke:_.data.color||fe()},markerEnd:{type:o.ArrowClosed,color:_.data.color||fe()}};D([ie]),G(f,{..._,data:{..._.data,parentId:d}}),console.log(`已创建新的分支到主干连接: ${ie.id}`)}}catch(_){console.error("确保分支到主干连接时出错:",_)}},Ne=f=>{try{const d=f.node;d.data.nodeType==="main"&&(console.log(`主干节点 ${d.id} 拖动结束, 当前Y位置: ${d.position.y}`),z(d.id,{x:Be,y:d.position.y}),ve()),d.data.nodeType==="branch"&&d.data.parentId&&(console.log(`分支节点 ${d.id} 拖动结束, 当前位置: (${d.position.x}, ${d.position.y})`),setTimeout(()=>{Se(d.id,d.data.parentId)},50)),s.value=null,At()}catch(d){console.error("节点拖动结束时出错:",d),s.value=null}},ve=()=>{try{console.log("开始优化主干连接...");const f=x(),d=X();if(!f||!f.length)return;const _=f.filter(be=>be.data.nodeType==="main").sort((be,Ue)=>be.position.y-Ue.position.y);if(_.length<=1)return;console.log("排序后的主干节点顺序:",_.map(be=>be.id));const B=d.filter(be=>{const Ue=g(be.source),it=g(be.target);return Ue?.data?.nodeType==="main"&&it?.data?.nodeType==="main"}),C=new Map;B.forEach(be=>{C.set(`${be.source}-${be.target}`,be)});let j=!1,ue=[];for(let be=0;be<_.length-1;be++){const Ue=_[be].id,it=_[be+1].id,le=`${Ue}-${it}`;C.has(le)||(j=!0,ue.push({source:Ue,target:it}))}if(!j){console.log("主干连接已经是最新状态，无需更新");return}console.log("发现缺失的主干连接，添加这些连接");const ie=ue.map(be=>({id:`e-main-${be.source}-${be.target}-${Date.now()}`,source:be.source,sourceHandle:"bottom",target:be.target,targetHandle:"top",type:n.SmoothStep,style:{strokeWidth:4,stroke:"#409EFF"},markerEnd:{type:o.ArrowClosed,color:"#409EFF"},animated:!1,updatable:!0,draggable:!0}));ie.length>0&&(D(ie),console.log(`已添加 ${ie.length} 条缺失的主干连接`));const _e=new Set;for(let be=0;be<_.length-1;be++)_e.add(`${_[be].id}-${_[be+1].id}`);const K=B.filter(be=>!_e.has(`${be.source}-${be.target}`));return K.length>0&&(console.log(`移除 ${K.length} 条多余的主干连接`),O(K.map(be=>be.id))),!0}catch(f){return console.error("优化主干连接时出错:",f),!1}},$e=()=>{h.value=!1,S.value="",y.value="",N.value=""},he=f=>{console.log("节点被点击:",f.node),h.value=!1},xe=f=>{f.event.preventDefault();const{clientX:d,clientY:_}=f.event,B=document.querySelector(".timeline-flow-wrapper");if(!B)return;const C=B.getBoundingClientRect(),j=d-C.left,ue=_-C.top;h.value=!0,v.value={position:"absolute",top:`${ue}px`,left:`${j}px`},y.value=f.node.id,M.value=f.node.data.nodeType,S.value="node"},de=f=>{const d=y.value;if(!d)return;const _=g(d);if(_){switch(f){case"edit":dt(d,_.data);break;case"delete":ma(d);break;case"add-up":pn(d,"up");break;case"add-down":pn(d,"down");break;case"add-left":pn(d,"left");break;case"add-right":pn(d,"right");break;case"add-free":_a(d);break}h.value=!1}},m=f=>{if(console.log("创建新连接:",f),!(!f.source||!f.target))try{const d=g(f.source),_=g(f.target);if(!d||!_)return;let C={id:`e-${f.source}-${f.target}-${et(4)}`,...f,type:n.SmoothStep,animated:!0,style:{cursor:"move",strokeWidth:2}};if(d.data.nodeType==="main"&&_.data.nodeType==="main")C.type=n.Straight,C.style={...C.style,strokeWidth:4,stroke:"#409EFF"},C.markerEnd={type:o.ArrowClosed,color:"#409EFF"},f.sourceHandle==="bottom"&&f.targetHandle==="top"||(C.sourceHandle="bottom",C.targetHandle="top");else if(d.data.nodeType==="branch"&&_.data.nodeType==="main"){const j=d.data.color||fe();C.type=n.SmoothStep,C.style={...C.style,strokeWidth:3,stroke:j},C.markerEnd={type:o.ArrowClosed,color:j}}else if(d.data.nodeType==="main"&&_.data.nodeType==="branch"){const j=_.data.color||fe();C.type=n.SmoothStep,C.style={...C.style,strokeWidth:3,stroke:j},C.markerEnd={type:o.ArrowClosed,color:j}}else if(d.data.nodeType==="branch"&&_.data.nodeType==="branch"){const j=d.data.color||fe();C.type=n.SmoothStep,C.style={...C.style,strokeWidth:3,stroke:j},C.markerEnd={type:o.ArrowClosed,color:j}}D([C]),d.data.nodeType==="main"&&_.data.nodeType==="branch"?G(_.id,{..._,data:{..._.data,parentId:d.id}}):d.data.nodeType==="branch"&&_.data.nodeType==="main"&&G(d.id,{...d,data:{...d.data,targetId:_.id}}),console.log("成功创建新连接"),At()}catch(d){console.error("创建连接时出错:",d)}},p=f=>{const{edge:d,connection:_}=f;console.log("边更新:",d.id,_);try{const C=E(d.id)?.data?.offsetY||0;if(!R(d,_)){console.error("边更新失败");return}const ue=g(_.source),ie=g(_.target);if(ue&&ie){const _e=X().find(K=>K.id===d.id||K.source===_.source&&K.target===_.target);_e&&(ue.data.nodeType==="main"&&ie.data.nodeType==="main"?(_e.type=n.SmoothStep,_e.markerEnd||(_e.markerEnd={type:o.ArrowClosed,color:_e.style?.stroke||"#409EFF"})):ue.data.nodeType==="branch"&&ie.data.nodeType==="branch"&&(_e.markerEnd||(_e.markerEnd={type:o.ArrowClosed,color:ue.data.color||"#409EFF"}))),ue.data.nodeType==="branch"&&ie.data.nodeType==="main"?G(ue.id,{...ue,data:{...ue.data,targetId:ie.id}}):ue.data.nodeType==="main"&&ie.data.nodeType==="branch"&&G(ie.id,{...ie,data:{...ie.data,parentId:ue.id}}),Le(()=>{ro()})}}catch(B){console.error("边更新处理出错:",B)}},b=f=>{const{edge:d,event:_}=f;console.log("边被点击:",d),_.detail===2&&Ce(d)},w=f=>{f.event.preventDefault();const{clientX:d,clientY:_}=f.event,B=document.querySelector(".timeline-flow-wrapper");if(!B)return;const C=B.getBoundingClientRect(),j=d-C.left,ue=_-C.top;h.value=!0,v.value={position:"absolute",top:`${ue}px`,left:`${j}px`},S.value="edge",N.value=f.edge.id,console.log("显示边右键菜单:",f.edge.id)},P=f=>{const d=N.value;if(!d)return;const _=X().find(B=>B.id===d);if(_){switch(console.log("边菜单操作:",f,d),f){case"change-style":ye(_);break;case"delete":Ce(_);break}h.value=!1}},ne=me(!1),oe=me(null),q=me({strokeWidth:2,stroke:"#409EFF",animated:!1}),ye=f=>{oe.value=f,q.value={strokeWidth:f.style?.strokeWidth||2,stroke:f.style?.stroke||"#409EFF",animated:f.animated||!1},ne.value=!0},ke=()=>{if(oe.value)try{const f=E(oe.value.id);if(!f)return;const d=f.data?.offsetY||0;f.style={...f.style,strokeWidth:q.value.strokeWidth,stroke:q.value.stroke},f.animated=q.value.animated,f.data||(f.data={}),f.data.offsetY=d,f.markerEnd&&(f.markerEnd.color=q.value.stroke),console.log("边样式已更新:",f.id),ne.value=!1}catch(f){console.error("更新边样式时出错:",f)}},Ce=f=>{try{const d=g(f.source),_=g(f.target);if(d?.data.nodeType==="main"&&_?.data.nodeType==="main"){Pt.alert("主干连接不能直接删除，请移除相关节点","提示");return}Pt.confirm("确定删除此连接吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{O([f.id]),console.log("边已删除:",f.id),d?.data.nodeType==="branch"&&_?.data.nodeType==="main"?G(d.id,{...d,data:{...d.data,targetId:null}}):d?.data.nodeType==="main"&&_?.data.nodeType==="branch"&&G(_.id,{..._,data:{..._.data,parentId:null}})}).catch(()=>{})}catch(d){console.error("处理边删除时出错:",d)}},Ae=()=>{try{const f=x();if(!f){console.error("获取节点失败");return}const d=f.filter(j=>j.data.nodeType==="main"),_=d.length>0?Math.max(...d.map(j=>j.position.y)):0,B=`main-${et(6)}`,C={id:B,type:"main-event",position:{x:Be,y:_+150},connectable:!0,data:{label:"新事件",year:new Date().getFullYear(),month:new Date().getMonth()+1,day:new Date().getDate(),content:"",nodeType:"main",color:"#409EFF"}};if(f.value=[...f.value,C],d.length>0){const j=d.reduce((ie,_e)=>ie.position.y>_e.position.y?ie:_e),ue={id:`e-${j.id}-${B}`,source:j.id,sourceHandle:"bottom",target:B,targetHandle:"top",type:n.SmoothStep,style:{strokeWidth:4,stroke:"#409EFF"},markerEnd:{type:o.ArrowClosed,color:"#409EFF"},updatable:!0,draggable:!0,data:{editable:!0,offsetY:0}};r.value=[...r.value,ue]}dt(B,C.data)}catch(f){console.error("添加主干事件时出错:",f)}},je=()=>{try{const f=x();if(!f){console.error("获取节点失败");return}const d=f.filter(_e=>_e.data.nodeType==="main");if(d.length===0){Pt.alert("请先添加主干事件","提示");return}const _=d[Math.floor(Math.random()*d.length)],B=Math.random()>.5,C=fe(),j=`branch-${et(6)}`,ue={id:j,type:"branch-event",position:{x:B?Be-250:Be+250,y:_.position.y},connectable:!0,data:{label:"分支事件",year:new Date().getFullYear(),month:new Date().getMonth()+1,day:new Date().getDate(),content:"",nodeType:"branch",color:C,parentId:_.id,isLeftSide:B}};Y([ue]);const ie={id:`e-${_.id}-${j}`,source:_.id,sourceHandle:B?"left":"right",target:j,targetHandle:B?"right":"left",type:n.SmoothStep,animated:!0,style:{strokeWidth:3,stroke:C},markerEnd:{type:o.ArrowClosed,color:C}};D([ie]),dt(j,ue.data)}catch(f){console.error("添加分支事件时出错:",f)}},dt=(f,d)=>{l.value={...d,year:d.year?Number(d.year):d.year,month:d.month?Number(d.month):d.month,day:d.day?Number(d.day):d.day},a.value=!!d.customTime,c.value=f,u.value=!0},no=()=>{try{if(c.value==="new-node"){oo();return}if(!c.value)return;const f=g(c.value);if(!f)return;if(G(c.value,{...f,data:{...f.data,label:l.value.label,year:l.value.year,month:l.value.month,day:l.value.day,content:l.value.content,color:l.value.color||f.data.color,customTime:a.value?l.value.customTime:null,useCustomTime:a.value}}),f.data.nodeType==="branch"&&l.value.color){const d=X();if(!d){console.error("获取边失败");return}d.filter(B=>B.source===c.value||B.target===c.value).forEach(B=>{B.style={...B.style,stroke:l.value.color},B.markerEnd&&(B.markerEnd.color=l.value.color)})}u.value=!1,c.value="",At()}catch(f){console.error("保存编辑时出错:",f),u.value=!1,c.value=""}},oo=()=>{try{const f=Ut.value;if(!f.sourceNodeId){console.error("源节点ID为空"),u.value=!1;return}const d=g(f.sourceNodeId);if(!d){console.error("找不到源节点"),u.value=!1;return}const _=f.nodeType==="main"?`main-${et(6)}`:`branch-${et(6)}`,B={id:_,type:f.nodeType==="main"?"main-event":"branch-event",position:f.position,connectable:!0,data:{label:l.value.label,year:l.value.year,month:l.value.month,day:l.value.day,content:l.value.content,nodeType:f.nodeType,color:l.value.color||(f.nodeType==="main"?"#409EFF":fe()),parentId:f.nodeType==="branch"&&!f.isFreeEvent?f.sourceNodeId:null,isLeftSide:f.direction==="left",customTime:a.value?l.value.customTime:null,useCustomTime:a.value}};if(Y([B]),console.log(`节点 ${_} 已添加`),!f.isFreeEvent)switch(f.direction){case"up":ua(d,B);break;case"down":ca(d,B);break;case"left":da(d,B);break;case"right":fa(d,B);break}u.value=!1,c.value="",f.nodeType==="main"&&(f.direction==="up"||f.direction==="down")&&setTimeout(()=>{Qo()},100)}catch(f){console.error("创建新节点时出错:",f),u.value=!1,c.value=""}},ua=(f,d)=>{if(f.data.nodeType==="main"){const _={id:`e-${d.id}-${f.id}`,source:d.id,sourceHandle:"bottom",target:f.id,targetHandle:"top",type:n.SmoothStep,style:{strokeWidth:4,stroke:"#409EFF"},markerEnd:{type:o.ArrowClosed,color:"#409EFF"},updatable:!0,draggable:!0,data:{editable:!0,offsetY:0}};D([_])}else{const _=d.data.color,B={id:`e-${d.id}-${f.id}`,source:d.id,sourceHandle:"bottom",target:f.id,targetHandle:"top",type:n.SmoothStep,animated:!0,style:{strokeWidth:3,stroke:_},markerEnd:{type:o.ArrowClosed,color:_}};D([B])}},ca=(f,d)=>{if(f.data.nodeType==="main"){const _=X(),B=_.filter(ue=>ue.source===f.id&&g(ue.target)?.data.nodeType==="main"),C=B.length>0?B[0].target:null;if(C){const ue=_.find(ie=>ie.source===f.id&&ie.target===C);ue&&O([ue.id])}const j={id:`e-${f.id}-${d.id}`,source:f.id,sourceHandle:"bottom",target:d.id,targetHandle:"top",type:n.SmoothStep,style:{strokeWidth:4,stroke:"#409EFF"},markerEnd:{type:o.ArrowClosed,color:"#409EFF"},updatable:!0,draggable:!0,data:{editable:!0,offsetY:0}};if(D([j]),C){const ue={id:`e-${d.id}-${C}`,source:d.id,sourceHandle:"bottom",target:C,targetHandle:"top",type:n.SmoothStep,style:{strokeWidth:4,stroke:"#409EFF"},markerEnd:{type:o.ArrowClosed,color:"#409EFF"},updatable:!0,draggable:!0,data:{editable:!0,offsetY:0}};D([ue])}}else{const _=f.data.color,B={id:`e-${f.id}-${d.id}`,source:f.id,sourceHandle:"bottom",target:d.id,targetHandle:"top",type:n.SmoothStep,animated:!0,style:{strokeWidth:3,stroke:_},markerEnd:{type:o.ArrowClosed,color:_}};D([B])}},da=(f,d)=>{const _=d.data.color,B={id:`e-${f.id}-${d.id}`,source:f.id,sourceHandle:"left",target:d.id,targetHandle:"right",type:n.SmoothStep,animated:!0,style:{strokeWidth:3,stroke:_},markerEnd:{type:o.ArrowClosed,color:_}};D([B])},fa=(f,d)=>{const _=d.data.color,B={id:`e-${f.id}-${d.id}`,source:f.id,sourceHandle:"right",target:d.id,targetHandle:"left",type:n.SmoothStep,animated:!0,style:{strokeWidth:3,stroke:_},markerEnd:{type:o.ArrowClosed,color:_}};D([B])},io=()=>{u.value=!1,c.value=""},ha=f=>{f.target===f.currentTarget&&io()},pa=f=>{f.target===f.currentTarget&&(ne.value=!1)},ga=f=>{f.target===f.currentTarget&&(Wt.value=!1)},ma=f=>{Pt.confirm("确定删除此节点吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{try{const d=g(f);if(!d)return;if(d.data.nodeType==="main")va(f);else{A([f]);const C=X().filter(j=>j.source===f||j.target===f);C.length>0&&O(C.map(j=>j.id))}At()}catch(d){console.error("删除节点时出错:",d)}}).catch(()=>{})},va=f=>{const d=x(),_=X(),B=_.filter(K=>K.target===f&&g(K.source)?.data.nodeType==="main"),C=_.filter(K=>K.source===f&&g(K.target)?.data.nodeType==="main"),j=B.length>0?B[0].source:null,ue=C.length>0?C[0].target:null;console.log(`删除主干节点 ${f}，前节点: ${j||"无"}，后节点: ${ue||"无"}`),A([f]);const ie=_.filter(K=>K.source===f||K.target===f);ie.length>0&&O(ie.map(K=>K.id));const _e=d.filter(K=>K.data.nodeType==="branch"&&K.data.parentId===f);j&&_e.length>0&&_e.forEach(K=>{G(K.id,{...K,data:{...K.data,parentId:j}});const be=K.data.isLeftSide,Ue={id:`e-${j}-${K.id}`,source:j,sourceHandle:be?"left":"right",target:K.id,targetHandle:be?"right":"left",type:n.SmoothStep,animated:!0,style:{strokeWidth:3,stroke:K.data.color},markerEnd:{type:o.ArrowClosed,color:K.data.color}};D([Ue])}),Le(()=>{ve()})},ro=()=>{try{console.log("开始自动布局...");const f=x();if(!f){console.error("获取节点失败");return}f.filter(B=>B.data.nodeType==="main").forEach(B=>{B.position.x!==Be&&z(B.id,{x:Be,y:B.position.y})}),Qo();const _=f.filter(B=>B.data.nodeType==="branch"&&B.data.parentId);if(_.length>0){console.log(`正在检查 ${_.length} 个分支节点连接`);const B=X();_.forEach(C=>{const j=C.data.parentId;if(!j)return;const ue=B.find(ie=>ie.source===j&&ie.target===C.id||ie.source===C.id&&ie.target===j);if(!ue)console.log(`节点 ${C.id} 缺少到主干节点 ${j} 的连接，正在创建...`),setTimeout(()=>{Se(C.id,j)},50);else{console.log(`节点 ${C.id} 到主干节点 ${j} 的连接已存在`);const ie=ue.source===C.id?C:g(ue.source),_e=ue.target===C.id?C:g(ue.target);if(ie&&_e&&(ie.position.x!==ie._rf?.position?.x||ie.position.y!==ie._rf?.position?.y||_e.position.x!==_e._rf?.position?.x||_e.position.y!==_e._rf?.position?.y)){console.log(`刷新节点 ${C.id} 的连接显示`);const K={...ue,style:{...ue.style}};R(ue,K)}}})}console.log("自动布局完成")}catch(f){console.error("自动布局时出错:",f)}},Qo=()=>{try{console.log("开始重排主干节点...");const f=x();if(!f||!Array.isArray(f)||f.length===0){console.error("获取节点失败或节点列表为空");return}const d=f.filter(_=>_.data?.nodeType==="main");if(console.log(`找到 ${d.length} 个主干节点`),d.length<=1){console.log("主干节点数量不足，无需重排");return}d.sort((_,B)=>_.position.y-B.position.y),d.forEach(_=>{_.position.x!==Be&&z(_.id,{x:Be,y:_.position.y})}),ve()}catch(f){console.error("重排主干节点时出错:",f)}},ya=()=>{try{const f=x(),d=X(),_=f.map(K=>({id:K.id,type:K.type,position:{x:K.position.x,y:K.position.y},x:K.position.x,y:K.position.y,data:{label:K.data.label,year:K.data.year,month:K.data.month,day:K.data.day,content:K.data.content,nodeType:K.data.nodeType,color:K.data.color,parentId:K.data.parentId,isLeftSide:K.data.isLeftSide,customTime:K.data.customTime,useCustomTime:K.data.useCustomTime},label:K.data.label,year:K.data.year,month:K.data.month,day:K.data.day,content:K.data.content,nodeType:K.data.nodeType,color:K.data.color,parentId:K.data.parentId,isLeftSide:K.data.isLeftSide,customTime:K.data.customTime,useCustomTime:K.data.useCustomTime})),B=d.map(K=>({id:K.id,source:K.source,target:K.target,sourceHandle:K.sourceHandle,targetHandle:K.targetHandle,type:K.type,animated:K.animated,style:{strokeWidth:K.style?.strokeWidth||2,stroke:K.style?.stroke||"#409EFF"},markerEnd:K.markerEnd})),j=JSON.stringify({nodes:_,edges:B,version:"2.0"},null,2);try{window.pywebview.api.copy_to_clipboard(j).then(()=>{Ie.success("数据已复制到剪贴板并开始下载")}).catch(K=>{console.error("剪贴板复制失败:",K),Ie.info("数据已导出为文件")})}catch(K){console.error("剪贴板访问失败:",K),Ie.info("数据已导出为文件")}const ue=new Blob([j],{type:"application/json"}),ie=URL.createObjectURL(ue),_e=document.createElement("a");_e.href=ie,_e.download=`timeline-data-${new Date().toISOString().split("T")[0]}.json`,_e.click(),URL.revokeObjectURL(ie)}catch(f){console.error("导出数据时出错:",f),Ie.error("导出数据失败")}},Wt=me(!1),hn=me(""),jo=()=>{const f=`main-${et(6)}`,d={id:f,type:"main-event",position:{x:Be,y:100},connectable:!0,data:{label:"故事起点",year:new Date().getFullYear(),month:new Date().getMonth()+1,day:new Date().getDate(),content:"在此处开始您的故事",nodeType:"main",color:"#409EFF"}};return i.value=[d],r.value=[],console.log("已创建默认主线事件节点:",f),Le(()=>{F()}),{nodes:[d],edges:[]}};t({addMainEvent:Ae,addBranchEvent:je,autoLayout:ro,getNodes:x,getEdges:X,fitView:F,exportTimelineData:ya,createDefaultMainEvent:jo,importFromJson:f=>{try{if(typeof f=="string")try{f=JSON.parse(f)}catch(_){return console.error("解析数据失败:",_),!1}return f&&f.status==="success"&&f.data&&(f=f.data),!f||!f.nodes&&!f.edges&&!f.mainTrunkEvents?(console.error("无效的时间线数据格式"),!1):(!f.nodes||f.nodes.length===0)&&(!f.mainTrunkEvents||f.mainTrunkEvents.length===0)?(console.log("导入的数据为空，创建一个默认节点"),jo(),!0):(i.value=[],r.value=[],Le(()=>{if(f.nodes&&f.edges){const _=f.nodes.map(C=>({id:C.id||`node-${et(6)}`,type:C.nodeType==="main"?"main-event":"branch-event",position:{x:C.nodeType==="main"?Be:C.x||C.position?.x||(C.isLeftSide?Be-250:Be+250),y:C.y||C.position?.y||100},data:{label:C.label||C.data?.label||"未命名事件",year:Number(C.year||C.data?.year||new Date().getFullYear()),month:Number(C.month||C.data?.month||new Date().getMonth()+1),day:Number(C.day||C.data?.day||new Date().getDate()),content:C.content||C.data?.content||"",nodeType:C.nodeType||C.data?.nodeType||"main",color:C.color||C.data?.color||(C.nodeType==="main"?"#409EFF":fe()),parentId:C.parentId||C.data?.parentId,isLeftSide:C.isLeftSide||C.data?.isLeftSide,customTime:C.customTime||C.data?.customTime,useCustomTime:C.useCustomTime||C.data?.useCustomTime}}));console.log(`导入 ${_.length} 个节点`),i.value=_;const B=f.edges.map(C=>({id:C.id||`edge-${et(6)}`,source:C.source,target:C.target,sourceHandle:C.sourceHandle,targetHandle:C.targetHandle,type:C.type||n.SmoothStep,animated:C.animated||!1,style:{strokeWidth:C.style?.strokeWidth||2,stroke:C.style?.stroke||"#409EFF"},markerEnd:C.markerEnd||{type:o.ArrowClosed,color:C.style?.stroke||"#409EFF"},data:C.data}));console.log(`导入 ${B.length} 条边`),r.value=B}else if(f.mainTrunkEvents){const _=f.mainTrunkEvents.map((ie,_e)=>({id:ie.id||`main-${et(6)}`,type:"main-event",position:{x:Be,y:100+_e*150},data:{label:ie.title,year:Number(ie.year),month:Number(ie.month),day:Number(ie.day),content:ie.description||"",nodeType:"main",color:"#409EFF"}})),B=[];for(let ie=0;ie<_.length-1;ie++)B.push({id:`e-${_[ie].id}-${_[ie+1].id}`,source:_[ie].id,sourceHandle:"bottom",target:_[ie+1].id,targetHandle:"top",type:n.SmoothStep,style:{strokeWidth:4,stroke:"#409EFF"},markerEnd:{type:o.ArrowClosed,color:"#409EFF"}});const C=[],j=[];if(f.branches&&f.branchEvents){const ie={};f.branches.forEach(_e=>{_e.origin&&_e.origin.id&&(ie[_e.id]={mainNodeId:_e.origin.id,point:_e.origin.point||"right",color:_e.color||fe()})}),f.branchEvents.forEach((_e,K)=>{const be=ie[_e.branchId];if(!be)return;const Ue=be.point==="left",it={id:_e.id||`branch-${et(6)}`,type:"branch-event",position:{x:Ue?Be-250:Be+250,y:100+K*100},data:{label:_e.title,year:Number(_e.year),month:Number(_e.month),day:Number(_e.day),content:_e.description||"",nodeType:"branch",color:be.color,parentId:be.mainNodeId,isLeftSide:Ue}};C.push(it),j.push({id:`e-${be.mainNodeId}-${it.id}`,source:be.mainNodeId,sourceHandle:Ue?"left":"right",target:it.id,targetHandle:Ue?"right":"left",type:n.SmoothStep,animated:!0,style:{strokeWidth:3,stroke:be.color},markerEnd:{type:o.ArrowClosed,color:be.color}})})}const ue=[];f.connections&&f.connections.length>0&&f.connections.forEach(ie=>{ue.push({id:`e-custom-${et(6)}`,source:ie.source,target:ie.target,type:n.SmoothStep,animated:ie.animated||!1,style:{strokeWidth:ie.style?.strokeWidth||2,stroke:ie.style?.stroke||"#409EFF"},markerEnd:{type:o.ArrowClosed,color:ie.style?.stroke||"#409EFF"}})}),i.value=[..._,...C],r.value=[...B,...j,...ue]}Le(()=>{ro()})}),!0)}catch(d){return console.error("导入数据时出错:",d),!1}},getTimelineData:()=>{try{const f=x(),d=X(),_=f.map(C=>({id:C.id,type:C.type,x:C.position.x,y:C.position.y,data:C.data,nodeType:C.data.nodeType})),B=d.map(C=>({id:C.id,source:C.source,target:C.target,sourceHandle:C.sourceHandle,targetHandle:C.targetHandle,type:C.type,animated:C.animated,style:{strokeWidth:C.style?.strokeWidth||2,stroke:C.style?.stroke||"#409EFF"},markerEnd:C.markerEnd}));return{nodes:_,edges:B,version:"2.0"}}catch(f){throw console.error("获取时间线数据失败:",f),f}},triggerSave:()=>{const f=new CustomEvent("save-timeline");document.dispatchEvent(f)}});const wa=()=>{try{const f=JSON.parse(hn.value);if(!f||!f.nodes||!f.edges){Pt.alert("无效的数据格式","错误",{type:"error"});return}i.value=[],r.value=[],f.nodes.forEach(_=>{const B={x:_.position?.x||_.x||(_.nodeType==="main"?Be:_.isLeftSide?Be-250:Be+250),y:_.position?.y||_.y||100},C={label:_.data?.label||_.label||"未命名事件",year:_.data?.year||_.year||new Date().getFullYear(),month:_.data?.month||_.month||new Date().getMonth()+1,day:_.data?.day||_.day||new Date().getDate(),content:_.data?.content||_.content||"",nodeType:_.data?.nodeType||_.nodeType||"main",color:_.data?.color||_.color||(_.nodeType==="main"?"#409EFF":fe()),parentId:_.data?.parentId||_.parentId,isLeftSide:_.data?.isLeftSide||_.isLeftSide,customTime:_.data?.customTime||_.customTime,useCustomTime:_.data?.useCustomTime||_.useCustomTime};Y([{id:_.id,type:_.type,position:B,data:C}])}),f.edges.filter(_=>{const B=f.nodes.find(j=>j.id===_.source)?.data||f.nodes.find(j=>j.id===_.source),C=f.nodes.find(j=>j.id===_.target)?.data||f.nodes.find(j=>j.id===_.target);return!((B?.nodeType==="main"||B?.data?.nodeType==="main")&&(C?.nodeType==="main"||C?.data?.nodeType==="main"))}).forEach(_=>{const B={id:_.id,source:_.source,target:_.target,sourceHandle:_.sourceHandle,targetHandle:_.targetHandle,type:_.type||n.SmoothStep,animated:_.animated||!1,style:{strokeWidth:_.style?.strokeWidth||2,stroke:_.style?.stroke||"#409EFF"},markerEnd:_.markerEnd||{type:o.ArrowClosed,color:_.style?.stroke||"#409EFF"}};D([B])}),Wt.value=!1,hn.value="",Le(()=>{ve(),F(),Ie.success("数据导入成功")})}catch(f){console.error("导入数据时出错:",f),Pt.alert("导入数据失败: "+f.message,"错误",{type:"error"})}},ao=["#409EFF","#e84393","#00b894","#e17055","#6c5ce7","#fdcb6e","#00cec9","#ff7675","#74b9ff","#a29bfe","#55efc4","#fab1a0","#ff9ff3","#ffeaa7","#636e72","#2d3436","#b71540","#0a3d62"],Ut=me({sourceNodeId:"",direction:"",position:{x:0,y:0},isFreeEvent:!1,nodeType:""}),pn=(f,d)=>{const _=g(f);if(!_)return;let B={x:_.position.x,y:_.position.y};switch(d){case"up":B.y-=180;break;case"down":B.y+=80;break;case"left":B.x-=250;break;case"right":B.x+=250;break}Ut.value={sourceNodeId:f,direction:d,position:B,isFreeEvent:!1,nodeType:d==="left"||d==="right"?"branch":_.data.nodeType};const C={label:"新事件",year:new Date().getFullYear(),month:new Date().getMonth()+1,day:new Date().getDate(),content:"",nodeType:Ut.value.nodeType,color:Ut.value.nodeType==="main"?"#409EFF":fe(),isLeftSide:d==="left"};l.value=C,c.value="new-node",u.value=!0},_a=f=>{const d=g(f);if(!d)return;const _={x:d.position.x+100,y:d.position.y+100};Ut.value={sourceNodeId:f,direction:"free",position:_,isFreeEvent:!0,nodeType:d.data.nodeType};const B={label:"自由事件",year:new Date().getFullYear(),month:new Date().getMonth()+1,day:new Date().getDate(),content:"",nodeType:d.data.nodeType,color:d.data.nodeType==="main"?"#409EFF":fe()};l.value=B,c.value="new-node",u.value=!0},so=f=>({"#409EFF":"主线/主要事件 - 蓝色","#e84393":"爱情/情感 - 粉红色","#00b894":"成长/进步 - 绿色","#e17055":"冲突/战斗 - 橙红色","#6c5ce7":"神秘/魔法 - 紫色","#fdcb6e":"发现/启示 - 黄色","#00cec9":"旅程/探索 - 青色","#ff7675":"危机/危险 - 红色","#74b9ff":"回忆/过去 - 淡蓝色","#a29bfe":"梦境/幻想 - 淡紫色","#55efc4":"希望/治愈 - 薄荷绿","#fab1a0":"友情/同伴 - 淡橙色","#ff9ff3":"浪漫/爱慕 - 粉色","#ffeaa7":"欢乐/庆祝 - 淡黄色","#636e72":"悲伤/失落 - 灰色","#2d3436":"黑暗/恐惧 - 深灰色","#b71540":"仇恨/复仇 - 深红色","#0a3d62":"智慧/思考 - 深蓝色"})[f]||"未知颜色",ei=f=>({"#409EFF":"主线/主要事件 - 蓝色","#e84393":"爱情/情感 - 粉红色","#00b894":"成长/进步 - 绿色","#e17055":"冲突/战斗 - 橙红色","#6c5ce7":"神秘/魔法 - 紫色","#fdcb6e":"发现/启示 - 黄色","#00cec9":"旅程/探索 - 青色","#ff7675":"危机/危险 - 红色","#74b9ff":"回忆/过去 - 淡蓝色","#a29bfe":"梦境/幻想 - 淡紫色","#55efc4":"希望/治愈 - 薄荷绿","#fab1a0":"友情/同伴 - 淡橙色","#ff9ff3":"浪漫/爱慕 - 粉色","#ffeaa7":"欢乐/庆祝 - 淡黄色","#636e72":"悲伤/失落 - 灰色","#2d3436":"黑暗/恐惧 - 深灰色","#b71540":"仇恨/复仇 - 深红色","#0a3d62":"智慧/思考 - 深蓝色"})[f]||"未知颜色",ba=f=>{l.value.color=f,Ie({message:`已选择: ${ei(f)}`,type:"success",duration:1500})},Ea=f=>{if(f&&l.value.year)l.value.customTime||(l.value.customTime=`${l.value.year}年${l.value.month||""}月${l.value.day||""}日`);else if(!f&&l.value.customTime){const d=l.value.customTime.match(/(\d+)\s*年/),_=l.value.customTime.match(/(\d+)\s*月/),B=l.value.customTime.match(/(\d+)\s*日/);d&&(l.value.year=parseInt(d[1])),_&&(l.value.month=parseInt(_[1])),B&&(l.value.day=parseInt(B[1]))}},ti=f=>{if((f.ctrlKey||f.metaKey)&&f.key==="s"){f.preventDefault();const d=new CustomEvent("save-timeline");document.dispatchEvent(d),console.log("触发保存事件: Ctrl+S")}},At=()=>{const f=new CustomEvent("timeline-data-changed");document.dispatchEvent(f)};return(f,d)=>{const _=Ki,B=qi,C=za,j=Oa,ue=Fa,ie=Ba,_e=Va,K=Ha,be=La,Ue=Ya,it=Ji;return re(),we("div",np,[Z(V(oh),{nodes:i.value,edges:r.value,"default-viewport":{x:0,y:0,zoom:.85},"min-zoom":.3,"max-zoom":2,"nodes-draggable":!0,"snap-to-grid":!0,"snap-grid":[20,20],"connection-line-style":{stroke:"#409EFF",strokeWidth:2},"connection-line-type":"smoothstep","default-edge-options":$,"connect-on-click":!1,"edges-updatable":!0,"edges-draggable":!1,"edge-updatable":!0,"enable-pan-on-drag":!0,"enable-pan-on-scroll":!0,"enable-zoom-on-scroll":!0,"fit-view-on-init":!0,"fit-view-padding":[50,50],class:"timeline-flow",onNodesChange:Q,onEdgesChange:ae,onConnect:m,onEdgeUpdate:p,onEdgeClick:b,onEdgeContextmenu:w,onPaneclick:$e,onNodeClick:he,onNodeDrag:ce,onNodeDragStop:Ne,onNodeContextMenu:xe},{"node-main-event":ge(le=>[Z(Kh,xt(St(le)),null,16)]),"node-branch-event":ge(le=>[Z(tp,xt(St(le)),null,16)]),"edge-bezier":ge(le=>[Z(V(ia),xt(St(le)),null,16)]),"edge-straight":ge(le=>[Z(V(na),xt(St(le)),null,16)]),"edge-smoothstep":ge(le=>[Z(V(qo),xt(St(le)),null,16)]),"edge-step":ge(le=>[Z(V(oa),xt(St(le)),null,16)]),"edge-simplebezier":ge(le=>[Z(V(ra),xt(St(le)),null,16)]),default:ge(()=>[Z(V(dh),{"pattern-color":"#aaa",gap:20,size:1}),Z(V(Lh)),Z(V(aa),{position:"top-right",class:"panel-top"},{default:ge(()=>[U("div",op,[Z(_,{type:"primary",onClick:J,size:"small",icon:V(Pa)},{default:ge(()=>[Xe(Je(L.value?"退出全屏":"全屏"),1)]),_:1},8,["icon"])])]),_:1})]),_:1},8,["nodes","edges"]),u.value?(re(),we("div",{key:0,class:"custom-dialog-overlay",onClick:ha},[U("div",{class:"custom-dialog",onClick:d[8]||(d[8]=lo(()=>{},["stop"]))},[U("div",{class:"custom-dialog-header"},[d[35]||(d[35]=U("h3",null,"编辑事件详情",-1)),U("button",{class:"custom-dialog-close",onClick:io},"×")]),U("div",ip,[Z(be,{model:l.value,"label-width":"60px",class:"edit-event-form",size:"small"},{default:ge(()=>[Z(C,{label:"标题"},{default:ge(()=>[Z(B,{modelValue:l.value.label,"onUpdate:modelValue":d[0]||(d[0]=le=>l.value.label=le),placeholder:"请输入事件标题"},null,8,["modelValue"])]),_:1}),Z(C,{label:"时间"},{default:ge(()=>[U("div",rp,[Z(j,{modelValue:a.value,"onUpdate:modelValue":d[1]||(d[1]=le=>a.value=le),"active-text":"自定义时间","inactive-text":"标准时间","inline-prompt":"",onChange:Ea},null,8,["modelValue"])]),a.value?(re(),we("div",sp,[Z(B,{modelValue:l.value.customTime,"onUpdate:modelValue":d[5]||(d[5]=le=>l.value.customTime=le),placeholder:"如：第三纪元 2415年",class:"custom-time-input"},null,8,["modelValue"])])):(re(),we("div",ap,[Z(ue,{modelValue:l.value.year,"onUpdate:modelValue":d[2]||(d[2]=le=>l.value.year=le),min:1900,max:2100,placeholder:"年",controls:!1,class:"date-input-year"},null,8,["modelValue"]),d[36]||(d[36]=U("span",{class:"date-separator"},"年",-1)),Z(ue,{modelValue:l.value.month,"onUpdate:modelValue":d[3]||(d[3]=le=>l.value.month=le),min:1,max:12,placeholder:"月",controls:!1,class:"date-input-month"},null,8,["modelValue"]),d[37]||(d[37]=U("span",{class:"date-separator"},"月",-1)),Z(ue,{modelValue:l.value.day,"onUpdate:modelValue":d[4]||(d[4]=le=>l.value.day=le),min:1,max:31,placeholder:"日",controls:!1,class:"date-input-day"},null,8,["modelValue"]),d[38]||(d[38]=U("span",{class:"date-separator"},"日",-1))]))]),_:1}),Z(C,{label:"内容"},{default:ge(()=>[Z(B,{modelValue:l.value.content,"onUpdate:modelValue":d[6]||(d[6]=le=>l.value.content=le),type:"textarea",rows:4,placeholder:"请输入事件内容描述",class:"content-textarea"},null,8,["modelValue"])]),_:1}),l.value.nodeType==="branch"?(re(),We(C,{key:0,label:"颜色"},{default:ge(()=>[U("div",lp,[Z(ie,{modelValue:l.value.color,"onUpdate:modelValue":d[7]||(d[7]=le=>l.value.color=le),"show-alpha":"",predefine:ao},null,8,["modelValue"]),U("div",up,[Z(_e,{placement:"right",content:so(l.value.color),effect:"light"},{default:ge(()=>[U("span",null,"当前: "+Je(ei(l.value.color)),1)]),_:1},8,["content"]),Z(K,{placement:"right",width:380,trigger:"click","popper-class":"color-popover"},{default:ge(()=>[d[39]||(d[39]=U("div",{class:"color-popover-header"},[U("h4",null,"选择事件颜色"),U("p",{class:"color-popover-tip"},"点击颜色直接选择")],-1)),U("div",cp,[(re(),we(Ze,null,Rn(ao,(le,xa)=>U("div",{class:mt(["color-item",{"color-item-active":l.value.color===le}]),key:xa,onClick:Dp=>ba(le)},[Z(_e,{content:`点击选择: ${so(le)}`,placement:"top",effect:"light","show-after":300},{default:ge(()=>[U("div",{class:"color-swatch",style:Ge({backgroundColor:le})},null,4)]),_:2},1032,["content"]),U("span",fp,Je(so(le)),1)],10,dp)),64))])]),reference:ge(()=>[Z(_,{type:"primary",size:"small",plain:"",class:"color-help-button",icon:V(Ra)},{default:ge(()=>d[40]||(d[40]=[Xe(" 查看更多颜色含义 ")])),_:1},8,["icon"])]),_:1})])])]),_:1})):Pe("",!0)]),_:1},8,["model"])]),U("div",hp,[Z(_,{onClick:io},{default:ge(()=>d[41]||(d[41]=[Xe("取消")])),_:1}),Z(_,{type:"primary",onClick:no},{default:ge(()=>d[42]||(d[42]=[Xe("确认")])),_:1})])])])):Pe("",!0),ne.value?(re(),we("div",{key:1,class:"custom-dialog-overlay",onClick:pa},[U("div",{class:"custom-dialog custom-dialog-small",onClick:d[14]||(d[14]=lo(()=>{},["stop"]))},[U("div",pp,[d[43]||(d[43]=U("h3",null,"编辑连接样式",-1)),U("button",{class:"custom-dialog-close",onClick:d[9]||(d[9]=le=>ne.value=!1)},"×")]),U("div",gp,[Z(be,{model:q.value,"label-width":"60px",class:"edge-edit-form",size:"small"},{default:ge(()=>[Z(C,{label:"线宽"},{default:ge(()=>[Z(Ue,{modelValue:q.value.strokeWidth,"onUpdate:modelValue":d[10]||(d[10]=le=>q.value.strokeWidth=le),min:1,max:10,step:1},null,8,["modelValue"])]),_:1}),Z(C,{label:"颜色"},{default:ge(()=>[Z(ie,{modelValue:q.value.stroke,"onUpdate:modelValue":d[11]||(d[11]=le=>q.value.stroke=le),"show-alpha":"",predefine:ao},null,8,["modelValue"])]),_:1}),Z(C,{label:"动画效果"},{default:ge(()=>[Z(j,{modelValue:q.value.animated,"onUpdate:modelValue":d[12]||(d[12]=le=>q.value.animated=le)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),U("div",mp,[Z(_,{onClick:d[13]||(d[13]=le=>ne.value=!1)},{default:ge(()=>d[44]||(d[44]=[Xe("取消")])),_:1}),Z(_,{type:"primary",onClick:ke},{default:ge(()=>d[45]||(d[45]=[Xe("确认")])),_:1})])])])):Pe("",!0),Aa(U("div",{class:"context-menu",style:Ge(v.value)},[M.value==="main"?(re(),we(Ze,{key:0},[U("div",{class:"context-menu-item",onClick:d[15]||(d[15]=le=>de("add-up"))}," 向上添加事件 "),U("div",{class:"context-menu-item",onClick:d[16]||(d[16]=le=>de("add-down"))}," 向下添加事件 "),U("div",{class:"context-menu-item",onClick:d[17]||(d[17]=le=>de("add-left"))}," 向左添加事件 "),U("div",{class:"context-menu-item",onClick:d[18]||(d[18]=le=>de("add-right"))}," 向右添加事件 "),U("div",{class:"context-menu-item",onClick:d[19]||(d[19]=le=>de("add-free"))}," 添加自由事件 "),U("div",{class:"context-menu-item",onClick:d[20]||(d[20]=le=>de("edit"))}," 编辑节点 "),U("div",{class:"context-menu-item",onClick:d[21]||(d[21]=le=>de("delete"))}," 删除节点 ")],64)):M.value==="branch"?(re(),we(Ze,{key:1},[U("div",{class:"context-menu-item",onClick:d[22]||(d[22]=le=>de("add-up"))}," 向上添加事件 "),U("div",{class:"context-menu-item",onClick:d[23]||(d[23]=le=>de("add-down"))}," 向下添加事件 "),U("div",{class:"context-menu-item",onClick:d[24]||(d[24]=le=>de("add-left"))}," 向左添加事件 "),U("div",{class:"context-menu-item",onClick:d[25]||(d[25]=le=>de("add-right"))}," 向右添加事件 "),U("div",{class:"context-menu-item",onClick:d[26]||(d[26]=le=>de("add-free"))}," 添加自由事件 "),U("div",{class:"context-menu-item",onClick:d[27]||(d[27]=le=>de("edit"))}," 编辑节点 "),U("div",{class:"context-menu-item",onClick:d[28]||(d[28]=le=>de("delete"))}," 删除节点 ")],64)):S.value==="edge"?(re(),we(Ze,{key:2},[U("div",{class:"context-menu-item",onClick:d[29]||(d[29]=le=>P("change-style"))}," 修改样式 "),U("div",{class:"context-menu-item",onClick:d[30]||(d[30]=le=>P("delete"))}," 删除连接 ")],64)):Pe("",!0)],4),[[Ga,h.value]]),Wt.value?(re(),we("div",{key:2,class:"custom-dialog-overlay",onClick:ga},[U("div",{class:"custom-dialog",onClick:d[34]||(d[34]=lo(()=>{},["stop"]))},[U("div",vp,[d[46]||(d[46]=U("h3",null,"导入时间线数据",-1)),U("button",{class:"custom-dialog-close",onClick:d[31]||(d[31]=le=>Wt.value=!1)},"×")]),U("div",yp,[U("div",wp,[Z(it,{title:"数据格式提示",type:"info",description:"请粘贴有效的JSON格式数据，包含节点和连接信息","show-icon":"",closable:!1,size:"small"}),Z(B,{modelValue:hn.value,"onUpdate:modelValue":d[32]||(d[32]=le=>hn.value=le),type:"textarea",rows:9,placeholder:"粘贴JSON数据...",class:"import-textarea"},null,8,["modelValue"])])]),U("div",_p,[Z(_,{onClick:d[33]||(d[33]=le=>Wt.value=!1),size:"small"},{default:ge(()=>d[47]||(d[47]=[Xe("取消")])),_:1}),Z(_,{type:"primary",onClick:wa,size:"small"},{default:ge(()=>d[48]||(d[48]=[Xe("导入")])),_:1})])])])):Pe("",!0)])}}},Ep=Ln(bp,[["__scopeId","data-v-67ef34ec"]]),xp={class:"timeline-page"},Sp={class:"flow-timeline-section"},kp={class:"flow-header"},Np={class:"book-title"},Cp={class:"flow-controls"},$p={class:"import-container"},Tp={class:"import-tips"},Mp={class:"dialog-footer"},Ip={__name:"时间线",setup(e){const t=Xa(),n=Za(),o=me(t.query.id||t.params.id),i=me(t.query.title||t.params.title||"我的时间线"),r=me(null),a=me(!1),s=me(""),u=me(!1);let l=null;const c=me(!1);function h(){if(r.value)try{r.value.exportTimelineData()}catch(D){console.error("导出失败:",D),Ie.error("导出失败: "+D.message)}else Ie.warning("时间线组件未初始化")}async function v(){if(!r.value){Ie.warning("时间线组件未初始化");return}try{const D=ni.service({lock:!0,text:"准备导出TXT文件...",background:"rgba(255, 255, 255, 0.7)"}),A=r.value.getTimelineData();await $(!1);const O=await window.pywebview.api.export_timeline_to_txt(o.value,JSON.stringify(A));let R;try{R=JSON.parse(O)}catch(G){console.error("解析响应数据失败:",G),D.close(),Ie.error("导出失败: 数据格式错误");return}D.close(),R&&R.status==="success"?Ie.success(R.message||"时间线已成功导出为TXT文件"):Ie.error("导出失败: "+(R?.message||"未知错误"))}catch(D){console.error("导出TXT失败:",D),Ie.error("导出失败: "+(D.message||"未知错误"))}}function y(){a.value=!0}function M(){if(r.value)try{if(!s.value.trim()){Ie.warning("请输入有效的JSON数据");return}let D;try{D=JSON.parse(s.value)}catch{Ie.error("JSON格式无效，请检查数据格式");return}if(!D||!D.nodes&&!D.edges&&!D.mainTrunkEvents){Ie.error("数据格式不正确，缺少必要的节点或边数据");return}r.value.importFromJson(D)?(Ie.success("数据导入成功"),a.value=!1,s.value="",$(!1)):Ie.error("导入失败，请检查数据格式")}catch(D){console.error("导入失败:",D),Ie.error("导入失败: "+D.message)}else Ie.warning("时间线组件未初始化")}function S(){n.push({name:"bookWriting",query:{id:o.value}})}function N(){document.addEventListener("timeline-data-changed",()=>{c.value=!0}),l=setInterval(()=>{c.value&&(console.log("自动保存时间线数据..."),$(!1))},1*60*1e3)}async function $(D=!1){if(!r.value){console.error("时间线组件未初始化，无法保存");return}try{u.value=!0;const A=r.value.getTimelineData();await window.pywebview.api.save_timeline(o.value,JSON.stringify(A)),c.value=!1,console.log("时间线数据保存成功"),D&&Ie({message:"时间线数据已保存",type:"success",offset:70,duration:2e3})}catch(A){console.error("保存时间线数据失败:",A),Ie.error("保存失败: "+(A.message||"未知错误"))}finally{u.value=!1}}ut(async()=>{N(),await I(),document.addEventListener("save-timeline",T),window.addEventListener("beforeunload",Y)});async function I(){try{const D=ni.service({lock:!0,text:"加载时间线数据...",background:"rgba(255, 255, 255, 0.7)"});try{const A=await window.pywebview.api.get_timeline(o.value);let O;try{O=JSON.parse(A)}catch(G){console.error("解析响应数据失败:",G),D.close(),Ie.error("加载时间线数据失败: 数据格式错误");return}if(O&&O.status==="success"&&O.data&&(O.data.nodes&&O.data.nodes.length>0||O.data.mainTrunkEvents&&O.data.mainTrunkEvents.length>0)){const G=g(O.data);Le(()=>{r.value&&(r.value.importFromJson(G),D.close(),Ie.success("时间线数据加载成功"))})}else if(D.close(),Ie.info("没有找到时间线数据，正在创建默认主线事件..."),r.value){const G=r.value.createDefaultMainEvent();console.log("创建的默认数据:",G),await $(!1),Ie.success("已创建默认时间线起点")}else Ie.warning("时间线组件未初始化，无法创建默认事件")}catch(A){console.error("加载时间线数据失败:",A),D.close(),Ie.error("加载时间线数据失败: "+A.message)}}catch(D){console.error("初始化加载失败:",D),Ie.error("初始化加载失败: "+D.message)}}function g(D){if(D.nodes&&D.edges)return D;let A=[],O=[];return D.mainTrunkEvents&&D.mainTrunkEvents.forEach((R,G)=>{if(A.push({id:R.id,type:"main-event",position:{x:400,y:100+G*150},data:{label:R.title,year:R.year,month:R.month,day:R.day,content:R.description||"",nodeType:"main",color:"#409EFF"}}),G>0){const F=D.mainTrunkEvents[G-1].id;O.push({id:`e-${F}-${R.id}`,source:F,target:R.id,sourceHandle:"bottom",targetHandle:"top",type:"smoothstep",style:{strokeWidth:4,stroke:"#409EFF"}})}}),D.branches&&D.branchEvents&&D.branches.forEach(R=>{D.branchEvents.filter(F=>F.branchId===R.id).forEach((F,x)=>{const X=R.origin?.point==="left",E=X?150:650,z=R.origin?.position?.y||100+x*150;A.push({id:F.id,type:"branch-event",position:{x:E,y:z},data:{label:F.title,year:F.year,month:F.month,day:F.day,content:F.description||"",nodeType:"branch",color:R.color,isLeftSide:X,parentId:R.origin?.id||null}}),R.origin&&R.origin.id&&O.push({id:`e-${R.origin.id}-${F.id}`,source:R.origin.id,target:F.id,sourceHandle:X?"left":"right",targetHandle:X?"right":"left",type:"smoothstep",animated:!0,style:{strokeWidth:3,stroke:R.color}})})}),D.connections&&D.connections.length>0&&D.connections.forEach(R=>{O.push({id:`e-${R.source}-${R.target}-custom`,source:R.source,target:R.target,type:"smoothstep",animated:R.animated||!1,style:{strokeWidth:R.style?.strokeWidth||2,stroke:R.style?.stroke||"#409EFF"}})}),{nodes:A,edges:O}}Bo(()=>{l&&(clearInterval(l),l=null),document.removeEventListener("timeline-data-changed",()=>{}),document.removeEventListener("save-timeline",T),window.removeEventListener("beforeunload",Y)});function T(){$(!0)}function Y(D){if(c.value){const A="您有未保存的更改，确定要离开吗？";return D.returnValue=A,A}}return(D,A)=>{const O=Ki,R=Qa,G=Wa,F=Ji,x=qi,X=Ua;return re(),we("div",xp,[U("div",Sp,[U("div",kp,[U("h1",Np,Je(i.value)+" - 时间线",1),U("div",Cp,[Z(G,null,{default:ge(()=>[Z(O,{type:"success",plain:"",onClick:h,icon:V(Ka)},{default:ge(()=>A[4]||(A[4]=[Xe(" 导出数据 ")])),_:1},8,["icon"]),Z(O,{type:"success",plain:"",onClick:v,icon:V(qa)},{default:ge(()=>A[5]||(A[5]=[Xe(" 导出TXT ")])),_:1},8,["icon"]),Z(O,{type:"info",plain:"",onClick:y,icon:V(Ja)},{default:ge(()=>A[6]||(A[6]=[Xe(" 导入数据 ")])),_:1},8,["icon"]),Z(O,{type:"primary",plain:"",onClick:A[0]||(A[0]=E=>$(!0)),loading:u.value},{default:ge(()=>[Z(R,null,{default:ge(()=>[Z(V(ja))]),_:1}),A[7]||(A[7]=Xe(" 保存数据 "))]),_:1},8,["loading"]),Z(O,{class:"back-button",onClick:S,type:"primary",plain:""},{default:ge(()=>[Z(R,null,{default:ge(()=>[Z(V(es))]),_:1}),A[8]||(A[8]=Xe(" 返回写作 "))]),_:1})]),_:1})])]),Z(Ep,{ref_key:"timelineFlowRef",ref:r},null,512),Z(X,{modelValue:a.value,"onUpdate:modelValue":A[3]||(A[3]=E=>a.value=E),title:"导入时间线数据",width:"500px","close-on-click-modal":!1,class:"native-style-dialog"},{footer:ge(()=>[U("span",Mp,[Z(O,{onClick:A[2]||(A[2]=E=>a.value=!1)},{default:ge(()=>A[11]||(A[11]=[Xe("取消")])),_:1}),Z(O,{type:"primary",onClick:M},{default:ge(()=>A[12]||(A[12]=[Xe("导入")])),_:1})])]),default:ge(()=>[U("div",$p,[Z(F,{title:"导入说明",type:"info",closable:!1},{default:ge(()=>A[9]||(A[9]=[U("p",null,"请粘贴之前导出的JSON格式数据，支持以下方式导入：",-1),U("ol",{class:"import-instructions"},[U("li",null,"从导出功能复制的JSON数据"),U("li",null,"从已导出的JSON文件中复制内容")],-1)])),_:1}),Z(x,{modelValue:s.value,"onUpdate:modelValue":A[1]||(A[1]=E=>s.value=E),type:"textarea",rows:10,placeholder:"在此粘贴JSON数据...",class:"import-textarea"},null,8,["modelValue"]),U("div",Tp,[Z(R,null,{default:ge(()=>[Z(V(ts))]),_:1}),A[10]||(A[10]=U("span",null,"导入会替换当前所有时间线数据，请确保已保存重要内容",-1))])])]),_:1},8,["modelValue"])])])}}},Yp=Ln(Ip,[["__scopeId","data-v-df93f550"]]);export{Yp as default};
