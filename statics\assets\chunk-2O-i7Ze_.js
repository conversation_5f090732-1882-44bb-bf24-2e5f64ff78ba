import{_ as qe,a as Be,c as j,r as h,w as A,o as Le,E as u,$ as He,aR as G,b as D,m,d as t,g as s,bc as Ie,e as a,j as Oe,B as Pe,C,aU as Me,s as Fe,F as v,t as Je,v as p,a1 as Y,q as je,aD as Ae,R as $,S as q,M as b,aE as Ge,aY as Ye,aF as We,bn as Ke,cG as Qe,az as Xe,ax as Ze,bf as et,p as z,J as tt,aw as ot,bh as lt,a8 as st,bV as at,n as W,aS as K,ap as nt,bb as rt,b2 as it,k as dt,aG as ut,ah as N,Y as Q}from"./entry-DxFfH4M0.js";/* empty css                  *//* empty css                    *//* empty css                *//* empty css                    *//* empty css                *//* empty css                        *//* empty css                    *//* empty css               *//* empty css                 *//* empty css                 *//* empty css                        *//* empty css                  */const ct={class:"novel-download"},pt={class:"settings-panel"},ft={class:"settings-card"},mt={class:"main-content"},_t={class:"top-section"},vt={class:"url-input-section"},wt={class:"section-header"},gt={class:"url-input-content"},yt={class:"url-input-footer"},ht={class:"url-count"},bt={class:"url-actions"},kt={class:"download-settings"},Ct={class:"section-header"},Et={class:"settings-content"},Vt={class:"path-input-group"},St={class:"download-params"},Dt={class:"form-actions"},Tt={class:"chrome-config-section"},xt={class:"section-header"},Rt={class:"chrome-config-list"},Ut={class:"config-name"},zt={class:"config-path"},Nt={class:"config-tags"},$t={class:"task-management"},qt={class:"section-header"},Bt={class:"header-actions"},Lt={class:"task-list"},Ht=["onClick"],It={class:"task-info"},Ot={class:"task-title"},Pt={class:"time"},Mt={class:"rule"},Ft={class:"task-progress"},Jt={class:"task-status"},jt={class:"task-detail"},At={class:"detail-section"},Gt={class:"info-grid"},Yt={class:"info-item"},Wt={class:"value"},Kt={class:"info-item"},Qt={class:"value"},Xt={class:"info-item"},Zt={class:"value"},eo={class:"info-item"},to={class:"value"},oo={class:"detail-section"},lo={class:"section-title"},so={class:"log-actions"},ao={class:"log-content"},no={class:"message"},ro={key:1,class:"no-logs"},io={class:"table-container"},uo={class:"dialog-footer"},co={class:"dialog-footer"},po={__name:"小说下载",setup(fo){window.bridge=window.pywebview.api.drssion_controller;const _=Be(),T=j(()=>_.state.config.novel.rules||{}),B=j(()=>_.state.config.chrome.userDataDirs||[]),d=h({url:"",rule:"",chapterCount:30,intervalTime:1,downloadPath:_.state.config.chrome.downloadDir||"",chromeConfigId:_.state.config.chrome.userDataDirs.find(o=>o.isDefault)?.id||""});h([]),h([]),h(!1);const x=h({visible:!1}),i=h({visible:!1,isEdit:!1,form:{id:"",name:"",book_title_rule:"",directory_rule:"",content_rule:"",description_rule:"",chapter_title_rule:"",need_decrypt:!1}}),X=["qidian","fanqie","feilu","ciweimao","qimao"],Z=o=>X.includes(o),ee=async()=>{u({message:"正在加载规则列表...",type:"info",duration:1e3}),await _.loadNovelRules(),x.value.visible=!0},te=()=>{i.value.isEdit=!1,i.value.form={id:crypto.randomUUID(),name:"",book_title_rule:"",directory_rule:"",content_rule:"",description_rule:"",chapter_title_rule:"",need_decrypt:!1},i.value.visible=!0},oe=o=>{i.value.isEdit=!0,i.value.form={...o},i.value.visible=!0},le=async o=>{try{await N.confirm("确定要删除该规则吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await _.deleteNovelRule(o.id);e.status==="success"?(u.success("删除成功"),await _.loadNovelRules(),await k()):u.error(e.message||"删除失败")}catch(e){e!=="cancel"&&u.error("删除失败")}},se=async()=>{try{if(!i.value.form.name)throw new Error("规则名不能为空");if(!i.value.form.book_title_rule)throw new Error("书名选择器不能为空");if(!i.value.form.directory_rule)throw new Error("目录选择器不能为空");if(!i.value.form.content_rule)throw new Error("内容选择器不能为空");if(!i.value.form.description_rule)throw new Error("简介选择器不能为空");await _.saveNovelRule(i.value.form),u.success("保存成功"),i.value.visible=!1,await _.loadNovelRules(),await k()}catch(o){u.error(o.message)}},ae=async o=>{try{const e=await N.prompt("请输入要测试的URL","测试规则",{inputPattern:/^https?:\/\/.+/,inputErrorMessage:"请输入正确的URL"});u({message:"正在测试规则，请稍候...",type:"info",duration:2e3});const n=await _.testNovelRule({rule:o,url:e.value});await N.alert(JSON.stringify(n,null,2),"测试结果",{closeOnClickModal:!0}),await _.loadNovelRules()}catch(e){e!=="cancel"&&u.error(e.message||"测试失败")}},E=h([]),L={initializing:{type:"info",text:"初始化"},running:{type:"primary",text:"下载中"},completed:{type:"success",text:"已完成"},failed:{type:"danger",text:"失败"},stopped:{type:"warning",text:"已停止"}},ne=o=>L[o]?.type||"info",re=o=>L[o]?.text||o,ie=o=>o==="completed"?"success":o==="failed"?"exception":o==="stopped"?"warning":"",H=o=>{if(!o)return"";const e=new Date(o);return`${e.getFullYear()}-${(e.getMonth()+1).toString().padStart(2,"0")}-${e.getDate().toString().padStart(2,"0")} ${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}`},de=async()=>{try{const o=B.value.find(c=>c.id===d.value.chromeConfigId);if(!o)throw new Error("请选择Chrome配置");const e=d.value.url.split(`
`).map(c=>c.trim()).filter(c=>c&&c.startsWith("http"));if(e.length===0)throw new Error("请输入有效的小说网址");if(!d.value.rule)throw new Error("请选择规则");Object.keys(T.value).length===0&&await _.loadNovelRules();const n=T.value[d.value.rule];if(!n)throw new Error("选择的规则无效");const r={urls:e,rule:n,chapter_count:d.value.chapterCount||0,interval_time:d.value.intervalTime||3,download_path:d.value.downloadPath,chrome_config:{user_data_dir:`${o.path}/${o.name}`,port:o.port,load_extensions:o.enableExtensions}};u({message:"正在创建任务...",type:"info",duration:2e3});const w=JSON.parse(await window.bridge.create_download_task(r));if(w.status==="success")u.success("任务创建成功"),U.value="tasks",await k();else throw new Error(w.message||"创建任务失败")}catch(o){u.error(o.message)}},ue=async o=>{try{u({message:"正在停止任务...",type:"info",duration:1e3});const e=JSON.parse(await window.bridge.stop_task(o));if(e.status==="success")u.success(e.message||"任务已停止"),await k();else throw new Error(e.message||"停止任务失败")}catch(e){u.error(e.message)}},ce=async o=>{try{const e=E.value.find(r=>r.id===o);if(!e)throw new Error("任务不存在");u({message:"正在重试任务...",type:"info",duration:2e3});const n=JSON.parse(await window.bridge.create_download_task(e.config));if(n.status==="success")u.success(n.message||"任务已重新启动"),await k();else throw new Error(n.message||"重试任务失败")}catch(e){u.error(e.message)}},k=async()=>{try{const o=JSON.parse(await window.bridge.get_tasks());if(o.status==="success"){const e=new Set(E.value.filter(n=>n.showDetail).map(n=>n.id));E.value=o.data.map(n=>({...n,showDetail:e.has(n.id)||!1}))}else throw new Error(o.message||"获取任务列表失败")}catch(o){console.error("刷新任务列表失败:",o),u.error("刷新任务列表失败: "+o.message)}},pe=()=>{window.receiveTaskOutput=(o,e)=>{try{const n=JSON.parse(atob(e)),r=E.value.find(w=>w.id===o);r&&(r.outputs||(r.outputs=[]),r.outputs.push(n),n.type==="status"&&(r.status=n.status,r.progress=n.progress||r.progress,r.book_info=n.book_info||r.book_info,r.chapters=n.chapters||r.chapters,r.error=n.error))}catch(n){console.error("处理任务更新失败:",n)}}},S=h(new Map),fe=o=>o.scrollHeight-o.scrollTop-o.clientHeight<20;A(E,o=>{o.forEach(e=>{e.showDetail&&e.outputs?.length&&Q(()=>{const n=document.querySelector(`[data-task-id="${e.id}"]`);if(n){const r=n.querySelector(".log-content");if(r){const w=S.value.get(e.id);(!w||w.autoScroll)&&(r.scrollTop=r.scrollHeight)}}})})},{deep:!0});const R=h(null),me=()=>{R.value=setInterval(k,3e3)},_e=()=>{R.value&&(clearInterval(R.value),R.value=null)};Le(async()=>{try{u({message:"正在加载数据...",type:"info",duration:1e3}),await _.loadNovelRules(),pe(),await k(),me(),u.success("数据加载完成")}catch(o){u.error("初始化数据失败: "+o.message)}});const ve=()=>{S.value.forEach((o,e)=>{const n=document.querySelector(`[data-task-id="${e}"]`);if(n){const r=n.querySelector(".log-content");r&&r._scrollHandler&&(r.removeEventListener("scroll",r._scrollHandler),delete r._scrollHandler)}}),S.value.clear()};He(()=>{_e(),ve()});const we=o=>{Q(()=>{const e=document.querySelector(`[data-task-id="${o}"]`);if(e){const n=e.querySelector(".log-content");if(n){n.removeEventListener("scroll",n._scrollHandler);const r=()=>{const w=fe(n);S.value.set(o,{autoScroll:w})};n._scrollHandler=r,n.addEventListener("scroll",r),S.value.set(o,{autoScroll:!0}),n.scrollTop=n.scrollHeight}}})},ge=o=>{const e=document.querySelector(`[data-task-id="${o}"]`);if(e){const n=e.querySelector(".log-content");n&&(n.scrollTop=n.scrollHeight,S.value.set(o,{autoScroll:!0}))}},ye=o=>{E.value.forEach(e=>{e.id!==o.id&&(e.showDetail=!1)}),o.showDetail=!o.showDetail,o.showDetail&&we(o.id)},he=async o=>{try{const e=await window.pywebview.api.select_directory(),r=(typeof e=="string"?JSON.parse(e):e)?.data;r&&o==="downloadPath"&&(d.value.downloadPath=r,await _.updateConfigItem("chrome.downloadDir",r))}catch(e){u.error("选择目录失败: "+e.message)}},I=o=>{d.value.chromeConfigId=o.id},be=()=>d.value.url?d.value.url.split(`
`).filter(o=>o.trim()).length:0,ke=()=>{if(!d.value.url)return;const o=d.value.url.split(`
`).map(e=>e.trim()).filter(e=>e).sort().join(`
`);d.value.url=o,u.success("URL格式已整理")},Ce=()=>{N.confirm("确定要清空所有URL吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{d.value.url="",u.success("URL已清空")}).catch(()=>{})},U=h("settings");A(U,async o=>{o==="tasks"?await k():o==="settings"&&await _.loadNovelRules()},{immediate:!1});const Ee=async o=>{try{await window.pywebview.api.open_directory(o),u.success("已打开下载目录")}catch(e){u.error("打开目录失败: "+e.message)}};return(o,e)=>{const n=Pe,r=Fe,w=G("Sort"),c=Je,Ve=G("Delete"),Se=Ge,De=Ae,y=je,O=We,Te=et,g=Ze,V=tt,P=Xe,M=Oe,F=Ie,xe=at,Re=lt,Ue=rt,ze=it,J=dt,Ne=ut;return m(),D("div",ct,[t(Ue,{modelValue:U.value,"onUpdate:modelValue":e[7]||(e[7]=l=>U.value=l),class:"download-tabs"},{default:s(()=>[t(F,{label:"下载设置",name:"settings"},{default:s(()=>[a("div",pt,[a("div",ft,[t(M,{model:d.value,"label-width":"80px",class:"create-task-form"},{default:s(()=>[a("div",mt,[a("div",_t,[a("div",vt,[a("div",wt,[t(n,null,{default:s(()=>[t(C(Me))]),_:1}),e[18]||(e[18]=a("span",{class:"title"},"批量输入网址",-1))]),a("div",gt,[t(r,{modelValue:d.value.url,"onUpdate:modelValue":e[0]||(e[0]=l=>d.value.url=l),type:"textarea",rows:8,placeholder:"请输入小说网址，每行一个网址",resize:"none",spellcheck:"false",autocomplete:"off",autocorrect:"off",autocapitalize:"off"},null,8,["modelValue"]),a("div",yt,[a("span",ht,"已输入 "+v(be())+" 个网址",1),a("div",bt,[t(c,{type:"primary",link:"",onClick:ke},{default:s(()=>[t(n,null,{default:s(()=>[t(w)]),_:1}),e[19]||(e[19]=p(" 整理格式 "))]),_:1}),t(c,{type:"danger",link:"",onClick:Ce},{default:s(()=>[t(n,null,{default:s(()=>[t(Ve)]),_:1}),e[20]||(e[20]=p(" 清空 "))]),_:1})])])])]),a("div",kt,[a("div",Ct,[t(n,null,{default:s(()=>[t(C(Y))]),_:1}),e[22]||(e[22]=a("span",{class:"title"},"下载设置",-1)),t(c,{type:"primary",link:"",class:"manage-rules-btn",onClick:ee},{default:s(()=>[t(n,null,{default:s(()=>[t(C(Y))]),_:1}),e[21]||(e[21]=p(" 规则管理【标签】 "))]),_:1})]),a("div",Et,[t(y,{label:"规则",required:""},{default:s(()=>[t(De,{modelValue:d.value.rule,"onUpdate:modelValue":e[1]||(e[1]=l=>d.value.rule=l),placeholder:"请选择规则"},{default:s(()=>[(m(!0),D($,null,q(T.value,(l,f)=>(m(),b(Se,{key:f,label:l.name,value:f},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(y,{label:"目录",required:""},{default:s(()=>[a("div",Vt,[t(r,{modelValue:d.value.downloadPath,"onUpdate:modelValue":e[2]||(e[2]=l=>d.value.downloadPath=l),placeholder:"请选择下载目录",spellcheck:"false",autocomplete:"off",autocorrect:"off",autocapitalize:"off"},null,8,["modelValue"]),t(c,{type:"primary",onClick:e[3]||(e[3]=l=>he("downloadPath"))},{default:s(()=>[t(n,null,{default:s(()=>[t(C(Ye))]),_:1})]),_:1})])]),_:1}),a("div",St,[t(y,{label:"章节数"},{default:s(()=>[t(O,{modelValue:d.value.chapterCount,"onUpdate:modelValue":e[4]||(e[4]=l=>d.value.chapterCount=l),min:0,step:10,"controls-position":"right",placeholder:"全部"},null,8,["modelValue"])]),_:1}),t(y,{label:"间隔(秒)"},{default:s(()=>[t(O,{modelValue:d.value.intervalTime,"onUpdate:modelValue":e[5]||(e[5]=l=>d.value.intervalTime=l),min:1,max:60,step:1,"controls-position":"right"},null,8,["modelValue"])]),_:1})])]),a("div",Dt,[t(c,{type:"primary",size:"large",onClick:de},{default:s(()=>[t(n,null,{default:s(()=>[t(C(Ke))]),_:1}),e[23]||(e[23]=p(" 创建下载任务 "))]),_:1})])])]),a("div",Tt,[a("div",xt,[t(n,null,{default:s(()=>[t(C(Qe))]),_:1}),e[24]||(e[24]=a("span",{class:"title"},"Chrome配置",-1))]),a("div",Rt,[t(P,{data:B.value,style:{width:"100%"},"highlight-current-row":"",onCurrentChange:I},{default:s(()=>[t(g,{width:"120"},{default:s(l=>[t(Te,{modelValue:d.value.chromeConfigId,"onUpdate:modelValue":e[6]||(e[6]=f=>d.value.chromeConfigId=f),label:l.row.id,onChange:()=>I(l.row)},{default:s(()=>e[25]||(e[25]=[a("span",{class:"sr-only"},"选择 ",-1)])),_:2},1032,["modelValue","label","onChange"])]),_:1}),t(g,{prop:"name",label:"配置名称","min-width":"120"},{default:s(l=>[a("span",Ut,v(l.row.name),1)]),_:1}),t(g,{prop:"path",label:"路径","min-width":"200","show-overflow-tooltip":""},{default:s(l=>[a("span",zt,v(l.row.path),1)]),_:1}),t(g,{label:"标签",width:"120",align:"right"},{default:s(l=>[a("div",Nt,[t(V,{size:"small",class:"port-badge"},{default:s(()=>[p(v(l.row.port),1)]),_:2},1024),l.row.enableExtensions?(m(),b(V,{key:0,size:"small",type:"warning"},{default:s(()=>e[26]||(e[26]=[p("扩展")])),_:1})):z("",!0)])]),_:1})]),_:1},8,["data"])])])])]),_:1},8,["model"])])])]),_:1}),t(F,{label:"任务管理",name:"tasks"},{default:s(()=>[a("div",$t,[a("div",qt,[e[28]||(e[28]=a("h2",{class:"section-title"},"任务管理",-1)),a("div",Bt,[t(c,{type:"primary",onClick:k},{default:s(()=>[t(n,null,{default:s(()=>[t(C(ot))]),_:1}),e[27]||(e[27]=p(" 刷新列表 "))]),_:1})])]),a("div",Lt,[(m(!0),D($,null,q(E.value,l=>(m(),b(Re,{key:l.id,class:"task-card","body-style":{padding:"0"},"data-task-id":l.id},{default:s(()=>[a("div",{class:"task-header",onClick:f=>ye(l)},[a("div",It,[a("div",Ot,[a("span",Pt,v(H(l.created_at)),1),a("span",Mt,v(l.config.rule.name),1)]),a("div",Ft,[t(xe,{percentage:l.progress,status:ie(l.status),"stroke-width":8},null,8,["percentage","status"])])]),a("div",Jt,[t(V,{type:ne(l.status),size:"small"},{default:s(()=>[p(v(re(l.status)),1)]),_:2},1032,["type"]),t(n,{class:W(["expand-icon",{"is-active":l.showDetail}])},{default:s(()=>[t(C(K))]),_:2},1032,["class"])])],8,Ht),st(a("div",jt,[a("div",At,[e[33]||(e[33]=a("div",{class:"section-title"},"任务信息",-1)),a("div",Gt,[a("div",Yt,[e[29]||(e[29]=a("span",{class:"label"},"创建时间：",-1)),a("span",Wt,v(H(l.created_at)),1)]),a("div",Kt,[e[30]||(e[30]=a("span",{class:"label"},"下载规则：",-1)),a("span",Qt,v(l.config.rule.name),1)]),a("div",Xt,[e[31]||(e[31]=a("span",{class:"label"},"总进度：",-1)),a("span",Zt,v(l.progress)+"%",1)]),a("div",eo,[e[32]||(e[32]=a("span",{class:"label"},"下载目录：",-1)),a("span",to,v(l.config.download_path),1)])])]),a("div",oo,[a("div",lo,[e[38]||(e[38]=a("span",null,"任务日志",-1)),a("div",so,[t(c,{type:"info",size:"small",onClick:f=>ge(l.id),title:"跳转到最新日志"},{default:s(()=>[t(n,null,{default:s(()=>[t(C(K))]),_:1}),e[34]||(e[34]=p(" 底部 "))]),_:2},1032,["onClick"]),l.status==="running"?(m(),b(c,{key:0,type:"warning",size:"small",onClick:f=>ue(l.id)},{default:s(()=>e[35]||(e[35]=[p(" 停止任务 ")])),_:2},1032,["onClick"])):z("",!0),l.status==="failed"?(m(),b(c,{key:1,type:"primary",size:"small",onClick:f=>ce(l.id)},{default:s(()=>e[36]||(e[36]=[p(" 重试任务 ")])),_:2},1032,["onClick"])):z("",!0),l.status==="completed"?(m(),b(c,{key:2,type:"success",size:"small",onClick:f=>Ee(l.config.download_path)},{default:s(()=>e[37]||(e[37]=[p(" 打开目录 ")])),_:2},1032,["onClick"])):z("",!0)])]),a("div",ao,[l.outputs&&l.outputs.length?(m(!0),D($,{key:0},q(l.outputs,(f,$e)=>(m(),D("div",{key:$e,class:W(["log-line",f.type])},[f.type==="error"?(m(),b(V,{key:0,type:"danger",size:"small",effect:"dark"},{default:s(()=>e[39]||(e[39]=[p(" 错误 ")])),_:1})):f.type==="success"?(m(),b(V,{key:1,type:"success",size:"small",effect:"dark"},{default:s(()=>e[40]||(e[40]=[p(" 成功 ")])),_:1})):f.type==="warning"?(m(),b(V,{key:2,type:"warning",size:"small",effect:"dark"},{default:s(()=>e[41]||(e[41]=[p(" 警告 ")])),_:1})):(m(),b(V,{key:3,type:"info",size:"small",effect:"dark"},{default:s(()=>e[42]||(e[42]=[p(" 信息 ")])),_:1})),a("span",no,v(f.message),1)],2))),128)):(m(),D("div",ro," 暂无日志信息 "))])])],512),[[nt,l.showDetail]])]),_:2},1032,["data-task-id"]))),128))])])]),_:1})]),_:1},8,["modelValue"]),t(J,{modelValue:x.value.visible,"onUpdate:modelValue":e[9]||(e[9]=l=>x.value.visible=l),title:"规则管理",width:"80%",class:"rule-dialog","close-on-click-modal":!1,"close-on-press-escape":!1,"modal-append-to-body":!1},{footer:s(()=>[a("div",uo,[t(c,{onClick:e[8]||(e[8]=l=>x.value.visible=!1)},{default:s(()=>e[46]||(e[46]=[p("关闭")])),_:1}),t(c,{type:"primary",onClick:te},{default:s(()=>e[47]||(e[47]=[p("添加规则")])),_:1})])]),default:s(()=>[a("div",io,[t(P,{data:Object.entries(T.value).map(([l,f])=>({id:l,...f})),style:{width:"100%"},height:"500px"},{default:s(()=>[t(g,{prop:"name",label:"规则名称","min-width":"120"}),t(g,{prop:"book_title_rule",label:"书名选择器","min-width":"150","show-overflow-tooltip":""}),t(g,{prop:"directory_rule",label:"目录选择器","min-width":"150","show-overflow-tooltip":""}),t(g,{prop:"content_rule",label:"内容选择器","min-width":"150","show-overflow-tooltip":""}),t(g,{prop:"description_rule",label:"简介选择器","min-width":"150","show-overflow-tooltip":""}),t(g,{prop:"need_decrypt",label:"要解密",width:"100"},{default:s(l=>[p(v(l.row.need_decrypt?"是":"否"),1)]),_:1}),t(g,{label:"操作",width:"200",fixed:"right"},{default:s(l=>[t(ze,null,{default:s(()=>[t(c,{type:"primary",link:"",onClick:f=>oe(l.row)},{default:s(()=>e[43]||(e[43]=[p("编辑")])),_:2},1032,["onClick"]),t(c,{type:"danger",link:"",onClick:f=>le(l.row),disabled:Z(l.row.id)},{default:s(()=>e[44]||(e[44]=[p("删除")])),_:2},1032,["onClick","disabled"]),t(c,{type:"success",link:"",onClick:f=>ae(l.row)},{default:s(()=>e[45]||(e[45]=[p("测试")])),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])])]),_:1},8,["modelValue"]),t(J,{modelValue:i.value.visible,"onUpdate:modelValue":e[17]||(e[17]=l=>i.value.visible=l),title:i.value.isEdit?"编辑规则":"添加规则",width:"50%",class:"edit-rule-dialog","close-on-click-modal":!1,"close-on-press-escape":!1,"modal-append-to-body":!1},{footer:s(()=>[a("span",co,[t(c,{onClick:e[16]||(e[16]=l=>i.value.visible=!1)},{default:s(()=>e[48]||(e[48]=[p("取消")])),_:1}),t(c,{type:"primary",onClick:se},{default:s(()=>e[49]||(e[49]=[p("保存")])),_:1})])]),default:s(()=>[t(M,{model:i.value.form,"label-width":"120px"},{default:s(()=>[t(y,{label:"规则名称",required:""},{default:s(()=>[t(r,{modelValue:i.value.form.name,"onUpdate:modelValue":e[10]||(e[10]=l=>i.value.form.name=l),spellcheck:"false",autocomplete:"off",autocorrect:"off",autocapitalize:"off"},null,8,["modelValue"])]),_:1}),t(y,{label:"书名选择器",required:""},{default:s(()=>[t(r,{modelValue:i.value.form.book_title_rule,"onUpdate:modelValue":e[11]||(e[11]=l=>i.value.form.book_title_rule=l),spellcheck:"false",autocomplete:"off",autocorrect:"off",autocapitalize:"off"},null,8,["modelValue"])]),_:1}),t(y,{label:"目录选择器",required:""},{default:s(()=>[t(r,{modelValue:i.value.form.directory_rule,"onUpdate:modelValue":e[12]||(e[12]=l=>i.value.form.directory_rule=l),spellcheck:"false",autocomplete:"off",autocorrect:"off",autocapitalize:"off"},null,8,["modelValue"])]),_:1}),t(y,{label:"内容选择器",required:""},{default:s(()=>[t(r,{modelValue:i.value.form.content_rule,"onUpdate:modelValue":e[13]||(e[13]=l=>i.value.form.content_rule=l),spellcheck:"false",autocomplete:"off",autocorrect:"off",autocapitalize:"off"},null,8,["modelValue"])]),_:1}),t(y,{label:"简介选择器",required:""},{default:s(()=>[t(r,{modelValue:i.value.form.description_rule,"onUpdate:modelValue":e[14]||(e[14]=l=>i.value.form.description_rule=l),spellcheck:"false",autocomplete:"off",autocorrect:"off",autocapitalize:"off"},null,8,["modelValue"])]),_:1}),t(y,{label:"需要解密"},{default:s(()=>[t(Ne,{modelValue:i.value.form.need_decrypt,"onUpdate:modelValue":e[15]||(e[15]=l=>i.value.form.need_decrypt=l)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},Do=qe(po,[["__scopeId","data-v-a18a9281"]]);export{Do as default};
