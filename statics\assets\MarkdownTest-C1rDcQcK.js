import{_ as m,r as s,b as _,e,d as l,aR as n,m as v}from"./entry-DxFfH4M0.js";const c={class:"markdown-test"},V={class:"test-section"},x={class:"test-section"},M={class:"test-section"},f={__name:"MarkdownTest",setup(w){const d=s(`# 基础编辑器测试

这是一个**粗体**文本和*斜体*文本。

## 代码块

\`\`\`javascript
console.log('Hello World!')
\`\`\`

## 列表

- 项目 1
- 项目 2
- 项目 3

## 表格

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
`),r=s(`# CodeMirror 编辑器测试

这是使用 **CodeMirror** 的高级编辑器。

## 特性

- 语法高亮
- 代码折叠
- 智能提示

\`\`\`python
def hello_world():
    print("Hello, World!")
\`\`\`
`),a=s(`# 预览组件测试

这是一个纯预览组件，用于显示已经编写好的 Markdown 内容。

## 格式化测试

### 文本样式
- **粗体文本**
- *斜体文本*
- ~~删除线文本~~
- \`行内代码\`

### 引用
> 这是一个引用块
> 可以包含多行内容

### 代码块
\`\`\`css
.example {
  color: #333;
  font-size: 16px;
}
\`\`\`

### 链接和图片
[Vue.js](https://vuejs.org/)

### 数学公式（如果支持）
$E = mc^2$
`);return(k,o)=>{const i=n("VMdPreview"),u=n("v-md-editor"),p=n("VMdEditor");return v(),_("div",c,[o[5]||(o[5]=e("h2",null,"Vue Markdown Editor 测试",-1)),e("div",V,[o[2]||(o[2]=e("h3",null,"预览组件测试",-1)),l(i,{text:a.value},null,8,["text"])]),e("div",x,[o[3]||(o[3]=e("h3",null,"基础编辑器测试",-1)),l(u,{modelValue:d.value,"onUpdate:modelValue":o[0]||(o[0]=t=>d.value=t),height:"300px"},null,8,["modelValue"])]),e("div",M,[o[4]||(o[4]=e("h3",null,"CodeMirror 编辑器测试",-1)),l(p,{modelValue:r.value,"onUpdate:modelValue":o[1]||(o[1]=t=>r.value=t),height:"300px"},null,8,["modelValue"])])])}}},g=m(f,[["__scopeId","data-v-3722fb60"]]);export{g as default};
