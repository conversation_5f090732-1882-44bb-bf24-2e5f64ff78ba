import{_ as Te,r as c,bt as xe,c as A,o as Ce,$ as Le,E as l,b as h,m,e as r,d as o,g as n,t as Ee,B as De,C as E,aw as Ve,s as Se,p as U,M as J,v as f,T as Ne,U as Ue,F as $,a4 as $e,cF as Fe,V as Be,ad as Re,ae as Oe,ab as ze,aE as Pe,aD as We,aG as Ie,bK as Ae,R as Je,S as qe,au as Ge,a9 as je,ba as Ke,k as Ye,Y as D}from"./entry-DxFfH4M0.js";/* empty css                  *//* empty css                 *//* empty css                        *//* empty css                   *//* empty css                  *//* empty css               *//* empty css                  *//* empty css                 *//* empty css                    */import{p as Qe}from"./purify-es-DhD2mIk-.js";const Xe={class:"mhtml-reader"},Ze={class:"reader-container"},et={class:"sidebar"},tt={class:"directory-input"},ot={class:"custom-tree-node"},lt={key:0,class:"note-indicator"},at={class:"content"},nt={key:0,class:"placeholder"},st={key:1,class:"reader-view"},rt={class:"reader-header"},it={class:"actions"},ct={key:0,class:"loading-overlay"},dt=["srcdoc"],ut={key:2,class:"editor-view"},mt=["srcdoc"],pt={class:"notes-panel"},vt={key:0,class:"no-notes"},ft={key:1,class:"notes-list"},yt={class:"note-header"},ht={class:"note-title"},wt={class:"reading-progress"},_t={class:"extract-dialog-content"},gt={class:"extract-actions"},bt={__name:"阅读器",setup(Mt){const p=c(!1),q=c([]),v=c(null),w=c(null),T=c([]),_=c(!1),Z=c(null),x=c(null),g=c(null),ee=c(null),V=c(""),te=c(null),oe=c(null),G=c(null),y=c("normal"),b=c("utf-8"),S=c(""),C=c(""),R=c(0),u=xe({visible:!1,outputDir:"",mainHtmlFile:"",mainHtmlPath:"",fileCount:0});A(()=>{if(!w.value)return"";if(V.value)return V.value;try{const t=atob(w.value),e=[],a=512;for(let k=0;k<t.length;k+=a){const N=t.slice(k,k+a),H=new Array(N.length);for(let L=0;L<N.length;L++)H[L]=N.charCodeAt(L);e.push(new Uint8Array(H))}const s=new Blob(e,{type:"message/rfc822"}),i=URL.createObjectURL(s);return V.value=i,i}catch(t){return console.error("创建MHTML URL失败:",t),`data:message/rfc822;base64,${w.value}`}});const le=A(()=>S.value?Qe.sanitize(S.value):""),ae={children:"children",label:"name"};Ce(()=>{window.addEventListener("message",Q),j()}),Le(()=>{window.removeEventListener("message",Q),V.value&&URL.revokeObjectURL(V.value)});const O=async()=>{if(!C.value){l.warning("请输入有效的目录路径");return}p.value=!0;try{const t=await window.pywebview.api.local_controller.load_dir_mhtml(C.value),e=typeof t=="string"?JSON.parse(t):t;e.status==="success"?(q.value=e.data||[],e.data&&e.data.length>0?l.success("加载目录成功"):l.info("目录为空或不存在MHTML文件")):l.error("加载目录失败："+(e?.message||"未知错误"))}catch(t){console.error("加载目录出错:",t),l.error("加载目录出错: "+t.toString())}finally{p.value=!1}},j=async()=>{C.value?await O():(C.value="",await O())},F=A(()=>{if(!w.value)return"<html><body><div>无内容可显示</div></body></html>";if(y.value==="normal")return le.value||"<html><body><div>正在处理内容...</div></body></html>";if(y.value==="raw")return`<html><body><pre style="white-space: pre-wrap; word-break: break-all;">${w.value}</pre></body></html>`;if(y.value==="decoded")try{const t=atob(w.value);return`<html><body><pre style="white-space: pre-wrap; word-break: break-all;">${z(t)}</pre></body></html>`}catch(t){return`<html><body><div>解码失败: ${t.message}</div></body></html>`}else if(y.value==="plainhtml")try{const e=atob(w.value).match(/<html[\s\S]*<\/html>/i);return e?e[0]:"<html><body><div>未找到HTML内容</div></body></html>"}catch(t){return`<html><body><div>解析失败: ${t.message}</div></body></html>`}else if(y.value==="simple")try{const t=atob(w.value);let e="";return e=t.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g,""),`
        <html>
        <head>
          <meta charset="utf-8">
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.5;
              padding: 20px;
              background: #fff;
              color: #333;
            }
            pre {
              white-space: pre-wrap;
              word-break: break-all;
              background: #f8f8f8;
              padding: 15px;
              border: 1px solid #ddd;
              border-radius: 4px;
              font-size: 14px;
              max-height: none;
              overflow: visible;
            }
          </style>
        </head>
        <body>
          <h2>MHTML文件内容（简单文本模式）</h2>
          <pre>${z(e)}</pre>
        </body>
        </html>
      `}catch(t){return`<html><body><div>解析文件失败: ${t.message}</div></body></html>`}return"<html><body><div>未知视图模式</div></body></html>"}),z=t=>t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;"),ne=async t=>{if(t.type==="file"&&t.fileType==="mhtml")try{p.value=!0;const e=await window.pywebview.api.local_controller.get_mhtml_content(t.path),a=typeof e=="string"?JSON.parse(e):e;if(a.status==="success"&&a.data){v.value=t,w.value=a.data.content,T.value=a.data.notes||[];const s=await window.pywebview.api.local_controller.improve_mhtml_rendering(t.path,b.value),i=typeof s=="string"?JSON.parse(s):s;i.status==="success"&&i.data?(S.value=i.data.html,i.data.charset&&(b.value=i.data.charset),y.value="normal",l.success(`文件加载成功，包含${i.data.resourceCount}个资源`),D(()=>{M()})):(console.warn("增强渲染失败，回退到简单模式"),y.value="simple",D(()=>{M()}),l.warning("渲染失败，显示简单内容")),_.value=!1}else l.error("加载文件失败："+(a?.message||"未知错误"))}catch(e){console.error("加载文件出错:",e),l.error("加载文件出错: "+e.toString()),y.value="simple",D(()=>{M()})}finally{p.value=!1}},se=t=>{_.value=t,D(()=>{pe(),t&&re()})},re=()=>{g.value&&(g.value.onload=()=>{try{const t=g.value.contentDocument||g.value.contentWindow.document;console.log("iframe loaded, ready for annotations")}catch(t){console.error("无法访问iframe内容:",t)}})},ie=()=>{T.value.push({id:Date.now(),content:"",position:{x:0,y:0},timestamp:new Date().toISOString()})},ce=t=>{T.value.splice(t,1)},de=async()=>{if(v.value)try{p.value=!0;const t=await window.pywebview.api.local_controller.save_mhtml_with_notes(v.value.path,null,T.value),e=typeof t=="string"?JSON.parse(t):t;e.status==="success"?(l.success("笔记保存成功"),await j()):l.error("保存笔记失败："+(e?.message||"未知错误"))}catch(t){console.error("保存笔记出错:",t),l.error("保存笔记出错: "+t.toString())}finally{p.value=!1}},K=t=>{console.log("iframe已加载");try{const e=t.target,a=e.contentDocument||e.contentWindow.document;if(P(e),a&&a.body){const s=a.body.innerHTML;console.log(`iframe内容加载完成，内容大小: ${s.length}`),s.length<10?(console.warn("iframe内容可能为空"),l.warning("文档内容可能未正确加载")):l.success("文档加载成功")}else console.warn("iframe文档为空"),l.warning("文档可能未正确加载"),M();W()}catch(e){console.error("访问iframe内容失败:",e),M()}},M=()=>{if(_.value){if(g.value){const t=g.value.contentDocument||g.value.contentWindow.document;t.open(),t.write(F.value),t.close(),D(()=>{P(g.value)})}}else{const t=x.value;if(t){const e=t.contentDocument||t.contentWindow.document;e.open(),e.write(F.value),e.close(),D(()=>{P(t)})}}},ue=async()=>{if(v.value)try{p.value=!0,console.log("字符集变更为:",b.value);const t=await window.pywebview.api.local_controller.process_mhtml_to_html(v.value.path,b.value),e=typeof t=="string"?JSON.parse(t):t;e.status==="success"&&e.data?(S.value=e.data.html,M(),l.success(`已切换到${b.value}字符集`)):l.error("更新内容失败："+(e?.message||"未知错误"))}catch(t){console.error("更新字符集出错:",t),l.error("更新字符集出错: "+t.toString())}finally{p.value=!1}},Y=()=>({normal:"正常模式",raw:"原始内容",decoded:"解码内容",plainhtml:"直接HTML",simple:"简单文本模式"})[y.value]||"正常模式",me=t=>{y.value=t,console.log(`切换到${t}模式`),l.success(`已切换到${Y()}`),M()};function P(t){try{const e=t.contentDocument||t.contentWindow.document;e.querySelectorAll("a").forEach(i=>{i.addEventListener("click",k=>{k.preventDefault(),console.log("链接点击被阻止:",i.href)})}),e.querySelectorAll("img").forEach(i=>{i.onerror=function(){this.style.display="none",console.log("图片加载失败:",this.src)}})}catch(e){console.error("修复iframe链接时出错:",e)}}const pe=()=>{try{const t=_.value?oe.value:te.value;if(!t||!G.value){console.warn("找不到HTML容器或内容为空");return}t.innerHTML=`
      <div class="html-container" style="width:100%; height:100%; overflow:auto;">
        ${G.value}
      </div>
    `,console.log("已渲染HTML内容"),l.success("文档已加载")}catch(t){console.error("渲染HTML内容失败:",t),l.error("显示文档内容失败")}},Q=t=>{t.data==="mhtml-loaded"&&(console.log("MHTML内容加载完成"),l.success("文档加载成功"),setTimeout(()=>{W()},500))},W=()=>{if(x.value)try{const t=x.value,e=t.contentDocument||t.contentWindow.document;if(!e||!e.body){console.warn("iframe文档尚未准备好，稍后重试"),setTimeout(W,500);return}t.contentWindow.addEventListener("scroll",()=>{const a=e.documentElement.scrollHeight-e.documentElement.clientHeight,s=e.documentElement.scrollTop||e.body.scrollTop;R.value=Math.round(s/a*100)}),console.log("滚动跟踪设置成功")}catch(t){console.error("设置滚动跟踪失败:",t)}},ve=t=>{if(x.value)try{const e=x.value,a=e.contentDocument||e.contentWindow.document,s=a.documentElement.scrollHeight-a.documentElement.clientHeight,i=t/100*s;a.documentElement.scrollTop=i,a.body.scrollTop=i}catch(e){console.error("滚动到位置失败:",e)}},fe=async()=>{try{if(!v.value)return;p.value=!0;const t=await window.pywebview.api.local_controller.get_mhtml_content(v.value.path),e=typeof t=="string"?JSON.parse(t):t;if(e.status==="success"&&e.data)try{const s=atob(e.data.content).replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g,""),i=`
          <html>
          <head>
            <meta charset="${b.value}">
            <style>
              body { font-family: monospace; white-space: pre-wrap; padding: 20px; line-height: 1.5; }
            </style>
          </head>
          <body>${z(s)}</body>
          </html>
        `;S.value=i,y.value="normal",M(),l.success("已使用应急模式显示文件内容")}catch(a){l.error("应急显示失败: "+a.toString())}else l.error("获取文件内容失败")}catch(t){console.error("应急显示出错:",t),l.error("应急显示出错: "+t.toString())}finally{p.value=!1}},ye=async()=>{try{if(!v.value)return;p.value=!0,l.info("正在提取MHTML文件，请稍候...");const t=await window.pywebview.api.local_controller.extract_mhtml(v.value.path),e=typeof t=="string"?JSON.parse(t):t;e.status==="success"&&e.data?(u.outputDir=e.data.outputDir,u.mainHtmlFile=e.data.mainHtmlFile,u.mainHtmlPath=e.data.mainHtmlPath,u.fileCount=e.data.fileCount,u.visible=!0,l.success(`MHTML提取成功，共提取了${e.data.fileCount}个文件`)):l.error("提取MHTML失败："+(e?.message||"未知错误"))}catch(t){console.error("提取MHTML出错:",t),l.error("提取MHTML出错: "+t.toString())}finally{p.value=!1}},he=async()=>{try{if(!u.outputDir)return;await window.pywebview.api.local_controller.open_directory(u.outputDir)}catch(t){console.error("打开目录失败:",t),l.error("打开目录失败: "+t.toString())}},we=async()=>{try{if(!u.mainHtmlPath)return;await window.pywebview.api.local_controller.open_file(u.mainHtmlPath)}catch(t){console.error("打开文件失败:",t),l.error("打开文件失败: "+t.toString())}};return(t,e)=>{const a=De,s=Ee,i=Se,k=Fe,N=Be,H=Oe,L=Re,_e=ze,B=Pe,ge=We,be=Ie,Me=Ae,ke=Ke,He=Ye;return m(),h("div",Xe,[r("div",Ze,[r("div",et,[r("div",tt,[o(i,{modelValue:C.value,"onUpdate:modelValue":e[0]||(e[0]=d=>C.value=d),placeholder:"输入MHTML文件目录路径",size:"small"},{append:n(()=>[o(s,{onClick:O},{default:n(()=>[o(a,null,{default:n(()=>[o(E(Ve))]),_:1})]),_:1})]),_:1},8,["modelValue"])]),e[6]||(e[6]=r("h3",null,"MHTML文件目录",-1)),o(k,{data:q.value,props:ae,onNodeClick:ne,"default-expand-all":!0,"node-key":"path"},{default:n(({data:d})=>[r("div",ot,[r("span",null,[d.type==="directory"?(m(),J(a,{key:0},{default:n(()=>[o(E(Ne))]),_:1})):(m(),J(a,{key:1},{default:n(()=>[o(E(Ue))]),_:1})),f(" "+$(d.name),1)]),d.hasNotes?(m(),h("span",lt,[o(a,null,{default:n(()=>[o(E($e))]),_:1})])):U("",!0)])]),_:1},8,["data"])]),r("div",at,[v.value?(m(),h("div",st,[r("div",rt,[r("h3",null,$(v.value?.name),1),r("div",it,[v.value?(m(),J(_e,{key:0,onCommand:me},{dropdown:n(()=>[o(L,null,{default:n(()=>[o(H,{command:"normal"},{default:n(()=>e[7]||(e[7]=[f("正常模式")])),_:1}),o(H,{command:"raw"},{default:n(()=>e[8]||(e[8]=[f("原始内容")])),_:1}),o(H,{command:"decoded"},{default:n(()=>e[9]||(e[9]=[f("解码内容")])),_:1}),o(H,{command:"plainhtml"},{default:n(()=>e[10]||(e[10]=[f("直接HTML")])),_:1}),o(H,{command:"simple"},{default:n(()=>e[11]||(e[11]=[f("简单文本模式")])),_:1})]),_:1})]),default:n(()=>[o(s,{type:"primary"},{default:n(()=>[f($(Y()),1)]),_:1})]),_:1})):U("",!0),o(ge,{modelValue:b.value,"onUpdate:modelValue":e[1]||(e[1]=d=>b.value=d),onChange:ue,size:"small",style:{width:"100px"}},{default:n(()=>[o(B,{label:"UTF-8",value:"utf-8"}),o(B,{label:"GBK",value:"gbk"}),o(B,{label:"GB2312",value:"gb2312"}),o(B,{label:"Big5",value:"big5"})]),_:1},8,["modelValue"]),o(be,{modelValue:_.value,"onUpdate:modelValue":e[2]||(e[2]=d=>_.value=d),"active-text":"批注模式","inactive-text":"阅读模式",onChange:se},null,8,["modelValue"]),o(s,{type:"primary",onClick:de,disabled:!_.value},{default:n(()=>e[12]||(e[12]=[f(" 保存批注 ")])),_:1},8,["disabled"]),o(s,{type:"warning",onClick:fe,size:"small"},{default:n(()=>e[13]||(e[13]=[f(" 应急显示 ")])),_:1}),o(s,{type:"success",onClick:ye,disabled:!v.value,size:"small"},{default:n(()=>e[14]||(e[14]=[f(" 提取MHTML ")])),_:1},8,["disabled"])])]),r("div",{class:"reader-content",ref_key:"readerContent",ref:Z},[p.value?(m(),h("div",ct,[o(Me),e[15]||(e[15]=r("p",null,"正在处理MHTML文件...",-1))])):U("",!0),w.value&&!_.value?(m(),h("iframe",{key:1,ref_key:"mhtmlFrame",ref:x,class:"mhtml-frame",srcdoc:F.value,sandbox:"allow-same-origin allow-scripts allow-forms",onLoad:K},null,40,dt)):U("",!0),_.value?(m(),h("div",ut,[r("div",{class:"mhtml-container",ref_key:"mhtmlContainer",ref:ee},[r("iframe",{ref_key:"editFrame",ref:g,class:"mhtml-frame",srcdoc:F.value,sandbox:"allow-same-origin allow-scripts allow-forms",onLoad:K},null,40,mt)],512),r("div",pt,[e[18]||(e[18]=r("h4",null,"文档批注",-1)),T.value.length===0?(m(),h("div",vt,e[16]||(e[16]=[r("p",null,'尚无批注，点击"添加批注"按钮进行添加',-1)]))):(m(),h("div",ft,[(m(!0),h(Je,null,qe(T.value,(d,I)=>(m(),h("div",{key:I,class:"note-item"},[r("div",yt,[r("span",ht,"批注 #"+$(I+1),1),o(s,{type:"danger",size:"small",circle:"",onClick:X=>ce(I),icon:E(Ge)},null,8,["onClick","icon"])]),o(i,{modelValue:d.content,"onUpdate:modelValue":X=>d.content=X,type:"textarea",rows:3,placeholder:"请输入批注内容"},null,8,["modelValue","onUpdate:modelValue"])]))),128))])),o(s,{type:"primary",onClick:ie,class:"add-note-btn"},{default:n(()=>[o(a,null,{default:n(()=>[o(E(je))]),_:1}),e[17]||(e[17]=f(" 添加批注 "))]),_:1})])])):U("",!0)],512)])):(m(),h("div",nt,[o(N,{description:"请选择MHTML文件进行阅读"})]))])]),r("div",wt,[o(ke,{modelValue:R.value,"onUpdate:modelValue":e[3]||(e[3]=d=>R.value=d),max:100,min:0,onChange:ve,size:"small"},null,8,["modelValue"])]),o(He,{modelValue:u.visible,"onUpdate:modelValue":e[5]||(e[5]=d=>u.visible=d),title:"MHTML提取成功",width:"500px"},{default:n(()=>[r("div",_t,[e[21]||(e[21]=r("p",null,"已成功将MHTML文件提取到以下目录：",-1)),o(i,{modelValue:u.outputDir,"onUpdate:modelValue":e[4]||(e[4]=d=>u.outputDir=d),readonly:""},{append:n(()=>[o(s,{onClick:he},{default:n(()=>e[19]||(e[19]=[f(" 打开目录 ")])),_:1})]),_:1},8,["modelValue"]),r("p",null,"提取文件数量："+$(u.fileCount),1),r("div",gt,[o(s,{type:"primary",onClick:we},{default:n(()=>e[20]||(e[20]=[f(" 打开主HTML文件 ")])),_:1})])])]),_:1},8,["modelValue"])])}}},Ut=Te(bt,[["__scopeId","data-v-821f9736"]]);export{Ut as default};
