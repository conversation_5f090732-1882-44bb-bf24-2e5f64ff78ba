const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./entry-DxFfH4M0.js","./css/main.css-Dp-yYx0W.css"])))=>i.map(i=>d[i]);
import{bL as L,r as w,c as f,E as d,a7 as T}from"./entry-DxFfH4M0.js";const R=L("aiProviders",()=>{const o=w([]),u=w(!1),c=w(null),y=w(!1),p=w(!1),g=f(()=>o.value),h=f(()=>u.value),_=f(()=>!!c.value),m=f(()=>{const s=[];o.value.forEach(n=>{n.models&&Array.isArray(n.models)&&n.models.forEach(t=>{t.available===!0&&s.push({id:t.id,name:t.name||t.id,providerId:n.id,providerName:n.name,available:!0,config:t.config||{temperature:.8,max_tokens:8192,top_p:.8,frequency_penalty:0,presence_penalty:0,stream:!0},uniqueId:`${n.id}:${t.id}`})})});const e=[],r=new Set;return s.forEach(n=>{r.has(n.uniqueId)||(r.add(n.uniqueId),e.push(n))}),console.log(`aiProvidersStore: 找到 ${e.length} 个可用模型`),e}),E=f(()=>{const s=[];return o.value.forEach(e=>{e.models&&Array.isArray(e.models)&&e.models.forEach(r=>{s.push({id:r.id,name:r.name||r.id,providerId:e.id,providerName:e.name,available:r.available!==!1,config:r.config||{temperature:.8,max_tokens:8192,top_p:.8,frequency_penalty:0,presence_penalty:0,stream:!0},uniqueId:`${e.id}:${r.id}`})})}),s}),A=f(()=>m.value.map(s=>({value:s.uniqueId,label:s.name===s.id?`${s.id} (${s.providerName})`:`${s.name}  (${s.providerName})`,id:s.id,name:s.name,providerId:s.providerId,providerName:s.providerName,uniqueId:s.uniqueId,config:s.config})));async function $(s=!1){if(p.value&&!s)return o.value;try{u.value=!0,c.value=null;let e=0;const r=3;let n=!1;for(;!n&&e<r;)try{const t=window.pywebview.api.get_ai_providers(),i=new Promise((v,J)=>{setTimeout(()=>J(new Error("获取AI服务商列表超时")),5e3)}),l=await Promise.race([t,i]),a=typeof l=="string"?JSON.parse(l):l;if(a&&a.status==="success"){if(Array.isArray(a.data))return o.value=a.data,y.value=!0,p.value=!0,n=!0,o.value;throw console.warn("服务器返回的数据不是数组格式:",a.data),new Error("服务器返回的数据格式不正确")}else throw new Error(a?.message||"获取AI服务商列表失败")}catch(t){if(e++,e>=r)throw t;console.warn(`加载AI服务商失败 (尝试 ${e}/${r}):`,t),await new Promise(i=>setTimeout(i,1e3))}}catch(e){throw c.value=e.message,console.error("加载AI服务商失败:",e),p.value=!0,o.value=[],e}finally{u.value=!1}}async function b(){try{if(u.value=!0,c.value=null,!Array.isArray(o.value))throw new Error("服务商数据必须是数组格式");for(const r of o.value){if(!r||typeof r!="object")throw new Error("服务商必须是对象格式");r.name||(r.name="未命名服务商"),r.baseUrl||(r.baseUrl="https://api.openai.com/v1"),Array.isArray(r.apiKeys)||(r.apiKeys=[]),Array.isArray(r.models)||(r.models=[])}const s=await window.pywebview.api.save_ai_providers(o.value),e=typeof s=="string"?JSON.parse(s):s;if(e&&e.status==="success"){d.success("AI服务商配置保存成功");try{const{useConfigStore:r}=await T(async()=>{const{useConfigStore:t}=await import("./entry-DxFfH4M0.js").then(i=>i.iK);return{useConfigStore:t}},__vite__mapDeps([0,1]),import.meta.url),n=r();console.log("AI提供商配置已更新，重新加载模型列表..."),await n.reloadModels(),console.log("模型列表重新加载完成")}catch(r){console.warn("重新加载模型列表失败:",r)}return!0}else throw new Error(e?.message||"保存AI服务商配置失败")}catch(s){throw c.value=s.message,d.error("保存AI服务商配置失败: "+s.message),console.error("保存AI服务商配置失败:",s),s}finally{u.value=!1}}async function P(s){const e={id:Date.now().toString(),name:s.name||"新服务商",baseUrl:s.baseUrl||"",apiKeys:s.apiKeys||[],models:s.models||[]};return o.value.push(e),e}async function I(s,e){const r=o.value.find(n=>n.id===s);if(!r)throw new Error(`未找到ID为 ${s} 的服务商`);return Object.assign(r,e),r}async function K(s){const e=o.value.findIndex(r=>r.id===s);if(e===-1)throw new Error(`未找到ID为 ${s} 的服务商`);return o.value.splice(e,1),!0}async function M(s,e){try{u.value=!0;const r=o.value.find(v=>v.id===s);if(!r)throw new Error(`未找到ID为 ${s} 的服务商`);const n=r.apiKeys.find(v=>v.id===e);if(!n)throw new Error(`未找到ID为 ${e} 的API密钥`);let t="gpt-3.5-turbo";r.models&&r.models.length>0&&(t=r.models[0].id);const i={provider_id:s,api_key:n.key,base_url:r.baseUrl,test_model:t},l=await window.pywebview.api.test_api_key(i),a=typeof l=="string"?JSON.parse(l):l;if(a&&a.status==="success")return n.status="active",d.success("API密钥测试成功"),!0;throw n.status="error",new Error(a?.message||"API密钥测试失败")}catch(r){return c.value=r.message,d.error("API密钥测试失败: "+r.message),!1}finally{u.value=!1}}async function S(s){try{u.value=!0;const e=o.value.find(i=>i.id===s);if(!e)throw new Error(`未找到ID为 ${s} 的服务商`);if(!e.apiKeys||e.apiKeys.length===0)throw new Error("请先添加API密钥");const r={provider_id:s,api_key:e.apiKeys[0].key,base_url:e.baseUrl},n=await window.pywebview.api.fetch_models(r),t=typeof n=="string"?JSON.parse(n):n;if(t&&t.status==="success"){const i={};e.models.forEach(a=>{i[a.id]=a});const l=t.data.map(a=>i[a]?i[a]:{id:a,name:a,available:!0});return e.models=l,d.success(`成功获取 ${l.length} 个模型`),l}else throw new Error(t?.message||"获取模型列表失败")}catch(e){return c.value=e.message,d.error("获取模型列表失败: "+e.message),[]}finally{u.value=!1}}function N(s,e={}){const r=o.value.find(t=>t.id===s);if(!r)throw new Error(`未找到ID为 ${s} 的服务商`);const n={id:Date.now().toString(),key:e.key||"",weight:e.weight||1,status:"active"};return r.apiKeys||(r.apiKeys=[]),r.apiKeys.push(n),n}function k(s,e){const r=o.value.find(n=>n.id===s);if(!r||!r.apiKeys)throw new Error("未找到服务商或API密钥列表");if(e<0||e>=r.apiKeys.length)throw new Error(`无效的API密钥索引: ${e}`);return r.apiKeys.splice(e,1),!0}function q(s,e={}){const r=o.value.find(t=>t.id===s);if(!r)throw new Error(`未找到ID为 ${s} 的服务商`);if(!e.id||!e.id.trim())throw new Error("模型ID不能为空");if(r.models&&r.models.some(t=>t.id===e.id))throw new Error(`模型ID "${e.id}" 已存在`);const n={id:e.id.trim(),name:e.name?.trim()||e.id.trim(),available:e.available!==!1,config:{temperature:e.config?.temperature??.8,max_tokens:e.config?.max_tokens??8192,top_p:e.config?.top_p??.8,frequency_penalty:e.config?.frequency_penalty??0,presence_penalty:e.config?.presence_penalty??0,stream:e.config?.stream??!0}};return r.models||(r.models=[]),r.models.push(n),console.log(`添加模型: ID="${n.id}", 别名="${n.name}", 配置:`,n.config),n}function O(s,e){const r=o.value.find(n=>n.id===s);if(!r||!r.models)throw new Error("未找到服务商或模型列表");if(e<0||e>=r.models.length)throw new Error(`无效的模型索引: ${e}`);return r.models.splice(e,1),!0}function U(s){if(!s||!s.includes(":"))return console.warn("无效的模型唯一标识符:",s),null;const[e,r]=s.split(":"),n=o.value.find(i=>i.id===e);if(!n||!n.models)return console.warn(`未找到服务商: ${e}`),null;const t=n.models.find(i=>i.id===r);return t?{id:t.id,name:t.name||t.id,providerId:e,providerName:n.name,uniqueId:s,config:t.config||{temperature:.8,max_tokens:8192,top_p:.8,frequency_penalty:0,presence_penalty:0,stream:!0}}:(console.warn(`未找到模型: ${r} 在服务商 ${e} 中`),null)}function C(s,e){if(!s||!s.includes(":"))throw new Error("无效的模型唯一标识符");const[r,n]=s.split(":"),t=o.value.find(l=>l.id===r);if(!t||!t.models)throw new Error(`未找到服务商: ${r}`);const i=t.models.find(l=>l.id===n);if(!i)throw new Error(`未找到模型: ${n} 在服务商 ${r} 中`);return i.config={...i.config,...e},console.log(`更新模型配置: ${s}`,i.config),i.config}function x(){o.value=[],y.value=!1,p.value=!1,c.value=null}return{providers:o,loading:u,error:c,isLoaded:y,initialized:p,allProviders:g,isLoading:h,hasError:_,allAvailableModels:m,allModels:E,modelOptions:A,loadProviders:$,saveProviders:b,addProvider:P,updateProvider:I,removeProvider:K,testApiKey:M,fetchModels:S,addApiKey:N,removeApiKey:k,addModel:q,removeModel:O,getModelConfig:U,updateModelConfig:C,reset:x}});export{R as useAIProvidersStore};
