const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./aiProviders-Zwr_VLaf.js","./entry-DxFfH4M0.js","./css/main.css-Dp-yYx0W.css"])))=>i.map(i=>d[i]);
import{_ as ut,r as I,c as me,w as Ie,M as Ne,m as P,b as q,p as ge,e as g,d as f,g as y,B as _t,C as j,N as fs,s as bn,n as we,O as si,P as gf,Q as vf,R as _e,S as Ae,T as oi,F as ee,U as Rs,V as yo,h as Oe,W as Et,X as yf,Y as Le,E as $,o as ct,Z as en,a as wn,$ as lt,a0 as xc,a1 as yr,a2 as Sc,a3 as Cc,x as St,a4 as zt,a5 as Yi,a6 as Gi,a7 as bf,a8 as Fn,t as kn,a9 as on,v as ne,aa as wf,ab as Qi,ac as kf,ad as Zi,ae as el,af as xf,ag as _c,ah as qt,ai as Sf,aj as Cf,ak as _f,al as Mf,am as Tf,an as Ef,ao as Mc,ap as bo,aq as Tc,ar as nr,as as If,at as Ec,au as Vs,av as Of,aw as Nf,ax as Df,J as br,ay as Af,az as $f,G as sr,aA as Pf,j as wo,q as ko,aB as Rf,aC as Vf,aD as wr,aE as kr,aF as Lf,aG as tl,aH as Ic,aI as Oc,aJ as Bf,y as zf,aK as vs,aL as Xl,aM as Ff,aN as ps,k as xr,aO as Nc,aP as Dc,aQ as Ac,aR as Es,aS as $c,aT as Wf,aU as Hf,aV as qf,aW as nl,aX as Ti,L as Jf,aY as ri,aZ as Pc,a_ as Ei,a$ as Uf,b0 as Bo,l as jf,b1 as Kf,b2 as Xf,b3 as Yf,i as Gf,b4 as Yl,b5 as Qf,b6 as Zf,b7 as ep,b8 as Gl,b9 as tp,ba as np,bb as sp,bc as op,bd as rp,be as ip,bf as lp,bg as ap}from"./entry-DxFfH4M0.js";/* empty css                       *//* empty css                 *//* empty css                    *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                        *//* empty css                  *//* empty css                   *//* empty css                 */import{j as Ql,p as Zl,x as cp,c as up,a as dp,m as fp,U as ea,M as Rc}from"./UniversalSelector-WSkEUteJ.js";import"./purify-es-DhD2mIk-.js";import{useAIRolesStore as pp}from"./aiRoles-Cy8pLKqW.js";import{useAIProvidersStore as Sr}from"./aiProviders-Zwr_VLaf.js";import{n as Nn}from"./index-browser-OxPLOBIU.js";/* empty css               *//* empty css                *//* empty css                   */import{u as Zs}from"./book-mHAKpUpK.js";/* empty css                    *//* empty css               *//* empty css                        *//* empty css                         *//* empty css                         */const hp={class:"selector-header"},mp={class:"selector-content"},gp={class:"main-options"},vp={key:0,class:"template-list"},yp=["data-index","onClick"],bp={class:"template-name"},wp={class:"entity-count"},kp={key:1,class:"entity-list"},xp={class:"template-header"},Sp={class:"count"},Cp=["data-index","onClick"],_p={class:"entity-info"},Mp={class:"entity-name-line"},Tp={class:"entity-name"},Ep={key:0,class:"template-badge"},Ip={key:0,class:"entity-desc"},Op={key:2,class:"chapters-list"},Np={key:0},Dp={class:"volume-header"},Ap={class:"count"},$p=["data-index","onClick"],Pp={class:"chapter-info"},Rp={class:"chapter-name-line"},Vp={class:"chapter-name"},Lp={class:"chapter-order"},Bp={key:0,class:"chapter-desc"},zp={key:1},Fp={key:3,class:"scene-pools-list"},Wp={key:0},Hp=["data-index","onClick"],qp={class:"pool-info"},Jp={class:"pool-name-line"},Up={class:"pool-name"},jp={class:"scene-count"},Kp={key:0,class:"pool-desc"},Xp={key:1},Yp={__name:"EntitySelector",props:{visible:Boolean,bookId:{type:[String,Number],required:!0},position:{type:Object,default:()=>({top:0,left:0})},searchQuery:{type:String,default:""}},emits:["select","close"],setup(n,{emit:e}){const t=n,s=e,o=I([]),r=I([]),i=I([]),l=I([]),a=I(""),c=I(""),u=I(null),d=I(-1),v=I(null),p=me(()=>{if(!c.value)return o.value;const F=c.value.toLowerCase();return o.value.filter(S=>S.name.toLowerCase().includes(F))}),b=me(()=>{const F=c.value.toLowerCase();if(F){const S=r.value.filter(A=>A.name.toLowerCase().includes(F)||A.description&&A.description.toLowerCase().includes(F));return S.length===0?[]:[{id:"search-results",name:"搜索结果",isSearchResult:!0,entities:S.map(A=>{const Z=o.value.find(G=>G.id===A.template_id);return{...A,templateInfo:Z}})}]}else return o.value.map(S=>{const x=r.value.filter(A=>A.template_id===S.id);return{...S,entities:x}}).filter(S=>S.entities.length>0)}),w=me(()=>{if(!c.value)return i.value;const F=c.value.toLowerCase();return i.value.map(S=>{const x=(S.chapters||[]).filter(A=>A.title.toLowerCase().includes(F)||A.summary&&A.summary.toLowerCase().includes(F));return S.title.toLowerCase().includes(F)||x.length>0?{...S,chapters:x}:null}).filter(Boolean)}),C=me(()=>{if(!c.value)return l.value;const F=c.value.toLowerCase();return l.value.filter(S=>S.name.toLowerCase().includes(F)||S.description&&S.description.toLowerCase().includes(F))}),M=me(()=>{const F=[];return a.value==="all-entities"?p.value.forEach(S=>{F.push({type:"template",data:S,key:`template-${S.id}`})}):a.value==="single-entity"?b.value.forEach(S=>{S.entities.forEach(x=>{F.push({type:"entity",data:x,template:S,key:`entity-${x.id||x.name}`})})}):a.value==="chapters"?w.value.forEach(S=>{(S.chapters||[]).forEach(x=>{F.push({type:"chapter",data:x,volume:S,key:`chapter-${x.id}`})})}):a.value==="scene-pools"&&C.value.forEach(S=>{F.push({type:"scenePool",data:S,key:`pool-${S.id}`})}),F}),X=async()=>{try{if(console.log("EntitySelector: 加载模板，bookId:",t.bookId),!t.bookId){$.error("bookId 未提供");return}const F=await window.pywebview.api.book_controller.get_templates(t.bookId),S=typeof F=="string"?JSON.parse(F):F;console.log("EntitySelector: 模板加载结果:",S),S.status==="success"?(o.value=S.data||[],console.log("EntitySelector: 加载到模板数量:",o.value.length)):$.error(S.message||"加载模板失败")}catch(F){console.error("加载模板失败:",F),$.error("加载模板失败："+F.message)}},L=async()=>{try{if(console.log("EntitySelector: 加载实体，bookId:",t.bookId),!t.bookId){$.error("bookId 未提供");return}const F=await window.pywebview.api.book_controller.get_entities(t.bookId),S=typeof F=="string"?JSON.parse(F):F;console.log("EntitySelector: 实体加载结果:",S),S.status==="success"?(r.value=S.data||[],console.log("EntitySelector: 加载到实体数量:",r.value.length)):$.error(S.message||"加载实体失败")}catch(F){console.error("加载实体失败:",F),$.error("加载实体失败："+F.message)}},D=async()=>{try{if(console.log("EntitySelector: 加载卷和章节，bookId:",t.bookId),!t.bookId){$.error("bookId 未提供");return}const F=await window.pywebview.api.book_controller.get_volumes(t.bookId),S=typeof F=="string"?JSON.parse(F):F;console.log("EntitySelector: 卷和章节加载结果:",S),S.status==="success"?(i.value=S.data||[],console.log("EntitySelector: 加载到卷数量:",i.value.length)):$.error(S.message||"加载卷和章节失败")}catch(F){console.error("加载卷和章节失败:",F),$.error("加载卷和章节失败："+F.message)}},E=async()=>{try{if(console.log("EntitySelector: 加载场景卡池，bookId:",t.bookId),!t.bookId){$.error("bookId 未提供");return}const F=await window.pywebview.api.book_controller.get_scene_events(t.bookId),S=typeof F=="string"?JSON.parse(F):F;if(console.log("EntitySelector: 场景卡池加载结果:",S),S.status==="success"){const x=S.data||{};l.value=x.pools||[],console.log("EntitySelector: 加载到场景卡池数量:",l.value.length)}else $.error(S.message||"加载场景卡池失败")}catch(F){console.error("加载场景卡池失败:",F),$.error("加载场景卡池失败："+F.message)}},B=F=>r.value.filter(S=>S.template_id===F.id).length,N=()=>{a.value="all-entities"},V=()=>{a.value="single-entity"},_=()=>{a.value="chapters"},T=()=>{a.value="scene-pools"},U=F=>{const S=r.value.filter(x=>x.template_id===F.id);s("select",{type:"template",template:F,entities:S})},W=(F,S)=>{const x=S.isSearchResult?F.templateInfo:S;s("select",{type:"entity",entity:F,template:x})},z=(F,S)=>{s("select",{type:"chapter",chapter:F,volume:S})},re=F=>{s("select",{type:"scenePool",pool:F})},fe=F=>{c.value=F,d.value=-1},Te=(F,S)=>{if(a.value!=="chapters")return S;let x=0;for(let A=0;A<w.value.length;A++){const Z=w.value[A];if(Z.id===F.id)return x+S;x+=Z.chapters?.length||0}return S},ke=F=>{const S=M.value;if(S.length!==0)switch(F.key){case"ArrowDown":F.preventDefault(),d.value=Math.min(d.value+1,S.length-1),Fe();break;case"ArrowUp":F.preventDefault(),d.value=Math.max(d.value-1,-1),Fe();break;case"Enter":if(F.preventDefault(),d.value>=0&&d.value<S.length){const x=S[d.value];x.type==="template"?U(x.data):x.type==="entity"?W(x.data,x.template):x.type==="chapter"?z(x.data,x.volume):x.type==="scenePool"&&re(x.data)}break;case"Escape":F.preventDefault(),s("close");break}},Fe=()=>{d.value<0||Le(()=>{const F=v.value?.querySelector(`[data-index="${d.value}"]`);F&&F.scrollIntoView({behavior:"smooth",block:"nearest"})})},be=(F,S)=>{if(a.value==="single-entity"){let x=0;const A=b.value;for(let Z=0;Z<A.length;Z++){if(A[Z].id===F.id)return x+S;x+=A[Z].entities.length}}return S};return Ie(()=>t.visible,async F=>{console.log("EntitySelector: visible 变化为:",F),console.log("EntitySelector: position:",t.position),F&&(await X(),await L(),await D(),await E(),a.value="",c.value=t.searchQuery,d.value=-1,Le(()=>{u.value&&u.value.focus(),v.value&&v.value.focus()}))}),Ie(()=>t.searchQuery,F=>{c.value=F}),(F,S)=>{const x=_t,A=bn,Z=yo;return P(),Ne(yf,{to:"body"},[n.visible?(P(),q("div",{key:0,ref_key:"selectorRef",ref:v,class:"entity-selector",style:Et({top:n.position.top+"px",left:n.position.left+"px"}),onClick:S[1]||(S[1]=Oe(()=>{},["stop"])),onKeydown:ke,tabindex:"-1"},[g("div",hp,[f(A,{modelValue:c.value,"onUpdate:modelValue":S[0]||(S[0]=G=>c.value=G),placeholder:"搜索实体...",size:"small",clearable:"",onInput:fe,onKeydown:ke,ref_key:"searchInputRef",ref:u},{prefix:y(()=>[f(x,null,{default:y(()=>[f(j(fs))]),_:1})]),_:1},8,["modelValue"])]),g("div",mp,[g("div",gp,[g("div",{class:we(["option-item main-option",{active:a.value==="all-entities"}]),onClick:N},[f(x,null,{default:y(()=>[f(j(si))]),_:1}),S[2]||(S[2]=g("span",null,"选择模板下的所有实体",-1))],2),g("div",{class:we(["option-item main-option",{active:a.value==="single-entity"}]),onClick:V},[f(x,null,{default:y(()=>[f(j(gf))]),_:1}),S[3]||(S[3]=g("span",null,"选择单个实体",-1))],2),g("div",{class:we(["option-item main-option",{active:a.value==="chapters"}]),onClick:_},[f(x,null,{default:y(()=>[f(j(vf))]),_:1}),S[4]||(S[4]=g("span",null,"选择章节目录",-1))],2),g("div",{class:we(["option-item main-option",{active:a.value==="scene-pools"}]),onClick:T},[f(x,null,{default:y(()=>[f(j(si))]),_:1}),S[5]||(S[5]=g("span",null,"选择场景卡池",-1))],2)]),a.value==="all-entities"?(P(),q("div",vp,[S[6]||(S[6]=g("div",{class:"section-title"},"选择模板",-1)),(P(!0),q(_e,null,Ae(p.value,(G,se)=>(P(),q("div",{key:G.id,"data-index":se,class:we(["option-item template-item",{selected:d.value===se}]),onClick:pe=>U(G)},[f(x,null,{default:y(()=>[f(j(oi))]),_:1}),g("span",bp,ee(G.name),1),g("span",wp,ee(B(G))+"个实体",1)],10,yp))),128))])):ge("",!0),a.value==="single-entity"?(P(),q("div",kp,[S[7]||(S[7]=g("div",{class:"section-title"},"选择实体",-1)),(P(!0),q(_e,null,Ae(b.value,G=>(P(),q("div",{key:G.id,class:"template-group"},[g("div",xp,[f(x,null,{default:y(()=>[f(j(oi))]),_:1}),g("span",null,ee(G.name),1),g("span",Sp,"("+ee(G.entities.length)+")",1)]),(P(!0),q(_e,null,Ae(G.entities,(se,pe)=>(P(),q("div",{key:se.id||se.name,"data-index":be(G,pe),class:we(["option-item entity-item",{selected:d.value===be(G,pe)}]),onClick:$e=>W(se,G)},[f(x,null,{default:y(()=>[f(j(Rs))]),_:1}),g("div",_p,[g("div",Mp,[g("span",Tp,ee(se.name),1),G.isSearchResult&&se.templateInfo?(P(),q("span",Ep,ee(se.templateInfo.name),1)):ge("",!0)]),se.description?(P(),q("span",Ip,ee(se.description),1)):ge("",!0)])],10,Cp))),128))]))),128))])):ge("",!0),a.value==="chapters"?(P(),q("div",Op,[S[8]||(S[8]=g("div",{class:"section-title"},"选择章节",-1)),w.value.length>0?(P(),q("div",Np,[(P(!0),q(_e,null,Ae(w.value,G=>(P(),q("div",{key:G.id,class:"volume-group"},[g("div",Dp,[f(x,null,{default:y(()=>[f(j(oi))]),_:1}),g("span",null,ee(G.title),1),g("span",Ap,"("+ee(G.chapters?.length||0)+"章)",1)]),(P(!0),q(_e,null,Ae(G.chapters,(se,pe)=>(P(),q("div",{key:se.id,"data-index":Te(G,pe),class:we(["option-item chapter-item",{selected:d.value===Te(G,pe)}]),onClick:$e=>z(se,G)},[f(x,null,{default:y(()=>[f(j(Rs))]),_:1}),g("div",Pp,[g("div",Rp,[g("span",Vp,ee(se.title),1),g("span",Lp,"第"+ee(se.order)+"章",1)]),se.summary?(P(),q("span",Bp,ee(se.summary),1)):ge("",!0)])],10,$p))),128))]))),128))])):(P(),q("div",zp,[f(Z,{description:"暂无章节"})]))])):ge("",!0),a.value==="scene-pools"?(P(),q("div",Fp,[S[9]||(S[9]=g("div",{class:"section-title"},"选择场景卡池",-1)),C.value.length>0?(P(),q("div",Wp,[(P(!0),q(_e,null,Ae(C.value,(G,se)=>(P(),q("div",{key:G.id,"data-index":se,class:we(["option-item scene-pool-item",{selected:d.value===se}]),onClick:pe=>re(G)},[f(x,null,{default:y(()=>[f(j(si))]),_:1}),g("div",qp,[g("div",Jp,[g("span",Up,ee(G.name),1),g("span",jp,ee(G.scenes?.length||0)+"个场景",1)]),G.description?(P(),q("span",Kp,ee(G.description),1)):ge("",!0)])],10,Hp))),128))])):(P(),q("div",Xp,[f(Z,{description:"暂无场景卡池"})]))])):ge("",!0)])],36)):ge("",!0)])}}},Gp=ut(Yp,[["__scopeId","data-v-d032e531"]]),Qp={class:"highlight-input-container"},Zp=["data-placeholder"],eh={__name:"HighlightInput",props:{modelValue:{type:String,default:""},placeholder:{type:String,default:"请输入内容..."},entityReferences:{type:Map,default:()=>new Map}},emits:["update:modelValue","input","keydown","focus","blur","at-typed"],setup(n,{expose:e,emit:t}){const s=n,o=t,r=I(null),i=I(!1);ct(()=>{s.modelValue&&l(s.modelValue)}),Ie(()=>s.modelValue,D=>{if(i.value)return;const E=c();D!==E&&l(D)}),Ie(()=>s.entityReferences,()=>{const D=c();l(D)},{deep:!0});const l=D=>{if(!r.value)return;const E=a(D),B=window.getSelection();let N=0;if(B.rangeCount>0){const V=B.getRangeAt(0);N=u(V.startContainer,V.startOffset)}r.value.innerHTML=E,Le(()=>{v(N)})},a=D=>{if(!D)return"";let E=D.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;");if(E=E.replace(/\n/g,"<br>"),s.entityReferences&&s.entityReferences.size>0)for(const[B,N]of s.entityReferences.entries()){const V=B.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),_=new RegExp(V+"(?=\\s|$|<br>)","g");E=E.replace(_,`<span class="entity-tag" contenteditable="false">${B}</span>`)}return E=E.replace(/@([^\s\n@<>]+)(?=\s|$|<br>)/g,(B,N)=>E.includes(`<span class="entity-tag" contenteditable="false">@${N}</span>`)?B:`<span class="entity-tag" contenteditable="false">@${N}</span>`),E},c=()=>{if(!r.value)return"";const D=r.value.cloneNode(!0);return D.querySelectorAll(".entity-tag").forEach(N=>{const V=document.createTextNode(N.textContent);N.parentNode.replaceChild(V,N)}),D.querySelectorAll("br").forEach(N=>{const V=document.createTextNode(`
`);N.parentNode.replaceChild(V,N)}),D.textContent||""},u=(D,E)=>{if(!r.value)return 0;const B=document.createRange();B.selectNodeContents(r.value),B.setEnd(D,E);const N=B.cloneContents();return N.querySelectorAll(".entity-tag").forEach(T=>{const U=document.createTextNode(T.textContent);T.parentNode.replaceChild(U,T)}),N.querySelectorAll("br").forEach(T=>{const U=document.createTextNode(`
`);T.parentNode.replaceChild(U,T)}),(N.textContent||"").length},d=()=>{if(!r.value)return 0;const D=window.getSelection();if(D.rangeCount===0)return 0;const E=D.getRangeAt(0),B=document.createRange();B.selectNodeContents(r.value),B.setEnd(E.startContainer,E.startOffset);const N=B.cloneContents();return N.querySelectorAll(".entity-tag").forEach(T=>{const U=document.createTextNode(T.textContent);T.parentNode.replaceChild(U,T)}),N.querySelectorAll("br").forEach(T=>{const U=document.createTextNode(`
`);T.parentNode.replaceChild(U,T)}),(N.textContent||"").length},v=D=>{if(!r.value)return;const E=document.createTreeWalker(r.value,NodeFilter.SHOW_TEXT,null,!1);let B=0,N;for(;N=E.nextNode();){const T=N.textContent.length;if(B+T>=D){const U=document.createRange(),W=window.getSelection();U.setStart(N,D-B),U.collapse(!0),W.removeAllRanges(),W.addRange(U);return}B+=T}const V=document.createRange(),_=window.getSelection();V.selectNodeContents(r.value),V.collapse(!1),_.removeAllRanges(),_.addRange(V)},p=()=>{const D=c();i.value=!0,o("update:modelValue",D),o("input",D),Le(()=>{i.value=!1})},b=D=>{(D.key==="@"||D.shiftKey&&D.key==="2"||D.shiftKey&&D.code==="Digit2")&&setTimeout(()=>{const N=d();o("at-typed",{position:N})},10);const B=window.getSelection();if(B.rangeCount>0){const N=B.getRangeAt(0);if(D.key==="Backspace"&&N.collapsed){if(N.startOffset===0&&N.startContainer.nodeType===Node.TEXT_NODE){const V=N.startContainer.previousSibling;if(V&&V.classList&&V.classList.contains("entity-tag")){D.preventDefault(),V.remove(),p();return}}else if(N.startContainer.nodeType===Node.ELEMENT_NODE){const V=N.startContainer.childNodes[N.startOffset-1];if(V&&V.classList&&V.classList.contains("entity-tag")){D.preventDefault(),V.remove(),p();return}}}if(D.key==="Delete"&&N.collapsed){if(N.startContainer.nodeType===Node.TEXT_NODE&&N.startOffset===N.startContainer.textContent.length){const V=N.startContainer.nextSibling;if(V&&V.classList&&V.classList.contains("entity-tag")){D.preventDefault(),V.remove(),p();return}}else if(N.startContainer.nodeType===Node.ELEMENT_NODE){const V=N.startContainer.childNodes[N.startOffset];if(V&&V.classList&&V.classList.contains("entity-tag")){D.preventDefault(),V.remove(),p();return}}}}o("keydown",D)},w=D=>{D.preventDefault();const E=D.clipboardData.getData("text/plain"),B=window.getSelection();if(B.rangeCount>0){const N=B.getRangeAt(0);N.deleteContents(),N.insertNode(document.createTextNode(E)),N.collapse(!1),B.removeAllRanges(),B.addRange(N),p()}};return e({focus:()=>{r.value&&r.value.focus()},editableDiv:r,replaceAtWithEntity:(D,E="")=>{if(!r.value)return;console.log("replaceAtWithEntity 被调用:",{entityText:D,searchText:E});const B=c(),N=d();console.log("当前文本:",B),console.log("光标位置:",N);const _=B.slice(0,N).lastIndexOf("@");if(console.log("@ 符号位置:",_),_===-1)return;const T=B.slice(0,_),U=B.slice(_+1+E.length),W=T+D+U;console.log("新文本内容:",W),i.value=!0,o("update:modelValue",W),Le(()=>{l(W);const z=_+D.length;v(z),console.log("设置新光标位置:",z),i.value=!1})},getSelectionRange:()=>{if(!r.value)return{start:0,end:0};const D=window.getSelection();if(D.rangeCount===0)return{start:0,end:0};const E=D.getRangeAt(0),B=u(E.startContainer,E.startOffset),N=u(E.endContainer,E.endOffset);return{start:B,end:N}},setSelectionRange:(D,E)=>{v(D)},getCursorPosition:d}),(D,E)=>(P(),q("div",Qp,[g("div",{ref_key:"editableDiv",ref:r,class:"editable-input",contenteditable:"true","data-placeholder":n.placeholder,onInput:p,onKeydown:b,onPaste:w,onFocus:E[0]||(E[0]=B=>D.$emit("focus")),onBlur:E[1]||(E[1]=B=>D.$emit("blur"))},null,40,Zp)]))}},th=ut(eh,[["__scopeId","data-v-5b1ac206"]]),nh={class:"chat-header"},sh={class:"header-left"},oh={class:"chat-title"},rh={key:0},ih={key:1},lh={class:"header-right"},ah=["title"],ch={class:"settings-content"},uh={class:"setting-group"},dh={class:"setting-group"},fh={key:0,class:"system-role-info"},ph={class:"messages-list"},hh={key:1,class:"typing-indicator"},mh={class:"input-area"},gh={class:"input-container"},vh={class:"textarea-wrapper"},yh=["disabled"],bh={class:"btn-icon"},wh=["disabled"],kh={class:"btn-icon"},xh={__name:"ChatPanel",props:{chatId:{type:String,default:""},bookId:{type:String,default:""},editor:{type:Object,default:null},selectedText:{type:String,default:""}},emits:["back","chat-updated","insert-text"],setup(n,{emit:e}){en.registerLanguage("javascript",Ql),en.registerLanguage("js",Ql),en.registerLanguage("python",Zl),en.registerLanguage("py",Zl),en.registerLanguage("html",cp),en.registerLanguage("css",up),en.registerLanguage("json",dp),fp.setOptions({highlight:(H,J)=>{if(J&&en.getLanguage(J))try{return en.highlight(H,{language:J}).value}catch(oe){console.error("代码高亮出错:",oe)}return en.highlightAuto(H).value},breaks:!0,gfm:!0}),wn();const t=pp(),s=Sr(),o=H=>{try{const J=s.allAvailableModels.find(oe=>oe.uniqueId===H);return J&&J.config?(console.log("ChatPanel获取到模型配置:",J.config),J.config):(console.log("ChatPanel未找到模型配置，使用默认配置"),{temperature:.8,max_tokens:8192,top_p:.8,frequency_penalty:0,presence_penalty:0,stream:!0})}catch(J){return console.error("ChatPanel获取模型配置失败:",J),{temperature:.8,max_tokens:8192,top_p:.8,frequency_penalty:0,presence_penalty:0,stream:!0}}},r=n,i=e,l=I([]),a=I(""),c=I(!1),u=I(null),d=I(null),v=I(!1),p=I(null),b=I(!0),w=I(""),C=I(!1),M=I([]),X=I(!1),L=I(!1),D=I({top:0,left:0}),E=I(""),B=I(null),N=I(0);let V=null,_=null,T=null;const U=me(()=>s.modelOptions.map(J=>({id:J.uniqueId,name:J.label,providerId:J.providerId,providerName:J.providerName,uniqueId:J.uniqueId,config:J.config}))),W=me(()=>U.value.map(H=>({value:H.id,label:H.name,description:H.providerName?`提供商: ${H.providerName}`:void 0,provider:H.providerName}))),z=me(()=>{const H=re.value.map(J=>({value:J.id,label:J.name||J.id,description:J.description}));return console.log("ChatPanel roleOptions:",H),H}),re=me(()=>t.roles.filter(H=>H.isEnabled!==!1).map(H=>({id:H.id,name:H.name||H.id,description:H.description||"",prompt:H.prompt||""}))),fe=()=>{i("back")},Te=H=>{if(L.value){const J=d.value?.getCursorPosition?.()||0,oe=H.slice(0,J),ce=oe.lastIndexOf("@");if(ce!==-1&&ce===N.value){const ve=oe.slice(ce+1);ve.includes(" ")||ve.includes(`
`)?F():E.value=ve}else F()}},ke=H=>{L.value&&H.key==="Escape"&&(H.preventDefault(),F())},Fe=H=>{N.value=H.position-1,L.value=!0,E.value="",be()},be=()=>{if(!d.value?.editableDiv)return;const H=d.value.editableDiv;if(!H)return;const J=H.getBoundingClientRect(),oe=window.getSelection();let ce="";if(oe.rangeCount>0){const fn=oe.getRangeAt(0),xt=document.createRange();xt.selectNodeContents(H),xt.setEnd(fn.startContainer,fn.startOffset),ce=xt.toString()}const ve=ce.split(`
`),Re=ve.length-1,Ve=ve[Re]||"",We=window.getComputedStyle(H),qe=parseFloat(We.fontSize),Dt=parseFloat(We.lineHeight)||qe*1.5;let Xt=J.left+12,At=J.top+12;if(oe.rangeCount>0)try{const xt=oe.getRangeAt(0).getBoundingClientRect();if(xt.width>0||xt.height>0)Xt=xt.left,At=xt.top;else{const js=qe*.6,Fr=Ve.length*js;Xt=J.left+Math.min(Fr,J.width-20)+12,At=J.top+Re*Dt+12}}catch{const xt=qe*.6,js=Ve.length*xt;Xt=J.left+Math.min(js,J.width-20)+12,At=J.top+Re*Dt+12}const Mo=window.innerHeight,ws=window.innerWidth,pt=400,un=320,Yn=document.querySelector(".messages-container");let Mt=0,dn=Mo;if(Yn){const fn=Yn.getBoundingClientRect();Mt=fn.top,dn=fn.bottom}let Ht,kt;At-pt>Mt+10?Ht=At-pt-8:At+24+pt<dn-10?Ht=At+24:Yn&&Xt+un+20<ws?(Ht=Math.max(Mt+10,Math.min(At-50,dn-pt-10)),kt=Xt+20):Yn&&Xt-un-20>0?(Ht=Math.max(Mt+10,Math.min(At-50,dn-pt-10)),kt=Xt-un-20):Ht=Math.max(10,J.top-pt-8),kt===void 0&&(kt=Xt,kt+un>ws&&(kt=ws-un-10),kt<10&&(kt=10)),Ht=Math.max(10,Math.min(Ht,Mo-pt-10)),kt=Math.max(10,Math.min(kt,ws-un-10));const To={top:Ht,left:kt};D.value=To},F=()=>{L.value=!1,E.value="",N.value=0},S=I(new Map),x=H=>{if(!H||S.value.size===0)return H;let J=H;return S.value.forEach((oe,ce)=>{const ve=new RegExp(A(ce),"g");J=J.replace(ve,oe)}),J},A=H=>H.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),Z=H=>{const{type:J,template:oe,entity:ce,entities:ve,chapter:Re,volume:Ve,pool:We}=H;let qe="",Dt="";J==="template"?(qe=`@${oe.name}`,Dt=G(oe,ve)):J==="entity"?(qe=`@${ce.name}`,Dt=se(ce,oe)):J==="chapter"?(qe=`@${Re.title}`,Dt=pe(Re,Ve)):J==="scenePool"&&(qe=`@${We.name}`,Dt=$e(We)),S.value.set(qe,Dt),d.value?.replaceAtWithEntity&&d.value.replaceAtWithEntity(qe,E.value),F(),Le(()=>{d.value?.focus&&d.value.focus()})},G=(H,J)=>{if(!J||J.length===0)return`
【${H.name}】
暂无实体
`;let oe=`
【${H.name}】
`;return J.forEach(ce=>{if(oe+=`
${ce.name}：`,ce.description&&(oe+=ce.description),ce.dimensions&&typeof ce.dimensions=="object"){const ve=Object.entries(ce.dimensions).filter(([,Re])=>Re&&Re!=="未设定").map(([Re,Ve])=>`${Re}：${Ve}`);ve.length>0&&(oe+=`
  ${ve.join(`
  `)}`)}oe+=`
`}),oe+`
`},se=(H,J)=>{let oe=`
【${H.name}】`;if(J&&(oe+=`（${J.name}）`),oe+=`
`,H.description&&(oe+=`描述：${H.description}
`),H.dimensions&&typeof H.dimensions=="object"){const ce=Object.entries(H.dimensions).filter(([,ve])=>ve&&ve!=="未设定");ce.length>0&&(oe+=`
`,ce.forEach(([ve,Re])=>{oe+=`${ve}：${Re}
`}))}return oe+`
`},pe=(H,J)=>{let oe=`
【${H.title}】`;return J&&(oe+=`（${J.title}）`),oe+=`
`,H.order&&(oe+=`章节序号：第${H.order}章
`),H.summary&&(oe+=`章节简介：${H.summary}
`),H.word_count&&(oe+=`字数：${H.word_count}字
`),H.status&&(oe+=`状态：${H.status}
`),oe+`
`},$e=H=>{let J=`
【${H.name}】（场景卡池）
`;H.description&&(J+=`描述：${H.description}
`);const oe=H.scenes?.length||0;return J+=`场景数量：${oe}个
`,oe>0&&(J+=`
场景列表：
`,H.scenes.forEach((ce,ve)=>{J+=`${ve+1}. ${ce.title}`,ce.description&&(J+=`：${ce.description}`),J+=`
`})),J+`
`},he=H=>{if(!L.value)return;const J=B.value?.$el,oe=d.value?.$el||d.value?.editableDiv?.parentElement;J&&!J.contains(H.target)&&oe&&!oe.contains(H.target)&&F()},De=async()=>{if(r.chatId)try{v.value=!0;const H=await window.pywebview.api.model_controller.get_chat(r.chatId),J=typeof H=="string"?JSON.parse(H):H;if(J.status==="success"){p.value=J.data;const oe=p.value.model_id||p.value.model||"";if(U.value.find(ve=>ve.id===oe))w.value=oe;else{const ve=U.value.find(Re=>Re.uniqueId&&Re.uniqueId.endsWith(":"+oe));ve?(w.value=ve.id,p.value.model_id=ve.id):U.value.length>0&&(w.value=U.value[0].id,p.value.model_id=U.value[0].id)}l.value=p.value.messages||[],b.value=p.value.memory_enabled!==!1,M.value=p.value.roles||[],i("chat-updated",p.value),Le(()=>{Wt()})}else $.error(J.message||"加载聊天数据失败")}catch(H){console.error("加载聊天数据失败:",H),$.error("加载聊天数据失败")}finally{v.value=!1}},Ce=async()=>{try{C.value=!0;try{const{useAIProvidersStore:H}=await bf(async()=>{const{useAIProvidersStore:oe}=await import("./aiProviders-Zwr_VLaf.js");return{useAIProvidersStore:oe}},__vite__mapDeps([0,1,2]),import.meta.url),J=H();J.initialized||(console.log("ChatPanel: AI提供商配置未初始化，先加载提供商配置"),await J.loadProviders())}catch(H){console.warn("ChatPanel: 加载AI提供商配置失败:",H)}if(console.log("ChatPanel: 可用模型数量:",U.value.length),!w.value&&U.value.length){const H=U.value[0];w.value=H.id}}catch(H){console.error("ChatPanel: 加载模型列表失败:",H)}finally{C.value=!1}},Pe=async()=>{if(p.value)try{const H=await window.pywebview.api.model_controller.save_chat(r.chatId,{...p.value,messages:l.value,model_id:w.value,memory_enabled:b.value,roles:M.value,last_updated:Math.floor(Date.now()/1e3)}),J=typeof H=="string"?JSON.parse(H):H;J.status!=="success"&&console.error("保存聊天数据失败:",J.message),i("chat-updated",p.value)}catch(H){console.error("保存聊天数据失败:",H)}},wt=async()=>{if(!(!a.value.trim()||c.value))try{p.value||(p.value={id:r.chatId||`chat_${Date.now()}`,title:`新对话 ${new Date().toLocaleTimeString()}`,model_id:w.value,messages:[],roles:M.value,memory_enabled:b.value,created_at:Math.floor(Date.now()/1e3),last_updated:Math.floor(Date.now()/1e3)});const J={role:"user",content:x(a.value),id:Nn(),timestamp:Math.floor(Date.now()/1e3)};l.value.push(J),p.value.messages=l.value,await Pe();const oe=a.value;a.value="",S.value.clear(),Wt(),c.value=!0;const ce=[];if(M.value.length>0){const Ve=M.value.map(We=>re.value.find(Dt=>Dt.id===We)?.prompt||"").filter(Boolean);Ve.length>0&&ce.push({role:"system",content:Ve.join(`

`)})}b.value?l.value.forEach(Ve=>{Ve.role!=="system"&&!Ve.isSystemNotification&&ce.push({role:Ve.role,content:Ve.content})}):ce.push({role:"user",content:oe});const Re={stream:!0,...o(w.value)};try{const Ve=await window.pywebview.api.model_controller.chat(p.value.id,w.value,ce,Re);if(Ve&&typeof Ve=="object"){const We=typeof Ve=="string"?JSON.parse(Ve):Ve;if(We.status!=="success")throw new Error(We.message||"生成回复失败");if(!We.data.stream&&We.data.content){const qe={role:"assistant",content:We.data.content,id:Nn(),timestamp:Math.floor(Date.now()/1e3)};l.value.push(qe),p.value.messages=l.value,await Pe()}}}catch(Ve){console.error("生成回复失败:",Ve),l.value.push({role:"assistant",content:`生成回复时出错: ${Ve.message||"未知错误"}`,id:Nn(),timestamp:Math.floor(Date.now()/1e3),isError:!0}),p.value.messages=l.value,await Pe(),$.error("生成回复失败")}finally{c.value=!1,Wt()}}catch(H){console.error("发送消息失败:",H),$.error("发送消息失败"),c.value=!1}},Ft=async H=>{if(c.value)return;const J=l.value.findIndex(oe=>oe===H);J!==-1&&(l.value=l.value.slice(0,J+1),a.value=H.content,p.value.messages=l.value,await Pe(),await wt())},jn=H=>{i("insert-text",H),$.success("内容已插入到编辑器")},cn=()=>{r.selectedText&&(a.value=r.selectedText,$.success("已使用选中文本"))},Wt=()=>{Le(()=>{u.value&&(u.value.scrollTop=u.value.scrollHeight)})},bs=async()=>{b.value=!b.value,p.value&&(p.value.memory_enabled=b.value,await Pe(),$.info(`已切换到${b.value?"记忆":"单次"}模式`))},Us=H=>re.value.find(oe=>oe.id===H)?.name||H,Kn=H=>{try{const J=atob(H),oe=new TextDecoder("utf-8").decode(new Uint8Array([...J].map(We=>We.charCodeAt(0)))),ce=JSON.parse(oe),{chat_id:ve,content:Re,reasoning:Ve}=ce;if(ve===r.chatId){const We=l.value[l.value.length-1];if(!We||We.role!=="assistant"){const qe={role:"assistant",content:Re||"",id:Nn(),timestamp:Math.floor(Date.now()/1e3)};Ve&&(qe.reasoning=Ve,qe.reasoningCollapsed=!0,qe.reasoningTime="思考中...",qe.reasoningStartTime=Date.now()),l.value.push(qe)}else Re&&(We.content+=Re),Ve&&(We.reasoning||(We.reasoning="",We.reasoningCollapsed=!0,We.reasoningTime="思考中...",We.reasoningStartTime=Date.now()),We.reasoning+=Ve);Wt()}}catch(J){console.error("处理消息块失败:",J)}},st=H=>{if(H===r.chatId){c.value=!1;const J=l.value[l.value.length-1];if(J&&J.role==="assistant"&&J.reasoning&&J.reasoningStartTime){const oe=Date.now()-J.reasoningStartTime;J.reasoningTime=`${(oe/1e3).toFixed(1)}秒`,delete J.reasoningStartTime}p.value.messages=l.value,Pe(),Wt()}},te=H=>{try{const J=atob(H),oe=new TextDecoder("utf-8").decode(new Uint8Array([...J].map(Ve=>Ve.charCodeAt(0)))),ce=JSON.parse(oe),{chat_id:ve,error_message:Re}=ce;console.log("ChatPanel: 收到错误消息:",ve,Re),$.error({message:`AI回复失败: ${Re}`,duration:5e3,showClose:!0}),ve===r.chatId&&(c.value=!1,l.value.push({role:"assistant",content:`[错误: ${Re}]`,id:Nn(),timestamp:Math.floor(Date.now()/1e3),isError:!0}),p.value.messages=l.value,Pe(),Wt())}catch(J){console.error("处理错误消息失败:",J)}},ft=()=>{X.value=!X.value},Br=async(H,J)=>{console.log("Model changed:",H,J),p.value&&(p.value.model_id=H,await Pe())},zr=async(H,J)=>{if(console.log("Roles changed:",H,J),M.value=H,p.value)if(p.value.roles=H,await Pe(),H.length>0){const oe=H.map(ce=>{const ve=re.value.find(Re=>Re.id===ce);return ve?ve.name:ce}).join(", ");$.success(`已选择角色: ${oe}`)}else $.info("已清除所有角色设定")};Ie(w,async(H,J)=>{H!==J&&p.value&&(p.value.model_id=H,await Pe())}),ct(async()=>{V=window.receiveChunk,_=window.onMessageComplete,T=window.receiveChatError,window.receiveChunk=Kn,window.onMessageComplete=st,window.receiveChatError=te,t.roles.length||await t.loadRoles(),await Ce(),await De(),Le(()=>{d.value?.focus&&d.value.focus()}),document.addEventListener("click",he)}),lt(()=>{V&&(window.receiveChunk=V),_&&(window.onMessageComplete=_),T&&(window.receiveChatError=T),document.removeEventListener("click",he)});const _o=async()=>{if(!(!r.chatId||!c.value))try{const H=await window.pywebview.api.model_controller.stop_chat(r.chatId),J=typeof H=="string"?JSON.parse(H):H;if(J.status==="success")c.value=!1,$.success("已停止生成");else throw new Error(J.message||"停止生成失败")}catch(H){console.error("停止对话失败:",H),$.error(H.message||"停止对话失败"),c.value=!1}};let xn=null,Xn=null;return ct(()=>{Xn=()=>{L.value&&Le(()=>{be()})},window.addEventListener("resize",Xn);const H=new MutationObserver(J=>{J.forEach(oe=>{if(oe.type==="attributes"&&oe.attributeName==="style"){const ce=document.documentElement.getAttribute("style");ce&&ce.includes("--chat-sidebar-width")&&L.value&&Le(()=>{be()})}})});H.observe(document.documentElement,{attributes:!0,attributeFilter:["style"]}),xn=H}),lt(()=>{Xn&&window.removeEventListener("resize",Xn),xn&&xn.disconnect()}),(H,J)=>{const oe=_t;return P(),q("div",{class:we(["chat-panel",{"settings-open":X.value}])},[g("header",nh,[g("div",sh,[g("button",{class:"back-btn",onClick:fe},[f(oe,null,{default:y(()=>[f(j(xc))]),_:1})]),g("div",oh,[p.value?(P(),q("span",rh,ee(p.value.title||"未命名对话"),1)):(P(),q("span",ih,ee(n.chatId?"加载中...":"新对话"),1))])]),g("div",lh,[g("div",{class:"settings-toggle",onClick:ft},[f(oe,null,{default:y(()=>[f(j(yr))]),_:1})]),g("div",{class:we(["memory-toggle",{"memory-enabled":b.value,"memory-disabled":!b.value}]),onClick:bs,title:b.value?"记忆模式：保持对话上下文":"单次模式：每次独立对话"},[b.value?(P(),Ne(oe,{key:0},{default:y(()=>[f(j(Sc))]),_:1})):(P(),Ne(oe,{key:1},{default:y(()=>[f(j(Cc))]),_:1}))],10,ah)])]),g("div",{class:we(["settings-panel",{"is-open":X.value}])},[g("div",ch,[g("div",uh,[J[5]||(J[5]=g("label",{class:"setting-label"},"AI模型",-1)),f(ea,{modelValue:w.value,"onUpdate:modelValue":J[0]||(J[0]=ce=>w.value=ce),options:W.value,placeholder:"请选择模型","header-title":"选择AI模型",searchable:W.value.length>8,"max-height":"320px",onChange:Br},null,8,["modelValue","options","searchable"])]),g("div",dh,[J[6]||(J[6]=g("label",{class:"setting-label"},"AI角色",-1)),f(ea,{modelValue:M.value,"onUpdate:modelValue":J[1]||(J[1]=ce=>M.value=ce),options:z.value,multiple:!0,placeholder:"默认角色","header-title":"选择AI角色",searchable:z.value.length>6,"max-height":"300px",onChange:zr},null,8,["modelValue","options","searchable"])])])],2),g("div",{ref_key:"messagesContainer",ref:u,class:"messages-container"},[p.value&&M.value.length>0?(P(),q("div",fh,[(P(!0),q(_e,null,Ae(M.value,ce=>(P(),q("div",{class:"role-badge",key:ce},ee(Us(ce)),1))),128))])):ge("",!0),g("div",ph,[(P(!0),q(_e,null,Ae(l.value,(ce,ve)=>(P(),q(_e,null,[ce.isSystemNotification?(P(),q("div",{key:ce.id||"system-"+ve,class:"system-message"},ee(ce.content),1)):(P(),Ne(Rc,{key:ce.id||ve,content:ce.content,isUser:ce.role==="user",isError:ce.isError,timestamp:ce.timestamp,selectedModel:w.value,reasoning:ce.reasoning,reasoningTime:ce.reasoningTime,disabled:c.value,hasEditor:!!r.editor,onResend:Re=>Ft(ce),onInsert:Re=>jn(ce.content),onInsertCode:jn},null,8,["content","isUser","isError","timestamp","selectedModel","reasoning","reasoningTime","disabled","hasEditor","onResend","onInsert"]))],64))),256))]),c.value?(P(),q("div",hh,J[7]||(J[7]=[g("span",null,null,-1),g("span",null,null,-1),g("span",null,null,-1)]))):ge("",!0)],512),g("div",mh,[g("div",gh,[g("div",vh,[f(th,{ref_key:"inputRef",ref:d,modelValue:a.value,"onUpdate:modelValue":J[2]||(J[2]=ce=>a.value=ce),autosize:{minRows:1,maxRows:5},placeholder:"输入消息... (输入 @ 可选择实体)",disabled:c.value&&!r.selectedText,"entity-references":S.value,onKeydown:[St(Oe(wt,["exact","prevent"]),["enter"]),J[3]||(J[3]=St(Oe(ce=>a.value+=`
`,["ctrl"]),["enter"])),ke],onInput:Te,onAtTyped:Fe},null,8,["modelValue","disabled","entity-references","onKeydown"]),r.selectedText?(P(),q("button",{key:0,class:"selected-text-btn",onClick:cn,disabled:c.value},[g("div",bh,[f(oe,null,{default:y(()=>[f(j(zt))]),_:1})]),J[8]||(J[8]=g("span",null,"使用选中文本",-1))],8,yh)):ge("",!0)]),g("button",{class:we(["send-btn",{"is-generating":c.value,"stop-generating":c.value}]),onClick:J[4]||(J[4]=ce=>c.value?_o():wt()),disabled:!c.value&&!a.value.trim()},[g("div",kh,[c.value?(P(),Ne(oe,{key:1},{default:y(()=>[f(j(Gi))]),_:1})):(P(),Ne(oe,{key:0},{default:y(()=>[f(j(Yi))]),_:1}))]),g("span",null,ee(c.value?"停止":"发送"),1)],10,wh)])]),f(Gp,{visible:L.value,"book-id":r.bookId,position:D.value,"search-query":E.value,onSelect:Z,onClose:F,ref_key:"entitySelectorRef",ref:B},null,8,["visible","book-id","position","search-query"])],2)}}},Sh=ut(xh,[["__scopeId","data-v-1445b463"]]),zo=43200,ta=1440,na=Symbol.for("constructDateFrom");function sl(n,e){return typeof n=="function"?n(e):n&&typeof n=="object"&&na in n?n[na](e):n instanceof Date?new n.constructor(e):new Date(e)}function yn(n,e){return sl(e||n,n)}let Ch={};function Vc(){return Ch}function sa(n,e){const t=Vc(),s=e?.weekStartsOn??e?.locale?.options?.weekStartsOn??t.weekStartsOn??t.locale?.options?.weekStartsOn??0,o=yn(n,e?.in),r=o.getDay(),i=(r<s?7:0)+r-s;return o.setDate(o.getDate()-i),o.setHours(0,0,0,0),o}function oa(n){const e=yn(n),t=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return t.setUTCFullYear(e.getFullYear()),+n-+t}function Cr(n,...e){const t=sl.bind(null,n||e.find(s=>typeof s=="object"));return e.map(t)}function Xo(n,e){const t=+yn(n)-+yn(e);return t<0?-1:t>0?1:t}function _h(n){return sl(n,Date.now())}function Mh(n,e,t){const[s,o]=Cr(t?.in,n,e),r=s.getFullYear()-o.getFullYear(),i=s.getMonth()-o.getMonth();return r*12+i}function Th(n){return e=>{const s=(n?Math[n]:Math.trunc)(e);return s===0?0:s}}function Eh(n,e){return+yn(n)-+yn(e)}function Ih(n,e){const t=yn(n,e?.in);return t.setHours(23,59,59,999),t}function Oh(n,e){const t=yn(n,e?.in),s=t.getMonth();return t.setFullYear(t.getFullYear(),s+1,0),t.setHours(23,59,59,999),t}function Nh(n,e){const t=yn(n,e?.in);return+Ih(t,e)==+Oh(t,e)}function Dh(n,e,t){const[s,o,r]=Cr(t?.in,n,n,e),i=Xo(o,r),l=Math.abs(Mh(o,r));if(l<1)return 0;o.getMonth()===1&&o.getDate()>27&&o.setDate(30),o.setMonth(o.getMonth()-i*l);let a=Xo(o,r)===-i;Nh(s)&&l===1&&Xo(s,r)===1&&(a=!1);const c=i*(l-+a);return c===0?0:c}function Ah(n,e,t){const s=Eh(n,e)/1e3;return Th(t?.roundingMethod)(s)}const $h={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},Ph=(n,e,t)=>{let s;const o=$h[n];return typeof o=="string"?s=o:e===1?s=o.one:s=o.other.replace("{{count}}",e.toString()),t?.addSuffix?t.comparison&&t.comparison>0?"in "+s:s+" ago":s};function As(n){return(e={})=>{const t=e.width?String(e.width):n.defaultWidth;return n.formats[t]||n.formats[n.defaultWidth]}}const Rh={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},Vh={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},Lh={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Bh={date:As({formats:Rh,defaultWidth:"full"}),time:As({formats:Vh,defaultWidth:"full"}),dateTime:As({formats:Lh,defaultWidth:"full"})},zh={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},Fh=(n,e,t,s)=>zh[n];function nn(n){return(e,t)=>{const s=t?.context?String(t.context):"standalone";let o;if(s==="formatting"&&n.formattingValues){const i=n.defaultFormattingWidth||n.defaultWidth,l=t?.width?String(t.width):i;o=n.formattingValues[l]||n.formattingValues[i]}else{const i=n.defaultWidth,l=t?.width?String(t.width):n.defaultWidth;o=n.values[l]||n.values[i]}const r=n.argumentCallback?n.argumentCallback(e):e;return o[r]}}const Wh={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},Hh={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},qh={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},Jh={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},Uh={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},jh={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},Kh=(n,e)=>{const t=Number(n),s=t%100;if(s>20||s<10)switch(s%10){case 1:return t+"st";case 2:return t+"nd";case 3:return t+"rd"}return t+"th"},Xh={ordinalNumber:Kh,era:nn({values:Wh,defaultWidth:"wide"}),quarter:nn({values:Hh,defaultWidth:"wide",argumentCallback:n=>n-1}),month:nn({values:qh,defaultWidth:"wide"}),day:nn({values:Jh,defaultWidth:"wide"}),dayPeriod:nn({values:Uh,defaultWidth:"wide",formattingValues:jh,defaultFormattingWidth:"wide"})};function sn(n){return(e,t={})=>{const s=t.width,o=s&&n.matchPatterns[s]||n.matchPatterns[n.defaultMatchWidth],r=e.match(o);if(!r)return null;const i=r[0],l=s&&n.parsePatterns[s]||n.parsePatterns[n.defaultParseWidth],a=Array.isArray(l)?Gh(l,d=>d.test(i)):Yh(l,d=>d.test(i));let c;c=n.valueCallback?n.valueCallback(a):a,c=t.valueCallback?t.valueCallback(c):c;const u=e.slice(i.length);return{value:c,rest:u}}}function Yh(n,e){for(const t in n)if(Object.prototype.hasOwnProperty.call(n,t)&&e(n[t]))return t}function Gh(n,e){for(let t=0;t<n.length;t++)if(e(n[t]))return t}function Lc(n){return(e,t={})=>{const s=e.match(n.matchPattern);if(!s)return null;const o=s[0],r=e.match(n.parsePattern);if(!r)return null;let i=n.valueCallback?n.valueCallback(r[0]):r[0];i=t.valueCallback?t.valueCallback(i):i;const l=e.slice(o.length);return{value:i,rest:l}}}const Qh=/^(\d+)(th|st|nd|rd)?/i,Zh=/\d+/i,em={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},tm={any:[/^b/i,/^(a|c)/i]},nm={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},sm={any:[/1/i,/2/i,/3/i,/4/i]},om={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},rm={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},im={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},lm={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},am={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},cm={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},um={ordinalNumber:Lc({matchPattern:Qh,parsePattern:Zh,valueCallback:n=>parseInt(n,10)}),era:sn({matchPatterns:em,defaultMatchWidth:"wide",parsePatterns:tm,defaultParseWidth:"any"}),quarter:sn({matchPatterns:nm,defaultMatchWidth:"wide",parsePatterns:sm,defaultParseWidth:"any",valueCallback:n=>n+1}),month:sn({matchPatterns:om,defaultMatchWidth:"wide",parsePatterns:rm,defaultParseWidth:"any"}),day:sn({matchPatterns:im,defaultMatchWidth:"wide",parsePatterns:lm,defaultParseWidth:"any"}),dayPeriod:sn({matchPatterns:am,defaultMatchWidth:"any",parsePatterns:cm,defaultParseWidth:"any"})},dm={code:"en-US",formatDistance:Ph,formatLong:Bh,formatRelative:Fh,localize:Xh,match:um,options:{weekStartsOn:0,firstWeekContainsDate:1}};function fm(n,e,t){const s=Vc(),o=t?.locale??s.locale??dm,r=2520,i=Xo(n,e);if(isNaN(i))throw new RangeError("Invalid time value");const l=Object.assign({},t,{addSuffix:t?.addSuffix,comparison:i}),[a,c]=Cr(t?.in,...i>0?[e,n]:[n,e]),u=Ah(c,a),d=(oa(c)-oa(a))/1e3,v=Math.round((u-d)/60);let p;if(v<2)return t?.includeSeconds?u<5?o.formatDistance("lessThanXSeconds",5,l):u<10?o.formatDistance("lessThanXSeconds",10,l):u<20?o.formatDistance("lessThanXSeconds",20,l):u<40?o.formatDistance("halfAMinute",0,l):u<60?o.formatDistance("lessThanXMinutes",1,l):o.formatDistance("xMinutes",1,l):v===0?o.formatDistance("lessThanXMinutes",1,l):o.formatDistance("xMinutes",v,l);if(v<45)return o.formatDistance("xMinutes",v,l);if(v<90)return o.formatDistance("aboutXHours",1,l);if(v<ta){const b=Math.round(v/60);return o.formatDistance("aboutXHours",b,l)}else{if(v<r)return o.formatDistance("xDays",1,l);if(v<zo){const b=Math.round(v/ta);return o.formatDistance("xDays",b,l)}else if(v<zo*2)return p=Math.round(v/zo),o.formatDistance("aboutXMonths",p,l)}if(p=Dh(c,a),p<12){const b=Math.round(v/zo);return o.formatDistance("xMonths",b,l)}else{const b=p%12,w=Math.trunc(p/12);return b<3?o.formatDistance("aboutXYears",w,l):b<9?o.formatDistance("overXYears",w,l):o.formatDistance("almostXYears",w+1,l)}}function pm(n,e){return fm(n,_h(n),e)}function hm(n,e,t){const[s,o]=Cr(t?.in,n,e);return+sa(s,t)==+sa(o,t)}const mm={lessThanXSeconds:{one:"不到 1 秒",other:"不到 {{count}} 秒"},xSeconds:{one:"1 秒",other:"{{count}} 秒"},halfAMinute:"半分钟",lessThanXMinutes:{one:"不到 1 分钟",other:"不到 {{count}} 分钟"},xMinutes:{one:"1 分钟",other:"{{count}} 分钟"},xHours:{one:"1 小时",other:"{{count}} 小时"},aboutXHours:{one:"大约 1 小时",other:"大约 {{count}} 小时"},xDays:{one:"1 天",other:"{{count}} 天"},aboutXWeeks:{one:"大约 1 个星期",other:"大约 {{count}} 个星期"},xWeeks:{one:"1 个星期",other:"{{count}} 个星期"},aboutXMonths:{one:"大约 1 个月",other:"大约 {{count}} 个月"},xMonths:{one:"1 个月",other:"{{count}} 个月"},aboutXYears:{one:"大约 1 年",other:"大约 {{count}} 年"},xYears:{one:"1 年",other:"{{count}} 年"},overXYears:{one:"超过 1 年",other:"超过 {{count}} 年"},almostXYears:{one:"将近 1 年",other:"将近 {{count}} 年"}},gm=(n,e,t)=>{let s;const o=mm[n];return typeof o=="string"?s=o:e===1?s=o.one:s=o.other.replace("{{count}}",String(e)),t?.addSuffix?t.comparison&&t.comparison>0?s+"内":s+"前":s},vm={full:"y'年'M'月'd'日' EEEE",long:"y'年'M'月'd'日'",medium:"yyyy-MM-dd",short:"yy-MM-dd"},ym={full:"zzzz a h:mm:ss",long:"z a h:mm:ss",medium:"a h:mm:ss",short:"a h:mm"},bm={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},wm={date:As({formats:vm,defaultWidth:"full"}),time:As({formats:ym,defaultWidth:"full"}),dateTime:As({formats:bm,defaultWidth:"full"})};function ra(n,e,t){const s="eeee p";return hm(n,e,t)?s:n.getTime()>e.getTime()?"'下个'"+s:"'上个'"+s}const km={lastWeek:ra,yesterday:"'昨天' p",today:"'今天' p",tomorrow:"'明天' p",nextWeek:ra,other:"PP p"},xm=(n,e,t,s)=>{const o=km[n];return typeof o=="function"?o(e,t,s):o},Sm={narrow:["前","公元"],abbreviated:["前","公元"],wide:["公元前","公元"]},Cm={narrow:["1","2","3","4"],abbreviated:["第一季","第二季","第三季","第四季"],wide:["第一季度","第二季度","第三季度","第四季度"]},_m={narrow:["一","二","三","四","五","六","七","八","九","十","十一","十二"],abbreviated:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],wide:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]},Mm={narrow:["日","一","二","三","四","五","六"],short:["日","一","二","三","四","五","六"],abbreviated:["周日","周一","周二","周三","周四","周五","周六"],wide:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"]},Tm={narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},Em={narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},Im=(n,e)=>{const t=Number(n);switch(e?.unit){case"date":return t.toString()+"日";case"hour":return t.toString()+"时";case"minute":return t.toString()+"分";case"second":return t.toString()+"秒";default:return"第 "+t.toString()}},Om={ordinalNumber:Im,era:nn({values:Sm,defaultWidth:"wide"}),quarter:nn({values:Cm,defaultWidth:"wide",argumentCallback:n=>n-1}),month:nn({values:_m,defaultWidth:"wide"}),day:nn({values:Mm,defaultWidth:"wide"}),dayPeriod:nn({values:Tm,defaultWidth:"wide",formattingValues:Em,defaultFormattingWidth:"wide"})},Nm=/^(第\s*)?\d+(日|时|分|秒)?/i,Dm=/\d+/i,Am={narrow:/^(前)/i,abbreviated:/^(前)/i,wide:/^(公元前|公元)/i},$m={any:[/^(前)/i,/^(公元)/i]},Pm={narrow:/^[1234]/i,abbreviated:/^第[一二三四]刻/i,wide:/^第[一二三四]刻钟/i},Rm={any:[/(1|一)/i,/(2|二)/i,/(3|三)/i,/(4|四)/i]},Vm={narrow:/^(一|二|三|四|五|六|七|八|九|十[二一])/i,abbreviated:/^(一|二|三|四|五|六|七|八|九|十[二一]|\d|1[12])月/i,wide:/^(一|二|三|四|五|六|七|八|九|十[二一])月/i},Lm={narrow:[/^一/i,/^二/i,/^三/i,/^四/i,/^五/i,/^六/i,/^七/i,/^八/i,/^九/i,/^十(?!(一|二))/i,/^十一/i,/^十二/i],any:[/^一|1/i,/^二|2/i,/^三|3/i,/^四|4/i,/^五|5/i,/^六|6/i,/^七|7/i,/^八|8/i,/^九|9/i,/^十(?!(一|二))|10/i,/^十一|11/i,/^十二|12/i]},Bm={narrow:/^[一二三四五六日]/i,short:/^[一二三四五六日]/i,abbreviated:/^周[一二三四五六日]/i,wide:/^星期[一二三四五六日]/i},zm={any:[/日/i,/一/i,/二/i,/三/i,/四/i,/五/i,/六/i]},Fm={any:/^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨|)/i},Wm={any:{am:/^上午?/i,pm:/^下午?/i,midnight:/^午夜/i,noon:/^[中正]午/i,morning:/^早上/i,afternoon:/^下午/i,evening:/^晚上?/i,night:/^凌晨/i}},Hm={ordinalNumber:Lc({matchPattern:Nm,parsePattern:Dm,valueCallback:n=>parseInt(n,10)}),era:sn({matchPatterns:Am,defaultMatchWidth:"wide",parsePatterns:$m,defaultParseWidth:"any"}),quarter:sn({matchPatterns:Pm,defaultMatchWidth:"wide",parsePatterns:Rm,defaultParseWidth:"any",valueCallback:n=>n+1}),month:sn({matchPatterns:Vm,defaultMatchWidth:"wide",parsePatterns:Lm,defaultParseWidth:"any"}),day:sn({matchPatterns:Bm,defaultMatchWidth:"wide",parsePatterns:zm,defaultParseWidth:"any"}),dayPeriod:sn({matchPatterns:Fm,defaultMatchWidth:"any",parsePatterns:Wm,defaultParseWidth:"any"})},qm={code:"zh-CN",formatDistance:gm,formatLong:wm,formatRelative:xm,localize:Om,match:Hm,options:{weekStartsOn:1,firstWeekContainsDate:4}},Jm={class:"chat-sidebar-content"},Um={key:0,class:"chat-sidebar"},jm={key:0,class:"chat-content"},Km={class:"chat-search"},Xm={class:"chat-list"},Ym={key:0,class:"empty-chats"},Gm=["onClick"],Qm={class:"chat-item-content"},Zm={class:"chat-icon"},eg={class:"chat-details"},tg={class:"chat-title"},ng={class:"chat-info"},sg={class:"chat-date"},og={class:"chat-model"},rg={__name:"ChatSidebar",props:{visible:{type:Boolean,default:!1},bookId:{type:String,required:!0},selectedText:{type:String,default:""},editor:{type:Object,default:null},initialChatId:{type:String,default:null},initialDetailView:{type:Boolean,default:!1}},emits:["close","insert-text","chat-loaded","select-chat","chat-close"],setup(n,{emit:e}){const t=wn();me(()=>t.models);const s=n,o=e,r=I(""),i=I([]),l=I(!1),a=I(null),c=me(()=>`last_chat_${s.bookId}`);me(()=>{if(!a.value)return"";const _=i.value.find(T=>T.id===a.value);return _?_.title||"未命名会话":"聊天"});const u=me(()=>{if(!r.value)return i.value;const _=r.value.toLowerCase();return i.value.filter(T=>(T.title||"未命名会话").toLowerCase().includes(_))}),d=async()=>{try{l.value=!0;const _=await window.pywebview.api.model_controller.get_all_chats(),T=typeof _=="string"?JSON.parse(_):_;if(T&&T.status==="success"&&Array.isArray(T.data)){if(i.value=T.data.sort((U,W)=>(W.last_updated||0)-(U.last_updated||0)),!s.initialDetailView&&!s.initialChatId){const U=localStorage.getItem(c.value);U&&i.value.find(W=>(W.chat_id||W.id)===U)&&(a.value=U)}o("chat-loaded",a.value)}else throw new Error(T?.message||"加载聊天列表失败")}catch(_){console.error("加载聊天列表失败:",_),$.error("加载聊天列表失败")}finally{l.value=!1}},v=I(400),p=I(!1),b=I(null),w=I(0),C=I(0);ct(()=>{document.documentElement.style.setProperty("--chat-sidebar-width",`${v.value}px`)});const M=_=>{const T=_.chat_id||_.id;a.value=T,localStorage.setItem("chat-sidebar-width",document.documentElement.style.getPropertyValue("--chat-sidebar-width")||"400px"),o("select-chat",_)},X=async()=>{try{l.value=!0;const _=Date.now(),T=`chat_${_}`,U=t.selectedModel||(t.models?.length>0?t.models[0].id||t.models[0]:"chatglm3-6b");let W=0;const z=/^新对话(\d+)$/;i.value.forEach(ke=>{const be=(ke.title||"").match(z);if(be&&be[1]){const F=parseInt(be[1]);W=Math.max(W,F)}});const re={id:T,chat_id:T,title:`新对话${W+1}`,model:U,model_id:U,messages:[],timestamp:_,last_updated:Date.now()/1e3,updatedAt:new Date().toISOString()},fe=await window.pywebview.api.model_controller.save_chat(T,re),Te=typeof fe=="string"?JSON.parse(fe):fe;if(Te&&Te.status==="success")i.value.unshift(re),a.value=T,localStorage.setItem(c.value,T),o("select-chat",re);else throw new Error(Te?.message||"创建对话失败")}catch(_){console.error("创建对话失败:",_),$.error("创建对话失败")}finally{l.value=!1}},L=_=>{const T=_.chat_id||_.id,U=i.value.findIndex(W=>(W.chat_id||W.id)===T);U!==-1&&(i.value[U]={...i.value[U],..._})},D=async(_,T)=>{const U=T.chat_id||T.id;if(_==="rename")try{const{value:W}=await qt.prompt("请输入新名称","重命名会话",{inputValue:T.title||"未命名会话",confirmButtonText:"确定",cancelButtonText:"取消"});if(W&&W.trim()){const z=i.value.findIndex(re=>(re.chat_id||re.id)===U);if(z!==-1){i.value[z].title=W.trim(),i.value[z].last_updated=Date.now()/1e3;const re={...i.value[z]};await window.pywebview.api.model_controller.save_chat(U,re),$.success("重命名成功")}}}catch(W){W!=="cancel"&&(console.error("重命名失败:",W),$.error("重命名失败"))}else if(_==="delete")try{await qt.confirm("确定要删除这个会话吗？删除后将无法恢复。","删除会话",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"});const W=await window.pywebview.api.model_controller.delete_chat(U),z=typeof W=="string"?JSON.parse(W):W;if(z&&z.status==="success"){const re=i.value.findIndex(fe=>(fe.chat_id||fe.id)===U);re!==-1&&i.value.splice(re,1),a.value===U&&(a.value=null),$.success("删除成功")}else throw new Error(z?.message||"删除失败")}catch(W){W!=="cancel"&&(console.error("删除失败:",W),$.error(typeof W=="string"?W:"删除失败"))}},E=_=>{try{if(!_)return"未知时间";const T=typeof _=="number"&&_<1e10?_*1e3:_,U=new Date(T);return isNaN(U.getTime())?"未知时间":pm(U,{addSuffix:!0,locale:qm})}catch(T){return console.error("时间格式化错误:",T),"未知时间"}};Ie(()=>s.visible,_=>{_&&d().then(()=>{s.initialDetailView&&s.initialChatId&&i.value.some(U=>(U.chat_id||U.id)===s.initialChatId)&&(a.value=s.initialChatId)})});const B=_=>{_.preventDefault(),w.value=_.clientX||_.touches&&_.touches[0].clientX||0,C.value=parseInt(getComputedStyle(b.value).width,10),p.value=!0,document.body.classList.add("chat-sidebar-resizing"),document.addEventListener("mousemove",N),document.addEventListener("mouseup",V),document.addEventListener("touchmove",N),document.addEventListener("touchend",V)},N=_=>{if(!p.value)return;const T=_.clientX||_.touches&&_.touches[0].clientX||0,U=w.value-T;let W=C.value+U;W=Math.max(300,Math.min(W,window.innerWidth*.6)),document.documentElement.style.setProperty("--chat-sidebar-width",`${W}px`)},V=()=>{p.value=!1,document.body.classList.remove("chat-sidebar-resizing"),document.removeEventListener("mousemove",N),document.removeEventListener("mouseup",V),document.removeEventListener("touchmove",N),document.removeEventListener("touchend",V)};return lt(()=>{document.removeEventListener("mousemove",N),document.removeEventListener("mouseup",V),document.removeEventListener("touchmove",N),document.removeEventListener("touchend",V),document.body.classList.remove("chat-sidebar-resizing")}),(_,T)=>{const U=bn,W=_t,z=kn,re=yo,fe=el,Te=Zi,ke=Qi,Fe=xf;return P(),q("div",{class:we(["chat-sidebar-container",{collapsed:!n.visible}]),ref_key:"sidebarElement",ref:b},[g("div",{class:we(["resize-handle",{dragging:p.value}]),onMousedown:B,onTouchstart:B},null,34),g("div",Jm,[f(_c,{name:"chat-slide"},{default:y(()=>[n.visible?(P(),q("div",Um,[a.value?(P(),Ne(Sh,{key:1,"chat-id":a.value,"book-id":n.bookId,"selected-text":n.selectedText,editor:n.editor,onInsertText:T[2]||(T[2]=be=>_.$emit("insert-text",be)),onChatUpdated:L,onBack:T[3]||(T[3]=be=>a.value=null),class:"chat-panel-container"},null,8,["chat-id","book-id","selected-text","editor"])):(P(),q("div",jm,[g("div",Km,[f(U,{modelValue:r.value,"onUpdate:modelValue":T[0]||(T[0]=be=>r.value=be),placeholder:"搜索会话...","prefix-icon":"Search",clearable:""},null,8,["modelValue"]),f(z,{type:"primary",onClick:X,class:"new-chat-btn"},{default:y(()=>[f(W,null,{default:y(()=>[f(j(on))]),_:1}),T[4]||(T[4]=g("span",null,"新建会话",-1))]),_:1})]),Fn((P(),q("div",Xm,[u.value.length===0&&!l.value?(P(),q("div",Ym,[f(re,{description:"暂无会话"}),f(z,{type:"primary",onClick:X},{default:y(()=>T[5]||(T[5]=[ne("创建第一个会话")])),_:1})])):ge("",!0),(P(!0),q(_e,null,Ae(u.value,be=>(P(),q("div",{key:be.chat_id||be.id,class:we(["chat-item",{active:a.value===(be.chat_id||be.id),has_error:be.has_error}]),onClick:F=>M(be)},[g("div",Qm,[g("div",Zm,[f(W,null,{default:y(()=>[f(j(wf))]),_:1})]),g("div",eg,[g("div",tg,ee(be.title||"未命名会话"),1),g("div",ng,[g("span",sg,ee(E(be.last_updated)),1),g("span",og,ee(be.model_id||""),1)])])]),g("div",{class:"chat-actions",onClick:T[1]||(T[1]=Oe(()=>{},["stop"]))},[f(ke,{trigger:"click",onCommand:F=>D(F,be)},{dropdown:y(()=>[f(Te,null,{default:y(()=>[f(fe,{command:"rename"},{default:y(()=>T[6]||(T[6]=[ne("重命名")])),_:1}),f(fe,{command:"delete",divided:""},{default:y(()=>T[7]||(T[7]=[g("span",{style:{color:"var(--el-color-danger)"}},"删除",-1)])),_:1})]),_:1})]),default:y(()=>[f(z,{link:""},{default:y(()=>[f(W,null,{default:y(()=>[f(j(kf))]),_:1})]),_:1})]),_:2},1032,["onCommand"])])],10,Gm))),128))])),[[Fe,l.value]])]))])):ge("",!0)]),_:1})])],2)}}},ig=ut(rg,[["__scopeId","data-v-24dd17df"]]);function Ze(n){this.content=n}Ze.prototype={constructor:Ze,find:function(n){for(var e=0;e<this.content.length;e+=2)if(this.content[e]===n)return e;return-1},get:function(n){var e=this.find(n);return e==-1?void 0:this.content[e+1]},update:function(n,e,t){var s=t&&t!=n?this.remove(t):this,o=s.find(n),r=s.content.slice();return o==-1?r.push(t||n,e):(r[o+1]=e,t&&(r[o]=t)),new Ze(r)},remove:function(n){var e=this.find(n);if(e==-1)return this;var t=this.content.slice();return t.splice(e,2),new Ze(t)},addToStart:function(n,e){return new Ze([n,e].concat(this.remove(n).content))},addToEnd:function(n,e){var t=this.remove(n).content.slice();return t.push(n,e),new Ze(t)},addBefore:function(n,e,t){var s=this.remove(e),o=s.content.slice(),r=s.find(n);return o.splice(r==-1?o.length:r,0,e,t),new Ze(o)},forEach:function(n){for(var e=0;e<this.content.length;e+=2)n(this.content[e],this.content[e+1])},prepend:function(n){return n=Ze.from(n),n.size?new Ze(n.content.concat(this.subtract(n).content)):this},append:function(n){return n=Ze.from(n),n.size?new Ze(this.subtract(n).content.concat(n.content)):this},subtract:function(n){var e=this;n=Ze.from(n);for(var t=0;t<n.content.length;t+=2)e=e.remove(n.content[t]);return e},toObject:function(){var n={};return this.forEach(function(e,t){n[e]=t}),n},get size(){return this.content.length>>1}};Ze.from=function(n){if(n instanceof Ze)return n;var e=[];if(n)for(var t in n)e.push(t,n[t]);return new Ze(e)};function Bc(n,e,t){for(let s=0;;s++){if(s==n.childCount||s==e.childCount)return n.childCount==e.childCount?null:t;let o=n.child(s),r=e.child(s);if(o==r){t+=o.nodeSize;continue}if(!o.sameMarkup(r))return t;if(o.isText&&o.text!=r.text){for(let i=0;o.text[i]==r.text[i];i++)t++;return t}if(o.content.size||r.content.size){let i=Bc(o.content,r.content,t+1);if(i!=null)return i}t+=o.nodeSize}}function zc(n,e,t,s){for(let o=n.childCount,r=e.childCount;;){if(o==0||r==0)return o==r?null:{a:t,b:s};let i=n.child(--o),l=e.child(--r),a=i.nodeSize;if(i==l){t-=a,s-=a;continue}if(!i.sameMarkup(l))return{a:t,b:s};if(i.isText&&i.text!=l.text){let c=0,u=Math.min(i.text.length,l.text.length);for(;c<u&&i.text[i.text.length-c-1]==l.text[l.text.length-c-1];)c++,t--,s--;return{a:t,b:s}}if(i.content.size||l.content.size){let c=zc(i.content,l.content,t-1,s-1);if(c)return c}t-=a,s-=a}}class Q{constructor(e,t){if(this.content=e,this.size=t||0,t==null)for(let s=0;s<e.length;s++)this.size+=e[s].nodeSize}nodesBetween(e,t,s,o=0,r){for(let i=0,l=0;l<t;i++){let a=this.content[i],c=l+a.nodeSize;if(c>e&&s(a,o+l,r||null,i)!==!1&&a.content.size){let u=l+1;a.nodesBetween(Math.max(0,e-u),Math.min(a.content.size,t-u),s,o+u)}l=c}}descendants(e){this.nodesBetween(0,this.size,e)}textBetween(e,t,s,o){let r="",i=!0;return this.nodesBetween(e,t,(l,a)=>{let c=l.isText?l.text.slice(Math.max(e,a)-a,t-a):l.isLeaf?o?typeof o=="function"?o(l):o:l.type.spec.leafText?l.type.spec.leafText(l):"":"";l.isBlock&&(l.isLeaf&&c||l.isTextblock)&&s&&(i?i=!1:r+=s),r+=c},0),r}append(e){if(!e.size)return this;if(!this.size)return e;let t=this.lastChild,s=e.firstChild,o=this.content.slice(),r=0;for(t.isText&&t.sameMarkup(s)&&(o[o.length-1]=t.withText(t.text+s.text),r=1);r<e.content.length;r++)o.push(e.content[r]);return new Q(o,this.size+e.size)}cut(e,t=this.size){if(e==0&&t==this.size)return this;let s=[],o=0;if(t>e)for(let r=0,i=0;i<t;r++){let l=this.content[r],a=i+l.nodeSize;a>e&&((i<e||a>t)&&(l.isText?l=l.cut(Math.max(0,e-i),Math.min(l.text.length,t-i)):l=l.cut(Math.max(0,e-i-1),Math.min(l.content.size,t-i-1))),s.push(l),o+=l.nodeSize),i=a}return new Q(s,o)}cutByIndex(e,t){return e==t?Q.empty:e==0&&t==this.content.length?this:new Q(this.content.slice(e,t))}replaceChild(e,t){let s=this.content[e];if(s==t)return this;let o=this.content.slice(),r=this.size+t.nodeSize-s.nodeSize;return o[e]=t,new Q(o,r)}addToStart(e){return new Q([e].concat(this.content),this.size+e.nodeSize)}addToEnd(e){return new Q(this.content.concat(e),this.size+e.nodeSize)}eq(e){if(this.content.length!=e.content.length)return!1;for(let t=0;t<this.content.length;t++)if(!this.content[t].eq(e.content[t]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(e){let t=this.content[e];if(!t)throw new RangeError("Index "+e+" out of range for "+this);return t}maybeChild(e){return this.content[e]||null}forEach(e){for(let t=0,s=0;t<this.content.length;t++){let o=this.content[t];e(o,s,t),s+=o.nodeSize}}findDiffStart(e,t=0){return Bc(this,e,t)}findDiffEnd(e,t=this.size,s=e.size){return zc(this,e,t,s)}findIndex(e,t=-1){if(e==0)return Fo(0,e);if(e==this.size)return Fo(this.content.length,e);if(e>this.size||e<0)throw new RangeError(`Position ${e} outside of fragment (${this})`);for(let s=0,o=0;;s++){let r=this.child(s),i=o+r.nodeSize;if(i>=e)return i==e||t>0?Fo(s+1,i):Fo(s,o);o=i}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(e=>e.toJSON()):null}static fromJSON(e,t){if(!t)return Q.empty;if(!Array.isArray(t))throw new RangeError("Invalid input for Fragment.fromJSON");return new Q(t.map(e.nodeFromJSON))}static fromArray(e){if(!e.length)return Q.empty;let t,s=0;for(let o=0;o<e.length;o++){let r=e[o];s+=r.nodeSize,o&&r.isText&&e[o-1].sameMarkup(r)?(t||(t=e.slice(0,o)),t[t.length-1]=r.withText(t[t.length-1].text+r.text)):t&&t.push(r)}return new Q(t||e,s)}static from(e){if(!e)return Q.empty;if(e instanceof Q)return e;if(Array.isArray(e))return this.fromArray(e);if(e.attrs)return new Q([e],e.nodeSize);throw new RangeError("Can not convert "+e+" to a Fragment"+(e.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}Q.empty=new Q([],0);const ii={index:0,offset:0};function Fo(n,e){return ii.index=n,ii.offset=e,ii}function or(n,e){if(n===e)return!0;if(!(n&&typeof n=="object")||!(e&&typeof e=="object"))return!1;let t=Array.isArray(n);if(Array.isArray(e)!=t)return!1;if(t){if(n.length!=e.length)return!1;for(let s=0;s<n.length;s++)if(!or(n[s],e[s]))return!1}else{for(let s in n)if(!(s in e)||!or(n[s],e[s]))return!1;for(let s in e)if(!(s in n))return!1}return!0}let He=class Ii{constructor(e,t){this.type=e,this.attrs=t}addToSet(e){let t,s=!1;for(let o=0;o<e.length;o++){let r=e[o];if(this.eq(r))return e;if(this.type.excludes(r.type))t||(t=e.slice(0,o));else{if(r.type.excludes(this.type))return e;!s&&r.type.rank>this.type.rank&&(t||(t=e.slice(0,o)),t.push(this),s=!0),t&&t.push(r)}}return t||(t=e.slice()),s||t.push(this),t}removeFromSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return e.slice(0,t).concat(e.slice(t+1));return e}isInSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return!0;return!1}eq(e){return this==e||this.type==e.type&&or(this.attrs,e.attrs)}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Mark.fromJSON");let s=e.marks[t.type];if(!s)throw new RangeError(`There is no mark type ${t.type} in this schema`);let o=s.create(t.attrs);return s.checkAttrs(o.attrs),o}static sameSet(e,t){if(e==t)return!0;if(e.length!=t.length)return!1;for(let s=0;s<e.length;s++)if(!e[s].eq(t[s]))return!1;return!0}static setFrom(e){if(!e||Array.isArray(e)&&e.length==0)return Ii.none;if(e instanceof Ii)return[e];let t=e.slice();return t.sort((s,o)=>s.type.rank-o.type.rank),t}};He.none=[];class rr extends Error{}class le{constructor(e,t,s){this.content=e,this.openStart=t,this.openEnd=s}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(e,t){let s=Wc(this.content,e+this.openStart,t);return s&&new le(s,this.openStart,this.openEnd)}removeBetween(e,t){return new le(Fc(this.content,e+this.openStart,t+this.openStart),this.openStart,this.openEnd)}eq(e){return this.content.eq(e.content)&&this.openStart==e.openStart&&this.openEnd==e.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let e={content:this.content.toJSON()};return this.openStart>0&&(e.openStart=this.openStart),this.openEnd>0&&(e.openEnd=this.openEnd),e}static fromJSON(e,t){if(!t)return le.empty;let s=t.openStart||0,o=t.openEnd||0;if(typeof s!="number"||typeof o!="number")throw new RangeError("Invalid input for Slice.fromJSON");return new le(Q.fromJSON(e,t.content),s,o)}static maxOpen(e,t=!0){let s=0,o=0;for(let r=e.firstChild;r&&!r.isLeaf&&(t||!r.type.spec.isolating);r=r.firstChild)s++;for(let r=e.lastChild;r&&!r.isLeaf&&(t||!r.type.spec.isolating);r=r.lastChild)o++;return new le(e,s,o)}}le.empty=new le(Q.empty,0,0);function Fc(n,e,t){let{index:s,offset:o}=n.findIndex(e),r=n.maybeChild(s),{index:i,offset:l}=n.findIndex(t);if(o==e||r.isText){if(l!=t&&!n.child(i).isText)throw new RangeError("Removing non-flat range");return n.cut(0,e).append(n.cut(t))}if(s!=i)throw new RangeError("Removing non-flat range");return n.replaceChild(s,r.copy(Fc(r.content,e-o-1,t-o-1)))}function Wc(n,e,t,s){let{index:o,offset:r}=n.findIndex(e),i=n.maybeChild(o);if(r==e||i.isText)return n.cut(0,e).append(t).append(n.cut(e));let l=Wc(i.content,e-r-1,t);return l&&n.replaceChild(o,i.copy(l))}function lg(n,e,t){if(t.openStart>n.depth)throw new rr("Inserted content deeper than insertion position");if(n.depth-t.openStart!=e.depth-t.openEnd)throw new rr("Inconsistent open depths");return Hc(n,e,t,0)}function Hc(n,e,t,s){let o=n.index(s),r=n.node(s);if(o==e.index(s)&&s<n.depth-t.openStart){let i=Hc(n,e,t,s+1);return r.copy(r.content.replaceChild(o,i))}else if(t.content.size)if(!t.openStart&&!t.openEnd&&n.depth==s&&e.depth==s){let i=n.parent,l=i.content;return as(i,l.cut(0,n.parentOffset).append(t.content).append(l.cut(e.parentOffset)))}else{let{start:i,end:l}=ag(t,n);return as(r,Jc(n,i,l,e,s))}else return as(r,ir(n,e,s))}function qc(n,e){if(!e.type.compatibleContent(n.type))throw new rr("Cannot join "+e.type.name+" onto "+n.type.name)}function Oi(n,e,t){let s=n.node(t);return qc(s,e.node(t)),s}function ls(n,e){let t=e.length-1;t>=0&&n.isText&&n.sameMarkup(e[t])?e[t]=n.withText(e[t].text+n.text):e.push(n)}function ro(n,e,t,s){let o=(e||n).node(t),r=0,i=e?e.index(t):o.childCount;n&&(r=n.index(t),n.depth>t?r++:n.textOffset&&(ls(n.nodeAfter,s),r++));for(let l=r;l<i;l++)ls(o.child(l),s);e&&e.depth==t&&e.textOffset&&ls(e.nodeBefore,s)}function as(n,e){return n.type.checkContent(e),n.copy(e)}function Jc(n,e,t,s,o){let r=n.depth>o&&Oi(n,e,o+1),i=s.depth>o&&Oi(t,s,o+1),l=[];return ro(null,n,o,l),r&&i&&e.index(o)==t.index(o)?(qc(r,i),ls(as(r,Jc(n,e,t,s,o+1)),l)):(r&&ls(as(r,ir(n,e,o+1)),l),ro(e,t,o,l),i&&ls(as(i,ir(t,s,o+1)),l)),ro(s,null,o,l),new Q(l)}function ir(n,e,t){let s=[];if(ro(null,n,t,s),n.depth>t){let o=Oi(n,e,t+1);ls(as(o,ir(n,e,t+1)),s)}return ro(e,null,t,s),new Q(s)}function ag(n,e){let t=e.depth-n.openStart,o=e.node(t).copy(n.content);for(let r=t-1;r>=0;r--)o=e.node(r).copy(Q.from(o));return{start:o.resolveNoCache(n.openStart+t),end:o.resolveNoCache(o.content.size-n.openEnd-t)}}class ao{constructor(e,t,s){this.pos=e,this.path=t,this.parentOffset=s,this.depth=t.length/3-1}resolveDepth(e){return e==null?this.depth:e<0?this.depth+e:e}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(e){return this.path[this.resolveDepth(e)*3]}index(e){return this.path[this.resolveDepth(e)*3+1]}indexAfter(e){return e=this.resolveDepth(e),this.index(e)+(e==this.depth&&!this.textOffset?0:1)}start(e){return e=this.resolveDepth(e),e==0?0:this.path[e*3-1]+1}end(e){return e=this.resolveDepth(e),this.start(e)+this.node(e).content.size}before(e){if(e=this.resolveDepth(e),!e)throw new RangeError("There is no position before the top-level node");return e==this.depth+1?this.pos:this.path[e*3-1]}after(e){if(e=this.resolveDepth(e),!e)throw new RangeError("There is no position after the top-level node");return e==this.depth+1?this.pos:this.path[e*3-1]+this.path[e*3].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let e=this.parent,t=this.index(this.depth);if(t==e.childCount)return null;let s=this.pos-this.path[this.path.length-1],o=e.child(t);return s?e.child(t).cut(s):o}get nodeBefore(){let e=this.index(this.depth),t=this.pos-this.path[this.path.length-1];return t?this.parent.child(e).cut(0,t):e==0?null:this.parent.child(e-1)}posAtIndex(e,t){t=this.resolveDepth(t);let s=this.path[t*3],o=t==0?0:this.path[t*3-1]+1;for(let r=0;r<e;r++)o+=s.child(r).nodeSize;return o}marks(){let e=this.parent,t=this.index();if(e.content.size==0)return He.none;if(this.textOffset)return e.child(t).marks;let s=e.maybeChild(t-1),o=e.maybeChild(t);if(!s){let l=s;s=o,o=l}let r=s.marks;for(var i=0;i<r.length;i++)r[i].type.spec.inclusive===!1&&(!o||!r[i].isInSet(o.marks))&&(r=r[i--].removeFromSet(r));return r}marksAcross(e){let t=this.parent.maybeChild(this.index());if(!t||!t.isInline)return null;let s=t.marks,o=e.parent.maybeChild(e.index());for(var r=0;r<s.length;r++)s[r].type.spec.inclusive===!1&&(!o||!s[r].isInSet(o.marks))&&(s=s[r--].removeFromSet(s));return s}sharedDepth(e){for(let t=this.depth;t>0;t--)if(this.start(t)<=e&&this.end(t)>=e)return t;return 0}blockRange(e=this,t){if(e.pos<this.pos)return e.blockRange(this);for(let s=this.depth-(this.parent.inlineContent||this.pos==e.pos?1:0);s>=0;s--)if(e.pos<=this.end(s)&&(!t||t(this.node(s))))return new lr(this,e,s);return null}sameParent(e){return this.pos-this.parentOffset==e.pos-e.parentOffset}max(e){return e.pos>this.pos?e:this}min(e){return e.pos<this.pos?e:this}toString(){let e="";for(let t=1;t<=this.depth;t++)e+=(e?"/":"")+this.node(t).type.name+"_"+this.index(t-1);return e+":"+this.parentOffset}static resolve(e,t){if(!(t>=0&&t<=e.content.size))throw new RangeError("Position "+t+" out of range");let s=[],o=0,r=t;for(let i=e;;){let{index:l,offset:a}=i.content.findIndex(r),c=r-a;if(s.push(i,l,o+a),!c||(i=i.child(l),i.isText))break;r=c-1,o+=a+1}return new ao(t,s,r)}static resolveCached(e,t){let s=ia.get(e);if(s)for(let r=0;r<s.elts.length;r++){let i=s.elts[r];if(i.pos==t)return i}else ia.set(e,s=new cg);let o=s.elts[s.i]=ao.resolve(e,t);return s.i=(s.i+1)%ug,o}}class cg{constructor(){this.elts=[],this.i=0}}const ug=12,ia=new WeakMap;class lr{constructor(e,t,s){this.$from=e,this.$to=t,this.depth=s}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}const dg=Object.create(null);let Rn=class Ni{constructor(e,t,s,o=He.none){this.type=e,this.attrs=t,this.marks=o,this.content=s||Q.empty}get children(){return this.content.content}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(e){return this.content.child(e)}maybeChild(e){return this.content.maybeChild(e)}forEach(e){this.content.forEach(e)}nodesBetween(e,t,s,o=0){this.content.nodesBetween(e,t,s,o,this)}descendants(e){this.nodesBetween(0,this.content.size,e)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(e,t,s,o){return this.content.textBetween(e,t,s,o)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(e){return this==e||this.sameMarkup(e)&&this.content.eq(e.content)}sameMarkup(e){return this.hasMarkup(e.type,e.attrs,e.marks)}hasMarkup(e,t,s){return this.type==e&&or(this.attrs,t||e.defaultAttrs||dg)&&He.sameSet(this.marks,s||He.none)}copy(e=null){return e==this.content?this:new Ni(this.type,this.attrs,e,this.marks)}mark(e){return e==this.marks?this:new Ni(this.type,this.attrs,this.content,e)}cut(e,t=this.content.size){return e==0&&t==this.content.size?this:this.copy(this.content.cut(e,t))}slice(e,t=this.content.size,s=!1){if(e==t)return le.empty;let o=this.resolve(e),r=this.resolve(t),i=s?0:o.sharedDepth(t),l=o.start(i),c=o.node(i).content.cut(o.pos-l,r.pos-l);return new le(c,o.depth-i,r.depth-i)}replace(e,t,s){return lg(this.resolve(e),this.resolve(t),s)}nodeAt(e){for(let t=this;;){let{index:s,offset:o}=t.content.findIndex(e);if(t=t.maybeChild(s),!t)return null;if(o==e||t.isText)return t;e-=o+1}}childAfter(e){let{index:t,offset:s}=this.content.findIndex(e);return{node:this.content.maybeChild(t),index:t,offset:s}}childBefore(e){if(e==0)return{node:null,index:0,offset:0};let{index:t,offset:s}=this.content.findIndex(e);if(s<e)return{node:this.content.child(t),index:t,offset:s};let o=this.content.child(t-1);return{node:o,index:t-1,offset:s-o.nodeSize}}resolve(e){return ao.resolveCached(this,e)}resolveNoCache(e){return ao.resolve(this,e)}rangeHasMark(e,t,s){let o=!1;return t>e&&this.nodesBetween(e,t,r=>(s.isInSet(r.marks)&&(o=!0),!o)),o}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let e=this.type.name;return this.content.size&&(e+="("+this.content.toStringInner()+")"),Uc(this.marks,e)}contentMatchAt(e){let t=this.type.contentMatch.matchFragment(this.content,0,e);if(!t)throw new Error("Called contentMatchAt on a node with invalid content");return t}canReplace(e,t,s=Q.empty,o=0,r=s.childCount){let i=this.contentMatchAt(e).matchFragment(s,o,r),l=i&&i.matchFragment(this.content,t);if(!l||!l.validEnd)return!1;for(let a=o;a<r;a++)if(!this.type.allowsMarks(s.child(a).marks))return!1;return!0}canReplaceWith(e,t,s,o){if(o&&!this.type.allowsMarks(o))return!1;let r=this.contentMatchAt(e).matchType(s),i=r&&r.matchFragment(this.content,t);return i?i.validEnd:!1}canAppend(e){return e.content.size?this.canReplace(this.childCount,this.childCount,e.content):this.type.compatibleContent(e.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let e=He.none;for(let t=0;t<this.marks.length;t++){let s=this.marks[t];s.type.checkAttrs(s.attrs),e=s.addToSet(e)}if(!He.sameSet(e,this.marks))throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(t=>t.type.name)}`);this.content.forEach(t=>t.check())}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return this.content.size&&(e.content=this.content.toJSON()),this.marks.length&&(e.marks=this.marks.map(t=>t.toJSON())),e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Node.fromJSON");let s;if(t.marks){if(!Array.isArray(t.marks))throw new RangeError("Invalid mark data for Node.fromJSON");s=t.marks.map(e.markFromJSON)}if(t.type=="text"){if(typeof t.text!="string")throw new RangeError("Invalid text node in JSON");return e.text(t.text,s)}let o=Q.fromJSON(e,t.content),r=e.nodeType(t.type).create(t.attrs,o,s);return r.type.checkAttrs(r.attrs),r}};Rn.prototype.text=void 0;class ar extends Rn{constructor(e,t,s,o){if(super(e,t,null,o),!s)throw new RangeError("Empty text nodes are not allowed");this.text=s}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):Uc(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(e,t){return this.text.slice(e,t)}get nodeSize(){return this.text.length}mark(e){return e==this.marks?this:new ar(this.type,this.attrs,this.text,e)}withText(e){return e==this.text?this:new ar(this.type,this.attrs,e,this.marks)}cut(e=0,t=this.text.length){return e==0&&t==this.text.length?this:this.withText(this.text.slice(e,t))}eq(e){return this.sameMarkup(e)&&this.text==e.text}toJSON(){let e=super.toJSON();return e.text=this.text,e}}function Uc(n,e){for(let t=n.length-1;t>=0;t--)e=n[t].type.name+"("+e+")";return e}class hs{constructor(e){this.validEnd=e,this.next=[],this.wrapCache=[]}static parse(e,t){let s=new fg(e,t);if(s.next==null)return hs.empty;let o=jc(s);s.next&&s.err("Unexpected trailing text");let r=bg(yg(o));return wg(r,s),r}matchType(e){for(let t=0;t<this.next.length;t++)if(this.next[t].type==e)return this.next[t].next;return null}matchFragment(e,t=0,s=e.childCount){let o=this;for(let r=t;o&&r<s;r++)o=o.matchType(e.child(r).type);return o}get inlineContent(){return this.next.length!=0&&this.next[0].type.isInline}get defaultType(){for(let e=0;e<this.next.length;e++){let{type:t}=this.next[e];if(!(t.isText||t.hasRequiredAttrs()))return t}return null}compatible(e){for(let t=0;t<this.next.length;t++)for(let s=0;s<e.next.length;s++)if(this.next[t].type==e.next[s].type)return!0;return!1}fillBefore(e,t=!1,s=0){let o=[this];function r(i,l){let a=i.matchFragment(e,s);if(a&&(!t||a.validEnd))return Q.from(l.map(c=>c.createAndFill()));for(let c=0;c<i.next.length;c++){let{type:u,next:d}=i.next[c];if(!(u.isText||u.hasRequiredAttrs())&&o.indexOf(d)==-1){o.push(d);let v=r(d,l.concat(u));if(v)return v}}return null}return r(this,[])}findWrapping(e){for(let s=0;s<this.wrapCache.length;s+=2)if(this.wrapCache[s]==e)return this.wrapCache[s+1];let t=this.computeWrapping(e);return this.wrapCache.push(e,t),t}computeWrapping(e){let t=Object.create(null),s=[{match:this,type:null,via:null}];for(;s.length;){let o=s.shift(),r=o.match;if(r.matchType(e)){let i=[];for(let l=o;l.type;l=l.via)i.push(l.type);return i.reverse()}for(let i=0;i<r.next.length;i++){let{type:l,next:a}=r.next[i];!l.isLeaf&&!l.hasRequiredAttrs()&&!(l.name in t)&&(!o.type||a.validEnd)&&(s.push({match:l.contentMatch,type:l,via:o}),t[l.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(e){if(e>=this.next.length)throw new RangeError(`There's no ${e}th edge in this content match`);return this.next[e]}toString(){let e=[];function t(s){e.push(s);for(let o=0;o<s.next.length;o++)e.indexOf(s.next[o].next)==-1&&t(s.next[o].next)}return t(this),e.map((s,o)=>{let r=o+(s.validEnd?"*":" ")+" ";for(let i=0;i<s.next.length;i++)r+=(i?", ":"")+s.next[i].type.name+"->"+e.indexOf(s.next[i].next);return r}).join(`
`)}}hs.empty=new hs(!0);class fg{constructor(e,t){this.string=e,this.nodeTypes=t,this.inline=null,this.pos=0,this.tokens=e.split(/\s*(?=\b|\W|$)/),this.tokens[this.tokens.length-1]==""&&this.tokens.pop(),this.tokens[0]==""&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(e){return this.next==e&&(this.pos++||!0)}err(e){throw new SyntaxError(e+" (in content expression '"+this.string+"')")}}function jc(n){let e=[];do e.push(pg(n));while(n.eat("|"));return e.length==1?e[0]:{type:"choice",exprs:e}}function pg(n){let e=[];do e.push(hg(n));while(n.next&&n.next!=")"&&n.next!="|");return e.length==1?e[0]:{type:"seq",exprs:e}}function hg(n){let e=vg(n);for(;;)if(n.eat("+"))e={type:"plus",expr:e};else if(n.eat("*"))e={type:"star",expr:e};else if(n.eat("?"))e={type:"opt",expr:e};else if(n.eat("{"))e=mg(n,e);else break;return e}function la(n){/\D/.test(n.next)&&n.err("Expected number, got '"+n.next+"'");let e=Number(n.next);return n.pos++,e}function mg(n,e){let t=la(n),s=t;return n.eat(",")&&(n.next!="}"?s=la(n):s=-1),n.eat("}")||n.err("Unclosed braced range"),{type:"range",min:t,max:s,expr:e}}function gg(n,e){let t=n.nodeTypes,s=t[e];if(s)return[s];let o=[];for(let r in t){let i=t[r];i.isInGroup(e)&&o.push(i)}return o.length==0&&n.err("No node type or group '"+e+"' found"),o}function vg(n){if(n.eat("(")){let e=jc(n);return n.eat(")")||n.err("Missing closing paren"),e}else if(/\W/.test(n.next))n.err("Unexpected token '"+n.next+"'");else{let e=gg(n,n.next).map(t=>(n.inline==null?n.inline=t.isInline:n.inline!=t.isInline&&n.err("Mixing inline and block content"),{type:"name",value:t}));return n.pos++,e.length==1?e[0]:{type:"choice",exprs:e}}}function yg(n){let e=[[]];return o(r(n,0),t()),e;function t(){return e.push([])-1}function s(i,l,a){let c={term:a,to:l};return e[i].push(c),c}function o(i,l){i.forEach(a=>a.to=l)}function r(i,l){if(i.type=="choice")return i.exprs.reduce((a,c)=>a.concat(r(c,l)),[]);if(i.type=="seq")for(let a=0;;a++){let c=r(i.exprs[a],l);if(a==i.exprs.length-1)return c;o(c,l=t())}else if(i.type=="star"){let a=t();return s(l,a),o(r(i.expr,a),a),[s(a)]}else if(i.type=="plus"){let a=t();return o(r(i.expr,l),a),o(r(i.expr,a),a),[s(a)]}else{if(i.type=="opt")return[s(l)].concat(r(i.expr,l));if(i.type=="range"){let a=l;for(let c=0;c<i.min;c++){let u=t();o(r(i.expr,a),u),a=u}if(i.max==-1)o(r(i.expr,a),a);else for(let c=i.min;c<i.max;c++){let u=t();s(a,u),o(r(i.expr,a),u),a=u}return[s(a)]}else{if(i.type=="name")return[s(l,void 0,i.value)];throw new Error("Unknown expr type")}}}}function Kc(n,e){return e-n}function aa(n,e){let t=[];return s(e),t.sort(Kc);function s(o){let r=n[o];if(r.length==1&&!r[0].term)return s(r[0].to);t.push(o);for(let i=0;i<r.length;i++){let{term:l,to:a}=r[i];!l&&t.indexOf(a)==-1&&s(a)}}}function bg(n){let e=Object.create(null);return t(aa(n,0));function t(s){let o=[];s.forEach(i=>{n[i].forEach(({term:l,to:a})=>{if(!l)return;let c;for(let u=0;u<o.length;u++)o[u][0]==l&&(c=o[u][1]);aa(n,a).forEach(u=>{c||o.push([l,c=[]]),c.indexOf(u)==-1&&c.push(u)})})});let r=e[s.join(",")]=new hs(s.indexOf(n.length-1)>-1);for(let i=0;i<o.length;i++){let l=o[i][1].sort(Kc);r.next.push({type:o[i][0],next:e[l.join(",")]||t(l)})}return r}}function wg(n,e){for(let t=0,s=[n];t<s.length;t++){let o=s[t],r=!o.validEnd,i=[];for(let l=0;l<o.next.length;l++){let{type:a,next:c}=o.next[l];i.push(a.name),r&&!(a.isText||a.hasRequiredAttrs())&&(r=!1),s.indexOf(c)==-1&&s.push(c)}r&&e.err("Only non-generatable nodes ("+i.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}function Xc(n){let e=Object.create(null);for(let t in n){let s=n[t];if(!s.hasDefault)return null;e[t]=s.default}return e}function Yc(n,e){let t=Object.create(null);for(let s in n){let o=e&&e[s];if(o===void 0){let r=n[s];if(r.hasDefault)o=r.default;else throw new RangeError("No value supplied for attribute "+s)}t[s]=o}return t}function Gc(n,e,t,s){for(let o in e)if(!(o in n))throw new RangeError(`Unsupported attribute ${o} for ${t} of type ${o}`);for(let o in n){let r=n[o];r.validate&&r.validate(e[o])}}function Qc(n,e){let t=Object.create(null);if(e)for(let s in e)t[s]=new xg(n,s,e[s]);return t}let ca=class Zc{constructor(e,t,s){this.name=e,this.schema=t,this.spec=s,this.markSet=null,this.groups=s.group?s.group.split(" "):[],this.attrs=Qc(e,s.attrs),this.defaultAttrs=Xc(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(s.inline||e=="text"),this.isText=e=="text"}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==hs.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(e){return this.groups.indexOf(e)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let e in this.attrs)if(this.attrs[e].isRequired)return!0;return!1}compatibleContent(e){return this==e||this.contentMatch.compatible(e.contentMatch)}computeAttrs(e){return!e&&this.defaultAttrs?this.defaultAttrs:Yc(this.attrs,e)}create(e=null,t,s){if(this.isText)throw new Error("NodeType.create can't construct text nodes");return new Rn(this,this.computeAttrs(e),Q.from(t),He.setFrom(s))}createChecked(e=null,t,s){return t=Q.from(t),this.checkContent(t),new Rn(this,this.computeAttrs(e),t,He.setFrom(s))}createAndFill(e=null,t,s){if(e=this.computeAttrs(e),t=Q.from(t),t.size){let i=this.contentMatch.fillBefore(t);if(!i)return null;t=i.append(t)}let o=this.contentMatch.matchFragment(t),r=o&&o.fillBefore(Q.empty,!0);return r?new Rn(this,e,t.append(r),He.setFrom(s)):null}validContent(e){let t=this.contentMatch.matchFragment(e);if(!t||!t.validEnd)return!1;for(let s=0;s<e.childCount;s++)if(!this.allowsMarks(e.child(s).marks))return!1;return!0}checkContent(e){if(!this.validContent(e))throw new RangeError(`Invalid content for node ${this.name}: ${e.toString().slice(0,50)}`)}checkAttrs(e){Gc(this.attrs,e,"node",this.name)}allowsMarkType(e){return this.markSet==null||this.markSet.indexOf(e)>-1}allowsMarks(e){if(this.markSet==null)return!0;for(let t=0;t<e.length;t++)if(!this.allowsMarkType(e[t].type))return!1;return!0}allowedMarks(e){if(this.markSet==null)return e;let t;for(let s=0;s<e.length;s++)this.allowsMarkType(e[s].type)?t&&t.push(e[s]):t||(t=e.slice(0,s));return t?t.length?t:He.none:e}static compile(e,t){let s=Object.create(null);e.forEach((r,i)=>s[r]=new Zc(r,t,i));let o=t.spec.topNode||"doc";if(!s[o])throw new RangeError("Schema is missing its top node type ('"+o+"')");if(!s.text)throw new RangeError("Every schema needs a 'text' type");for(let r in s.text.attrs)throw new RangeError("The text node type should not have attributes");return s}};function kg(n,e,t){let s=t.split("|");return o=>{let r=o===null?"null":typeof o;if(s.indexOf(r)<0)throw new RangeError(`Expected value of type ${s} for attribute ${e} on type ${n}, got ${r}`)}}class xg{constructor(e,t,s){this.hasDefault=Object.prototype.hasOwnProperty.call(s,"default"),this.default=s.default,this.validate=typeof s.validate=="string"?kg(e,t,s.validate):s.validate}get isRequired(){return!this.hasDefault}}class _r{constructor(e,t,s,o){this.name=e,this.rank=t,this.schema=s,this.spec=o,this.attrs=Qc(e,o.attrs),this.excluded=null;let r=Xc(this.attrs);this.instance=r?new He(this,r):null}create(e=null){return!e&&this.instance?this.instance:new He(this,Yc(this.attrs,e))}static compile(e,t){let s=Object.create(null),o=0;return e.forEach((r,i)=>s[r]=new _r(r,o++,t,i)),s}removeFromSet(e){for(var t=0;t<e.length;t++)e[t].type==this&&(e=e.slice(0,t).concat(e.slice(t+1)),t--);return e}isInSet(e){for(let t=0;t<e.length;t++)if(e[t].type==this)return e[t]}checkAttrs(e){Gc(this.attrs,e,"mark",this.name)}excludes(e){return this.excluded.indexOf(e)>-1}}class eu{constructor(e){this.linebreakReplacement=null,this.cached=Object.create(null);let t=this.spec={};for(let o in e)t[o]=e[o];t.nodes=Ze.from(e.nodes),t.marks=Ze.from(e.marks||{}),this.nodes=ca.compile(this.spec.nodes,this),this.marks=_r.compile(this.spec.marks,this);let s=Object.create(null);for(let o in this.nodes){if(o in this.marks)throw new RangeError(o+" can not be both a node and a mark");let r=this.nodes[o],i=r.spec.content||"",l=r.spec.marks;if(r.contentMatch=s[i]||(s[i]=hs.parse(i,this.nodes)),r.inlineContent=r.contentMatch.inlineContent,r.spec.linebreakReplacement){if(this.linebreakReplacement)throw new RangeError("Multiple linebreak nodes defined");if(!r.isInline||!r.isLeaf)throw new RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=r}r.markSet=l=="_"?null:l?ua(this,l.split(" ")):l==""||!r.inlineContent?[]:null}for(let o in this.marks){let r=this.marks[o],i=r.spec.excludes;r.excluded=i==null?[r]:i==""?[]:ua(this,i.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(e,t=null,s,o){if(typeof e=="string")e=this.nodeType(e);else if(e instanceof ca){if(e.schema!=this)throw new RangeError("Node type from different schema used ("+e.name+")")}else throw new RangeError("Invalid node type: "+e);return e.createChecked(t,s,o)}text(e,t){let s=this.nodes.text;return new ar(s,s.defaultAttrs,e,He.setFrom(t))}mark(e,t){return typeof e=="string"&&(e=this.marks[e]),e.create(t)}nodeFromJSON(e){return Rn.fromJSON(this,e)}markFromJSON(e){return He.fromJSON(this,e)}nodeType(e){let t=this.nodes[e];if(!t)throw new RangeError("Unknown node type: "+e);return t}}function ua(n,e){let t=[];for(let s=0;s<e.length;s++){let o=e[s],r=n.marks[o],i=r;if(r)t.push(r);else for(let l in n.marks){let a=n.marks[l];(o=="_"||a.spec.group&&a.spec.group.split(" ").indexOf(o)>-1)&&t.push(i=a)}if(!i)throw new SyntaxError("Unknown mark type: '"+e[s]+"'")}return t}function Sg(n){return n.tag!=null}function Cg(n){return n.style!=null}class Vn{constructor(e,t){this.schema=e,this.rules=t,this.tags=[],this.styles=[];let s=this.matchedStyles=[];t.forEach(o=>{if(Sg(o))this.tags.push(o);else if(Cg(o)){let r=/[^=]*/.exec(o.style)[0];s.indexOf(r)<0&&s.push(r),this.styles.push(o)}}),this.normalizeLists=!this.tags.some(o=>{if(!/^(ul|ol)\b/.test(o.tag)||!o.node)return!1;let r=e.nodes[o.node];return r.contentMatch.matchType(r)})}parse(e,t={}){let s=new fa(this,t,!1);return s.addAll(e,He.none,t.from,t.to),s.finish()}parseSlice(e,t={}){let s=new fa(this,t,!0);return s.addAll(e,He.none,t.from,t.to),le.maxOpen(s.finish())}matchTag(e,t,s){for(let o=s?this.tags.indexOf(s)+1:0;o<this.tags.length;o++){let r=this.tags[o];if(Tg(e,r.tag)&&(r.namespace===void 0||e.namespaceURI==r.namespace)&&(!r.context||t.matchesContext(r.context))){if(r.getAttrs){let i=r.getAttrs(e);if(i===!1)continue;r.attrs=i||void 0}return r}}}matchStyle(e,t,s,o){for(let r=o?this.styles.indexOf(o)+1:0;r<this.styles.length;r++){let i=this.styles[r],l=i.style;if(!(l.indexOf(e)!=0||i.context&&!s.matchesContext(i.context)||l.length>e.length&&(l.charCodeAt(e.length)!=61||l.slice(e.length+1)!=t))){if(i.getAttrs){let a=i.getAttrs(t);if(a===!1)continue;i.attrs=a||void 0}return i}}}static schemaRules(e){let t=[];function s(o){let r=o.priority==null?50:o.priority,i=0;for(;i<t.length;i++){let l=t[i];if((l.priority==null?50:l.priority)<r)break}t.splice(i,0,o)}for(let o in e.marks){let r=e.marks[o].spec.parseDOM;r&&r.forEach(i=>{s(i=pa(i)),i.mark||i.ignore||i.clearMark||(i.mark=o)})}for(let o in e.nodes){let r=e.nodes[o].spec.parseDOM;r&&r.forEach(i=>{s(i=pa(i)),i.node||i.ignore||i.mark||(i.node=o)})}return t}static fromSchema(e){return e.cached.domParser||(e.cached.domParser=new Vn(e,Vn.schemaRules(e)))}}const tu={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},_g={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},nu={ol:!0,ul:!0},co=1,Di=2,Yo=4;function da(n,e,t){return e!=null?(e?co:0)|(e==="full"?Di:0):n&&n.whitespace=="pre"?co|Di:t&-5}class Wo{constructor(e,t,s,o,r,i){this.type=e,this.attrs=t,this.marks=s,this.solid=o,this.options=i,this.content=[],this.activeMarks=He.none,this.match=r||(i&Yo?null:e.contentMatch)}findWrapping(e){if(!this.match){if(!this.type)return[];let t=this.type.contentMatch.fillBefore(Q.from(e));if(t)this.match=this.type.contentMatch.matchFragment(t);else{let s=this.type.contentMatch,o;return(o=s.findWrapping(e.type))?(this.match=s,o):null}}return this.match.findWrapping(e.type)}finish(e){if(!(this.options&co)){let s=this.content[this.content.length-1],o;if(s&&s.isText&&(o=/[ \t\r\n\u000c]+$/.exec(s.text))){let r=s;s.text.length==o[0].length?this.content.pop():this.content[this.content.length-1]=r.withText(r.text.slice(0,r.text.length-o[0].length))}}let t=Q.from(this.content);return!e&&this.match&&(t=t.append(this.match.fillBefore(Q.empty,!0))),this.type?this.type.create(this.attrs,t,this.marks):t}inlineContext(e){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:e.parentNode&&!tu.hasOwnProperty(e.parentNode.nodeName.toLowerCase())}}class fa{constructor(e,t,s){this.parser=e,this.options=t,this.isOpen=s,this.open=0,this.localPreserveWS=!1;let o=t.topNode,r,i=da(null,t.preserveWhitespace,0)|(s?Yo:0);o?r=new Wo(o.type,o.attrs,He.none,!0,t.topMatch||o.type.contentMatch,i):s?r=new Wo(null,null,He.none,!0,null,i):r=new Wo(e.schema.topNodeType,null,He.none,!0,null,i),this.nodes=[r],this.find=t.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(e,t){e.nodeType==3?this.addTextNode(e,t):e.nodeType==1&&this.addElement(e,t)}addTextNode(e,t){let s=e.nodeValue,o=this.top,r=o.options&Di?"full":this.localPreserveWS||(o.options&co)>0;if(r==="full"||o.inlineContext(e)||/[^ \t\r\n\u000c]/.test(s)){if(r)r!=="full"?s=s.replace(/\r?\n|\r/g," "):s=s.replace(/\r\n?/g,`
`);else if(s=s.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(s)&&this.open==this.nodes.length-1){let i=o.content[o.content.length-1],l=e.previousSibling;(!i||l&&l.nodeName=="BR"||i.isText&&/[ \t\r\n\u000c]$/.test(i.text))&&(s=s.slice(1))}s&&this.insertNode(this.parser.schema.text(s),t),this.findInText(e)}else this.findInside(e)}addElement(e,t,s){let o=this.localPreserveWS,r=this.top;(e.tagName=="PRE"||/pre/.test(e.style&&e.style.whiteSpace))&&(this.localPreserveWS=!0);let i=e.nodeName.toLowerCase(),l;nu.hasOwnProperty(i)&&this.parser.normalizeLists&&Mg(e);let a=this.options.ruleFromNode&&this.options.ruleFromNode(e)||(l=this.parser.matchTag(e,this,s));e:if(a?a.ignore:_g.hasOwnProperty(i))this.findInside(e),this.ignoreFallback(e,t);else if(!a||a.skip||a.closeParent){a&&a.closeParent?this.open=Math.max(0,this.open-1):a&&a.skip.nodeType&&(e=a.skip);let c,u=this.needsBlock;if(tu.hasOwnProperty(i))r.content.length&&r.content[0].isInline&&this.open&&(this.open--,r=this.top),c=!0,r.type||(this.needsBlock=!0);else if(!e.firstChild){this.leafFallback(e,t);break e}let d=a&&a.skip?t:this.readStyles(e,t);d&&this.addAll(e,d),c&&this.sync(r),this.needsBlock=u}else{let c=this.readStyles(e,t);c&&this.addElementByRule(e,a,c,a.consuming===!1?l:void 0)}this.localPreserveWS=o}leafFallback(e,t){e.nodeName=="BR"&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(e.ownerDocument.createTextNode(`
`),t)}ignoreFallback(e,t){e.nodeName=="BR"&&(!this.top.type||!this.top.type.inlineContent)&&this.findPlace(this.parser.schema.text("-"),t)}readStyles(e,t){let s=e.style;if(s&&s.length)for(let o=0;o<this.parser.matchedStyles.length;o++){let r=this.parser.matchedStyles[o],i=s.getPropertyValue(r);if(i)for(let l=void 0;;){let a=this.parser.matchStyle(r,i,this,l);if(!a)break;if(a.ignore)return null;if(a.clearMark?t=t.filter(c=>!a.clearMark(c)):t=t.concat(this.parser.schema.marks[a.mark].create(a.attrs)),a.consuming===!1)l=a;else break}}return t}addElementByRule(e,t,s,o){let r,i;if(t.node)if(i=this.parser.schema.nodes[t.node],i.isLeaf)this.insertNode(i.create(t.attrs),s)||this.leafFallback(e,s);else{let a=this.enter(i,t.attrs||null,s,t.preserveWhitespace);a&&(r=!0,s=a)}else{let a=this.parser.schema.marks[t.mark];s=s.concat(a.create(t.attrs))}let l=this.top;if(i&&i.isLeaf)this.findInside(e);else if(o)this.addElement(e,s,o);else if(t.getContent)this.findInside(e),t.getContent(e,this.parser.schema).forEach(a=>this.insertNode(a,s));else{let a=e;typeof t.contentElement=="string"?a=e.querySelector(t.contentElement):typeof t.contentElement=="function"?a=t.contentElement(e):t.contentElement&&(a=t.contentElement),this.findAround(e,a,!0),this.addAll(a,s),this.findAround(e,a,!1)}r&&this.sync(l)&&this.open--}addAll(e,t,s,o){let r=s||0;for(let i=s?e.childNodes[s]:e.firstChild,l=o==null?null:e.childNodes[o];i!=l;i=i.nextSibling,++r)this.findAtPoint(e,r),this.addDOM(i,t);this.findAtPoint(e,r)}findPlace(e,t){let s,o;for(let r=this.open;r>=0;r--){let i=this.nodes[r],l=i.findWrapping(e);if(l&&(!s||s.length>l.length)&&(s=l,o=i,!l.length)||i.solid)break}if(!s)return null;this.sync(o);for(let r=0;r<s.length;r++)t=this.enterInner(s[r],null,t,!1);return t}insertNode(e,t){if(e.isInline&&this.needsBlock&&!this.top.type){let o=this.textblockFromContext();o&&(t=this.enterInner(o,null,t))}let s=this.findPlace(e,t);if(s){this.closeExtra();let o=this.top;o.match&&(o.match=o.match.matchType(e.type));let r=He.none;for(let i of s.concat(e.marks))(o.type?o.type.allowsMarkType(i.type):ha(i.type,e.type))&&(r=i.addToSet(r));return o.content.push(e.mark(r)),!0}return!1}enter(e,t,s,o){let r=this.findPlace(e.create(t),s);return r&&(r=this.enterInner(e,t,s,!0,o)),r}enterInner(e,t,s,o=!1,r){this.closeExtra();let i=this.top;i.match=i.match&&i.match.matchType(e);let l=da(e,r,i.options);i.options&Yo&&i.content.length==0&&(l|=Yo);let a=He.none;return s=s.filter(c=>(i.type?i.type.allowsMarkType(c.type):ha(c.type,e))?(a=c.addToSet(a),!1):!0),this.nodes.push(new Wo(e,t,a,o,null,l)),this.open++,s}closeExtra(e=!1){let t=this.nodes.length-1;if(t>this.open){for(;t>this.open;t--)this.nodes[t-1].content.push(this.nodes[t].finish(e));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(!!(this.isOpen||this.options.topOpen))}sync(e){for(let t=this.open;t>=0;t--){if(this.nodes[t]==e)return this.open=t,!0;this.localPreserveWS&&(this.nodes[t].options|=co)}return!1}get currentPos(){this.closeExtra();let e=0;for(let t=this.open;t>=0;t--){let s=this.nodes[t].content;for(let o=s.length-1;o>=0;o--)e+=s[o].nodeSize;t&&e++}return e}findAtPoint(e,t){if(this.find)for(let s=0;s<this.find.length;s++)this.find[s].node==e&&this.find[s].offset==t&&(this.find[s].pos=this.currentPos)}findInside(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].pos==null&&e.nodeType==1&&e.contains(this.find[t].node)&&(this.find[t].pos=this.currentPos)}findAround(e,t,s){if(e!=t&&this.find)for(let o=0;o<this.find.length;o++)this.find[o].pos==null&&e.nodeType==1&&e.contains(this.find[o].node)&&t.compareDocumentPosition(this.find[o].node)&(s?2:4)&&(this.find[o].pos=this.currentPos)}findInText(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].node==e&&(this.find[t].pos=this.currentPos-(e.nodeValue.length-this.find[t].offset))}matchesContext(e){if(e.indexOf("|")>-1)return e.split(/\s*\|\s*/).some(this.matchesContext,this);let t=e.split("/"),s=this.options.context,o=!this.isOpen&&(!s||s.parent.type==this.nodes[0].type),r=-(s?s.depth+1:0)+(o?0:1),i=(l,a)=>{for(;l>=0;l--){let c=t[l];if(c==""){if(l==t.length-1||l==0)continue;for(;a>=r;a--)if(i(l-1,a))return!0;return!1}else{let u=a>0||a==0&&o?this.nodes[a].type:s&&a>=r?s.node(a-r).type:null;if(!u||u.name!=c&&!u.isInGroup(c))return!1;a--}}return!0};return i(t.length-1,this.open)}textblockFromContext(){let e=this.options.context;if(e)for(let t=e.depth;t>=0;t--){let s=e.node(t).contentMatchAt(e.indexAfter(t)).defaultType;if(s&&s.isTextblock&&s.defaultAttrs)return s}for(let t in this.parser.schema.nodes){let s=this.parser.schema.nodes[t];if(s.isTextblock&&s.defaultAttrs)return s}}}function Mg(n){for(let e=n.firstChild,t=null;e;e=e.nextSibling){let s=e.nodeType==1?e.nodeName.toLowerCase():null;s&&nu.hasOwnProperty(s)&&t?(t.appendChild(e),e=t):s=="li"?t=e:s&&(t=null)}}function Tg(n,e){return(n.matches||n.msMatchesSelector||n.webkitMatchesSelector||n.mozMatchesSelector).call(n,e)}function pa(n){let e={};for(let t in n)e[t]=n[t];return e}function ha(n,e){let t=e.schema.nodes;for(let s in t){let o=t[s];if(!o.allowsMarkType(n))continue;let r=[],i=l=>{r.push(l);for(let a=0;a<l.edgeCount;a++){let{type:c,next:u}=l.edge(a);if(c==e||r.indexOf(u)<0&&i(u))return!0}};if(i(o.contentMatch))return!0}}class ys{constructor(e,t){this.nodes=e,this.marks=t}serializeFragment(e,t={},s){s||(s=li(t).createDocumentFragment());let o=s,r=[];return e.forEach(i=>{if(r.length||i.marks.length){let l=0,a=0;for(;l<r.length&&a<i.marks.length;){let c=i.marks[a];if(!this.marks[c.type.name]){a++;continue}if(!c.eq(r[l][0])||c.type.spec.spanning===!1)break;l++,a++}for(;l<r.length;)o=r.pop()[1];for(;a<i.marks.length;){let c=i.marks[a++],u=this.serializeMark(c,i.isInline,t);u&&(r.push([c,o]),o.appendChild(u.dom),o=u.contentDOM||u.dom)}}o.appendChild(this.serializeNodeInner(i,t))}),s}serializeNodeInner(e,t){let{dom:s,contentDOM:o}=Go(li(t),this.nodes[e.type.name](e),null,e.attrs);if(o){if(e.isLeaf)throw new RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(e.content,t,o)}return s}serializeNode(e,t={}){let s=this.serializeNodeInner(e,t);for(let o=e.marks.length-1;o>=0;o--){let r=this.serializeMark(e.marks[o],e.isInline,t);r&&((r.contentDOM||r.dom).appendChild(s),s=r.dom)}return s}serializeMark(e,t,s={}){let o=this.marks[e.type.name];return o&&Go(li(s),o(e,t),null,e.attrs)}static renderSpec(e,t,s=null,o){return Go(e,t,s,o)}static fromSchema(e){return e.cached.domSerializer||(e.cached.domSerializer=new ys(this.nodesFromSchema(e),this.marksFromSchema(e)))}static nodesFromSchema(e){let t=ma(e.nodes);return t.text||(t.text=s=>s.text),t}static marksFromSchema(e){return ma(e.marks)}}function ma(n){let e={};for(let t in n){let s=n[t].spec.toDOM;s&&(e[t]=s)}return e}function li(n){return n.document||window.document}const ga=new WeakMap;function Eg(n){let e=ga.get(n);return e===void 0&&ga.set(n,e=Ig(n)),e}function Ig(n){let e=null;function t(s){if(s&&typeof s=="object")if(Array.isArray(s))if(typeof s[0]=="string")e||(e=[]),e.push(s);else for(let o=0;o<s.length;o++)t(s[o]);else for(let o in s)t(s[o])}return t(n),e}function Go(n,e,t,s){if(typeof e=="string")return{dom:n.createTextNode(e)};if(e.nodeType!=null)return{dom:e};if(e.dom&&e.dom.nodeType!=null)return e;let o=e[0],r;if(typeof o!="string")throw new RangeError("Invalid array passed to renderSpec");if(s&&(r=Eg(s))&&r.indexOf(e)>-1)throw new RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let i=o.indexOf(" ");i>0&&(t=o.slice(0,i),o=o.slice(i+1));let l,a=t?n.createElementNS(t,o):n.createElement(o),c=e[1],u=1;if(c&&typeof c=="object"&&c.nodeType==null&&!Array.isArray(c)){u=2;for(let d in c)if(c[d]!=null){let v=d.indexOf(" ");v>0?a.setAttributeNS(d.slice(0,v),d.slice(v+1),c[d]):a.setAttribute(d,c[d])}}for(let d=u;d<e.length;d++){let v=e[d];if(v===0){if(d<e.length-1||d>u)throw new RangeError("Content hole must be the only child of its parent node");return{dom:a,contentDOM:a}}else{let{dom:p,contentDOM:b}=Go(n,v,t,s);if(a.appendChild(p),b){if(l)throw new RangeError("Multiple content holes");l=b}}}return{dom:a,contentDOM:l}}const su=65535,ou=Math.pow(2,16);function Og(n,e){return n+e*ou}function va(n){return n&su}function Ng(n){return(n-(n&su))/ou}const ru=1,iu=2,Qo=4,lu=8;class Ai{constructor(e,t,s){this.pos=e,this.delInfo=t,this.recover=s}get deleted(){return(this.delInfo&lu)>0}get deletedBefore(){return(this.delInfo&(ru|Qo))>0}get deletedAfter(){return(this.delInfo&(iu|Qo))>0}get deletedAcross(){return(this.delInfo&Qo)>0}}class It{constructor(e,t=!1){if(this.ranges=e,this.inverted=t,!e.length&&It.empty)return It.empty}recover(e){let t=0,s=va(e);if(!this.inverted)for(let o=0;o<s;o++)t+=this.ranges[o*3+2]-this.ranges[o*3+1];return this.ranges[s*3]+t+Ng(e)}mapResult(e,t=1){return this._map(e,t,!1)}map(e,t=1){return this._map(e,t,!0)}_map(e,t,s){let o=0,r=this.inverted?2:1,i=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?o:0);if(a>e)break;let c=this.ranges[l+r],u=this.ranges[l+i],d=a+c;if(e<=d){let v=c?e==a?-1:e==d?1:t:t,p=a+o+(v<0?0:u);if(s)return p;let b=e==(t<0?a:d)?null:Og(l/3,e-a),w=e==a?iu:e==d?ru:Qo;return(t<0?e!=a:e!=d)&&(w|=lu),new Ai(p,w,b)}o+=u-c}return s?e+o:new Ai(e+o,0,null)}touches(e,t){let s=0,o=va(t),r=this.inverted?2:1,i=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?s:0);if(a>e)break;let c=this.ranges[l+r],u=a+c;if(e<=u&&l==o*3)return!0;s+=this.ranges[l+i]-c}return!1}forEach(e){let t=this.inverted?2:1,s=this.inverted?1:2;for(let o=0,r=0;o<this.ranges.length;o+=3){let i=this.ranges[o],l=i-(this.inverted?r:0),a=i+(this.inverted?0:r),c=this.ranges[o+t],u=this.ranges[o+s];e(l,l+c,a,a+u),r+=u-c}}invert(){return new It(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(e){return e==0?It.empty:new It(e<0?[0,-e,0]:[0,0,e])}}It.empty=new It([]);class uo{constructor(e,t,s=0,o=e?e.length:0){this.mirror=t,this.from=s,this.to=o,this._maps=e||[],this.ownData=!(e||t)}get maps(){return this._maps}slice(e=0,t=this.maps.length){return new uo(this._maps,this.mirror,e,t)}appendMap(e,t){this.ownData||(this._maps=this._maps.slice(),this.mirror=this.mirror&&this.mirror.slice(),this.ownData=!0),this.to=this._maps.push(e),t!=null&&this.setMirror(this._maps.length-1,t)}appendMapping(e){for(let t=0,s=this._maps.length;t<e._maps.length;t++){let o=e.getMirror(t);this.appendMap(e._maps[t],o!=null&&o<t?s+o:void 0)}}getMirror(e){if(this.mirror){for(let t=0;t<this.mirror.length;t++)if(this.mirror[t]==e)return this.mirror[t+(t%2?-1:1)]}}setMirror(e,t){this.mirror||(this.mirror=[]),this.mirror.push(e,t)}appendMappingInverted(e){for(let t=e.maps.length-1,s=this._maps.length+e._maps.length;t>=0;t--){let o=e.getMirror(t);this.appendMap(e._maps[t].invert(),o!=null&&o>t?s-o-1:void 0)}}invert(){let e=new uo;return e.appendMappingInverted(this),e}map(e,t=1){if(this.mirror)return this._map(e,t,!0);for(let s=this.from;s<this.to;s++)e=this._maps[s].map(e,t);return e}mapResult(e,t=1){return this._map(e,t,!1)}_map(e,t,s){let o=0;for(let r=this.from;r<this.to;r++){let i=this._maps[r],l=i.mapResult(e,t);if(l.recover!=null){let a=this.getMirror(r);if(a!=null&&a>r&&a<this.to){r=a,e=this._maps[a].recover(l.recover);continue}}o|=l.delInfo,e=l.pos}return s?e:new Ai(e,o,null)}}const ai=Object.create(null);class dt{getMap(){return It.empty}merge(e){return null}static fromJSON(e,t){if(!t||!t.stepType)throw new RangeError("Invalid input for Step.fromJSON");let s=ai[t.stepType];if(!s)throw new RangeError(`No step type ${t.stepType} defined`);return s.fromJSON(e,t)}static jsonID(e,t){if(e in ai)throw new RangeError("Duplicate use of step JSON ID "+e);return ai[e]=t,t.prototype.jsonID=e,t}}class je{constructor(e,t){this.doc=e,this.failed=t}static ok(e){return new je(e,null)}static fail(e){return new je(null,e)}static fromReplace(e,t,s,o){try{return je.ok(e.replace(t,s,o))}catch(r){if(r instanceof rr)return je.fail(r.message);throw r}}}function ol(n,e,t){let s=[];for(let o=0;o<n.childCount;o++){let r=n.child(o);r.content.size&&(r=r.copy(ol(r.content,e,r))),r.isInline&&(r=e(r,t,o)),s.push(r)}return Q.fromArray(s)}class An extends dt{constructor(e,t,s){super(),this.from=e,this.to=t,this.mark=s}apply(e){let t=e.slice(this.from,this.to),s=e.resolve(this.from),o=s.node(s.sharedDepth(this.to)),r=new le(ol(t.content,(i,l)=>!i.isAtom||!l.type.allowsMarkType(this.mark.type)?i:i.mark(this.mark.addToSet(i.marks)),o),t.openStart,t.openEnd);return je.fromReplace(e,this.from,this.to,r)}invert(){return new rn(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),s=e.mapResult(this.to,-1);return t.deleted&&s.deleted||t.pos>=s.pos?null:new An(t.pos,s.pos,this.mark)}merge(e){return e instanceof An&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new An(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for AddMarkStep.fromJSON");return new An(t.from,t.to,e.markFromJSON(t.mark))}}dt.jsonID("addMark",An);class rn extends dt{constructor(e,t,s){super(),this.from=e,this.to=t,this.mark=s}apply(e){let t=e.slice(this.from,this.to),s=new le(ol(t.content,o=>o.mark(this.mark.removeFromSet(o.marks)),e),t.openStart,t.openEnd);return je.fromReplace(e,this.from,this.to,s)}invert(){return new An(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),s=e.mapResult(this.to,-1);return t.deleted&&s.deleted||t.pos>=s.pos?null:new rn(t.pos,s.pos,this.mark)}merge(e){return e instanceof rn&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new rn(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for RemoveMarkStep.fromJSON");return new rn(t.from,t.to,e.markFromJSON(t.mark))}}dt.jsonID("removeMark",rn);class $n extends dt{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return je.fail("No node at mark step's position");let s=t.type.create(t.attrs,null,this.mark.addToSet(t.marks));return je.fromReplace(e,this.pos,this.pos+1,new le(Q.from(s),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);if(t){let s=this.mark.addToSet(t.marks);if(s.length==t.marks.length){for(let o=0;o<t.marks.length;o++)if(!t.marks[o].isInSet(s))return new $n(this.pos,t.marks[o]);return new $n(this.pos,this.mark)}}return new Ls(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new $n(t.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new $n(t.pos,e.markFromJSON(t.mark))}}dt.jsonID("addNodeMark",$n);class Ls extends dt{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return je.fail("No node at mark step's position");let s=t.type.create(t.attrs,null,this.mark.removeFromSet(t.marks));return je.fromReplace(e,this.pos,this.pos+1,new le(Q.from(s),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);return!t||!this.mark.isInSet(t.marks)?this:new $n(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new Ls(t.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new Ls(t.pos,e.markFromJSON(t.mark))}}dt.jsonID("removeNodeMark",Ls);class Ke extends dt{constructor(e,t,s,o=!1){super(),this.from=e,this.to=t,this.slice=s,this.structure=o}apply(e){return this.structure&&$i(e,this.from,this.to)?je.fail("Structure replace would overwrite content"):je.fromReplace(e,this.from,this.to,this.slice)}getMap(){return new It([this.from,this.to-this.from,this.slice.size])}invert(e){return new Ke(this.from,this.from+this.slice.size,e.slice(this.from,this.to))}map(e){let t=e.mapResult(this.from,1),s=e.mapResult(this.to,-1);return t.deletedAcross&&s.deletedAcross?null:new Ke(t.pos,Math.max(t.pos,s.pos),this.slice)}merge(e){if(!(e instanceof Ke)||e.structure||this.structure)return null;if(this.from+this.slice.size==e.from&&!this.slice.openEnd&&!e.slice.openStart){let t=this.slice.size+e.slice.size==0?le.empty:new le(this.slice.content.append(e.slice.content),this.slice.openStart,e.slice.openEnd);return new Ke(this.from,this.to+(e.to-e.from),t,this.structure)}else if(e.to==this.from&&!this.slice.openStart&&!e.slice.openEnd){let t=this.slice.size+e.slice.size==0?le.empty:new le(e.slice.content.append(this.slice.content),e.slice.openStart,this.slice.openEnd);return new Ke(e.from,this.to,t,this.structure)}else return null}toJSON(){let e={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for ReplaceStep.fromJSON");return new Ke(t.from,t.to,le.fromJSON(e,t.slice),!!t.structure)}}dt.jsonID("replace",Ke);class Xe extends dt{constructor(e,t,s,o,r,i,l=!1){super(),this.from=e,this.to=t,this.gapFrom=s,this.gapTo=o,this.slice=r,this.insert=i,this.structure=l}apply(e){if(this.structure&&($i(e,this.from,this.gapFrom)||$i(e,this.gapTo,this.to)))return je.fail("Structure gap-replace would overwrite content");let t=e.slice(this.gapFrom,this.gapTo);if(t.openStart||t.openEnd)return je.fail("Gap is not a flat range");let s=this.slice.insertAt(this.insert,t.content);return s?je.fromReplace(e,this.from,this.to,s):je.fail("Content does not fit in gap")}getMap(){return new It([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(e){let t=this.gapTo-this.gapFrom;return new Xe(this.from,this.from+this.slice.size+t,this.from+this.insert,this.from+this.insert+t,e.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(e){let t=e.mapResult(this.from,1),s=e.mapResult(this.to,-1),o=this.from==this.gapFrom?t.pos:e.map(this.gapFrom,-1),r=this.to==this.gapTo?s.pos:e.map(this.gapTo,1);return t.deletedAcross&&s.deletedAcross||o<t.pos||r>s.pos?null:new Xe(t.pos,s.pos,o,r,this.slice,this.insert,this.structure)}toJSON(){let e={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number"||typeof t.gapFrom!="number"||typeof t.gapTo!="number"||typeof t.insert!="number")throw new RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new Xe(t.from,t.to,t.gapFrom,t.gapTo,le.fromJSON(e,t.slice),t.insert,!!t.structure)}}dt.jsonID("replaceAround",Xe);function $i(n,e,t){let s=n.resolve(e),o=t-e,r=s.depth;for(;o>0&&r>0&&s.indexAfter(r)==s.node(r).childCount;)r--,o--;if(o>0){let i=s.node(r).maybeChild(s.indexAfter(r));for(;o>0;){if(!i||i.isLeaf)return!0;i=i.firstChild,o--}}return!1}function Dg(n,e,t,s){let o=[],r=[],i,l;n.doc.nodesBetween(e,t,(a,c,u)=>{if(!a.isInline)return;let d=a.marks;if(!s.isInSet(d)&&u.type.allowsMarkType(s.type)){let v=Math.max(c,e),p=Math.min(c+a.nodeSize,t),b=s.addToSet(d);for(let w=0;w<d.length;w++)d[w].isInSet(b)||(i&&i.to==v&&i.mark.eq(d[w])?i.to=p:o.push(i=new rn(v,p,d[w])));l&&l.to==v?l.to=p:r.push(l=new An(v,p,s))}}),o.forEach(a=>n.step(a)),r.forEach(a=>n.step(a))}function Ag(n,e,t,s){let o=[],r=0;n.doc.nodesBetween(e,t,(i,l)=>{if(!i.isInline)return;r++;let a=null;if(s instanceof _r){let c=i.marks,u;for(;u=s.isInSet(c);)(a||(a=[])).push(u),c=u.removeFromSet(c)}else s?s.isInSet(i.marks)&&(a=[s]):a=i.marks;if(a&&a.length){let c=Math.min(l+i.nodeSize,t);for(let u=0;u<a.length;u++){let d=a[u],v;for(let p=0;p<o.length;p++){let b=o[p];b.step==r-1&&d.eq(o[p].style)&&(v=b)}v?(v.to=c,v.step=r):o.push({style:d,from:Math.max(l,e),to:c,step:r})}}}),o.forEach(i=>n.step(new rn(i.from,i.to,i.style)))}function rl(n,e,t,s=t.contentMatch,o=!0){let r=n.doc.nodeAt(e),i=[],l=e+1;for(let a=0;a<r.childCount;a++){let c=r.child(a),u=l+c.nodeSize,d=s.matchType(c.type);if(!d)i.push(new Ke(l,u,le.empty));else{s=d;for(let v=0;v<c.marks.length;v++)t.allowsMarkType(c.marks[v].type)||n.step(new rn(l,u,c.marks[v]));if(o&&c.isText&&t.whitespace!="pre"){let v,p=/\r?\n|\r/g,b;for(;v=p.exec(c.text);)b||(b=new le(Q.from(t.schema.text(" ",t.allowedMarks(c.marks))),0,0)),i.push(new Ke(l+v.index,l+v.index+v[0].length,b))}}l=u}if(!s.validEnd){let a=s.fillBefore(Q.empty,!0);n.replace(l,l,new le(a,0,0))}for(let a=i.length-1;a>=0;a--)n.step(i[a])}function $g(n,e,t){return(e==0||n.canReplace(e,n.childCount))&&(t==n.childCount||n.canReplace(0,t))}function Js(n){let t=n.parent.content.cutByIndex(n.startIndex,n.endIndex);for(let s=n.depth;;--s){let o=n.$from.node(s),r=n.$from.index(s),i=n.$to.indexAfter(s);if(s<n.depth&&o.canReplace(r,i,t))return s;if(s==0||o.type.spec.isolating||!$g(o,r,i))break}return null}function Pg(n,e,t){let{$from:s,$to:o,depth:r}=e,i=s.before(r+1),l=o.after(r+1),a=i,c=l,u=Q.empty,d=0;for(let b=r,w=!1;b>t;b--)w||s.index(b)>0?(w=!0,u=Q.from(s.node(b).copy(u)),d++):a--;let v=Q.empty,p=0;for(let b=r,w=!1;b>t;b--)w||o.after(b+1)<o.end(b)?(w=!0,v=Q.from(o.node(b).copy(v)),p++):c++;n.step(new Xe(a,c,i,l,new le(u.append(v),d,p),u.size-d,!0))}function il(n,e,t=null,s=n){let o=Rg(n,e),r=o&&Vg(s,e);return r?o.map(ya).concat({type:e,attrs:t}).concat(r.map(ya)):null}function ya(n){return{type:n,attrs:null}}function Rg(n,e){let{parent:t,startIndex:s,endIndex:o}=n,r=t.contentMatchAt(s).findWrapping(e);if(!r)return null;let i=r.length?r[0]:e;return t.canReplaceWith(s,o,i)?r:null}function Vg(n,e){let{parent:t,startIndex:s,endIndex:o}=n,r=t.child(s),i=e.contentMatch.findWrapping(r.type);if(!i)return null;let a=(i.length?i[i.length-1]:e).contentMatch;for(let c=s;a&&c<o;c++)a=a.matchType(t.child(c).type);return!a||!a.validEnd?null:i}function Lg(n,e,t){let s=Q.empty;for(let i=t.length-1;i>=0;i--){if(s.size){let l=t[i].type.contentMatch.matchFragment(s);if(!l||!l.validEnd)throw new RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}s=Q.from(t[i].type.create(t[i].attrs,s))}let o=e.start,r=e.end;n.step(new Xe(o,r,o,r,new le(s,0,0),t.length,!0))}function Bg(n,e,t,s,o){if(!s.isTextblock)throw new RangeError("Type given to setBlockType should be a textblock");let r=n.steps.length;n.doc.nodesBetween(e,t,(i,l)=>{let a=typeof o=="function"?o(i):o;if(i.isTextblock&&!i.hasMarkup(s,a)&&zg(n.doc,n.mapping.slice(r).map(l),s)){let c=null;if(s.schema.linebreakReplacement){let p=s.whitespace=="pre",b=!!s.contentMatch.matchType(s.schema.linebreakReplacement);p&&!b?c=!1:!p&&b&&(c=!0)}c===!1&&cu(n,i,l,r),rl(n,n.mapping.slice(r).map(l,1),s,void 0,c===null);let u=n.mapping.slice(r),d=u.map(l,1),v=u.map(l+i.nodeSize,1);return n.step(new Xe(d,v,d+1,v-1,new le(Q.from(s.create(a,null,i.marks)),0,0),1,!0)),c===!0&&au(n,i,l,r),!1}})}function au(n,e,t,s){e.forEach((o,r)=>{if(o.isText){let i,l=/\r?\n|\r/g;for(;i=l.exec(o.text);){let a=n.mapping.slice(s).map(t+1+r+i.index);n.replaceWith(a,a+1,e.type.schema.linebreakReplacement.create())}}})}function cu(n,e,t,s){e.forEach((o,r)=>{if(o.type==o.type.schema.linebreakReplacement){let i=n.mapping.slice(s).map(t+1+r);n.replaceWith(i,i+1,e.type.schema.text(`
`))}})}function zg(n,e,t){let s=n.resolve(e),o=s.index();return s.parent.canReplaceWith(o,o+1,t)}function Fg(n,e,t,s,o){let r=n.doc.nodeAt(e);if(!r)throw new RangeError("No node at given position");t||(t=r.type);let i=t.create(s,null,o||r.marks);if(r.isLeaf)return n.replaceWith(e,e+r.nodeSize,i);if(!t.validContent(r.content))throw new RangeError("Invalid content for node type "+t.name);n.step(new Xe(e,e+r.nodeSize,e+1,e+r.nodeSize-1,new le(Q.from(i),0,0),1,!0))}function gn(n,e,t=1,s){let o=n.resolve(e),r=o.depth-t,i=s&&s[s.length-1]||o.parent;if(r<0||o.parent.type.spec.isolating||!o.parent.canReplace(o.index(),o.parent.childCount)||!i.type.validContent(o.parent.content.cutByIndex(o.index(),o.parent.childCount)))return!1;for(let c=o.depth-1,u=t-2;c>r;c--,u--){let d=o.node(c),v=o.index(c);if(d.type.spec.isolating)return!1;let p=d.content.cutByIndex(v,d.childCount),b=s&&s[u+1];b&&(p=p.replaceChild(0,b.type.create(b.attrs)));let w=s&&s[u]||d;if(!d.canReplace(v+1,d.childCount)||!w.type.validContent(p))return!1}let l=o.indexAfter(r),a=s&&s[0];return o.node(r).canReplaceWith(l,l,a?a.type:o.node(r+1).type)}function Wg(n,e,t=1,s){let o=n.doc.resolve(e),r=Q.empty,i=Q.empty;for(let l=o.depth,a=o.depth-t,c=t-1;l>a;l--,c--){r=Q.from(o.node(l).copy(r));let u=s&&s[c];i=Q.from(u?u.type.create(u.attrs,i):o.node(l).copy(i))}n.step(new Ke(e,e,new le(r.append(i),t,t),!0))}function qn(n,e){let t=n.resolve(e),s=t.index();return uu(t.nodeBefore,t.nodeAfter)&&t.parent.canReplace(s,s+1)}function Hg(n,e){e.content.size||n.type.compatibleContent(e.type);let t=n.contentMatchAt(n.childCount),{linebreakReplacement:s}=n.type.schema;for(let o=0;o<e.childCount;o++){let r=e.child(o),i=r.type==s?n.type.schema.nodes.text:r.type;if(t=t.matchType(i),!t||!n.type.allowsMarks(r.marks))return!1}return t.validEnd}function uu(n,e){return!!(n&&e&&!n.isLeaf&&Hg(n,e))}function Mr(n,e,t=-1){let s=n.resolve(e);for(let o=s.depth;;o--){let r,i,l=s.index(o);if(o==s.depth?(r=s.nodeBefore,i=s.nodeAfter):t>0?(r=s.node(o+1),l++,i=s.node(o).maybeChild(l)):(r=s.node(o).maybeChild(l-1),i=s.node(o+1)),r&&!r.isTextblock&&uu(r,i)&&s.node(o).canReplace(l,l+1))return e;if(o==0)break;e=t<0?s.before(o):s.after(o)}}function qg(n,e,t){let s=null,{linebreakReplacement:o}=n.doc.type.schema,r=n.doc.resolve(e-t),i=r.node().type;if(o&&i.inlineContent){let u=i.whitespace=="pre",d=!!i.contentMatch.matchType(o);u&&!d?s=!1:!u&&d&&(s=!0)}let l=n.steps.length;if(s===!1){let u=n.doc.resolve(e+t);cu(n,u.node(),u.before(),l)}i.inlineContent&&rl(n,e+t-1,i,r.node().contentMatchAt(r.index()),s==null);let a=n.mapping.slice(l),c=a.map(e-t);if(n.step(new Ke(c,a.map(e+t,-1),le.empty,!0)),s===!0){let u=n.doc.resolve(c);au(n,u.node(),u.before(),n.steps.length)}return n}function Jg(n,e,t){let s=n.resolve(e);if(s.parent.canReplaceWith(s.index(),s.index(),t))return e;if(s.parentOffset==0)for(let o=s.depth-1;o>=0;o--){let r=s.index(o);if(s.node(o).canReplaceWith(r,r,t))return s.before(o+1);if(r>0)return null}if(s.parentOffset==s.parent.content.size)for(let o=s.depth-1;o>=0;o--){let r=s.indexAfter(o);if(s.node(o).canReplaceWith(r,r,t))return s.after(o+1);if(r<s.node(o).childCount)return null}return null}function du(n,e,t){let s=n.resolve(e);if(!t.content.size)return e;let o=t.content;for(let r=0;r<t.openStart;r++)o=o.firstChild.content;for(let r=1;r<=(t.openStart==0&&t.size?2:1);r++)for(let i=s.depth;i>=0;i--){let l=i==s.depth?0:s.pos<=(s.start(i+1)+s.end(i+1))/2?-1:1,a=s.index(i)+(l>0?1:0),c=s.node(i),u=!1;if(r==1)u=c.canReplace(a,a,o);else{let d=c.contentMatchAt(a).findWrapping(o.firstChild.type);u=d&&c.canReplaceWith(a,a,d[0])}if(u)return l==0?s.pos:l<0?s.before(i+1):s.after(i+1)}return null}function Tr(n,e,t=e,s=le.empty){if(e==t&&!s.size)return null;let o=n.resolve(e),r=n.resolve(t);return fu(o,r,s)?new Ke(e,t,s):new Ug(o,r,s).fit()}function fu(n,e,t){return!t.openStart&&!t.openEnd&&n.start()==e.start()&&n.parent.canReplace(n.index(),e.index(),t.content)}class Ug{constructor(e,t,s){this.$from=e,this.$to=t,this.unplaced=s,this.frontier=[],this.placed=Q.empty;for(let o=0;o<=e.depth;o++){let r=e.node(o);this.frontier.push({type:r.type,match:r.contentMatchAt(e.indexAfter(o))})}for(let o=e.depth;o>0;o--)this.placed=Q.from(e.node(o).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let c=this.findFittable();c?this.placeNodes(c):this.openMore()||this.dropNode()}let e=this.mustMoveInline(),t=this.placed.size-this.depth-this.$from.depth,s=this.$from,o=this.close(e<0?this.$to:s.doc.resolve(e));if(!o)return null;let r=this.placed,i=s.depth,l=o.depth;for(;i&&l&&r.childCount==1;)r=r.firstChild.content,i--,l--;let a=new le(r,i,l);return e>-1?new Xe(s.pos,e,this.$to.pos,this.$to.end(),a,t):a.size||s.pos!=this.$to.pos?new Ke(s.pos,o.pos,a):null}findFittable(){let e=this.unplaced.openStart;for(let t=this.unplaced.content,s=0,o=this.unplaced.openEnd;s<e;s++){let r=t.firstChild;if(t.childCount>1&&(o=0),r.type.spec.isolating&&o<=s){e=s;break}t=r.content}for(let t=1;t<=2;t++)for(let s=t==1?e:this.unplaced.openStart;s>=0;s--){let o,r=null;s?(r=ci(this.unplaced.content,s-1).firstChild,o=r.content):o=this.unplaced.content;let i=o.firstChild;for(let l=this.depth;l>=0;l--){let{type:a,match:c}=this.frontier[l],u,d=null;if(t==1&&(i?c.matchType(i.type)||(d=c.fillBefore(Q.from(i),!1)):r&&a.compatibleContent(r.type)))return{sliceDepth:s,frontierDepth:l,parent:r,inject:d};if(t==2&&i&&(u=c.findWrapping(i.type)))return{sliceDepth:s,frontierDepth:l,parent:r,wrap:u};if(r&&c.matchType(r.type))break}}}openMore(){let{content:e,openStart:t,openEnd:s}=this.unplaced,o=ci(e,t);return!o.childCount||o.firstChild.isLeaf?!1:(this.unplaced=new le(e,t+1,Math.max(s,o.size+t>=e.size-s?t+1:0)),!0)}dropNode(){let{content:e,openStart:t,openEnd:s}=this.unplaced,o=ci(e,t);if(o.childCount<=1&&t>0){let r=e.size-t<=t+o.size;this.unplaced=new le(no(e,t-1,1),t-1,r?t-1:s)}else this.unplaced=new le(no(e,t,1),t,s)}placeNodes({sliceDepth:e,frontierDepth:t,parent:s,inject:o,wrap:r}){for(;this.depth>t;)this.closeFrontierNode();if(r)for(let w=0;w<r.length;w++)this.openFrontierNode(r[w]);let i=this.unplaced,l=s?s.content:i.content,a=i.openStart-e,c=0,u=[],{match:d,type:v}=this.frontier[t];if(o){for(let w=0;w<o.childCount;w++)u.push(o.child(w));d=d.matchFragment(o)}let p=l.size+e-(i.content.size-i.openEnd);for(;c<l.childCount;){let w=l.child(c),C=d.matchType(w.type);if(!C)break;c++,(c>1||a==0||w.content.size)&&(d=C,u.push(pu(w.mark(v.allowedMarks(w.marks)),c==1?a:0,c==l.childCount?p:-1)))}let b=c==l.childCount;b||(p=-1),this.placed=so(this.placed,t,Q.from(u)),this.frontier[t].match=d,b&&p<0&&s&&s.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let w=0,C=l;w<p;w++){let M=C.lastChild;this.frontier.push({type:M.type,match:M.contentMatchAt(M.childCount)}),C=M.content}this.unplaced=b?e==0?le.empty:new le(no(i.content,e-1,1),e-1,p<0?i.openEnd:e-1):new le(no(i.content,e,c),i.openStart,i.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return-1;let e=this.frontier[this.depth],t;if(!e.type.isTextblock||!ui(this.$to,this.$to.depth,e.type,e.match,!1)||this.$to.depth==this.depth&&(t=this.findCloseLevel(this.$to))&&t.depth==this.depth)return-1;let{depth:s}=this.$to,o=this.$to.after(s);for(;s>1&&o==this.$to.end(--s);)++o;return o}findCloseLevel(e){e:for(let t=Math.min(this.depth,e.depth);t>=0;t--){let{match:s,type:o}=this.frontier[t],r=t<e.depth&&e.end(t+1)==e.pos+(e.depth-(t+1)),i=ui(e,t,o,s,r);if(i){for(let l=t-1;l>=0;l--){let{match:a,type:c}=this.frontier[l],u=ui(e,l,c,a,!0);if(!u||u.childCount)continue e}return{depth:t,fit:i,move:r?e.doc.resolve(e.after(t+1)):e}}}}close(e){let t=this.findCloseLevel(e);if(!t)return null;for(;this.depth>t.depth;)this.closeFrontierNode();t.fit.childCount&&(this.placed=so(this.placed,t.depth,t.fit)),e=t.move;for(let s=t.depth+1;s<=e.depth;s++){let o=e.node(s),r=o.type.contentMatch.fillBefore(o.content,!0,e.index(s));this.openFrontierNode(o.type,o.attrs,r)}return e}openFrontierNode(e,t=null,s){let o=this.frontier[this.depth];o.match=o.match.matchType(e),this.placed=so(this.placed,this.depth,Q.from(e.create(t,s))),this.frontier.push({type:e,match:e.contentMatch})}closeFrontierNode(){let t=this.frontier.pop().match.fillBefore(Q.empty,!0);t.childCount&&(this.placed=so(this.placed,this.frontier.length,t))}}function no(n,e,t){return e==0?n.cutByIndex(t,n.childCount):n.replaceChild(0,n.firstChild.copy(no(n.firstChild.content,e-1,t)))}function so(n,e,t){return e==0?n.append(t):n.replaceChild(n.childCount-1,n.lastChild.copy(so(n.lastChild.content,e-1,t)))}function ci(n,e){for(let t=0;t<e;t++)n=n.firstChild.content;return n}function pu(n,e,t){if(e<=0)return n;let s=n.content;return e>1&&(s=s.replaceChild(0,pu(s.firstChild,e-1,s.childCount==1?t-1:0))),e>0&&(s=n.type.contentMatch.fillBefore(s).append(s),t<=0&&(s=s.append(n.type.contentMatch.matchFragment(s).fillBefore(Q.empty,!0)))),n.copy(s)}function ui(n,e,t,s,o){let r=n.node(e),i=o?n.indexAfter(e):n.index(e);if(i==r.childCount&&!t.compatibleContent(r.type))return null;let l=s.fillBefore(r.content,!0,i);return l&&!jg(t,r.content,i)?l:null}function jg(n,e,t){for(let s=t;s<e.childCount;s++)if(!n.allowsMarks(e.child(s).marks))return!0;return!1}function Kg(n){return n.spec.defining||n.spec.definingForContent}function Xg(n,e,t,s){if(!s.size)return n.deleteRange(e,t);let o=n.doc.resolve(e),r=n.doc.resolve(t);if(fu(o,r,s))return n.step(new Ke(e,t,s));let i=mu(o,n.doc.resolve(t));i[i.length-1]==0&&i.pop();let l=-(o.depth+1);i.unshift(l);for(let v=o.depth,p=o.pos-1;v>0;v--,p--){let b=o.node(v).type.spec;if(b.defining||b.definingAsContext||b.isolating)break;i.indexOf(v)>-1?l=v:o.before(v)==p&&i.splice(1,0,-v)}let a=i.indexOf(l),c=[],u=s.openStart;for(let v=s.content,p=0;;p++){let b=v.firstChild;if(c.push(b),p==s.openStart)break;v=b.content}for(let v=u-1;v>=0;v--){let p=c[v],b=Kg(p.type);if(b&&!p.sameMarkup(o.node(Math.abs(l)-1)))u=v;else if(b||!p.type.isTextblock)break}for(let v=s.openStart;v>=0;v--){let p=(v+u+1)%(s.openStart+1),b=c[p];if(b)for(let w=0;w<i.length;w++){let C=i[(w+a)%i.length],M=!0;C<0&&(M=!1,C=-C);let X=o.node(C-1),L=o.index(C-1);if(X.canReplaceWith(L,L,b.type,b.marks))return n.replace(o.before(C),M?r.after(C):t,new le(hu(s.content,0,s.openStart,p),p,s.openEnd))}}let d=n.steps.length;for(let v=i.length-1;v>=0&&(n.replace(e,t,s),!(n.steps.length>d));v--){let p=i[v];p<0||(e=o.before(p),t=r.after(p))}}function hu(n,e,t,s,o){if(e<t){let r=n.firstChild;n=n.replaceChild(0,r.copy(hu(r.content,e+1,t,s,r)))}if(e>s){let r=o.contentMatchAt(0),i=r.fillBefore(n).append(n);n=i.append(r.matchFragment(i).fillBefore(Q.empty,!0))}return n}function Yg(n,e,t,s){if(!s.isInline&&e==t&&n.doc.resolve(e).parent.content.size){let o=Jg(n.doc,e,s.type);o!=null&&(e=t=o)}n.replaceRange(e,t,new le(Q.from(s),0,0))}function Gg(n,e,t){let s=n.doc.resolve(e),o=n.doc.resolve(t),r=mu(s,o);for(let i=0;i<r.length;i++){let l=r[i],a=i==r.length-1;if(a&&l==0||s.node(l).type.contentMatch.validEnd)return n.delete(s.start(l),o.end(l));if(l>0&&(a||s.node(l-1).canReplace(s.index(l-1),o.indexAfter(l-1))))return n.delete(s.before(l),o.after(l))}for(let i=1;i<=s.depth&&i<=o.depth;i++)if(e-s.start(i)==s.depth-i&&t>s.end(i)&&o.end(i)-t!=o.depth-i&&s.start(i-1)==o.start(i-1)&&s.node(i-1).canReplace(s.index(i-1),o.index(i-1)))return n.delete(s.before(i),t);n.delete(e,t)}function mu(n,e){let t=[],s=Math.min(n.depth,e.depth);for(let o=s;o>=0;o--){let r=n.start(o);if(r<n.pos-(n.depth-o)||e.end(o)>e.pos+(e.depth-o)||n.node(o).type.spec.isolating||e.node(o).type.spec.isolating)break;(r==e.start(o)||o==n.depth&&o==e.depth&&n.parent.inlineContent&&e.parent.inlineContent&&o&&e.start(o-1)==r-1)&&t.push(o)}return t}class $s extends dt{constructor(e,t,s){super(),this.pos=e,this.attr=t,this.value=s}apply(e){let t=e.nodeAt(this.pos);if(!t)return je.fail("No node at attribute step's position");let s=Object.create(null);for(let r in t.attrs)s[r]=t.attrs[r];s[this.attr]=this.value;let o=t.type.create(s,null,t.marks);return je.fromReplace(e,this.pos,this.pos+1,new le(Q.from(o),0,t.isLeaf?0:1))}getMap(){return It.empty}invert(e){return new $s(this.pos,this.attr,e.nodeAt(this.pos).attrs[this.attr])}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new $s(t.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(e,t){if(typeof t.pos!="number"||typeof t.attr!="string")throw new RangeError("Invalid input for AttrStep.fromJSON");return new $s(t.pos,t.attr,t.value)}}dt.jsonID("attr",$s);class fo extends dt{constructor(e,t){super(),this.attr=e,this.value=t}apply(e){let t=Object.create(null);for(let o in e.attrs)t[o]=e.attrs[o];t[this.attr]=this.value;let s=e.type.create(t,e.content,e.marks);return je.ok(s)}getMap(){return It.empty}invert(e){return new fo(this.attr,e.attrs[this.attr])}map(e){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(e,t){if(typeof t.attr!="string")throw new RangeError("Invalid input for DocAttrStep.fromJSON");return new fo(t.attr,t.value)}}dt.jsonID("docAttr",fo);let Bs=class extends Error{};Bs=function n(e){let t=Error.call(this,e);return t.__proto__=n.prototype,t};Bs.prototype=Object.create(Error.prototype);Bs.prototype.constructor=Bs;Bs.prototype.name="TransformError";class Qg{constructor(e){this.doc=e,this.steps=[],this.docs=[],this.mapping=new uo}get before(){return this.docs.length?this.docs[0]:this.doc}step(e){let t=this.maybeStep(e);if(t.failed)throw new Bs(t.failed);return this}maybeStep(e){let t=e.apply(this.doc);return t.failed||this.addStep(e,t.doc),t}get docChanged(){return this.steps.length>0}addStep(e,t){this.docs.push(this.doc),this.steps.push(e),this.mapping.appendMap(e.getMap()),this.doc=t}replace(e,t=e,s=le.empty){let o=Tr(this.doc,e,t,s);return o&&this.step(o),this}replaceWith(e,t,s){return this.replace(e,t,new le(Q.from(s),0,0))}delete(e,t){return this.replace(e,t,le.empty)}insert(e,t){return this.replaceWith(e,e,t)}replaceRange(e,t,s){return Xg(this,e,t,s),this}replaceRangeWith(e,t,s){return Yg(this,e,t,s),this}deleteRange(e,t){return Gg(this,e,t),this}lift(e,t){return Pg(this,e,t),this}join(e,t=1){return qg(this,e,t),this}wrap(e,t){return Lg(this,e,t),this}setBlockType(e,t=e,s,o=null){return Bg(this,e,t,s,o),this}setNodeMarkup(e,t,s=null,o){return Fg(this,e,t,s,o),this}setNodeAttribute(e,t,s){return this.step(new $s(e,t,s)),this}setDocAttribute(e,t){return this.step(new fo(e,t)),this}addNodeMark(e,t){return this.step(new $n(e,t)),this}removeNodeMark(e,t){if(!(t instanceof He)){let s=this.doc.nodeAt(e);if(!s)throw new RangeError("No node at position "+e);if(t=t.isInSet(s.marks),!t)return this}return this.step(new Ls(e,t)),this}split(e,t=1,s){return Wg(this,e,t,s),this}addMark(e,t,s){return Dg(this,e,t,s),this}removeMark(e,t,s){return Ag(this,e,t,s),this}clearIncompatible(e,t,s){return rl(this,e,t,s),this}}const di=Object.create(null);class xe{constructor(e,t,s){this.$anchor=e,this.$head=t,this.ranges=s||[new Zg(e.min(t),e.max(t))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let e=this.ranges;for(let t=0;t<e.length;t++)if(e[t].$from.pos!=e[t].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(e,t=le.empty){let s=t.content.lastChild,o=null;for(let l=0;l<t.openEnd;l++)o=s,s=s.lastChild;let r=e.steps.length,i=this.ranges;for(let l=0;l<i.length;l++){let{$from:a,$to:c}=i[l],u=e.mapping.slice(r);e.replaceRange(u.map(a.pos),u.map(c.pos),l?le.empty:t),l==0&&ka(e,r,(s?s.isInline:o&&o.isTextblock)?-1:1)}}replaceWith(e,t){let s=e.steps.length,o=this.ranges;for(let r=0;r<o.length;r++){let{$from:i,$to:l}=o[r],a=e.mapping.slice(s),c=a.map(i.pos),u=a.map(l.pos);r?e.deleteRange(c,u):(e.replaceRangeWith(c,u,t),ka(e,s,t.isInline?-1:1))}}static findFrom(e,t,s=!1){let o=e.parent.inlineContent?new ye(e):Is(e.node(0),e.parent,e.pos,e.index(),t,s);if(o)return o;for(let r=e.depth-1;r>=0;r--){let i=t<0?Is(e.node(0),e.node(r),e.before(r+1),e.index(r),t,s):Is(e.node(0),e.node(r),e.after(r+1),e.index(r)+1,t,s);if(i)return i}return null}static near(e,t=1){return this.findFrom(e,t)||this.findFrom(e,-t)||new Ot(e.node(0))}static atStart(e){return Is(e,e,0,0,1)||new Ot(e)}static atEnd(e){return Is(e,e,e.content.size,e.childCount,-1)||new Ot(e)}static fromJSON(e,t){if(!t||!t.type)throw new RangeError("Invalid input for Selection.fromJSON");let s=di[t.type];if(!s)throw new RangeError(`No selection type ${t.type} defined`);return s.fromJSON(e,t)}static jsonID(e,t){if(e in di)throw new RangeError("Duplicate use of selection JSON ID "+e);return di[e]=t,t.prototype.jsonID=e,t}getBookmark(){return ye.between(this.$anchor,this.$head).getBookmark()}}xe.prototype.visible=!0;class Zg{constructor(e,t){this.$from=e,this.$to=t}}let ba=!1;function wa(n){!ba&&!n.parent.inlineContent&&(ba=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+n.parent.type.name+")"))}class ye extends xe{constructor(e,t=e){wa(e),wa(t),super(e,t)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(e,t){let s=e.resolve(t.map(this.head));if(!s.parent.inlineContent)return xe.near(s);let o=e.resolve(t.map(this.anchor));return new ye(o.parent.inlineContent?o:s,s)}replace(e,t=le.empty){if(super.replace(e,t),t==le.empty){let s=this.$from.marksAcross(this.$to);s&&e.ensureMarks(s)}}eq(e){return e instanceof ye&&e.anchor==this.anchor&&e.head==this.head}getBookmark(){return new Er(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(e,t){if(typeof t.anchor!="number"||typeof t.head!="number")throw new RangeError("Invalid input for TextSelection.fromJSON");return new ye(e.resolve(t.anchor),e.resolve(t.head))}static create(e,t,s=t){let o=e.resolve(t);return new this(o,s==t?o:e.resolve(s))}static between(e,t,s){let o=e.pos-t.pos;if((!s||o)&&(s=o>=0?1:-1),!t.parent.inlineContent){let r=xe.findFrom(t,s,!0)||xe.findFrom(t,-s,!0);if(r)t=r.$head;else return xe.near(t,s)}return e.parent.inlineContent||(o==0?e=t:(e=(xe.findFrom(e,-s,!0)||xe.findFrom(e,s,!0)).$anchor,e.pos<t.pos!=o<0&&(e=t))),new ye(e,t)}}xe.jsonID("text",ye);class Er{constructor(e,t){this.anchor=e,this.head=t}map(e){return new Er(e.map(this.anchor),e.map(this.head))}resolve(e){return ye.between(e.resolve(this.anchor),e.resolve(this.head))}}class de extends xe{constructor(e){let t=e.nodeAfter,s=e.node(0).resolve(e.pos+t.nodeSize);super(e,s),this.node=t}map(e,t){let{deleted:s,pos:o}=t.mapResult(this.anchor),r=e.resolve(o);return s?xe.near(r):new de(r)}content(){return new le(Q.from(this.node),0,0)}eq(e){return e instanceof de&&e.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new ll(this.anchor)}static fromJSON(e,t){if(typeof t.anchor!="number")throw new RangeError("Invalid input for NodeSelection.fromJSON");return new de(e.resolve(t.anchor))}static create(e,t){return new de(e.resolve(t))}static isSelectable(e){return!e.isText&&e.type.spec.selectable!==!1}}de.prototype.visible=!1;xe.jsonID("node",de);class ll{constructor(e){this.anchor=e}map(e){let{deleted:t,pos:s}=e.mapResult(this.anchor);return t?new Er(s,s):new ll(s)}resolve(e){let t=e.resolve(this.anchor),s=t.nodeAfter;return s&&de.isSelectable(s)?new de(t):xe.near(t)}}class Ot extends xe{constructor(e){super(e.resolve(0),e.resolve(e.content.size))}replace(e,t=le.empty){if(t==le.empty){e.delete(0,e.doc.content.size);let s=xe.atStart(e.doc);s.eq(e.selection)||e.setSelection(s)}else super.replace(e,t)}toJSON(){return{type:"all"}}static fromJSON(e){return new Ot(e)}map(e){return new Ot(e)}eq(e){return e instanceof Ot}getBookmark(){return ev}}xe.jsonID("all",Ot);const ev={map(){return this},resolve(n){return new Ot(n)}};function Is(n,e,t,s,o,r=!1){if(e.inlineContent)return ye.create(n,t);for(let i=s-(o>0?0:1);o>0?i<e.childCount:i>=0;i+=o){let l=e.child(i);if(l.isAtom){if(!r&&de.isSelectable(l))return de.create(n,t-(o<0?l.nodeSize:0))}else{let a=Is(n,l,t+o,o<0?l.childCount:0,o,r);if(a)return a}t+=l.nodeSize*o}return null}function ka(n,e,t){let s=n.steps.length-1;if(s<e)return;let o=n.steps[s];if(!(o instanceof Ke||o instanceof Xe))return;let r=n.mapping.maps[s],i;r.forEach((l,a,c,u)=>{i==null&&(i=u)}),n.setSelection(xe.near(n.doc.resolve(i),t))}const xa=1,Sa=2,Ca=4;class tv extends Qg{constructor(e){super(e.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=e.selection,this.storedMarks=e.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(e){if(e.$from.doc!=this.doc)throw new RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=e,this.curSelectionFor=this.steps.length,this.updated=(this.updated|xa)&-3,this.storedMarks=null,this}get selectionSet(){return(this.updated&xa)>0}setStoredMarks(e){return this.storedMarks=e,this.updated|=Sa,this}ensureMarks(e){return He.sameSet(this.storedMarks||this.selection.$from.marks(),e)||this.setStoredMarks(e),this}addStoredMark(e){return this.ensureMarks(e.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(e){return this.ensureMarks(e.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(this.updated&Sa)>0}addStep(e,t){super.addStep(e,t),this.updated=this.updated&-3,this.storedMarks=null}setTime(e){return this.time=e,this}replaceSelection(e){return this.selection.replace(this,e),this}replaceSelectionWith(e,t=!0){let s=this.selection;return t&&(e=e.mark(this.storedMarks||(s.empty?s.$from.marks():s.$from.marksAcross(s.$to)||He.none))),s.replaceWith(this,e),this}deleteSelection(){return this.selection.replace(this),this}insertText(e,t,s){let o=this.doc.type.schema;if(t==null)return e?this.replaceSelectionWith(o.text(e),!0):this.deleteSelection();{if(s==null&&(s=t),s=s??t,!e)return this.deleteRange(t,s);let r=this.storedMarks;if(!r){let i=this.doc.resolve(t);r=s==t?i.marks():i.marksAcross(this.doc.resolve(s))}return this.replaceRangeWith(t,s,o.text(e,r)),this.selection.empty||this.setSelection(xe.near(this.selection.$to)),this}}setMeta(e,t){return this.meta[typeof e=="string"?e:e.key]=t,this}getMeta(e){return this.meta[typeof e=="string"?e:e.key]}get isGeneric(){for(let e in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=Ca,this}get scrolledIntoView(){return(this.updated&Ca)>0}}function _a(n,e){return!e||!n?n:n.bind(e)}class oo{constructor(e,t,s){this.name=e,this.init=_a(t.init,s),this.apply=_a(t.apply,s)}}const nv=[new oo("doc",{init(n){return n.doc||n.schema.topNodeType.createAndFill()},apply(n){return n.doc}}),new oo("selection",{init(n,e){return n.selection||xe.atStart(e.doc)},apply(n){return n.selection}}),new oo("storedMarks",{init(n){return n.storedMarks||null},apply(n,e,t,s){return s.selection.$cursor?n.storedMarks:null}}),new oo("scrollToSelection",{init(){return 0},apply(n,e){return n.scrolledIntoView?e+1:e}})];class fi{constructor(e,t){this.schema=e,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=nv.slice(),t&&t.forEach(s=>{if(this.pluginsByKey[s.key])throw new RangeError("Adding different instances of a keyed plugin ("+s.key+")");this.plugins.push(s),this.pluginsByKey[s.key]=s,s.spec.state&&this.fields.push(new oo(s.key,s.spec.state,s))})}}class Ns{constructor(e){this.config=e}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(e){return this.applyTransaction(e).state}filterTransaction(e,t=-1){for(let s=0;s<this.config.plugins.length;s++)if(s!=t){let o=this.config.plugins[s];if(o.spec.filterTransaction&&!o.spec.filterTransaction.call(o,e,this))return!1}return!0}applyTransaction(e){if(!this.filterTransaction(e))return{state:this,transactions:[]};let t=[e],s=this.applyInner(e),o=null;for(;;){let r=!1;for(let i=0;i<this.config.plugins.length;i++){let l=this.config.plugins[i];if(l.spec.appendTransaction){let a=o?o[i].n:0,c=o?o[i].state:this,u=a<t.length&&l.spec.appendTransaction.call(l,a?t.slice(a):t,c,s);if(u&&s.filterTransaction(u,i)){if(u.setMeta("appendedTransaction",e),!o){o=[];for(let d=0;d<this.config.plugins.length;d++)o.push(d<i?{state:s,n:t.length}:{state:this,n:0})}t.push(u),s=s.applyInner(u),r=!0}o&&(o[i]={state:s,n:t.length})}}if(!r)return{state:s,transactions:t}}}applyInner(e){if(!e.before.eq(this.doc))throw new RangeError("Applying a mismatched transaction");let t=new Ns(this.config),s=this.config.fields;for(let o=0;o<s.length;o++){let r=s[o];t[r.name]=r.apply(e,this[r.name],this,t)}return t}get tr(){return new tv(this)}static create(e){let t=new fi(e.doc?e.doc.type.schema:e.schema,e.plugins),s=new Ns(t);for(let o=0;o<t.fields.length;o++)s[t.fields[o].name]=t.fields[o].init(e,s);return s}reconfigure(e){let t=new fi(this.schema,e.plugins),s=t.fields,o=new Ns(t);for(let r=0;r<s.length;r++){let i=s[r].name;o[i]=this.hasOwnProperty(i)?this[i]:s[r].init(e,o)}return o}toJSON(e){let t={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(t.storedMarks=this.storedMarks.map(s=>s.toJSON())),e&&typeof e=="object")for(let s in e){if(s=="doc"||s=="selection")throw new RangeError("The JSON fields `doc` and `selection` are reserved");let o=e[s],r=o.spec.state;r&&r.toJSON&&(t[s]=r.toJSON.call(o,this[o.key]))}return t}static fromJSON(e,t,s){if(!t)throw new RangeError("Invalid input for EditorState.fromJSON");if(!e.schema)throw new RangeError("Required config field 'schema' missing");let o=new fi(e.schema,e.plugins),r=new Ns(o);return o.fields.forEach(i=>{if(i.name=="doc")r.doc=Rn.fromJSON(e.schema,t.doc);else if(i.name=="selection")r.selection=xe.fromJSON(r.doc,t.selection);else if(i.name=="storedMarks")t.storedMarks&&(r.storedMarks=t.storedMarks.map(e.schema.markFromJSON));else{if(s)for(let l in s){let a=s[l],c=a.spec.state;if(a.key==i.name&&c&&c.fromJSON&&Object.prototype.hasOwnProperty.call(t,l)){r[i.name]=c.fromJSON.call(a,e,t[l],r);return}}r[i.name]=i.init(e,r)}}),r}}function gu(n,e,t){for(let s in n){let o=n[s];o instanceof Function?o=o.bind(e):s=="handleDOMEvents"&&(o=gu(o,e,{})),t[s]=o}return t}class nt{constructor(e){this.spec=e,this.props={},e.props&&gu(e.props,this,this.props),this.key=e.key?e.key.key:vu("plugin")}getState(e){return e[this.key]}}const pi=Object.create(null);function vu(n){return n in pi?n+"$"+ ++pi[n]:(pi[n]=0,n+"$")}class Nt{constructor(e="key"){this.key=vu(e)}get(e){return e.config.pluginsByKey[this.key]}getState(e){return e[this.key]}}const et=function(n){for(var e=0;;e++)if(n=n.previousSibling,!n)return e},zs=function(n){let e=n.assignedSlot||n.parentNode;return e&&e.nodeType==11?e.host:e};let Pi=null;const hn=function(n,e,t){let s=Pi||(Pi=document.createRange());return s.setEnd(n,t??n.nodeValue.length),s.setStart(n,e||0),s},sv=function(){Pi=null},ms=function(n,e,t,s){return t&&(Ma(n,e,t,s,-1)||Ma(n,e,t,s,1))},ov=/^(img|br|input|textarea|hr)$/i;function Ma(n,e,t,s,o){for(;;){if(n==t&&e==s)return!0;if(e==(o<0?0:Lt(n))){let r=n.parentNode;if(!r||r.nodeType!=1||xo(n)||ov.test(n.nodeName)||n.contentEditable=="false")return!1;e=et(n)+(o<0?0:1),n=r}else if(n.nodeType==1){if(n=n.childNodes[e+(o<0?-1:0)],n.contentEditable=="false")return!1;e=o<0?Lt(n):0}else return!1}}function Lt(n){return n.nodeType==3?n.nodeValue.length:n.childNodes.length}function rv(n,e){for(;;){if(n.nodeType==3&&e)return n;if(n.nodeType==1&&e>0){if(n.contentEditable=="false")return null;n=n.childNodes[e-1],e=Lt(n)}else if(n.parentNode&&!xo(n))e=et(n),n=n.parentNode;else return null}}function iv(n,e){for(;;){if(n.nodeType==3&&e<n.nodeValue.length)return n;if(n.nodeType==1&&e<n.childNodes.length){if(n.contentEditable=="false")return null;n=n.childNodes[e],e=0}else if(n.parentNode&&!xo(n))e=et(n)+1,n=n.parentNode;else return null}}function lv(n,e,t){for(let s=e==0,o=e==Lt(n);s||o;){if(n==t)return!0;let r=et(n);if(n=n.parentNode,!n)return!1;s=s&&r==0,o=o&&r==Lt(n)}}function xo(n){let e;for(let t=n;t&&!(e=t.pmViewDesc);t=t.parentNode);return e&&e.node&&e.node.isBlock&&(e.dom==n||e.contentDOM==n)}const Ir=function(n){return n.focusNode&&ms(n.focusNode,n.focusOffset,n.anchorNode,n.anchorOffset)};function ts(n,e){let t=document.createEvent("Event");return t.initEvent("keydown",!0,!0),t.keyCode=n,t.key=t.code=e,t}function av(n){let e=n.activeElement;for(;e&&e.shadowRoot;)e=e.shadowRoot.activeElement;return e}function cv(n,e,t){if(n.caretPositionFromPoint)try{let s=n.caretPositionFromPoint(e,t);if(s)return{node:s.offsetNode,offset:Math.min(Lt(s.offsetNode),s.offset)}}catch{}if(n.caretRangeFromPoint){let s=n.caretRangeFromPoint(e,t);if(s)return{node:s.startContainer,offset:Math.min(Lt(s.startContainer),s.startOffset)}}}const ln=typeof navigator<"u"?navigator:null,Ta=typeof document<"u"?document:null,Jn=ln&&ln.userAgent||"",Ri=/Edge\/(\d+)/.exec(Jn),yu=/MSIE \d/.exec(Jn),Vi=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(Jn),Ct=!!(yu||Vi||Ri),Ln=yu?document.documentMode:Vi?+Vi[1]:Ri?+Ri[1]:0,jt=!Ct&&/gecko\/(\d+)/i.test(Jn);jt&&+(/Firefox\/(\d+)/.exec(Jn)||[0,0])[1];const Li=!Ct&&/Chrome\/(\d+)/.exec(Jn),at=!!Li,bu=Li?+Li[1]:0,gt=!Ct&&!!ln&&/Apple Computer/.test(ln.vendor),Fs=gt&&(/Mobile\/\w+/.test(Jn)||!!ln&&ln.maxTouchPoints>2),Vt=Fs||(ln?/Mac/.test(ln.platform):!1),uv=ln?/Win/.test(ln.platform):!1,mn=/Android \d/.test(Jn),So=!!Ta&&"webkitFontSmoothing"in Ta.documentElement.style,dv=So?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function fv(n){let e=n.defaultView&&n.defaultView.visualViewport;return e?{left:0,right:e.width,top:0,bottom:e.height}:{left:0,right:n.documentElement.clientWidth,top:0,bottom:n.documentElement.clientHeight}}function pn(n,e){return typeof n=="number"?n:n[e]}function pv(n){let e=n.getBoundingClientRect(),t=e.width/n.offsetWidth||1,s=e.height/n.offsetHeight||1;return{left:e.left,right:e.left+n.clientWidth*t,top:e.top,bottom:e.top+n.clientHeight*s}}function Ea(n,e,t){let s=n.someProp("scrollThreshold")||0,o=n.someProp("scrollMargin")||5,r=n.dom.ownerDocument;for(let i=t||n.dom;i;){if(i.nodeType!=1){i=zs(i);continue}let l=i,a=l==r.body,c=a?fv(r):pv(l),u=0,d=0;if(e.top<c.top+pn(s,"top")?d=-(c.top-e.top+pn(o,"top")):e.bottom>c.bottom-pn(s,"bottom")&&(d=e.bottom-e.top>c.bottom-c.top?e.top+pn(o,"top")-c.top:e.bottom-c.bottom+pn(o,"bottom")),e.left<c.left+pn(s,"left")?u=-(c.left-e.left+pn(o,"left")):e.right>c.right-pn(s,"right")&&(u=e.right-c.right+pn(o,"right")),u||d)if(a)r.defaultView.scrollBy(u,d);else{let p=l.scrollLeft,b=l.scrollTop;d&&(l.scrollTop+=d),u&&(l.scrollLeft+=u);let w=l.scrollLeft-p,C=l.scrollTop-b;e={left:e.left-w,top:e.top-C,right:e.right-w,bottom:e.bottom-C}}let v=a?"fixed":getComputedStyle(i).position;if(/^(fixed|sticky)$/.test(v))break;i=v=="absolute"?i.offsetParent:zs(i)}}function hv(n){let e=n.dom.getBoundingClientRect(),t=Math.max(0,e.top),s,o;for(let r=(e.left+e.right)/2,i=t+1;i<Math.min(innerHeight,e.bottom);i+=5){let l=n.root.elementFromPoint(r,i);if(!l||l==n.dom||!n.dom.contains(l))continue;let a=l.getBoundingClientRect();if(a.top>=t-20){s=l,o=a.top;break}}return{refDOM:s,refTop:o,stack:wu(n.dom)}}function wu(n){let e=[],t=n.ownerDocument;for(let s=n;s&&(e.push({dom:s,top:s.scrollTop,left:s.scrollLeft}),n!=t);s=zs(s));return e}function mv({refDOM:n,refTop:e,stack:t}){let s=n?n.getBoundingClientRect().top:0;ku(t,s==0?0:s-e)}function ku(n,e){for(let t=0;t<n.length;t++){let{dom:s,top:o,left:r}=n[t];s.scrollTop!=o+e&&(s.scrollTop=o+e),s.scrollLeft!=r&&(s.scrollLeft=r)}}let Ms=null;function gv(n){if(n.setActive)return n.setActive();if(Ms)return n.focus(Ms);let e=wu(n);n.focus(Ms==null?{get preventScroll(){return Ms={preventScroll:!0},!0}}:void 0),Ms||(Ms=!1,ku(e,0))}function xu(n,e){let t,s=2e8,o,r=0,i=e.top,l=e.top,a,c;for(let u=n.firstChild,d=0;u;u=u.nextSibling,d++){let v;if(u.nodeType==1)v=u.getClientRects();else if(u.nodeType==3)v=hn(u).getClientRects();else continue;for(let p=0;p<v.length;p++){let b=v[p];if(b.top<=i&&b.bottom>=l){i=Math.max(b.bottom,i),l=Math.min(b.top,l);let w=b.left>e.left?b.left-e.left:b.right<e.left?e.left-b.right:0;if(w<s){t=u,s=w,o=w&&t.nodeType==3?{left:b.right<e.left?b.right:b.left,top:e.top}:e,u.nodeType==1&&w&&(r=d+(e.left>=(b.left+b.right)/2?1:0));continue}}else b.top>e.top&&!a&&b.left<=e.left&&b.right>=e.left&&(a=u,c={left:Math.max(b.left,Math.min(b.right,e.left)),top:b.top});!t&&(e.left>=b.right&&e.top>=b.top||e.left>=b.left&&e.top>=b.bottom)&&(r=d+1)}}return!t&&a&&(t=a,o=c,s=0),t&&t.nodeType==3?vv(t,o):!t||s&&t.nodeType==1?{node:n,offset:r}:xu(t,o)}function vv(n,e){let t=n.nodeValue.length,s=document.createRange();for(let o=0;o<t;o++){s.setEnd(n,o+1),s.setStart(n,o);let r=En(s,1);if(r.top!=r.bottom&&al(e,r))return{node:n,offset:o+(e.left>=(r.left+r.right)/2?1:0)}}return{node:n,offset:0}}function al(n,e){return n.left>=e.left-1&&n.left<=e.right+1&&n.top>=e.top-1&&n.top<=e.bottom+1}function yv(n,e){let t=n.parentNode;return t&&/^li$/i.test(t.nodeName)&&e.left<n.getBoundingClientRect().left?t:n}function bv(n,e,t){let{node:s,offset:o}=xu(e,t),r=-1;if(s.nodeType==1&&!s.firstChild){let i=s.getBoundingClientRect();r=i.left!=i.right&&t.left>(i.left+i.right)/2?1:-1}return n.docView.posFromDOM(s,o,r)}function wv(n,e,t,s){let o=-1;for(let r=e,i=!1;r!=n.dom;){let l=n.docView.nearestDesc(r,!0),a;if(!l)return null;if(l.dom.nodeType==1&&(l.node.isBlock&&l.parent||!l.contentDOM)&&((a=l.dom.getBoundingClientRect()).width||a.height)&&(l.node.isBlock&&l.parent&&(!i&&a.left>s.left||a.top>s.top?o=l.posBefore:(!i&&a.right<s.left||a.bottom<s.top)&&(o=l.posAfter),i=!0),!l.contentDOM&&o<0&&!l.node.isText))return(l.node.isBlock?s.top<(a.top+a.bottom)/2:s.left<(a.left+a.right)/2)?l.posBefore:l.posAfter;r=l.dom.parentNode}return o>-1?o:n.docView.posFromDOM(e,t,-1)}function Su(n,e,t){let s=n.childNodes.length;if(s&&t.top<t.bottom)for(let o=Math.max(0,Math.min(s-1,Math.floor(s*(e.top-t.top)/(t.bottom-t.top))-2)),r=o;;){let i=n.childNodes[r];if(i.nodeType==1){let l=i.getClientRects();for(let a=0;a<l.length;a++){let c=l[a];if(al(e,c))return Su(i,e,c)}}if((r=(r+1)%s)==o)break}return n}function kv(n,e){let t=n.dom.ownerDocument,s,o=0,r=cv(t,e.left,e.top);r&&({node:s,offset:o}=r);let i=(n.root.elementFromPoint?n.root:t).elementFromPoint(e.left,e.top),l;if(!i||!n.dom.contains(i.nodeType!=1?i.parentNode:i)){let c=n.dom.getBoundingClientRect();if(!al(e,c)||(i=Su(n.dom,e,c),!i))return null}if(gt)for(let c=i;s&&c;c=zs(c))c.draggable&&(s=void 0);if(i=yv(i,e),s){if(jt&&s.nodeType==1&&(o=Math.min(o,s.childNodes.length),o<s.childNodes.length)){let u=s.childNodes[o],d;u.nodeName=="IMG"&&(d=u.getBoundingClientRect()).right<=e.left&&d.bottom>e.top&&o++}let c;So&&o&&s.nodeType==1&&(c=s.childNodes[o-1]).nodeType==1&&c.contentEditable=="false"&&c.getBoundingClientRect().top>=e.top&&o--,s==n.dom&&o==s.childNodes.length-1&&s.lastChild.nodeType==1&&e.top>s.lastChild.getBoundingClientRect().bottom?l=n.state.doc.content.size:(o==0||s.nodeType!=1||s.childNodes[o-1].nodeName!="BR")&&(l=wv(n,s,o,e))}l==null&&(l=bv(n,i,e));let a=n.docView.nearestDesc(i,!0);return{pos:l,inside:a?a.posAtStart-a.border:-1}}function Ia(n){return n.top<n.bottom||n.left<n.right}function En(n,e){let t=n.getClientRects();if(t.length){let s=t[e<0?0:t.length-1];if(Ia(s))return s}return Array.prototype.find.call(t,Ia)||n.getBoundingClientRect()}const xv=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function Cu(n,e,t){let{node:s,offset:o,atom:r}=n.docView.domFromPos(e,t<0?-1:1),i=So||jt;if(s.nodeType==3)if(i&&(xv.test(s.nodeValue)||(t<0?!o:o==s.nodeValue.length))){let a=En(hn(s,o,o),t);if(jt&&o&&/\s/.test(s.nodeValue[o-1])&&o<s.nodeValue.length){let c=En(hn(s,o-1,o-1),-1);if(c.top==a.top){let u=En(hn(s,o,o+1),-1);if(u.top!=a.top)return eo(u,u.left<c.left)}}return a}else{let a=o,c=o,u=t<0?1:-1;return t<0&&!o?(c++,u=-1):t>=0&&o==s.nodeValue.length?(a--,u=1):t<0?a--:c++,eo(En(hn(s,a,c),u),u<0)}if(!n.state.doc.resolve(e-(r||0)).parent.inlineContent){if(r==null&&o&&(t<0||o==Lt(s))){let a=s.childNodes[o-1];if(a.nodeType==1)return hi(a.getBoundingClientRect(),!1)}if(r==null&&o<Lt(s)){let a=s.childNodes[o];if(a.nodeType==1)return hi(a.getBoundingClientRect(),!0)}return hi(s.getBoundingClientRect(),t>=0)}if(r==null&&o&&(t<0||o==Lt(s))){let a=s.childNodes[o-1],c=a.nodeType==3?hn(a,Lt(a)-(i?0:1)):a.nodeType==1&&(a.nodeName!="BR"||!a.nextSibling)?a:null;if(c)return eo(En(c,1),!1)}if(r==null&&o<Lt(s)){let a=s.childNodes[o];for(;a.pmViewDesc&&a.pmViewDesc.ignoreForCoords;)a=a.nextSibling;let c=a?a.nodeType==3?hn(a,0,i?0:1):a.nodeType==1?a:null:null;if(c)return eo(En(c,-1),!0)}return eo(En(s.nodeType==3?hn(s):s,-t),t>=0)}function eo(n,e){if(n.width==0)return n;let t=e?n.left:n.right;return{top:n.top,bottom:n.bottom,left:t,right:t}}function hi(n,e){if(n.height==0)return n;let t=e?n.top:n.bottom;return{top:t,bottom:t,left:n.left,right:n.right}}function _u(n,e,t){let s=n.state,o=n.root.activeElement;s!=e&&n.updateState(e),o!=n.dom&&n.focus();try{return t()}finally{s!=e&&n.updateState(s),o!=n.dom&&o&&o.focus()}}function Sv(n,e,t){let s=e.selection,o=t=="up"?s.$from:s.$to;return _u(n,e,()=>{let{node:r}=n.docView.domFromPos(o.pos,t=="up"?-1:1);for(;;){let l=n.docView.nearestDesc(r,!0);if(!l)break;if(l.node.isBlock){r=l.contentDOM||l.dom;break}r=l.dom.parentNode}let i=Cu(n,o.pos,1);for(let l=r.firstChild;l;l=l.nextSibling){let a;if(l.nodeType==1)a=l.getClientRects();else if(l.nodeType==3)a=hn(l,0,l.nodeValue.length).getClientRects();else continue;for(let c=0;c<a.length;c++){let u=a[c];if(u.bottom>u.top+1&&(t=="up"?i.top-u.top>(u.bottom-i.top)*2:u.bottom-i.bottom>(i.bottom-u.top)*2))return!1}}return!0})}const Cv=/[\u0590-\u08ac]/;function _v(n,e,t){let{$head:s}=e.selection;if(!s.parent.isTextblock)return!1;let o=s.parentOffset,r=!o,i=o==s.parent.content.size,l=n.domSelection();return l?!Cv.test(s.parent.textContent)||!l.modify?t=="left"||t=="backward"?r:i:_u(n,e,()=>{let{focusNode:a,focusOffset:c,anchorNode:u,anchorOffset:d}=n.domSelectionRange(),v=l.caretBidiLevel;l.modify("move",t,"character");let p=s.depth?n.docView.domAfterPos(s.before()):n.dom,{focusNode:b,focusOffset:w}=n.domSelectionRange(),C=b&&!p.contains(b.nodeType==1?b:b.parentNode)||a==b&&c==w;try{l.collapse(u,d),a&&(a!=u||c!=d)&&l.extend&&l.extend(a,c)}catch{}return v!=null&&(l.caretBidiLevel=v),C}):s.pos==s.start()||s.pos==s.end()}let Oa=null,Na=null,Da=!1;function Mv(n,e,t){return Oa==e&&Na==t?Da:(Oa=e,Na=t,Da=t=="up"||t=="down"?Sv(n,e,t):_v(n,e,t))}const Bt=0,Aa=1,ss=2,an=3;class Co{constructor(e,t,s,o){this.parent=e,this.children=t,this.dom=s,this.contentDOM=o,this.dirty=Bt,s.pmViewDesc=this}matchesWidget(e){return!1}matchesMark(e){return!1}matchesNode(e,t,s){return!1}matchesHack(e){return!1}parseRule(){return null}stopEvent(e){return!1}get size(){let e=0;for(let t=0;t<this.children.length;t++)e+=this.children[t].size;return e}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let e=0;e<this.children.length;e++)this.children[e].destroy()}posBeforeChild(e){for(let t=0,s=this.posAtStart;;t++){let o=this.children[t];if(o==e)return s;s+=o.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(e,t,s){if(this.contentDOM&&this.contentDOM.contains(e.nodeType==1?e:e.parentNode))if(s<0){let r,i;if(e==this.contentDOM)r=e.childNodes[t-1];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;r=e.previousSibling}for(;r&&!((i=r.pmViewDesc)&&i.parent==this);)r=r.previousSibling;return r?this.posBeforeChild(i)+i.size:this.posAtStart}else{let r,i;if(e==this.contentDOM)r=e.childNodes[t];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;r=e.nextSibling}for(;r&&!((i=r.pmViewDesc)&&i.parent==this);)r=r.nextSibling;return r?this.posBeforeChild(i):this.posAtEnd}let o;if(e==this.dom&&this.contentDOM)o=t>et(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))o=e.compareDocumentPosition(this.contentDOM)&2;else if(this.dom.firstChild){if(t==0)for(let r=e;;r=r.parentNode){if(r==this.dom){o=!1;break}if(r.previousSibling)break}if(o==null&&t==e.childNodes.length)for(let r=e;;r=r.parentNode){if(r==this.dom){o=!0;break}if(r.nextSibling)break}}return o??s>0?this.posAtEnd:this.posAtStart}nearestDesc(e,t=!1){for(let s=!0,o=e;o;o=o.parentNode){let r=this.getDesc(o),i;if(r&&(!t||r.node))if(s&&(i=r.nodeDOM)&&!(i.nodeType==1?i.contains(e.nodeType==1?e:e.parentNode):i==e))s=!1;else return r}}getDesc(e){let t=e.pmViewDesc;for(let s=t;s;s=s.parent)if(s==this)return t}posFromDOM(e,t,s){for(let o=e;o;o=o.parentNode){let r=this.getDesc(o);if(r)return r.localPosFromDOM(e,t,s)}return-1}descAt(e){for(let t=0,s=0;t<this.children.length;t++){let o=this.children[t],r=s+o.size;if(s==e&&r!=s){for(;!o.border&&o.children.length;)for(let i=0;i<o.children.length;i++){let l=o.children[i];if(l.size){o=l;break}}return o}if(e<r)return o.descAt(e-s-o.border);s=r}}domFromPos(e,t){if(!this.contentDOM)return{node:this.dom,offset:0,atom:e+1};let s=0,o=0;for(let r=0;s<this.children.length;s++){let i=this.children[s],l=r+i.size;if(l>e||i instanceof Tu){o=e-r;break}r=l}if(o)return this.children[s].domFromPos(o-this.children[s].border,t);for(let r;s&&!(r=this.children[s-1]).size&&r instanceof Mu&&r.side>=0;s--);if(t<=0){let r,i=!0;for(;r=s?this.children[s-1]:null,!(!r||r.dom.parentNode==this.contentDOM);s--,i=!1);return r&&t&&i&&!r.border&&!r.domAtom?r.domFromPos(r.size,t):{node:this.contentDOM,offset:r?et(r.dom)+1:0}}else{let r,i=!0;for(;r=s<this.children.length?this.children[s]:null,!(!r||r.dom.parentNode==this.contentDOM);s++,i=!1);return r&&i&&!r.border&&!r.domAtom?r.domFromPos(0,t):{node:this.contentDOM,offset:r?et(r.dom):this.contentDOM.childNodes.length}}}parseRange(e,t,s=0){if(this.children.length==0)return{node:this.contentDOM,from:e,to:t,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let o=-1,r=-1;for(let i=s,l=0;;l++){let a=this.children[l],c=i+a.size;if(o==-1&&e<=c){let u=i+a.border;if(e>=u&&t<=c-a.border&&a.node&&a.contentDOM&&this.contentDOM.contains(a.contentDOM))return a.parseRange(e,t,u);e=i;for(let d=l;d>0;d--){let v=this.children[d-1];if(v.size&&v.dom.parentNode==this.contentDOM&&!v.emptyChildAt(1)){o=et(v.dom)+1;break}e-=v.size}o==-1&&(o=0)}if(o>-1&&(c>t||l==this.children.length-1)){t=c;for(let u=l+1;u<this.children.length;u++){let d=this.children[u];if(d.size&&d.dom.parentNode==this.contentDOM&&!d.emptyChildAt(-1)){r=et(d.dom);break}t+=d.size}r==-1&&(r=this.contentDOM.childNodes.length);break}i=c}return{node:this.contentDOM,from:e,to:t,fromOffset:o,toOffset:r}}emptyChildAt(e){if(this.border||!this.contentDOM||!this.children.length)return!1;let t=this.children[e<0?0:this.children.length-1];return t.size==0||t.emptyChildAt(e)}domAfterPos(e){let{node:t,offset:s}=this.domFromPos(e,0);if(t.nodeType!=1||s==t.childNodes.length)throw new RangeError("No node after pos "+e);return t.childNodes[s]}setSelection(e,t,s,o=!1){let r=Math.min(e,t),i=Math.max(e,t);for(let p=0,b=0;p<this.children.length;p++){let w=this.children[p],C=b+w.size;if(r>b&&i<C)return w.setSelection(e-b-w.border,t-b-w.border,s,o);b=C}let l=this.domFromPos(e,e?-1:1),a=t==e?l:this.domFromPos(t,t?-1:1),c=s.root.getSelection(),u=s.domSelectionRange(),d=!1;if((jt||gt)&&e==t){let{node:p,offset:b}=l;if(p.nodeType==3){if(d=!!(b&&p.nodeValue[b-1]==`
`),d&&b==p.nodeValue.length)for(let w=p,C;w;w=w.parentNode){if(C=w.nextSibling){C.nodeName=="BR"&&(l=a={node:C.parentNode,offset:et(C)+1});break}let M=w.pmViewDesc;if(M&&M.node&&M.node.isBlock)break}}else{let w=p.childNodes[b-1];d=w&&(w.nodeName=="BR"||w.contentEditable=="false")}}if(jt&&u.focusNode&&u.focusNode!=a.node&&u.focusNode.nodeType==1){let p=u.focusNode.childNodes[u.focusOffset];p&&p.contentEditable=="false"&&(o=!0)}if(!(o||d&&gt)&&ms(l.node,l.offset,u.anchorNode,u.anchorOffset)&&ms(a.node,a.offset,u.focusNode,u.focusOffset))return;let v=!1;if((c.extend||e==t)&&!d){c.collapse(l.node,l.offset);try{e!=t&&c.extend(a.node,a.offset),v=!0}catch{}}if(!v){if(e>t){let b=l;l=a,a=b}let p=document.createRange();p.setEnd(a.node,a.offset),p.setStart(l.node,l.offset),c.removeAllRanges(),c.addRange(p)}}ignoreMutation(e){return!this.contentDOM&&e.type!="selection"}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(e,t){for(let s=0,o=0;o<this.children.length;o++){let r=this.children[o],i=s+r.size;if(s==i?e<=i&&t>=s:e<i&&t>s){let l=s+r.border,a=i-r.border;if(e>=l&&t<=a){this.dirty=e==s||t==i?ss:Aa,e==l&&t==a&&(r.contentLost||r.dom.parentNode!=this.contentDOM)?r.dirty=an:r.markDirty(e-l,t-l);return}else r.dirty=r.dom==r.contentDOM&&r.dom.parentNode==this.contentDOM&&!r.children.length?ss:an}s=i}this.dirty=ss}markParentsDirty(){let e=1;for(let t=this.parent;t;t=t.parent,e++){let s=e==1?ss:Aa;t.dirty<s&&(t.dirty=s)}}get domAtom(){return!1}get ignoreForCoords(){return!1}isText(e){return!1}}class Mu extends Co{constructor(e,t,s,o){let r,i=t.type.toDOM;if(typeof i=="function"&&(i=i(s,()=>{if(!r)return o;if(r.parent)return r.parent.posBeforeChild(r)})),!t.type.spec.raw){if(i.nodeType!=1){let l=document.createElement("span");l.appendChild(i),i=l}i.contentEditable="false",i.classList.add("ProseMirror-widget")}super(e,[],i,null),this.widget=t,this.widget=t,r=this}matchesWidget(e){return this.dirty==Bt&&e.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(e){let t=this.widget.spec.stopEvent;return t?t(e):!1}ignoreMutation(e){return e.type!="selection"||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get side(){return this.widget.type.side}}class Tv extends Co{constructor(e,t,s,o){super(e,[],t,null),this.textDOM=s,this.text=o}get size(){return this.text.length}localPosFromDOM(e,t){return e!=this.textDOM?this.posAtStart+(t?this.size:0):this.posAtStart+t}domFromPos(e){return{node:this.textDOM,offset:e}}ignoreMutation(e){return e.type==="characterData"&&e.target.nodeValue==e.oldValue}}class gs extends Co{constructor(e,t,s,o,r){super(e,[],s,o),this.mark=t,this.spec=r}static create(e,t,s,o){let r=o.nodeViews[t.type.name],i=r&&r(t,o,s);return(!i||!i.dom)&&(i=ys.renderSpec(document,t.type.spec.toDOM(t,s),null,t.attrs)),new gs(e,t,i.dom,i.contentDOM||i.dom,i)}parseRule(){return this.dirty&an||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM}}matchesMark(e){return this.dirty!=an&&this.mark.eq(e)}markDirty(e,t){if(super.markDirty(e,t),this.dirty!=Bt){let s=this.parent;for(;!s.node;)s=s.parent;s.dirty<this.dirty&&(s.dirty=this.dirty),this.dirty=Bt}}slice(e,t,s){let o=gs.create(this.parent,this.mark,!0,s),r=this.children,i=this.size;t<i&&(r=zi(r,t,i,s)),e>0&&(r=zi(r,0,e,s));for(let l=0;l<r.length;l++)r[l].parent=o;return o.children=r,o}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}}class Bn extends Co{constructor(e,t,s,o,r,i,l,a,c){super(e,[],r,i),this.node=t,this.outerDeco=s,this.innerDeco=o,this.nodeDOM=l}static create(e,t,s,o,r,i){let l=r.nodeViews[t.type.name],a,c=l&&l(t,r,()=>{if(!a)return i;if(a.parent)return a.parent.posBeforeChild(a)},s,o),u=c&&c.dom,d=c&&c.contentDOM;if(t.isText){if(!u)u=document.createTextNode(t.text);else if(u.nodeType!=3)throw new RangeError("Text must be rendered as a DOM text node")}else u||({dom:u,contentDOM:d}=ys.renderSpec(document,t.type.spec.toDOM(t),null,t.attrs));!d&&!t.isText&&u.nodeName!="BR"&&(u.hasAttribute("contenteditable")||(u.contentEditable="false"),t.type.spec.draggable&&(u.draggable=!0));let v=u;return u=Ou(u,s,t),c?a=new Ev(e,t,s,o,u,d||null,v,c,r,i+1):t.isText?new Or(e,t,s,o,u,v,r):new Bn(e,t,s,o,u,d||null,v,r,i+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let e={node:this.node.type.name,attrs:this.node.attrs};if(this.node.type.whitespace=="pre"&&(e.preserveWhitespace="full"),!this.contentDOM)e.getContent=()=>this.node.content;else if(!this.contentLost)e.contentElement=this.contentDOM;else{for(let t=this.children.length-1;t>=0;t--){let s=this.children[t];if(this.dom.contains(s.dom.parentNode)){e.contentElement=s.dom.parentNode;break}}e.contentElement||(e.getContent=()=>Q.empty)}return e}matchesNode(e,t,s){return this.dirty==Bt&&e.eq(this.node)&&cr(t,this.outerDeco)&&s.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return this.node.isLeaf?0:1}updateChildren(e,t){let s=this.node.inlineContent,o=t,r=e.composing?this.localCompositionInfo(e,t):null,i=r&&r.pos>-1?r:null,l=r&&r.pos<0,a=new Ov(this,i&&i.node,e);Av(this.node,this.innerDeco,(c,u,d)=>{c.spec.marks?a.syncToMarks(c.spec.marks,s,e):c.type.side>=0&&!d&&a.syncToMarks(u==this.node.childCount?He.none:this.node.child(u).marks,s,e),a.placeWidget(c,e,o)},(c,u,d,v)=>{a.syncToMarks(c.marks,s,e);let p;a.findNodeMatch(c,u,d,v)||l&&e.state.selection.from>o&&e.state.selection.to<o+c.nodeSize&&(p=a.findIndexWithChild(r.node))>-1&&a.updateNodeAt(c,u,d,p,e)||a.updateNextNode(c,u,d,e,v,o)||a.addNode(c,u,d,e,o),o+=c.nodeSize}),a.syncToMarks([],s,e),this.node.isTextblock&&a.addTextblockHacks(),a.destroyRest(),(a.changed||this.dirty==ss)&&(i&&this.protectLocalComposition(e,i),Eu(this.contentDOM,this.children,e),Fs&&$v(this.dom))}localCompositionInfo(e,t){let{from:s,to:o}=e.state.selection;if(!(e.state.selection instanceof ye)||s<t||o>t+this.node.content.size)return null;let r=e.input.compositionNode;if(!r||!this.dom.contains(r.parentNode))return null;if(this.node.inlineContent){let i=r.nodeValue,l=Pv(this.node.content,i,s-t,o-t);return l<0?null:{node:r,pos:l,text:i}}else return{node:r,pos:-1,text:""}}protectLocalComposition(e,{node:t,pos:s,text:o}){if(this.getDesc(t))return;let r=t;for(;r.parentNode!=this.contentDOM;r=r.parentNode){for(;r.previousSibling;)r.parentNode.removeChild(r.previousSibling);for(;r.nextSibling;)r.parentNode.removeChild(r.nextSibling);r.pmViewDesc&&(r.pmViewDesc=void 0)}let i=new Tv(this,r,t,o);e.input.compositionNodes.push(i),this.children=zi(this.children,s,s+o.length,e,i)}update(e,t,s,o){return this.dirty==an||!e.sameMarkup(this.node)?!1:(this.updateInner(e,t,s,o),!0)}updateInner(e,t,s,o){this.updateOuterDeco(t),this.node=e,this.innerDeco=s,this.contentDOM&&this.updateChildren(o,this.posAtStart),this.dirty=Bt}updateOuterDeco(e){if(cr(e,this.outerDeco))return;let t=this.nodeDOM.nodeType!=1,s=this.dom;this.dom=Iu(this.dom,this.nodeDOM,Bi(this.outerDeco,this.node,t),Bi(e,this.node,t)),this.dom!=s&&(s.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=e}selectNode(){this.nodeDOM.nodeType==1&&this.nodeDOM.classList.add("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&(this.dom.draggable=!0)}deselectNode(){this.nodeDOM.nodeType==1&&(this.nodeDOM.classList.remove("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&this.dom.removeAttribute("draggable"))}get domAtom(){return this.node.isAtom}}function $a(n,e,t,s,o){Ou(s,e,n);let r=new Bn(void 0,n,e,t,s,s,s,o,0);return r.contentDOM&&r.updateChildren(o,0),r}class Or extends Bn{constructor(e,t,s,o,r,i,l){super(e,t,s,o,r,null,i,l,0)}parseRule(){let e=this.nodeDOM.parentNode;for(;e&&e!=this.dom&&!e.pmIsDeco;)e=e.parentNode;return{skip:e||!0}}update(e,t,s,o){return this.dirty==an||this.dirty!=Bt&&!this.inParent()||!e.sameMarkup(this.node)?!1:(this.updateOuterDeco(t),(this.dirty!=Bt||e.text!=this.node.text)&&e.text!=this.nodeDOM.nodeValue&&(this.nodeDOM.nodeValue=e.text,o.trackWrites==this.nodeDOM&&(o.trackWrites=null)),this.node=e,this.dirty=Bt,!0)}inParent(){let e=this.parent.contentDOM;for(let t=this.nodeDOM;t;t=t.parentNode)if(t==e)return!0;return!1}domFromPos(e){return{node:this.nodeDOM,offset:e}}localPosFromDOM(e,t,s){return e==this.nodeDOM?this.posAtStart+Math.min(t,this.node.text.length):super.localPosFromDOM(e,t,s)}ignoreMutation(e){return e.type!="characterData"&&e.type!="selection"}slice(e,t,s){let o=this.node.cut(e,t),r=document.createTextNode(o.text);return new Or(this.parent,o,this.outerDeco,this.innerDeco,r,r,s)}markDirty(e,t){super.markDirty(e,t),this.dom!=this.nodeDOM&&(e==0||t==this.nodeDOM.nodeValue.length)&&(this.dirty=an)}get domAtom(){return!1}isText(e){return this.node.text==e}}class Tu extends Co{parseRule(){return{ignore:!0}}matchesHack(e){return this.dirty==Bt&&this.dom.nodeName==e}get domAtom(){return!0}get ignoreForCoords(){return this.dom.nodeName=="IMG"}}class Ev extends Bn{constructor(e,t,s,o,r,i,l,a,c,u){super(e,t,s,o,r,i,l,c,u),this.spec=a}update(e,t,s,o){if(this.dirty==an)return!1;if(this.spec.update&&(this.node.type==e.type||this.spec.multiType)){let r=this.spec.update(e,t,s);return r&&this.updateInner(e,t,s,o),r}else return!this.contentDOM&&!e.isLeaf?!1:super.update(e,t,s,o)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(e,t,s,o){this.spec.setSelection?this.spec.setSelection(e,t,s.root):super.setSelection(e,t,s,o)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(e){return this.spec.stopEvent?this.spec.stopEvent(e):!1}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}}function Eu(n,e,t){let s=n.firstChild,o=!1;for(let r=0;r<e.length;r++){let i=e[r],l=i.dom;if(l.parentNode==n){for(;l!=s;)s=Pa(s),o=!0;s=s.nextSibling}else o=!0,n.insertBefore(l,s);if(i instanceof gs){let a=s?s.previousSibling:n.lastChild;Eu(i.contentDOM,i.children,t),s=a?a.nextSibling:n.firstChild}}for(;s;)s=Pa(s),o=!0;o&&t.trackWrites==n&&(t.trackWrites=null)}const io=function(n){n&&(this.nodeName=n)};io.prototype=Object.create(null);const rs=[new io];function Bi(n,e,t){if(n.length==0)return rs;let s=t?rs[0]:new io,o=[s];for(let r=0;r<n.length;r++){let i=n[r].type.attrs;if(i){i.nodeName&&o.push(s=new io(i.nodeName));for(let l in i){let a=i[l];a!=null&&(t&&o.length==1&&o.push(s=new io(e.isInline?"span":"div")),l=="class"?s.class=(s.class?s.class+" ":"")+a:l=="style"?s.style=(s.style?s.style+";":"")+a:l!="nodeName"&&(s[l]=a))}}}return o}function Iu(n,e,t,s){if(t==rs&&s==rs)return e;let o=e;for(let r=0;r<s.length;r++){let i=s[r],l=t[r];if(r){let a;l&&l.nodeName==i.nodeName&&o!=n&&(a=o.parentNode)&&a.nodeName.toLowerCase()==i.nodeName||(a=document.createElement(i.nodeName),a.pmIsDeco=!0,a.appendChild(o),l=rs[0]),o=a}Iv(o,l||rs[0],i)}return o}function Iv(n,e,t){for(let s in e)s!="class"&&s!="style"&&s!="nodeName"&&!(s in t)&&n.removeAttribute(s);for(let s in t)s!="class"&&s!="style"&&s!="nodeName"&&t[s]!=e[s]&&n.setAttribute(s,t[s]);if(e.class!=t.class){let s=e.class?e.class.split(" ").filter(Boolean):[],o=t.class?t.class.split(" ").filter(Boolean):[];for(let r=0;r<s.length;r++)o.indexOf(s[r])==-1&&n.classList.remove(s[r]);for(let r=0;r<o.length;r++)s.indexOf(o[r])==-1&&n.classList.add(o[r]);n.classList.length==0&&n.removeAttribute("class")}if(e.style!=t.style){if(e.style){let s=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g,o;for(;o=s.exec(e.style);)n.style.removeProperty(o[1])}t.style&&(n.style.cssText+=t.style)}}function Ou(n,e,t){return Iu(n,n,rs,Bi(e,t,n.nodeType!=1))}function cr(n,e){if(n.length!=e.length)return!1;for(let t=0;t<n.length;t++)if(!n[t].type.eq(e[t].type))return!1;return!0}function Pa(n){let e=n.nextSibling;return n.parentNode.removeChild(n),e}class Ov{constructor(e,t,s){this.lock=t,this.view=s,this.index=0,this.stack=[],this.changed=!1,this.top=e,this.preMatch=Nv(e.node.content,e)}destroyBetween(e,t){if(e!=t){for(let s=e;s<t;s++)this.top.children[s].destroy();this.top.children.splice(e,t-e),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(e,t,s){let o=0,r=this.stack.length>>1,i=Math.min(r,e.length);for(;o<i&&(o==r-1?this.top:this.stack[o+1<<1]).matchesMark(e[o])&&e[o].type.spec.spanning!==!1;)o++;for(;o<r;)this.destroyRest(),this.top.dirty=Bt,this.index=this.stack.pop(),this.top=this.stack.pop(),r--;for(;r<e.length;){this.stack.push(this.top,this.index+1);let l=-1;for(let a=this.index;a<Math.min(this.index+3,this.top.children.length);a++){let c=this.top.children[a];if(c.matchesMark(e[r])&&!this.isLocked(c.dom)){l=a;break}}if(l>-1)l>this.index&&(this.changed=!0,this.destroyBetween(this.index,l)),this.top=this.top.children[this.index];else{let a=gs.create(this.top,e[r],t,s);this.top.children.splice(this.index,0,a),this.top=a,this.changed=!0}this.index=0,r++}}findNodeMatch(e,t,s,o){let r=-1,i;if(o>=this.preMatch.index&&(i=this.preMatch.matches[o-this.preMatch.index]).parent==this.top&&i.matchesNode(e,t,s))r=this.top.children.indexOf(i,this.index);else for(let l=this.index,a=Math.min(this.top.children.length,l+5);l<a;l++){let c=this.top.children[l];if(c.matchesNode(e,t,s)&&!this.preMatch.matched.has(c)){r=l;break}}return r<0?!1:(this.destroyBetween(this.index,r),this.index++,!0)}updateNodeAt(e,t,s,o,r){let i=this.top.children[o];return i.dirty==an&&i.dom==i.contentDOM&&(i.dirty=ss),i.update(e,t,s,r)?(this.destroyBetween(this.index,o),this.index++,!0):!1}findIndexWithChild(e){for(;;){let t=e.parentNode;if(!t)return-1;if(t==this.top.contentDOM){let s=e.pmViewDesc;if(s){for(let o=this.index;o<this.top.children.length;o++)if(this.top.children[o]==s)return o}return-1}e=t}}updateNextNode(e,t,s,o,r,i){for(let l=this.index;l<this.top.children.length;l++){let a=this.top.children[l];if(a instanceof Bn){let c=this.preMatch.matched.get(a);if(c!=null&&c!=r)return!1;let u=a.dom,d,v=this.isLocked(u)&&!(e.isText&&a.node&&a.node.isText&&a.nodeDOM.nodeValue==e.text&&a.dirty!=an&&cr(t,a.outerDeco));if(!v&&a.update(e,t,s,o))return this.destroyBetween(this.index,l),a.dom!=u&&(this.changed=!0),this.index++,!0;if(!v&&(d=this.recreateWrapper(a,e,t,s,o,i)))return this.destroyBetween(this.index,l),this.top.children[this.index]=d,d.contentDOM&&(d.dirty=ss,d.updateChildren(o,i+1),d.dirty=Bt),this.changed=!0,this.index++,!0;break}}return!1}recreateWrapper(e,t,s,o,r,i){if(e.dirty||t.isAtom||!e.children.length||!e.node.content.eq(t.content)||!cr(s,e.outerDeco)||!o.eq(e.innerDeco))return null;let l=Bn.create(this.top,t,s,o,r,i);if(l.contentDOM){l.children=e.children,e.children=[];for(let a of l.children)a.parent=l}return e.destroy(),l}addNode(e,t,s,o,r){let i=Bn.create(this.top,e,t,s,o,r);i.contentDOM&&i.updateChildren(o,r+1),this.top.children.splice(this.index++,0,i),this.changed=!0}placeWidget(e,t,s){let o=this.index<this.top.children.length?this.top.children[this.index]:null;if(o&&o.matchesWidget(e)&&(e==o.widget||!o.widget.type.toDOM.parentNode))this.index++;else{let r=new Mu(this.top,e,t,s);this.top.children.splice(this.index++,0,r),this.changed=!0}}addTextblockHacks(){let e=this.top.children[this.index-1],t=this.top;for(;e instanceof gs;)t=e,e=t.children[t.children.length-1];(!e||!(e instanceof Or)||/\n$/.test(e.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(e.node.text))&&((gt||at)&&e&&e.dom.contentEditable=="false"&&this.addHackNode("IMG",t),this.addHackNode("BR",this.top))}addHackNode(e,t){if(t==this.top&&this.index<t.children.length&&t.children[this.index].matchesHack(e))this.index++;else{let s=document.createElement(e);e=="IMG"&&(s.className="ProseMirror-separator",s.alt=""),e=="BR"&&(s.className="ProseMirror-trailingBreak");let o=new Tu(this.top,[],s,null);t!=this.top?t.children.push(o):t.children.splice(this.index++,0,o),this.changed=!0}}isLocked(e){return this.lock&&(e==this.lock||e.nodeType==1&&e.contains(this.lock.parentNode))}}function Nv(n,e){let t=e,s=t.children.length,o=n.childCount,r=new Map,i=[];e:for(;o>0;){let l;for(;;)if(s){let c=t.children[s-1];if(c instanceof gs)t=c,s=c.children.length;else{l=c,s--;break}}else{if(t==e)break e;s=t.parent.children.indexOf(t),t=t.parent}let a=l.node;if(a){if(a!=n.child(o-1))break;--o,r.set(l,o),i.push(l)}}return{index:o,matched:r,matches:i.reverse()}}function Dv(n,e){return n.type.side-e.type.side}function Av(n,e,t,s){let o=e.locals(n),r=0;if(o.length==0){for(let c=0;c<n.childCount;c++){let u=n.child(c);s(u,o,e.forChild(r,u),c),r+=u.nodeSize}return}let i=0,l=[],a=null;for(let c=0;;){let u,d;for(;i<o.length&&o[i].to==r;){let C=o[i++];C.widget&&(u?(d||(d=[u])).push(C):u=C)}if(u)if(d){d.sort(Dv);for(let C=0;C<d.length;C++)t(d[C],c,!!a)}else t(u,c,!!a);let v,p;if(a)p=-1,v=a,a=null;else if(c<n.childCount)p=c,v=n.child(c++);else break;for(let C=0;C<l.length;C++)l[C].to<=r&&l.splice(C--,1);for(;i<o.length&&o[i].from<=r&&o[i].to>r;)l.push(o[i++]);let b=r+v.nodeSize;if(v.isText){let C=b;i<o.length&&o[i].from<C&&(C=o[i].from);for(let M=0;M<l.length;M++)l[M].to<C&&(C=l[M].to);C<b&&(a=v.cut(C-r),v=v.cut(0,C-r),b=C,p=-1)}else for(;i<o.length&&o[i].to<b;)i++;let w=v.isInline&&!v.isLeaf?l.filter(C=>!C.inline):l.slice();s(v,w,e.forChild(r,v),p),r=b}}function $v(n){if(n.nodeName=="UL"||n.nodeName=="OL"){let e=n.style.cssText;n.style.cssText=e+"; list-style: square !important",window.getComputedStyle(n).listStyle,n.style.cssText=e}}function Pv(n,e,t,s){for(let o=0,r=0;o<n.childCount&&r<=s;){let i=n.child(o++),l=r;if(r+=i.nodeSize,!i.isText)continue;let a=i.text;for(;o<n.childCount;){let c=n.child(o++);if(r+=c.nodeSize,!c.isText)break;a+=c.text}if(r>=t){if(r>=s&&a.slice(s-e.length-l,s-l)==e)return s-e.length;let c=l<s?a.lastIndexOf(e,s-l-1):-1;if(c>=0&&c+e.length+l>=t)return l+c;if(t==s&&a.length>=s+e.length-l&&a.slice(s-l,s-l+e.length)==e)return s}}return-1}function zi(n,e,t,s,o){let r=[];for(let i=0,l=0;i<n.length;i++){let a=n[i],c=l,u=l+=a.size;c>=t||u<=e?r.push(a):(c<e&&r.push(a.slice(0,e-c,s)),o&&(r.push(o),o=void 0),u>t&&r.push(a.slice(t-c,a.size,s)))}return r}function cl(n,e=null){let t=n.domSelectionRange(),s=n.state.doc;if(!t.focusNode)return null;let o=n.docView.nearestDesc(t.focusNode),r=o&&o.size==0,i=n.docView.posFromDOM(t.focusNode,t.focusOffset,1);if(i<0)return null;let l=s.resolve(i),a,c;if(Ir(t)){for(a=i;o&&!o.node;)o=o.parent;let d=o.node;if(o&&d.isAtom&&de.isSelectable(d)&&o.parent&&!(d.isInline&&lv(t.focusNode,t.focusOffset,o.dom))){let v=o.posBefore;c=new de(i==v?l:s.resolve(v))}}else{if(t instanceof n.dom.ownerDocument.defaultView.Selection&&t.rangeCount>1){let d=i,v=i;for(let p=0;p<t.rangeCount;p++){let b=t.getRangeAt(p);d=Math.min(d,n.docView.posFromDOM(b.startContainer,b.startOffset,1)),v=Math.max(v,n.docView.posFromDOM(b.endContainer,b.endOffset,-1))}if(d<0)return null;[a,i]=v==n.state.selection.anchor?[v,d]:[d,v],l=s.resolve(i)}else a=n.docView.posFromDOM(t.anchorNode,t.anchorOffset,1);if(a<0)return null}let u=s.resolve(a);if(!c){let d=e=="pointer"||n.state.selection.head<l.pos&&!r?1:-1;c=ul(n,u,l,d)}return c}function Nu(n){return n.editable?n.hasFocus():Au(n)&&document.activeElement&&document.activeElement.contains(n.dom)}function vn(n,e=!1){let t=n.state.selection;if(Du(n,t),!!Nu(n)){if(!e&&n.input.mouseDown&&n.input.mouseDown.allowDefault&&at){let s=n.domSelectionRange(),o=n.domObserver.currentSelection;if(s.anchorNode&&o.anchorNode&&ms(s.anchorNode,s.anchorOffset,o.anchorNode,o.anchorOffset)){n.input.mouseDown.delayedSelectionSync=!0,n.domObserver.setCurSelection();return}}if(n.domObserver.disconnectSelection(),n.cursorWrapper)Vv(n);else{let{anchor:s,head:o}=t,r,i;Ra&&!(t instanceof ye)&&(t.$from.parent.inlineContent||(r=Va(n,t.from)),!t.empty&&!t.$from.parent.inlineContent&&(i=Va(n,t.to))),n.docView.setSelection(s,o,n,e),Ra&&(r&&La(r),i&&La(i)),t.visible?n.dom.classList.remove("ProseMirror-hideselection"):(n.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&Rv(n))}n.domObserver.setCurSelection(),n.domObserver.connectSelection()}}const Ra=gt||at&&bu<63;function Va(n,e){let{node:t,offset:s}=n.docView.domFromPos(e,0),o=s<t.childNodes.length?t.childNodes[s]:null,r=s?t.childNodes[s-1]:null;if(gt&&o&&o.contentEditable=="false")return mi(o);if((!o||o.contentEditable=="false")&&(!r||r.contentEditable=="false")){if(o)return mi(o);if(r)return mi(r)}}function mi(n){return n.contentEditable="true",gt&&n.draggable&&(n.draggable=!1,n.wasDraggable=!0),n}function La(n){n.contentEditable="false",n.wasDraggable&&(n.draggable=!0,n.wasDraggable=null)}function Rv(n){let e=n.dom.ownerDocument;e.removeEventListener("selectionchange",n.input.hideSelectionGuard);let t=n.domSelectionRange(),s=t.anchorNode,o=t.anchorOffset;e.addEventListener("selectionchange",n.input.hideSelectionGuard=()=>{(t.anchorNode!=s||t.anchorOffset!=o)&&(e.removeEventListener("selectionchange",n.input.hideSelectionGuard),setTimeout(()=>{(!Nu(n)||n.state.selection.visible)&&n.dom.classList.remove("ProseMirror-hideselection")},20))})}function Vv(n){let e=n.domSelection(),t=document.createRange();if(!e)return;let s=n.cursorWrapper.dom,o=s.nodeName=="IMG";o?t.setStart(s.parentNode,et(s)+1):t.setStart(s,0),t.collapse(!0),e.removeAllRanges(),e.addRange(t),!o&&!n.state.selection.visible&&Ct&&Ln<=11&&(s.disabled=!0,s.disabled=!1)}function Du(n,e){if(e instanceof de){let t=n.docView.descAt(e.from);t!=n.lastSelectedViewDesc&&(Ba(n),t&&t.selectNode(),n.lastSelectedViewDesc=t)}else Ba(n)}function Ba(n){n.lastSelectedViewDesc&&(n.lastSelectedViewDesc.parent&&n.lastSelectedViewDesc.deselectNode(),n.lastSelectedViewDesc=void 0)}function ul(n,e,t,s){return n.someProp("createSelectionBetween",o=>o(n,e,t))||ye.between(e,t,s)}function za(n){return n.editable&&!n.hasFocus()?!1:Au(n)}function Au(n){let e=n.domSelectionRange();if(!e.anchorNode)return!1;try{return n.dom.contains(e.anchorNode.nodeType==3?e.anchorNode.parentNode:e.anchorNode)&&(n.editable||n.dom.contains(e.focusNode.nodeType==3?e.focusNode.parentNode:e.focusNode))}catch{return!1}}function Lv(n){let e=n.docView.domFromPos(n.state.selection.anchor,0),t=n.domSelectionRange();return ms(e.node,e.offset,t.anchorNode,t.anchorOffset)}function Fi(n,e){let{$anchor:t,$head:s}=n.selection,o=e>0?t.max(s):t.min(s),r=o.parent.inlineContent?o.depth?n.doc.resolve(e>0?o.after():o.before()):null:o;return r&&xe.findFrom(r,e)}function In(n,e){return n.dispatch(n.state.tr.setSelection(e).scrollIntoView()),!0}function Fa(n,e,t){let s=n.state.selection;if(s instanceof ye)if(t.indexOf("s")>-1){let{$head:o}=s,r=o.textOffset?null:e<0?o.nodeBefore:o.nodeAfter;if(!r||r.isText||!r.isLeaf)return!1;let i=n.state.doc.resolve(o.pos+r.nodeSize*(e<0?-1:1));return In(n,new ye(s.$anchor,i))}else if(s.empty){if(n.endOfTextblock(e>0?"forward":"backward")){let o=Fi(n.state,e);return o&&o instanceof de?In(n,o):!1}else if(!(Vt&&t.indexOf("m")>-1)){let o=s.$head,r=o.textOffset?null:e<0?o.nodeBefore:o.nodeAfter,i;if(!r||r.isText)return!1;let l=e<0?o.pos-r.nodeSize:o.pos;return r.isAtom||(i=n.docView.descAt(l))&&!i.contentDOM?de.isSelectable(r)?In(n,new de(e<0?n.state.doc.resolve(o.pos-r.nodeSize):o)):So?In(n,new ye(n.state.doc.resolve(e<0?l:l+r.nodeSize))):!1:!1}}else return!1;else{if(s instanceof de&&s.node.isInline)return In(n,new ye(e>0?s.$to:s.$from));{let o=Fi(n.state,e);return o?In(n,o):!1}}}function ur(n){return n.nodeType==3?n.nodeValue.length:n.childNodes.length}function lo(n,e){let t=n.pmViewDesc;return t&&t.size==0&&(e<0||n.nextSibling||n.nodeName!="BR")}function Ts(n,e){return e<0?Bv(n):zv(n)}function Bv(n){let e=n.domSelectionRange(),t=e.focusNode,s=e.focusOffset;if(!t)return;let o,r,i=!1;for(jt&&t.nodeType==1&&s<ur(t)&&lo(t.childNodes[s],-1)&&(i=!0);;)if(s>0){if(t.nodeType!=1)break;{let l=t.childNodes[s-1];if(lo(l,-1))o=t,r=--s;else if(l.nodeType==3)t=l,s=t.nodeValue.length;else break}}else{if($u(t))break;{let l=t.previousSibling;for(;l&&lo(l,-1);)o=t.parentNode,r=et(l),l=l.previousSibling;if(l)t=l,s=ur(t);else{if(t=t.parentNode,t==n.dom)break;s=0}}}i?Wi(n,t,s):o&&Wi(n,o,r)}function zv(n){let e=n.domSelectionRange(),t=e.focusNode,s=e.focusOffset;if(!t)return;let o=ur(t),r,i;for(;;)if(s<o){if(t.nodeType!=1)break;let l=t.childNodes[s];if(lo(l,1))r=t,i=++s;else break}else{if($u(t))break;{let l=t.nextSibling;for(;l&&lo(l,1);)r=l.parentNode,i=et(l)+1,l=l.nextSibling;if(l)t=l,s=0,o=ur(t);else{if(t=t.parentNode,t==n.dom)break;s=o=0}}}r&&Wi(n,r,i)}function $u(n){let e=n.pmViewDesc;return e&&e.node&&e.node.isBlock}function Fv(n,e){for(;n&&e==n.childNodes.length&&!xo(n);)e=et(n)+1,n=n.parentNode;for(;n&&e<n.childNodes.length;){let t=n.childNodes[e];if(t.nodeType==3)return t;if(t.nodeType==1&&t.contentEditable=="false")break;n=t,e=0}}function Wv(n,e){for(;n&&!e&&!xo(n);)e=et(n),n=n.parentNode;for(;n&&e;){let t=n.childNodes[e-1];if(t.nodeType==3)return t;if(t.nodeType==1&&t.contentEditable=="false")break;n=t,e=n.childNodes.length}}function Wi(n,e,t){if(e.nodeType!=3){let r,i;(i=Fv(e,t))?(e=i,t=0):(r=Wv(e,t))&&(e=r,t=r.nodeValue.length)}let s=n.domSelection();if(!s)return;if(Ir(s)){let r=document.createRange();r.setEnd(e,t),r.setStart(e,t),s.removeAllRanges(),s.addRange(r)}else s.extend&&s.extend(e,t);n.domObserver.setCurSelection();let{state:o}=n;setTimeout(()=>{n.state==o&&vn(n)},50)}function Wa(n,e){let t=n.state.doc.resolve(e);if(!(at||uv)&&t.parent.inlineContent){let o=n.coordsAtPos(e);if(e>t.start()){let r=n.coordsAtPos(e-1),i=(r.top+r.bottom)/2;if(i>o.top&&i<o.bottom&&Math.abs(r.left-o.left)>1)return r.left<o.left?"ltr":"rtl"}if(e<t.end()){let r=n.coordsAtPos(e+1),i=(r.top+r.bottom)/2;if(i>o.top&&i<o.bottom&&Math.abs(r.left-o.left)>1)return r.left>o.left?"ltr":"rtl"}}return getComputedStyle(n.dom).direction=="rtl"?"rtl":"ltr"}function Ha(n,e,t){let s=n.state.selection;if(s instanceof ye&&!s.empty||t.indexOf("s")>-1||Vt&&t.indexOf("m")>-1)return!1;let{$from:o,$to:r}=s;if(!o.parent.inlineContent||n.endOfTextblock(e<0?"up":"down")){let i=Fi(n.state,e);if(i&&i instanceof de)return In(n,i)}if(!o.parent.inlineContent){let i=e<0?o:r,l=s instanceof Ot?xe.near(i,e):xe.findFrom(i,e);return l?In(n,l):!1}return!1}function qa(n,e){if(!(n.state.selection instanceof ye))return!0;let{$head:t,$anchor:s,empty:o}=n.state.selection;if(!t.sameParent(s))return!0;if(!o)return!1;if(n.endOfTextblock(e>0?"forward":"backward"))return!0;let r=!t.textOffset&&(e<0?t.nodeBefore:t.nodeAfter);if(r&&!r.isText){let i=n.state.tr;return e<0?i.delete(t.pos-r.nodeSize,t.pos):i.delete(t.pos,t.pos+r.nodeSize),n.dispatch(i),!0}return!1}function Ja(n,e,t){n.domObserver.stop(),e.contentEditable=t,n.domObserver.start()}function Hv(n){if(!gt||n.state.selection.$head.parentOffset>0)return!1;let{focusNode:e,focusOffset:t}=n.domSelectionRange();if(e&&e.nodeType==1&&t==0&&e.firstChild&&e.firstChild.contentEditable=="false"){let s=e.firstChild;Ja(n,s,"true"),setTimeout(()=>Ja(n,s,"false"),20)}return!1}function qv(n){let e="";return n.ctrlKey&&(e+="c"),n.metaKey&&(e+="m"),n.altKey&&(e+="a"),n.shiftKey&&(e+="s"),e}function Jv(n,e){let t=e.keyCode,s=qv(e);if(t==8||Vt&&t==72&&s=="c")return qa(n,-1)||Ts(n,-1);if(t==46&&!e.shiftKey||Vt&&t==68&&s=="c")return qa(n,1)||Ts(n,1);if(t==13||t==27)return!0;if(t==37||Vt&&t==66&&s=="c"){let o=t==37?Wa(n,n.state.selection.from)=="ltr"?-1:1:-1;return Fa(n,o,s)||Ts(n,o)}else if(t==39||Vt&&t==70&&s=="c"){let o=t==39?Wa(n,n.state.selection.from)=="ltr"?1:-1:1;return Fa(n,o,s)||Ts(n,o)}else{if(t==38||Vt&&t==80&&s=="c")return Ha(n,-1,s)||Ts(n,-1);if(t==40||Vt&&t==78&&s=="c")return Hv(n)||Ha(n,1,s)||Ts(n,1);if(s==(Vt?"m":"c")&&(t==66||t==73||t==89||t==90))return!0}return!1}function dl(n,e){n.someProp("transformCopied",p=>{e=p(e,n)});let t=[],{content:s,openStart:o,openEnd:r}=e;for(;o>1&&r>1&&s.childCount==1&&s.firstChild.childCount==1;){o--,r--;let p=s.firstChild;t.push(p.type.name,p.attrs!=p.type.defaultAttrs?p.attrs:null),s=p.content}let i=n.someProp("clipboardSerializer")||ys.fromSchema(n.state.schema),l=zu(),a=l.createElement("div");a.appendChild(i.serializeFragment(s,{document:l}));let c=a.firstChild,u,d=0;for(;c&&c.nodeType==1&&(u=Bu[c.nodeName.toLowerCase()]);){for(let p=u.length-1;p>=0;p--){let b=l.createElement(u[p]);for(;a.firstChild;)b.appendChild(a.firstChild);a.appendChild(b),d++}c=a.firstChild}c&&c.nodeType==1&&c.setAttribute("data-pm-slice",`${o} ${r}${d?` -${d}`:""} ${JSON.stringify(t)}`);let v=n.someProp("clipboardTextSerializer",p=>p(e,n))||e.content.textBetween(0,e.content.size,`

`);return{dom:a,text:v,slice:e}}function Pu(n,e,t,s,o){let r=o.parent.type.spec.code,i,l;if(!t&&!e)return null;let a=e&&(s||r||!t);if(a){if(n.someProp("transformPastedText",v=>{e=v(e,r||s,n)}),r)return e?new le(Q.from(n.state.schema.text(e.replace(/\r\n?/g,`
`))),0,0):le.empty;let d=n.someProp("clipboardTextParser",v=>v(e,o,s,n));if(d)l=d;else{let v=o.marks(),{schema:p}=n.state,b=ys.fromSchema(p);i=document.createElement("div"),e.split(/(?:\r\n?|\n)+/).forEach(w=>{let C=i.appendChild(document.createElement("p"));w&&C.appendChild(b.serializeNode(p.text(w,v)))})}}else n.someProp("transformPastedHTML",d=>{t=d(t,n)}),i=Xv(t),So&&Yv(i);let c=i&&i.querySelector("[data-pm-slice]"),u=c&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(c.getAttribute("data-pm-slice")||"");if(u&&u[3])for(let d=+u[3];d>0;d--){let v=i.firstChild;for(;v&&v.nodeType!=1;)v=v.nextSibling;if(!v)break;i=v}if(l||(l=(n.someProp("clipboardParser")||n.someProp("domParser")||Vn.fromSchema(n.state.schema)).parseSlice(i,{preserveWhitespace:!!(a||u),context:o,ruleFromNode(v){return v.nodeName=="BR"&&!v.nextSibling&&v.parentNode&&!Uv.test(v.parentNode.nodeName)?{ignore:!0}:null}})),u)l=Gv(Ua(l,+u[1],+u[2]),u[4]);else if(l=le.maxOpen(jv(l.content,o),!0),l.openStart||l.openEnd){let d=0,v=0;for(let p=l.content.firstChild;d<l.openStart&&!p.type.spec.isolating;d++,p=p.firstChild);for(let p=l.content.lastChild;v<l.openEnd&&!p.type.spec.isolating;v++,p=p.lastChild);l=Ua(l,d,v)}return n.someProp("transformPasted",d=>{l=d(l,n)}),l}const Uv=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function jv(n,e){if(n.childCount<2)return n;for(let t=e.depth;t>=0;t--){let o=e.node(t).contentMatchAt(e.index(t)),r,i=[];if(n.forEach(l=>{if(!i)return;let a=o.findWrapping(l.type),c;if(!a)return i=null;if(c=i.length&&r.length&&Vu(a,r,l,i[i.length-1],0))i[i.length-1]=c;else{i.length&&(i[i.length-1]=Lu(i[i.length-1],r.length));let u=Ru(l,a);i.push(u),o=o.matchType(u.type),r=a}}),i)return Q.from(i)}return n}function Ru(n,e,t=0){for(let s=e.length-1;s>=t;s--)n=e[s].create(null,Q.from(n));return n}function Vu(n,e,t,s,o){if(o<n.length&&o<e.length&&n[o]==e[o]){let r=Vu(n,e,t,s.lastChild,o+1);if(r)return s.copy(s.content.replaceChild(s.childCount-1,r));if(s.contentMatchAt(s.childCount).matchType(o==n.length-1?t.type:n[o+1]))return s.copy(s.content.append(Q.from(Ru(t,n,o+1))))}}function Lu(n,e){if(e==0)return n;let t=n.content.replaceChild(n.childCount-1,Lu(n.lastChild,e-1)),s=n.contentMatchAt(n.childCount).fillBefore(Q.empty,!0);return n.copy(t.append(s))}function Hi(n,e,t,s,o,r){let i=e<0?n.firstChild:n.lastChild,l=i.content;return n.childCount>1&&(r=0),o<s-1&&(l=Hi(l,e,t,s,o+1,r)),o>=t&&(l=e<0?i.contentMatchAt(0).fillBefore(l,r<=o).append(l):l.append(i.contentMatchAt(i.childCount).fillBefore(Q.empty,!0))),n.replaceChild(e<0?0:n.childCount-1,i.copy(l))}function Ua(n,e,t){return e<n.openStart&&(n=new le(Hi(n.content,-1,e,n.openStart,0,n.openEnd),e,n.openEnd)),t<n.openEnd&&(n=new le(Hi(n.content,1,t,n.openEnd,0,0),n.openStart,t)),n}const Bu={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]};let ja=null;function zu(){return ja||(ja=document.implementation.createHTMLDocument("title"))}let gi=null;function Kv(n){let e=window.trustedTypes;return e?(gi||(gi=e.createPolicy("ProseMirrorClipboard",{createHTML:t=>t})),gi.createHTML(n)):n}function Xv(n){let e=/^(\s*<meta [^>]*>)*/.exec(n);e&&(n=n.slice(e[0].length));let t=zu().createElement("div"),s=/<([a-z][^>\s]+)/i.exec(n),o;if((o=s&&Bu[s[1].toLowerCase()])&&(n=o.map(r=>"<"+r+">").join("")+n+o.map(r=>"</"+r+">").reverse().join("")),t.innerHTML=Kv(n),o)for(let r=0;r<o.length;r++)t=t.querySelector(o[r])||t;return t}function Yv(n){let e=n.querySelectorAll(at?"span:not([class]):not([style])":"span.Apple-converted-space");for(let t=0;t<e.length;t++){let s=e[t];s.childNodes.length==1&&s.textContent==" "&&s.parentNode&&s.parentNode.replaceChild(n.ownerDocument.createTextNode(" "),s)}}function Gv(n,e){if(!n.size)return n;let t=n.content.firstChild.type.schema,s;try{s=JSON.parse(e)}catch{return n}let{content:o,openStart:r,openEnd:i}=n;for(let l=s.length-2;l>=0;l-=2){let a=t.nodes[s[l]];if(!a||a.hasRequiredAttrs())break;o=Q.from(a.create(s[l+1],o)),r++,i++}return new le(o,r,i)}const vt={},yt={},Qv={touchstart:!0,touchmove:!0};class Zv{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:""},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastChromeDelete=0,this.composing=!1,this.compositionNode=null,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.compositionID=1,this.compositionPendingChanges=0,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}}function ey(n){for(let e in vt){let t=vt[e];n.dom.addEventListener(e,n.input.eventHandlers[e]=s=>{ny(n,s)&&!fl(n,s)&&(n.editable||!(s.type in yt))&&t(n,s)},Qv[e]?{passive:!0}:void 0)}gt&&n.dom.addEventListener("input",()=>null),qi(n)}function Pn(n,e){n.input.lastSelectionOrigin=e,n.input.lastSelectionTime=Date.now()}function ty(n){n.domObserver.stop();for(let e in n.input.eventHandlers)n.dom.removeEventListener(e,n.input.eventHandlers[e]);clearTimeout(n.input.composingTimeout),clearTimeout(n.input.lastIOSEnterFallbackTimeout)}function qi(n){n.someProp("handleDOMEvents",e=>{for(let t in e)n.input.eventHandlers[t]||n.dom.addEventListener(t,n.input.eventHandlers[t]=s=>fl(n,s))})}function fl(n,e){return n.someProp("handleDOMEvents",t=>{let s=t[e.type];return s?s(n,e)||e.defaultPrevented:!1})}function ny(n,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let t=e.target;t!=n.dom;t=t.parentNode)if(!t||t.nodeType==11||t.pmViewDesc&&t.pmViewDesc.stopEvent(e))return!1;return!0}function sy(n,e){!fl(n,e)&&vt[e.type]&&(n.editable||!(e.type in yt))&&vt[e.type](n,e)}yt.keydown=(n,e)=>{let t=e;if(n.input.shiftKey=t.keyCode==16||t.shiftKey,!Wu(n,t)&&(n.input.lastKeyCode=t.keyCode,n.input.lastKeyCodeTime=Date.now(),!(mn&&at&&t.keyCode==13)))if(t.keyCode!=229&&n.domObserver.forceFlush(),Fs&&t.keyCode==13&&!t.ctrlKey&&!t.altKey&&!t.metaKey){let s=Date.now();n.input.lastIOSEnter=s,n.input.lastIOSEnterFallbackTimeout=setTimeout(()=>{n.input.lastIOSEnter==s&&(n.someProp("handleKeyDown",o=>o(n,ts(13,"Enter"))),n.input.lastIOSEnter=0)},200)}else n.someProp("handleKeyDown",s=>s(n,t))||Jv(n,t)?t.preventDefault():Pn(n,"key")};yt.keyup=(n,e)=>{e.keyCode==16&&(n.input.shiftKey=!1)};yt.keypress=(n,e)=>{let t=e;if(Wu(n,t)||!t.charCode||t.ctrlKey&&!t.altKey||Vt&&t.metaKey)return;if(n.someProp("handleKeyPress",o=>o(n,t))){t.preventDefault();return}let s=n.state.selection;if(!(s instanceof ye)||!s.$from.sameParent(s.$to)){let o=String.fromCharCode(t.charCode);!/[\r\n]/.test(o)&&!n.someProp("handleTextInput",r=>r(n,s.$from.pos,s.$to.pos,o))&&n.dispatch(n.state.tr.insertText(o).scrollIntoView()),t.preventDefault()}};function Nr(n){return{left:n.clientX,top:n.clientY}}function oy(n,e){let t=e.x-n.clientX,s=e.y-n.clientY;return t*t+s*s<100}function pl(n,e,t,s,o){if(s==-1)return!1;let r=n.state.doc.resolve(s);for(let i=r.depth+1;i>0;i--)if(n.someProp(e,l=>i>r.depth?l(n,t,r.nodeAfter,r.before(i),o,!0):l(n,t,r.node(i),r.before(i),o,!1)))return!0;return!1}function Ps(n,e,t){if(n.focused||n.focus(),n.state.selection.eq(e))return;let s=n.state.tr.setSelection(e);s.setMeta("pointer",!0),n.dispatch(s)}function ry(n,e){if(e==-1)return!1;let t=n.state.doc.resolve(e),s=t.nodeAfter;return s&&s.isAtom&&de.isSelectable(s)?(Ps(n,new de(t)),!0):!1}function iy(n,e){if(e==-1)return!1;let t=n.state.selection,s,o;t instanceof de&&(s=t.node);let r=n.state.doc.resolve(e);for(let i=r.depth+1;i>0;i--){let l=i>r.depth?r.nodeAfter:r.node(i);if(de.isSelectable(l)){s&&t.$from.depth>0&&i>=t.$from.depth&&r.before(t.$from.depth+1)==t.$from.pos?o=r.before(t.$from.depth):o=r.before(i);break}}return o!=null?(Ps(n,de.create(n.state.doc,o)),!0):!1}function ly(n,e,t,s,o){return pl(n,"handleClickOn",e,t,s)||n.someProp("handleClick",r=>r(n,e,s))||(o?iy(n,t):ry(n,t))}function ay(n,e,t,s){return pl(n,"handleDoubleClickOn",e,t,s)||n.someProp("handleDoubleClick",o=>o(n,e,s))}function cy(n,e,t,s){return pl(n,"handleTripleClickOn",e,t,s)||n.someProp("handleTripleClick",o=>o(n,e,s))||uy(n,t,s)}function uy(n,e,t){if(t.button!=0)return!1;let s=n.state.doc;if(e==-1)return s.inlineContent?(Ps(n,ye.create(s,0,s.content.size)),!0):!1;let o=s.resolve(e);for(let r=o.depth+1;r>0;r--){let i=r>o.depth?o.nodeAfter:o.node(r),l=o.before(r);if(i.inlineContent)Ps(n,ye.create(s,l+1,l+1+i.content.size));else if(de.isSelectable(i))Ps(n,de.create(s,l));else continue;return!0}}function hl(n){return dr(n)}const Fu=Vt?"metaKey":"ctrlKey";vt.mousedown=(n,e)=>{let t=e;n.input.shiftKey=t.shiftKey;let s=hl(n),o=Date.now(),r="singleClick";o-n.input.lastClick.time<500&&oy(t,n.input.lastClick)&&!t[Fu]&&(n.input.lastClick.type=="singleClick"?r="doubleClick":n.input.lastClick.type=="doubleClick"&&(r="tripleClick")),n.input.lastClick={time:o,x:t.clientX,y:t.clientY,type:r};let i=n.posAtCoords(Nr(t));i&&(r=="singleClick"?(n.input.mouseDown&&n.input.mouseDown.done(),n.input.mouseDown=new dy(n,i,t,!!s)):(r=="doubleClick"?ay:cy)(n,i.pos,i.inside,t)?t.preventDefault():Pn(n,"pointer"))};class dy{constructor(e,t,s,o){this.view=e,this.pos=t,this.event=s,this.flushed=o,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=e.state.doc,this.selectNode=!!s[Fu],this.allowDefault=s.shiftKey;let r,i;if(t.inside>-1)r=e.state.doc.nodeAt(t.inside),i=t.inside;else{let u=e.state.doc.resolve(t.pos);r=u.parent,i=u.depth?u.before():0}const l=o?null:s.target,a=l?e.docView.nearestDesc(l,!0):null;this.target=a&&a.dom.nodeType==1?a.dom:null;let{selection:c}=e.state;(s.button==0&&r.type.spec.draggable&&r.type.spec.selectable!==!1||c instanceof de&&c.from<=i&&c.to>i)&&(this.mightDrag={node:r,pos:i,addAttr:!!(this.target&&!this.target.draggable),setUneditable:!!(this.target&&jt&&!this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout(()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")},20),this.view.domObserver.start()),e.root.addEventListener("mouseup",this.up=this.up.bind(this)),e.root.addEventListener("mousemove",this.move=this.move.bind(this)),Pn(e,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout(()=>vn(this.view)),this.view.input.mouseDown=null}up(e){if(this.done(),!this.view.dom.contains(e.target))return;let t=this.pos;this.view.state.doc!=this.startDoc&&(t=this.view.posAtCoords(Nr(e))),this.updateAllowDefault(e),this.allowDefault||!t?Pn(this.view,"pointer"):ly(this.view,t.pos,t.inside,e,this.selectNode)?e.preventDefault():e.button==0&&(this.flushed||gt&&this.mightDrag&&!this.mightDrag.node.isAtom||at&&!this.view.state.selection.visible&&Math.min(Math.abs(t.pos-this.view.state.selection.from),Math.abs(t.pos-this.view.state.selection.to))<=2)?(Ps(this.view,xe.near(this.view.state.doc.resolve(t.pos))),e.preventDefault()):Pn(this.view,"pointer")}move(e){this.updateAllowDefault(e),Pn(this.view,"pointer"),e.buttons==0&&this.done()}updateAllowDefault(e){!this.allowDefault&&(Math.abs(this.event.x-e.clientX)>4||Math.abs(this.event.y-e.clientY)>4)&&(this.allowDefault=!0)}}vt.touchstart=n=>{n.input.lastTouch=Date.now(),hl(n),Pn(n,"pointer")};vt.touchmove=n=>{n.input.lastTouch=Date.now(),Pn(n,"pointer")};vt.contextmenu=n=>hl(n);function Wu(n,e){return n.composing?!0:gt&&Math.abs(e.timeStamp-n.input.compositionEndedAt)<500?(n.input.compositionEndedAt=-2e8,!0):!1}const fy=mn?5e3:-1;yt.compositionstart=yt.compositionupdate=n=>{if(!n.composing){n.domObserver.flush();let{state:e}=n,t=e.selection.$to;if(e.selection instanceof ye&&(e.storedMarks||!t.textOffset&&t.parentOffset&&t.nodeBefore.marks.some(s=>s.type.spec.inclusive===!1)))n.markCursor=n.state.storedMarks||t.marks(),dr(n,!0),n.markCursor=null;else if(dr(n,!e.selection.empty),jt&&e.selection.empty&&t.parentOffset&&!t.textOffset&&t.nodeBefore.marks.length){let s=n.domSelectionRange();for(let o=s.focusNode,r=s.focusOffset;o&&o.nodeType==1&&r!=0;){let i=r<0?o.lastChild:o.childNodes[r-1];if(!i)break;if(i.nodeType==3){let l=n.domSelection();l&&l.collapse(i,i.nodeValue.length);break}else o=i,r=-1}}n.input.composing=!0}Hu(n,fy)};yt.compositionend=(n,e)=>{n.composing&&(n.input.composing=!1,n.input.compositionEndedAt=e.timeStamp,n.input.compositionPendingChanges=n.domObserver.pendingRecords().length?n.input.compositionID:0,n.input.compositionNode=null,n.input.compositionPendingChanges&&Promise.resolve().then(()=>n.domObserver.flush()),n.input.compositionID++,Hu(n,20))};function Hu(n,e){clearTimeout(n.input.composingTimeout),e>-1&&(n.input.composingTimeout=setTimeout(()=>dr(n),e))}function qu(n){for(n.composing&&(n.input.composing=!1,n.input.compositionEndedAt=hy());n.input.compositionNodes.length>0;)n.input.compositionNodes.pop().markParentsDirty()}function py(n){let e=n.domSelectionRange();if(!e.focusNode)return null;let t=rv(e.focusNode,e.focusOffset),s=iv(e.focusNode,e.focusOffset);if(t&&s&&t!=s){let o=s.pmViewDesc,r=n.domObserver.lastChangedTextNode;if(t==r||s==r)return r;if(!o||!o.isText(s.nodeValue))return s;if(n.input.compositionNode==s){let i=t.pmViewDesc;if(!(!i||!i.isText(t.nodeValue)))return s}}return t||s}function hy(){let n=document.createEvent("Event");return n.initEvent("event",!0,!0),n.timeStamp}function dr(n,e=!1){if(!(mn&&n.domObserver.flushingSoon>=0)){if(n.domObserver.forceFlush(),qu(n),e||n.docView&&n.docView.dirty){let t=cl(n);return t&&!t.eq(n.state.selection)?n.dispatch(n.state.tr.setSelection(t)):(n.markCursor||e)&&!n.state.selection.empty?n.dispatch(n.state.tr.deleteSelection()):n.updateState(n.state),!0}return!1}}function my(n,e){if(!n.dom.parentNode)return;let t=n.dom.parentNode.appendChild(document.createElement("div"));t.appendChild(e),t.style.cssText="position: fixed; left: -10000px; top: 10px";let s=getSelection(),o=document.createRange();o.selectNodeContents(e),n.dom.blur(),s.removeAllRanges(),s.addRange(o),setTimeout(()=>{t.parentNode&&t.parentNode.removeChild(t),n.focus()},50)}const po=Ct&&Ln<15||Fs&&dv<604;vt.copy=yt.cut=(n,e)=>{let t=e,s=n.state.selection,o=t.type=="cut";if(s.empty)return;let r=po?null:t.clipboardData,i=s.content(),{dom:l,text:a}=dl(n,i);r?(t.preventDefault(),r.clearData(),r.setData("text/html",l.innerHTML),r.setData("text/plain",a)):my(n,l),o&&n.dispatch(n.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))};function gy(n){return n.openStart==0&&n.openEnd==0&&n.content.childCount==1?n.content.firstChild:null}function vy(n,e){if(!n.dom.parentNode)return;let t=n.input.shiftKey||n.state.selection.$from.parent.type.spec.code,s=n.dom.parentNode.appendChild(document.createElement(t?"textarea":"div"));t||(s.contentEditable="true"),s.style.cssText="position: fixed; left: -10000px; top: 10px",s.focus();let o=n.input.shiftKey&&n.input.lastKeyCode!=45;setTimeout(()=>{n.focus(),s.parentNode&&s.parentNode.removeChild(s),t?ho(n,s.value,null,o,e):ho(n,s.textContent,s.innerHTML,o,e)},50)}function ho(n,e,t,s,o){let r=Pu(n,e,t,s,n.state.selection.$from);if(n.someProp("handlePaste",a=>a(n,o,r||le.empty)))return!0;if(!r)return!1;let i=gy(r),l=i?n.state.tr.replaceSelectionWith(i,s):n.state.tr.replaceSelection(r);return n.dispatch(l.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}function Ju(n){let e=n.getData("text/plain")||n.getData("Text");if(e)return e;let t=n.getData("text/uri-list");return t?t.replace(/\r?\n/g," "):""}yt.paste=(n,e)=>{let t=e;if(n.composing&&!mn)return;let s=po?null:t.clipboardData,o=n.input.shiftKey&&n.input.lastKeyCode!=45;s&&ho(n,Ju(s),s.getData("text/html"),o,t)?t.preventDefault():vy(n,t)};class Uu{constructor(e,t,s){this.slice=e,this.move=t,this.node=s}}const ju=Vt?"altKey":"ctrlKey";vt.dragstart=(n,e)=>{let t=e,s=n.input.mouseDown;if(s&&s.done(),!t.dataTransfer)return;let o=n.state.selection,r=o.empty?null:n.posAtCoords(Nr(t)),i;if(!(r&&r.pos>=o.from&&r.pos<=(o instanceof de?o.to-1:o.to))){if(s&&s.mightDrag)i=de.create(n.state.doc,s.mightDrag.pos);else if(t.target&&t.target.nodeType==1){let d=n.docView.nearestDesc(t.target,!0);d&&d.node.type.spec.draggable&&d!=n.docView&&(i=de.create(n.state.doc,d.posBefore))}}let l=(i||n.state.selection).content(),{dom:a,text:c,slice:u}=dl(n,l);(!t.dataTransfer.files.length||!at||bu>120)&&t.dataTransfer.clearData(),t.dataTransfer.setData(po?"Text":"text/html",a.innerHTML),t.dataTransfer.effectAllowed="copyMove",po||t.dataTransfer.setData("text/plain",c),n.dragging=new Uu(u,!t[ju],i)};vt.dragend=n=>{let e=n.dragging;window.setTimeout(()=>{n.dragging==e&&(n.dragging=null)},50)};yt.dragover=yt.dragenter=(n,e)=>e.preventDefault();yt.drop=(n,e)=>{let t=e,s=n.dragging;if(n.dragging=null,!t.dataTransfer)return;let o=n.posAtCoords(Nr(t));if(!o)return;let r=n.state.doc.resolve(o.pos),i=s&&s.slice;i?n.someProp("transformPasted",b=>{i=b(i,n)}):i=Pu(n,Ju(t.dataTransfer),po?null:t.dataTransfer.getData("text/html"),!1,r);let l=!!(s&&!t[ju]);if(n.someProp("handleDrop",b=>b(n,t,i||le.empty,l))){t.preventDefault();return}if(!i)return;t.preventDefault();let a=i?du(n.state.doc,r.pos,i):r.pos;a==null&&(a=r.pos);let c=n.state.tr;if(l){let{node:b}=s;b?b.replace(c):c.deleteSelection()}let u=c.mapping.map(a),d=i.openStart==0&&i.openEnd==0&&i.content.childCount==1,v=c.doc;if(d?c.replaceRangeWith(u,u,i.content.firstChild):c.replaceRange(u,u,i),c.doc.eq(v))return;let p=c.doc.resolve(u);if(d&&de.isSelectable(i.content.firstChild)&&p.nodeAfter&&p.nodeAfter.sameMarkup(i.content.firstChild))c.setSelection(new de(p));else{let b=c.mapping.map(a);c.mapping.maps[c.mapping.maps.length-1].forEach((w,C,M,X)=>b=X),c.setSelection(ul(n,p,c.doc.resolve(b)))}n.focus(),n.dispatch(c.setMeta("uiEvent","drop"))};vt.focus=n=>{n.input.lastFocus=Date.now(),n.focused||(n.domObserver.stop(),n.dom.classList.add("ProseMirror-focused"),n.domObserver.start(),n.focused=!0,setTimeout(()=>{n.docView&&n.hasFocus()&&!n.domObserver.currentSelection.eq(n.domSelectionRange())&&vn(n)},20))};vt.blur=(n,e)=>{let t=e;n.focused&&(n.domObserver.stop(),n.dom.classList.remove("ProseMirror-focused"),n.domObserver.start(),t.relatedTarget&&n.dom.contains(t.relatedTarget)&&n.domObserver.currentSelection.clear(),n.focused=!1)};vt.beforeinput=(n,e)=>{if(at&&mn&&e.inputType=="deleteContentBackward"){n.domObserver.flushSoon();let{domChangeCount:s}=n.input;setTimeout(()=>{if(n.input.domChangeCount!=s||(n.dom.blur(),n.focus(),n.someProp("handleKeyDown",r=>r(n,ts(8,"Backspace")))))return;let{$cursor:o}=n.state.selection;o&&o.pos>0&&n.dispatch(n.state.tr.delete(o.pos-1,o.pos).scrollIntoView())},50)}};for(let n in yt)vt[n]=yt[n];function mo(n,e){if(n==e)return!0;for(let t in n)if(n[t]!==e[t])return!1;for(let t in e)if(!(t in n))return!1;return!0}class fr{constructor(e,t){this.toDOM=e,this.spec=t||cs,this.side=this.spec.side||0}map(e,t,s,o){let{pos:r,deleted:i}=e.mapResult(t.from+o,this.side<0?-1:1);return i?null:new mt(r-s,r-s,this)}valid(){return!0}eq(e){return this==e||e instanceof fr&&(this.spec.key&&this.spec.key==e.spec.key||this.toDOM==e.toDOM&&mo(this.spec,e.spec))}destroy(e){this.spec.destroy&&this.spec.destroy(e)}}class zn{constructor(e,t){this.attrs=e,this.spec=t||cs}map(e,t,s,o){let r=e.map(t.from+o,this.spec.inclusiveStart?-1:1)-s,i=e.map(t.to+o,this.spec.inclusiveEnd?1:-1)-s;return r>=i?null:new mt(r,i,this)}valid(e,t){return t.from<t.to}eq(e){return this==e||e instanceof zn&&mo(this.attrs,e.attrs)&&mo(this.spec,e.spec)}static is(e){return e.type instanceof zn}destroy(){}}class ml{constructor(e,t){this.attrs=e,this.spec=t||cs}map(e,t,s,o){let r=e.mapResult(t.from+o,1);if(r.deleted)return null;let i=e.mapResult(t.to+o,-1);return i.deleted||i.pos<=r.pos?null:new mt(r.pos-s,i.pos-s,this)}valid(e,t){let{index:s,offset:o}=e.content.findIndex(t.from),r;return o==t.from&&!(r=e.child(s)).isText&&o+r.nodeSize==t.to}eq(e){return this==e||e instanceof ml&&mo(this.attrs,e.attrs)&&mo(this.spec,e.spec)}destroy(){}}class mt{constructor(e,t,s){this.from=e,this.to=t,this.type=s}copy(e,t){return new mt(e,t,this.type)}eq(e,t=0){return this.type.eq(e.type)&&this.from+t==e.from&&this.to+t==e.to}map(e,t,s){return this.type.map(e,this,t,s)}static widget(e,t,s){return new mt(e,e,new fr(t,s))}static inline(e,t,s,o){return new mt(e,t,new zn(s,o))}static node(e,t,s,o){return new mt(e,t,new ml(s,o))}get spec(){return this.type.spec}get inline(){return this.type instanceof zn}get widget(){return this.type instanceof fr}}const Os=[],cs={};class Be{constructor(e,t){this.local=e.length?e:Os,this.children=t.length?t:Os}static create(e,t){return t.length?pr(t,e,0,cs):it}find(e,t,s){let o=[];return this.findInner(e??0,t??1e9,o,0,s),o}findInner(e,t,s,o,r){for(let i=0;i<this.local.length;i++){let l=this.local[i];l.from<=t&&l.to>=e&&(!r||r(l.spec))&&s.push(l.copy(l.from+o,l.to+o))}for(let i=0;i<this.children.length;i+=3)if(this.children[i]<t&&this.children[i+1]>e){let l=this.children[i]+1;this.children[i+2].findInner(e-l,t-l,s,o+l,r)}}map(e,t,s){return this==it||e.maps.length==0?this:this.mapInner(e,t,0,0,s||cs)}mapInner(e,t,s,o,r){let i;for(let l=0;l<this.local.length;l++){let a=this.local[l].map(e,s,o);a&&a.type.valid(t,a)?(i||(i=[])).push(a):r.onRemove&&r.onRemove(this.local[l].spec)}return this.children.length?yy(this.children,i||[],e,t,s,o,r):i?new Be(i.sort(us),Os):it}add(e,t){return t.length?this==it?Be.create(e,t):this.addInner(e,t,0):this}addInner(e,t,s){let o,r=0;e.forEach((l,a)=>{let c=a+s,u;if(u=Xu(t,l,c)){for(o||(o=this.children.slice());r<o.length&&o[r]<a;)r+=3;o[r]==a?o[r+2]=o[r+2].addInner(l,u,c+1):o.splice(r,0,a,a+l.nodeSize,pr(u,l,c+1,cs)),r+=3}});let i=Ku(r?Yu(t):t,-s);for(let l=0;l<i.length;l++)i[l].type.valid(e,i[l])||i.splice(l--,1);return new Be(i.length?this.local.concat(i).sort(us):this.local,o||this.children)}remove(e){return e.length==0||this==it?this:this.removeInner(e,0)}removeInner(e,t){let s=this.children,o=this.local;for(let r=0;r<s.length;r+=3){let i,l=s[r]+t,a=s[r+1]+t;for(let u=0,d;u<e.length;u++)(d=e[u])&&d.from>l&&d.to<a&&(e[u]=null,(i||(i=[])).push(d));if(!i)continue;s==this.children&&(s=this.children.slice());let c=s[r+2].removeInner(i,l+1);c!=it?s[r+2]=c:(s.splice(r,3),r-=3)}if(o.length){for(let r=0,i;r<e.length;r++)if(i=e[r])for(let l=0;l<o.length;l++)o[l].eq(i,t)&&(o==this.local&&(o=this.local.slice()),o.splice(l--,1))}return s==this.children&&o==this.local?this:o.length||s.length?new Be(o,s):it}forChild(e,t){if(this==it)return this;if(t.isLeaf)return Be.empty;let s,o;for(let l=0;l<this.children.length;l+=3)if(this.children[l]>=e){this.children[l]==e&&(s=this.children[l+2]);break}let r=e+1,i=r+t.content.size;for(let l=0;l<this.local.length;l++){let a=this.local[l];if(a.from<i&&a.to>r&&a.type instanceof zn){let c=Math.max(r,a.from)-r,u=Math.min(i,a.to)-r;c<u&&(o||(o=[])).push(a.copy(c,u))}}if(o){let l=new Be(o.sort(us),Os);return s?new Dn([l,s]):l}return s||it}eq(e){if(this==e)return!0;if(!(e instanceof Be)||this.local.length!=e.local.length||this.children.length!=e.children.length)return!1;for(let t=0;t<this.local.length;t++)if(!this.local[t].eq(e.local[t]))return!1;for(let t=0;t<this.children.length;t+=3)if(this.children[t]!=e.children[t]||this.children[t+1]!=e.children[t+1]||!this.children[t+2].eq(e.children[t+2]))return!1;return!0}locals(e){return gl(this.localsInner(e))}localsInner(e){if(this==it)return Os;if(e.inlineContent||!this.local.some(zn.is))return this.local;let t=[];for(let s=0;s<this.local.length;s++)this.local[s].type instanceof zn||t.push(this.local[s]);return t}forEachSet(e){e(this)}}Be.empty=new Be([],[]);Be.removeOverlap=gl;const it=Be.empty;class Dn{constructor(e){this.members=e}map(e,t){const s=this.members.map(o=>o.map(e,t,cs));return Dn.from(s)}forChild(e,t){if(t.isLeaf)return Be.empty;let s=[];for(let o=0;o<this.members.length;o++){let r=this.members[o].forChild(e,t);r!=it&&(r instanceof Dn?s=s.concat(r.members):s.push(r))}return Dn.from(s)}eq(e){if(!(e instanceof Dn)||e.members.length!=this.members.length)return!1;for(let t=0;t<this.members.length;t++)if(!this.members[t].eq(e.members[t]))return!1;return!0}locals(e){let t,s=!0;for(let o=0;o<this.members.length;o++){let r=this.members[o].localsInner(e);if(r.length)if(!t)t=r;else{s&&(t=t.slice(),s=!1);for(let i=0;i<r.length;i++)t.push(r[i])}}return t?gl(s?t:t.sort(us)):Os}static from(e){switch(e.length){case 0:return it;case 1:return e[0];default:return new Dn(e.every(t=>t instanceof Be)?e:e.reduce((t,s)=>t.concat(s instanceof Be?s:s.members),[]))}}forEachSet(e){for(let t=0;t<this.members.length;t++)this.members[t].forEachSet(e)}}function yy(n,e,t,s,o,r,i){let l=n.slice();for(let c=0,u=r;c<t.maps.length;c++){let d=0;t.maps[c].forEach((v,p,b,w)=>{let C=w-b-(p-v);for(let M=0;M<l.length;M+=3){let X=l[M+1];if(X<0||v>X+u-d)continue;let L=l[M]+u-d;p>=L?l[M+1]=v<=L?-2:-1:v>=u&&C&&(l[M]+=C,l[M+1]+=C)}d+=C}),u=t.maps[c].map(u,-1)}let a=!1;for(let c=0;c<l.length;c+=3)if(l[c+1]<0){if(l[c+1]==-2){a=!0,l[c+1]=-1;continue}let u=t.map(n[c]+r),d=u-o;if(d<0||d>=s.content.size){a=!0;continue}let v=t.map(n[c+1]+r,-1),p=v-o,{index:b,offset:w}=s.content.findIndex(d),C=s.maybeChild(b);if(C&&w==d&&w+C.nodeSize==p){let M=l[c+2].mapInner(t,C,u+1,n[c]+r+1,i);M!=it?(l[c]=d,l[c+1]=p,l[c+2]=M):(l[c+1]=-2,a=!0)}else a=!0}if(a){let c=by(l,n,e,t,o,r,i),u=pr(c,s,0,i);e=u.local;for(let d=0;d<l.length;d+=3)l[d+1]<0&&(l.splice(d,3),d-=3);for(let d=0,v=0;d<u.children.length;d+=3){let p=u.children[d];for(;v<l.length&&l[v]<p;)v+=3;l.splice(v,0,u.children[d],u.children[d+1],u.children[d+2])}}return new Be(e.sort(us),l)}function Ku(n,e){if(!e||!n.length)return n;let t=[];for(let s=0;s<n.length;s++){let o=n[s];t.push(new mt(o.from+e,o.to+e,o.type))}return t}function by(n,e,t,s,o,r,i){function l(a,c){for(let u=0;u<a.local.length;u++){let d=a.local[u].map(s,o,c);d?t.push(d):i.onRemove&&i.onRemove(a.local[u].spec)}for(let u=0;u<a.children.length;u+=3)l(a.children[u+2],a.children[u]+c+1)}for(let a=0;a<n.length;a+=3)n[a+1]==-1&&l(n[a+2],e[a]+r+1);return t}function Xu(n,e,t){if(e.isLeaf)return null;let s=t+e.nodeSize,o=null;for(let r=0,i;r<n.length;r++)(i=n[r])&&i.from>t&&i.to<s&&((o||(o=[])).push(i),n[r]=null);return o}function Yu(n){let e=[];for(let t=0;t<n.length;t++)n[t]!=null&&e.push(n[t]);return e}function pr(n,e,t,s){let o=[],r=!1;e.forEach((l,a)=>{let c=Xu(n,l,a+t);if(c){r=!0;let u=pr(c,l,t+a+1,s);u!=it&&o.push(a,a+l.nodeSize,u)}});let i=Ku(r?Yu(n):n,-t).sort(us);for(let l=0;l<i.length;l++)i[l].type.valid(e,i[l])||(s.onRemove&&s.onRemove(i[l].spec),i.splice(l--,1));return i.length||o.length?new Be(i,o):it}function us(n,e){return n.from-e.from||n.to-e.to}function gl(n){let e=n;for(let t=0;t<e.length-1;t++){let s=e[t];if(s.from!=s.to)for(let o=t+1;o<e.length;o++){let r=e[o];if(r.from==s.from){r.to!=s.to&&(e==n&&(e=n.slice()),e[o]=r.copy(r.from,s.to),Ka(e,o+1,r.copy(s.to,r.to)));continue}else{r.from<s.to&&(e==n&&(e=n.slice()),e[t]=s.copy(s.from,r.from),Ka(e,o,s.copy(r.from,s.to)));break}}}return e}function Ka(n,e,t){for(;e<n.length&&us(t,n[e])>0;)e++;n.splice(e,0,t)}function vi(n){let e=[];return n.someProp("decorations",t=>{let s=t(n.state);s&&s!=it&&e.push(s)}),n.cursorWrapper&&e.push(Be.create(n.state.doc,[n.cursorWrapper.deco])),Dn.from(e)}const wy={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},ky=Ct&&Ln<=11;class xy{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(e){this.anchorNode=e.anchorNode,this.anchorOffset=e.anchorOffset,this.focusNode=e.focusNode,this.focusOffset=e.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(e){return e.anchorNode==this.anchorNode&&e.anchorOffset==this.anchorOffset&&e.focusNode==this.focusNode&&e.focusOffset==this.focusOffset}}class Sy{constructor(e,t){this.view=e,this.handleDOMChange=t,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new xy,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.lastChangedTextNode=null,this.observer=window.MutationObserver&&new window.MutationObserver(s=>{for(let o=0;o<s.length;o++)this.queue.push(s[o]);Ct&&Ln<=11&&s.some(o=>o.type=="childList"&&o.removedNodes.length||o.type=="characterData"&&o.oldValue.length>o.target.nodeValue.length)?this.flushSoon():this.flush()}),ky&&(this.onCharData=s=>{this.queue.push({target:s.target,type:"characterData",oldValue:s.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout(()=>{this.flushingSoon=-1,this.flush()},20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,wy)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let e=this.observer.takeRecords();if(e.length){for(let t=0;t<e.length;t++)this.queue.push(e[t]);window.setTimeout(()=>this.flush(),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout(()=>this.suppressingSelectionUpdates=!1,50)}onSelectionChange(){if(za(this.view)){if(this.suppressingSelectionUpdates)return vn(this.view);if(Ct&&Ln<=11&&!this.view.state.selection.empty){let e=this.view.domSelectionRange();if(e.focusNode&&ms(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(e){if(!e.focusNode)return!0;let t=new Set,s;for(let r=e.focusNode;r;r=zs(r))t.add(r);for(let r=e.anchorNode;r;r=zs(r))if(t.has(r)){s=r;break}let o=s&&this.view.docView.nearestDesc(s);if(o&&o.ignoreMutation({type:"selection",target:s.nodeType==3?s.parentNode:s}))return this.setCurSelection(),!0}pendingRecords(){if(this.observer)for(let e of this.observer.takeRecords())this.queue.push(e);return this.queue}flush(){let{view:e}=this;if(!e.docView||this.flushingSoon>-1)return;let t=this.pendingRecords();t.length&&(this.queue=[]);let s=e.domSelectionRange(),o=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(s)&&za(e)&&!this.ignoreSelectionChange(s),r=-1,i=-1,l=!1,a=[];if(e.editable)for(let u=0;u<t.length;u++){let d=this.registerMutation(t[u],a);d&&(r=r<0?d.from:Math.min(d.from,r),i=i<0?d.to:Math.max(d.to,i),d.typeOver&&(l=!0))}if(jt&&a.length){let u=a.filter(d=>d.nodeName=="BR");if(u.length==2){let[d,v]=u;d.parentNode&&d.parentNode.parentNode==v.parentNode?v.remove():d.remove()}else{let{focusNode:d}=this.currentSelection;for(let v of u){let p=v.parentNode;p&&p.nodeName=="LI"&&(!d||My(e,d)!=p)&&v.remove()}}}let c=null;r<0&&o&&e.input.lastFocus>Date.now()-200&&Math.max(e.input.lastTouch,e.input.lastClick.time)<Date.now()-300&&Ir(s)&&(c=cl(e))&&c.eq(xe.near(e.state.doc.resolve(0),1))?(e.input.lastFocus=0,vn(e),this.currentSelection.set(s),e.scrollToSelection()):(r>-1||o)&&(r>-1&&(e.docView.markDirty(r,i),Cy(e)),this.handleDOMChange(r,i,l,a),e.docView&&e.docView.dirty?e.updateState(e.state):this.currentSelection.eq(s)||vn(e),this.currentSelection.set(s))}registerMutation(e,t){if(t.indexOf(e.target)>-1)return null;let s=this.view.docView.nearestDesc(e.target);if(e.type=="attributes"&&(s==this.view.docView||e.attributeName=="contenteditable"||e.attributeName=="style"&&!e.oldValue&&!e.target.getAttribute("style"))||!s||s.ignoreMutation(e))return null;if(e.type=="childList"){for(let u=0;u<e.addedNodes.length;u++){let d=e.addedNodes[u];t.push(d),d.nodeType==3&&(this.lastChangedTextNode=d)}if(s.contentDOM&&s.contentDOM!=s.dom&&!s.contentDOM.contains(e.target))return{from:s.posBefore,to:s.posAfter};let o=e.previousSibling,r=e.nextSibling;if(Ct&&Ln<=11&&e.addedNodes.length)for(let u=0;u<e.addedNodes.length;u++){let{previousSibling:d,nextSibling:v}=e.addedNodes[u];(!d||Array.prototype.indexOf.call(e.addedNodes,d)<0)&&(o=d),(!v||Array.prototype.indexOf.call(e.addedNodes,v)<0)&&(r=v)}let i=o&&o.parentNode==e.target?et(o)+1:0,l=s.localPosFromDOM(e.target,i,-1),a=r&&r.parentNode==e.target?et(r):e.target.childNodes.length,c=s.localPosFromDOM(e.target,a,1);return{from:l,to:c}}else return e.type=="attributes"?{from:s.posAtStart-s.border,to:s.posAtEnd+s.border}:(this.lastChangedTextNode=e.target,{from:s.posAtStart,to:s.posAtEnd,typeOver:e.target.nodeValue==e.oldValue})}}let Xa=new WeakMap,Ya=!1;function Cy(n){if(!Xa.has(n)&&(Xa.set(n,null),["normal","nowrap","pre-line"].indexOf(getComputedStyle(n.dom).whiteSpace)!==-1)){if(n.requiresGeckoHackNode=jt,Ya)return;console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),Ya=!0}}function Ga(n,e){let t=e.startContainer,s=e.startOffset,o=e.endContainer,r=e.endOffset,i=n.domAtPos(n.state.selection.anchor);return ms(i.node,i.offset,o,r)&&([t,s,o,r]=[o,r,t,s]),{anchorNode:t,anchorOffset:s,focusNode:o,focusOffset:r}}function _y(n,e){if(e.getComposedRanges){let o=e.getComposedRanges(n.root)[0];if(o)return Ga(n,o)}let t;function s(o){o.preventDefault(),o.stopImmediatePropagation(),t=o.getTargetRanges()[0]}return n.dom.addEventListener("beforeinput",s,!0),document.execCommand("indent"),n.dom.removeEventListener("beforeinput",s,!0),t?Ga(n,t):null}function My(n,e){for(let t=e.parentNode;t&&t!=n.dom;t=t.parentNode){let s=n.docView.nearestDesc(t,!0);if(s&&s.node.isBlock)return t}return null}function Ty(n,e,t){let{node:s,fromOffset:o,toOffset:r,from:i,to:l}=n.docView.parseRange(e,t),a=n.domSelectionRange(),c,u=a.anchorNode;if(u&&n.dom.contains(u.nodeType==1?u:u.parentNode)&&(c=[{node:u,offset:a.anchorOffset}],Ir(a)||c.push({node:a.focusNode,offset:a.focusOffset})),at&&n.input.lastKeyCode===8)for(let C=r;C>o;C--){let M=s.childNodes[C-1],X=M.pmViewDesc;if(M.nodeName=="BR"&&!X){r=C;break}if(!X||X.size)break}let d=n.state.doc,v=n.someProp("domParser")||Vn.fromSchema(n.state.schema),p=d.resolve(i),b=null,w=v.parse(s,{topNode:p.parent,topMatch:p.parent.contentMatchAt(p.index()),topOpen:!0,from:o,to:r,preserveWhitespace:p.parent.type.whitespace=="pre"?"full":!0,findPositions:c,ruleFromNode:Ey,context:p});if(c&&c[0].pos!=null){let C=c[0].pos,M=c[1]&&c[1].pos;M==null&&(M=C),b={anchor:C+i,head:M+i}}return{doc:w,sel:b,from:i,to:l}}function Ey(n){let e=n.pmViewDesc;if(e)return e.parseRule();if(n.nodeName=="BR"&&n.parentNode){if(gt&&/^(ul|ol)$/i.test(n.parentNode.nodeName)){let t=document.createElement("div");return t.appendChild(document.createElement("li")),{skip:t}}else if(n.parentNode.lastChild==n||gt&&/^(tr|table)$/i.test(n.parentNode.nodeName))return{ignore:!0}}else if(n.nodeName=="IMG"&&n.getAttribute("mark-placeholder"))return{ignore:!0};return null}const Iy=/^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;function Oy(n,e,t,s,o){let r=n.input.compositionPendingChanges||(n.composing?n.input.compositionID:0);if(n.input.compositionPendingChanges=0,e<0){let _=n.input.lastSelectionTime>Date.now()-50?n.input.lastSelectionOrigin:null,T=cl(n,_);if(T&&!n.state.selection.eq(T)){if(at&&mn&&n.input.lastKeyCode===13&&Date.now()-100<n.input.lastKeyCodeTime&&n.someProp("handleKeyDown",W=>W(n,ts(13,"Enter"))))return;let U=n.state.tr.setSelection(T);_=="pointer"?U.setMeta("pointer",!0):_=="key"&&U.scrollIntoView(),r&&U.setMeta("composition",r),n.dispatch(U)}return}let i=n.state.doc.resolve(e),l=i.sharedDepth(t);e=i.before(l+1),t=n.state.doc.resolve(t).after(l+1);let a=n.state.selection,c=Ty(n,e,t),u=n.state.doc,d=u.slice(c.from,c.to),v,p;n.input.lastKeyCode===8&&Date.now()-100<n.input.lastKeyCodeTime?(v=n.state.selection.to,p="end"):(v=n.state.selection.from,p="start"),n.input.lastKeyCode=null;let b=Ay(d.content,c.doc.content,c.from,v,p);if(b&&n.input.domChangeCount++,(Fs&&n.input.lastIOSEnter>Date.now()-225||mn)&&o.some(_=>_.nodeType==1&&!Iy.test(_.nodeName))&&(!b||b.endA>=b.endB)&&n.someProp("handleKeyDown",_=>_(n,ts(13,"Enter")))){n.input.lastIOSEnter=0;return}if(!b)if(s&&a instanceof ye&&!a.empty&&a.$head.sameParent(a.$anchor)&&!n.composing&&!(c.sel&&c.sel.anchor!=c.sel.head))b={start:a.from,endA:a.to,endB:a.to};else{if(c.sel){let _=Qa(n,n.state.doc,c.sel);if(_&&!_.eq(n.state.selection)){let T=n.state.tr.setSelection(_);r&&T.setMeta("composition",r),n.dispatch(T)}}return}n.state.selection.from<n.state.selection.to&&b.start==b.endB&&n.state.selection instanceof ye&&(b.start>n.state.selection.from&&b.start<=n.state.selection.from+2&&n.state.selection.from>=c.from?b.start=n.state.selection.from:b.endA<n.state.selection.to&&b.endA>=n.state.selection.to-2&&n.state.selection.to<=c.to&&(b.endB+=n.state.selection.to-b.endA,b.endA=n.state.selection.to)),Ct&&Ln<=11&&b.endB==b.start+1&&b.endA==b.start&&b.start>c.from&&c.doc.textBetween(b.start-c.from-1,b.start-c.from+1)=="  "&&(b.start--,b.endA--,b.endB--);let w=c.doc.resolveNoCache(b.start-c.from),C=c.doc.resolveNoCache(b.endB-c.from),M=u.resolve(b.start),X=w.sameParent(C)&&w.parent.inlineContent&&M.end()>=b.endA,L;if((Fs&&n.input.lastIOSEnter>Date.now()-225&&(!X||o.some(_=>_.nodeName=="DIV"||_.nodeName=="P"))||!X&&w.pos<c.doc.content.size&&!w.sameParent(C)&&(L=xe.findFrom(c.doc.resolve(w.pos+1),1,!0))&&L.head==C.pos)&&n.someProp("handleKeyDown",_=>_(n,ts(13,"Enter")))){n.input.lastIOSEnter=0;return}if(n.state.selection.anchor>b.start&&Dy(u,b.start,b.endA,w,C)&&n.someProp("handleKeyDown",_=>_(n,ts(8,"Backspace")))){mn&&at&&n.domObserver.suppressSelectionUpdates();return}at&&b.endB==b.start&&(n.input.lastChromeDelete=Date.now()),mn&&!X&&w.start()!=C.start()&&C.parentOffset==0&&w.depth==C.depth&&c.sel&&c.sel.anchor==c.sel.head&&c.sel.head==b.endA&&(b.endB-=2,C=c.doc.resolveNoCache(b.endB-c.from),setTimeout(()=>{n.someProp("handleKeyDown",function(_){return _(n,ts(13,"Enter"))})},20));let D=b.start,E=b.endA,B,N,V;if(X){if(w.pos==C.pos)Ct&&Ln<=11&&w.parentOffset==0&&(n.domObserver.suppressSelectionUpdates(),setTimeout(()=>vn(n),20)),B=n.state.tr.delete(D,E),N=u.resolve(b.start).marksAcross(u.resolve(b.endA));else if(b.endA==b.endB&&(V=Ny(w.parent.content.cut(w.parentOffset,C.parentOffset),M.parent.content.cut(M.parentOffset,b.endA-M.start()))))B=n.state.tr,V.type=="add"?B.addMark(D,E,V.mark):B.removeMark(D,E,V.mark);else if(w.parent.child(w.index()).isText&&w.index()==C.index()-(C.textOffset?0:1)){let _=w.parent.textBetween(w.parentOffset,C.parentOffset);if(n.someProp("handleTextInput",T=>T(n,D,E,_)))return;B=n.state.tr.insertText(_,D,E)}}if(B||(B=n.state.tr.replace(D,E,c.doc.slice(b.start-c.from,b.endB-c.from))),c.sel){let _=Qa(n,B.doc,c.sel);_&&!(at&&n.composing&&_.empty&&(b.start!=b.endB||n.input.lastChromeDelete<Date.now()-100)&&(_.head==D||_.head==B.mapping.map(E)-1)||Ct&&_.empty&&_.head==D)&&B.setSelection(_)}N&&B.ensureMarks(N),r&&B.setMeta("composition",r),n.dispatch(B.scrollIntoView())}function Qa(n,e,t){return Math.max(t.anchor,t.head)>e.content.size?null:ul(n,e.resolve(t.anchor),e.resolve(t.head))}function Ny(n,e){let t=n.firstChild.marks,s=e.firstChild.marks,o=t,r=s,i,l,a;for(let u=0;u<s.length;u++)o=s[u].removeFromSet(o);for(let u=0;u<t.length;u++)r=t[u].removeFromSet(r);if(o.length==1&&r.length==0)l=o[0],i="add",a=u=>u.mark(l.addToSet(u.marks));else if(o.length==0&&r.length==1)l=r[0],i="remove",a=u=>u.mark(l.removeFromSet(u.marks));else return null;let c=[];for(let u=0;u<e.childCount;u++)c.push(a(e.child(u)));if(Q.from(c).eq(n))return{mark:l,type:i}}function Dy(n,e,t,s,o){if(t-e<=o.pos-s.pos||yi(s,!0,!1)<o.pos)return!1;let r=n.resolve(e);if(!s.parent.isTextblock){let l=r.nodeAfter;return l!=null&&t==e+l.nodeSize}if(r.parentOffset<r.parent.content.size||!r.parent.isTextblock)return!1;let i=n.resolve(yi(r,!0,!0));return!i.parent.isTextblock||i.pos>t||yi(i,!0,!1)<t?!1:s.parent.content.cut(s.parentOffset).eq(i.parent.content)}function yi(n,e,t){let s=n.depth,o=e?n.end():n.pos;for(;s>0&&(e||n.indexAfter(s)==n.node(s).childCount);)s--,o++,e=!1;if(t){let r=n.node(s).maybeChild(n.indexAfter(s));for(;r&&!r.isLeaf;)r=r.firstChild,o++}return o}function Ay(n,e,t,s,o){let r=n.findDiffStart(e,t);if(r==null)return null;let{a:i,b:l}=n.findDiffEnd(e,t+n.size,t+e.size);if(o=="end"){let a=Math.max(0,r-Math.min(i,l));s-=i+a-r}if(i<r&&n.size<e.size){let a=s<=r&&s>=i?r-s:0;r-=a,r&&r<e.size&&Za(e.textBetween(r-1,r+1))&&(r+=a?1:-1),l=r+(l-i),i=r}else if(l<r){let a=s<=r&&s>=l?r-s:0;r-=a,r&&r<n.size&&Za(n.textBetween(r-1,r+1))&&(r+=a?1:-1),i=r+(i-l),l=r}return{start:r,endA:i,endB:l}}function Za(n){if(n.length!=2)return!1;let e=n.charCodeAt(0),t=n.charCodeAt(1);return e>=56320&&e<=57343&&t>=55296&&t<=56319}class $y{constructor(e,t){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new Zv,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=t,this.state=t.state,this.directPlugins=t.plugins||[],this.directPlugins.forEach(oc),this.dispatch=this.dispatch.bind(this),this.dom=e&&e.mount||document.createElement("div"),e&&(e.appendChild?e.appendChild(this.dom):typeof e=="function"?e(this.dom):e.mount&&(this.mounted=!0)),this.editable=nc(this),tc(this),this.nodeViews=sc(this),this.docView=$a(this.state.doc,ec(this),vi(this),this.dom,this),this.domObserver=new Sy(this,(s,o,r,i)=>Oy(this,s,o,r,i)),this.domObserver.start(),ey(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let e=this._props;this._props={};for(let t in e)this._props[t]=e[t];this._props.state=this.state}return this._props}update(e){e.handleDOMEvents!=this._props.handleDOMEvents&&qi(this);let t=this._props;this._props=e,e.plugins&&(e.plugins.forEach(oc),this.directPlugins=e.plugins),this.updateStateInner(e.state,t)}setProps(e){let t={};for(let s in this._props)t[s]=this._props[s];t.state=this.state;for(let s in e)t[s]=e[s];this.update(t)}updateState(e){this.updateStateInner(e,this._props)}updateStateInner(e,t){var s;let o=this.state,r=!1,i=!1;e.storedMarks&&this.composing&&(qu(this),i=!0),this.state=e;let l=o.plugins!=e.plugins||this._props.plugins!=t.plugins;if(l||this._props.plugins!=t.plugins||this._props.nodeViews!=t.nodeViews){let p=sc(this);Ry(p,this.nodeViews)&&(this.nodeViews=p,r=!0)}(l||t.handleDOMEvents!=this._props.handleDOMEvents)&&qi(this),this.editable=nc(this),tc(this);let a=vi(this),c=ec(this),u=o.plugins!=e.plugins&&!o.doc.eq(e.doc)?"reset":e.scrollToSelection>o.scrollToSelection?"to selection":"preserve",d=r||!this.docView.matchesNode(e.doc,c,a);(d||!e.selection.eq(o.selection))&&(i=!0);let v=u=="preserve"&&i&&this.dom.style.overflowAnchor==null&&hv(this);if(i){this.domObserver.stop();let p=d&&(Ct||at)&&!this.composing&&!o.selection.empty&&!e.selection.empty&&Py(o.selection,e.selection);if(d){let b=at?this.trackWrites=this.domSelectionRange().focusNode:null;this.composing&&(this.input.compositionNode=py(this)),(r||!this.docView.update(e.doc,c,a,this))&&(this.docView.updateOuterDeco(c),this.docView.destroy(),this.docView=$a(e.doc,c,a,this.dom,this)),b&&!this.trackWrites&&(p=!0)}p||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&Lv(this))?vn(this,p):(Du(this,e.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(o),!((s=this.dragging)===null||s===void 0)&&s.node&&!o.doc.eq(e.doc)&&this.updateDraggedNode(this.dragging,o),u=="reset"?this.dom.scrollTop=0:u=="to selection"?this.scrollToSelection():v&&mv(v)}scrollToSelection(){let e=this.domSelectionRange().focusNode;if(!(!e||!this.dom.contains(e.nodeType==1?e:e.parentNode))){if(!this.someProp("handleScrollToSelection",t=>t(this)))if(this.state.selection instanceof de){let t=this.docView.domAfterPos(this.state.selection.from);t.nodeType==1&&Ea(this,t.getBoundingClientRect(),e)}else Ea(this,this.coordsAtPos(this.state.selection.head,1),e)}}destroyPluginViews(){let e;for(;e=this.pluginViews.pop();)e.destroy&&e.destroy()}updatePluginViews(e){if(!e||e.plugins!=this.state.plugins||this.directPlugins!=this.prevDirectPlugins){this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let t=0;t<this.directPlugins.length;t++){let s=this.directPlugins[t];s.spec.view&&this.pluginViews.push(s.spec.view(this))}for(let t=0;t<this.state.plugins.length;t++){let s=this.state.plugins[t];s.spec.view&&this.pluginViews.push(s.spec.view(this))}}else for(let t=0;t<this.pluginViews.length;t++){let s=this.pluginViews[t];s.update&&s.update(this,e)}}updateDraggedNode(e,t){let s=e.node,o=-1;if(this.state.doc.nodeAt(s.from)==s.node)o=s.from;else{let r=s.from+(this.state.doc.content.size-t.doc.content.size);(r>0&&this.state.doc.nodeAt(r))==s.node&&(o=r)}this.dragging=new Uu(e.slice,e.move,o<0?void 0:de.create(this.state.doc,o))}someProp(e,t){let s=this._props&&this._props[e],o;if(s!=null&&(o=t?t(s):s))return o;for(let i=0;i<this.directPlugins.length;i++){let l=this.directPlugins[i].props[e];if(l!=null&&(o=t?t(l):l))return o}let r=this.state.plugins;if(r)for(let i=0;i<r.length;i++){let l=r[i].props[e];if(l!=null&&(o=t?t(l):l))return o}}hasFocus(){if(Ct){let e=this.root.activeElement;if(e==this.dom)return!0;if(!e||!this.dom.contains(e))return!1;for(;e&&this.dom!=e&&this.dom.contains(e);){if(e.contentEditable=="false")return!1;e=e.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&gv(this.dom),vn(this),this.domObserver.start()}get root(){let e=this._root;if(e==null){for(let t=this.dom.parentNode;t;t=t.parentNode)if(t.nodeType==9||t.nodeType==11&&t.host)return t.getSelection||(Object.getPrototypeOf(t).getSelection=()=>t.ownerDocument.getSelection()),this._root=t}return e||document}updateRoot(){this._root=null}posAtCoords(e){return kv(this,e)}coordsAtPos(e,t=1){return Cu(this,e,t)}domAtPos(e,t=0){return this.docView.domFromPos(e,t)}nodeDOM(e){let t=this.docView.descAt(e);return t?t.nodeDOM:null}posAtDOM(e,t,s=-1){let o=this.docView.posFromDOM(e,t,s);if(o==null)throw new RangeError("DOM position not inside the editor");return o}endOfTextblock(e,t){return Mv(this,t||this.state,e)}pasteHTML(e,t){return ho(this,"",e,!1,t||new ClipboardEvent("paste"))}pasteText(e,t){return ho(this,e,null,!0,t||new ClipboardEvent("paste"))}serializeForClipboard(e){return dl(this,e)}destroy(){this.docView&&(ty(this),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],vi(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null,sv())}get isDestroyed(){return this.docView==null}dispatchEvent(e){return sy(this,e)}dispatch(e){let t=this._props.dispatchTransaction;t?t.call(this,e):this.updateState(this.state.apply(e))}domSelectionRange(){let e=this.domSelection();return e?gt&&this.root.nodeType===11&&av(this.dom.ownerDocument)==this.dom&&_y(this,e)||e:{focusNode:null,focusOffset:0,anchorNode:null,anchorOffset:0}}domSelection(){return this.root.getSelection()}}function ec(n){let e=Object.create(null);return e.class="ProseMirror",e.contenteditable=String(n.editable),n.someProp("attributes",t=>{if(typeof t=="function"&&(t=t(n.state)),t)for(let s in t)s=="class"?e.class+=" "+t[s]:s=="style"?e.style=(e.style?e.style+";":"")+t[s]:!e[s]&&s!="contenteditable"&&s!="nodeName"&&(e[s]=String(t[s]))}),e.translate||(e.translate="no"),[mt.node(0,n.state.doc.content.size,e)]}function tc(n){if(n.markCursor){let e=document.createElement("img");e.className="ProseMirror-separator",e.setAttribute("mark-placeholder","true"),e.setAttribute("alt",""),n.cursorWrapper={dom:e,deco:mt.widget(n.state.selection.from,e,{raw:!0,marks:n.markCursor})}}else n.cursorWrapper=null}function nc(n){return!n.someProp("editable",e=>e(n.state)===!1)}function Py(n,e){let t=Math.min(n.$anchor.sharedDepth(n.head),e.$anchor.sharedDepth(e.head));return n.$anchor.start(t)!=e.$anchor.start(t)}function sc(n){let e=Object.create(null);function t(s){for(let o in s)Object.prototype.hasOwnProperty.call(e,o)||(e[o]=s[o])}return n.someProp("nodeViews",t),n.someProp("markViews",t),e}function Ry(n,e){let t=0,s=0;for(let o in n){if(n[o]!=e[o])return!0;t++}for(let o in e)s++;return t!=s}function oc(n){if(n.spec.state||n.spec.filterTransaction||n.spec.appendTransaction)throw new RangeError("Plugins passed directly to the view must not have a state component")}var Wn={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},hr={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},Vy=typeof navigator<"u"&&/Mac/.test(navigator.platform),Ly=typeof navigator<"u"&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent);for(var tt=0;tt<10;tt++)Wn[48+tt]=Wn[96+tt]=String(tt);for(var tt=1;tt<=24;tt++)Wn[tt+111]="F"+tt;for(var tt=65;tt<=90;tt++)Wn[tt]=String.fromCharCode(tt+32),hr[tt]=String.fromCharCode(tt);for(var bi in Wn)hr.hasOwnProperty(bi)||(hr[bi]=Wn[bi]);function By(n){var e=Vy&&n.metaKey&&n.shiftKey&&!n.ctrlKey&&!n.altKey||Ly&&n.shiftKey&&n.key&&n.key.length==1||n.key=="Unidentified",t=!e&&n.key||(n.shiftKey?hr:Wn)[n.keyCode]||n.key||"Unidentified";return t=="Esc"&&(t="Escape"),t=="Del"&&(t="Delete"),t=="Left"&&(t="ArrowLeft"),t=="Up"&&(t="ArrowUp"),t=="Right"&&(t="ArrowRight"),t=="Down"&&(t="ArrowDown"),t}const zy=typeof navigator<"u"?/Mac|iP(hone|[oa]d)/.test(navigator.platform):!1;function Fy(n){let e=n.split(/-(?!$)/),t=e[e.length-1];t=="Space"&&(t=" ");let s,o,r,i;for(let l=0;l<e.length-1;l++){let a=e[l];if(/^(cmd|meta|m)$/i.test(a))i=!0;else if(/^a(lt)?$/i.test(a))s=!0;else if(/^(c|ctrl|control)$/i.test(a))o=!0;else if(/^s(hift)?$/i.test(a))r=!0;else if(/^mod$/i.test(a))zy?i=!0:o=!0;else throw new Error("Unrecognized modifier name: "+a)}return s&&(t="Alt-"+t),o&&(t="Ctrl-"+t),i&&(t="Meta-"+t),r&&(t="Shift-"+t),t}function Wy(n){let e=Object.create(null);for(let t in n)e[Fy(t)]=n[t];return e}function wi(n,e,t=!0){return e.altKey&&(n="Alt-"+n),e.ctrlKey&&(n="Ctrl-"+n),e.metaKey&&(n="Meta-"+n),t&&e.shiftKey&&(n="Shift-"+n),n}function Hy(n){return new nt({props:{handleKeyDown:Gu(n)}})}function Gu(n){let e=Wy(n);return function(t,s){let o=By(s),r,i=e[wi(o,s)];if(i&&i(t.state,t.dispatch,t))return!0;if(o.length==1&&o!=" "){if(s.shiftKey){let l=e[wi(o,s,!1)];if(l&&l(t.state,t.dispatch,t))return!0}if((s.shiftKey||s.altKey||s.metaKey||o.charCodeAt(0)>127)&&(r=Wn[s.keyCode])&&r!=o){let l=e[wi(r,s)];if(l&&l(t.state,t.dispatch,t))return!0}}return!1}}const vl=(n,e)=>n.selection.empty?!1:(e&&e(n.tr.deleteSelection().scrollIntoView()),!0);function Qu(n,e){let{$cursor:t}=n.selection;return!t||(e?!e.endOfTextblock("backward",n):t.parentOffset>0)?null:t}const Zu=(n,e,t)=>{let s=Qu(n,t);if(!s)return!1;let o=yl(s);if(!o){let i=s.blockRange(),l=i&&Js(i);return l==null?!1:(e&&e(n.tr.lift(i,l).scrollIntoView()),!0)}let r=o.nodeBefore;if(ad(n,o,e,-1))return!0;if(s.parent.content.size==0&&(Ws(r,"end")||de.isSelectable(r)))for(let i=s.depth;;i--){let l=Tr(n.doc,s.before(i),s.after(i),le.empty);if(l&&l.slice.size<l.to-l.from){if(e){let a=n.tr.step(l);a.setSelection(Ws(r,"end")?xe.findFrom(a.doc.resolve(a.mapping.map(o.pos,-1)),-1):de.create(a.doc,o.pos-r.nodeSize)),e(a.scrollIntoView())}return!0}if(i==1||s.node(i-1).childCount>1)break}return r.isAtom&&o.depth==s.depth-1?(e&&e(n.tr.delete(o.pos-r.nodeSize,o.pos).scrollIntoView()),!0):!1},qy=(n,e,t)=>{let s=Qu(n,t);if(!s)return!1;let o=yl(s);return o?ed(n,o,e):!1},Jy=(n,e,t)=>{let s=nd(n,t);if(!s)return!1;let o=bl(s);return o?ed(n,o,e):!1};function ed(n,e,t){let s=e.nodeBefore,o=s,r=e.pos-1;for(;!o.isTextblock;r--){if(o.type.spec.isolating)return!1;let u=o.lastChild;if(!u)return!1;o=u}let i=e.nodeAfter,l=i,a=e.pos+1;for(;!l.isTextblock;a++){if(l.type.spec.isolating)return!1;let u=l.firstChild;if(!u)return!1;l=u}let c=Tr(n.doc,r,a,le.empty);if(!c||c.from!=r||c instanceof Ke&&c.slice.size>=a-r)return!1;if(t){let u=n.tr.step(c);u.setSelection(ye.create(u.doc,r)),t(u.scrollIntoView())}return!0}function Ws(n,e,t=!1){for(let s=n;s;s=e=="start"?s.firstChild:s.lastChild){if(s.isTextblock)return!0;if(t&&s.childCount!=1)return!1}return!1}const td=(n,e,t)=>{let{$head:s,empty:o}=n.selection,r=s;if(!o)return!1;if(s.parent.isTextblock){if(t?!t.endOfTextblock("backward",n):s.parentOffset>0)return!1;r=yl(s)}let i=r&&r.nodeBefore;return!i||!de.isSelectable(i)?!1:(e&&e(n.tr.setSelection(de.create(n.doc,r.pos-i.nodeSize)).scrollIntoView()),!0)};function yl(n){if(!n.parent.type.spec.isolating)for(let e=n.depth-1;e>=0;e--){if(n.index(e)>0)return n.doc.resolve(n.before(e+1));if(n.node(e).type.spec.isolating)break}return null}function nd(n,e){let{$cursor:t}=n.selection;return!t||(e?!e.endOfTextblock("forward",n):t.parentOffset<t.parent.content.size)?null:t}const sd=(n,e,t)=>{let s=nd(n,t);if(!s)return!1;let o=bl(s);if(!o)return!1;let r=o.nodeAfter;if(ad(n,o,e,1))return!0;if(s.parent.content.size==0&&(Ws(r,"start")||de.isSelectable(r))){let i=Tr(n.doc,s.before(),s.after(),le.empty);if(i&&i.slice.size<i.to-i.from){if(e){let l=n.tr.step(i);l.setSelection(Ws(r,"start")?xe.findFrom(l.doc.resolve(l.mapping.map(o.pos)),1):de.create(l.doc,l.mapping.map(o.pos))),e(l.scrollIntoView())}return!0}}return r.isAtom&&o.depth==s.depth-1?(e&&e(n.tr.delete(o.pos,o.pos+r.nodeSize).scrollIntoView()),!0):!1},od=(n,e,t)=>{let{$head:s,empty:o}=n.selection,r=s;if(!o)return!1;if(s.parent.isTextblock){if(t?!t.endOfTextblock("forward",n):s.parentOffset<s.parent.content.size)return!1;r=bl(s)}let i=r&&r.nodeAfter;return!i||!de.isSelectable(i)?!1:(e&&e(n.tr.setSelection(de.create(n.doc,r.pos)).scrollIntoView()),!0)};function bl(n){if(!n.parent.type.spec.isolating)for(let e=n.depth-1;e>=0;e--){let t=n.node(e);if(n.index(e)+1<t.childCount)return n.doc.resolve(n.after(e+1));if(t.type.spec.isolating)break}return null}const Uy=(n,e)=>{let t=n.selection,s=t instanceof de,o;if(s){if(t.node.isTextblock||!qn(n.doc,t.from))return!1;o=t.from}else if(o=Mr(n.doc,t.from,-1),o==null)return!1;if(e){let r=n.tr.join(o);s&&r.setSelection(de.create(r.doc,o-n.doc.resolve(o).nodeBefore.nodeSize)),e(r.scrollIntoView())}return!0},jy=(n,e)=>{let t=n.selection,s;if(t instanceof de){if(t.node.isTextblock||!qn(n.doc,t.to))return!1;s=t.to}else if(s=Mr(n.doc,t.to,1),s==null)return!1;return e&&e(n.tr.join(s).scrollIntoView()),!0},Ky=(n,e)=>{let{$from:t,$to:s}=n.selection,o=t.blockRange(s),r=o&&Js(o);return r==null?!1:(e&&e(n.tr.lift(o,r).scrollIntoView()),!0)},rd=(n,e)=>{let{$head:t,$anchor:s}=n.selection;return!t.parent.type.spec.code||!t.sameParent(s)?!1:(e&&e(n.tr.insertText(`
`).scrollIntoView()),!0)};function wl(n){for(let e=0;e<n.edgeCount;e++){let{type:t}=n.edge(e);if(t.isTextblock&&!t.hasRequiredAttrs())return t}return null}const Xy=(n,e)=>{let{$head:t,$anchor:s}=n.selection;if(!t.parent.type.spec.code||!t.sameParent(s))return!1;let o=t.node(-1),r=t.indexAfter(-1),i=wl(o.contentMatchAt(r));if(!i||!o.canReplaceWith(r,r,i))return!1;if(e){let l=t.after(),a=n.tr.replaceWith(l,l,i.createAndFill());a.setSelection(xe.near(a.doc.resolve(l),1)),e(a.scrollIntoView())}return!0},id=(n,e)=>{let t=n.selection,{$from:s,$to:o}=t;if(t instanceof Ot||s.parent.inlineContent||o.parent.inlineContent)return!1;let r=wl(o.parent.contentMatchAt(o.indexAfter()));if(!r||!r.isTextblock)return!1;if(e){let i=(!s.parentOffset&&o.index()<o.parent.childCount?s:o).pos,l=n.tr.insert(i,r.createAndFill());l.setSelection(ye.create(l.doc,i+1)),e(l.scrollIntoView())}return!0},ld=(n,e)=>{let{$cursor:t}=n.selection;if(!t||t.parent.content.size)return!1;if(t.depth>1&&t.after()!=t.end(-1)){let r=t.before();if(gn(n.doc,r))return e&&e(n.tr.split(r).scrollIntoView()),!0}let s=t.blockRange(),o=s&&Js(s);return o==null?!1:(e&&e(n.tr.lift(s,o).scrollIntoView()),!0)};function Yy(n){return(e,t)=>{let{$from:s,$to:o}=e.selection;if(e.selection instanceof de&&e.selection.node.isBlock)return!s.parentOffset||!gn(e.doc,s.pos)?!1:(t&&t(e.tr.split(s.pos).scrollIntoView()),!0);if(!s.depth)return!1;let r=[],i,l,a=!1,c=!1;for(let p=s.depth;;p--)if(s.node(p).isBlock){a=s.end(p)==s.pos+(s.depth-p),c=s.start(p)==s.pos-(s.depth-p),l=wl(s.node(p-1).contentMatchAt(s.indexAfter(p-1))),r.unshift(a&&l?{type:l}:null),i=p;break}else{if(p==1)return!1;r.unshift(null)}let u=e.tr;(e.selection instanceof ye||e.selection instanceof Ot)&&u.deleteSelection();let d=u.mapping.map(s.pos),v=gn(u.doc,d,r.length,r);if(v||(r[0]=l?{type:l}:null,v=gn(u.doc,d,r.length,r)),u.split(d,r.length,r),!a&&c&&s.node(i).type!=l){let p=u.mapping.map(s.before(i)),b=u.doc.resolve(p);l&&s.node(i-1).canReplaceWith(b.index(),b.index()+1,l)&&u.setNodeMarkup(u.mapping.map(s.before(i)),l)}return t&&t(u.scrollIntoView()),!0}}const Gy=Yy(),Qy=(n,e)=>{let{$from:t,to:s}=n.selection,o,r=t.sharedDepth(s);return r==0?!1:(o=t.before(r),e&&e(n.tr.setSelection(de.create(n.doc,o))),!0)};function Zy(n,e,t){let s=e.nodeBefore,o=e.nodeAfter,r=e.index();return!s||!o||!s.type.compatibleContent(o.type)?!1:!s.content.size&&e.parent.canReplace(r-1,r)?(t&&t(n.tr.delete(e.pos-s.nodeSize,e.pos).scrollIntoView()),!0):!e.parent.canReplace(r,r+1)||!(o.isTextblock||qn(n.doc,e.pos))?!1:(t&&t(n.tr.join(e.pos).scrollIntoView()),!0)}function ad(n,e,t,s){let o=e.nodeBefore,r=e.nodeAfter,i,l,a=o.type.spec.isolating||r.type.spec.isolating;if(!a&&Zy(n,e,t))return!0;let c=!a&&e.parent.canReplace(e.index(),e.index()+1);if(c&&(i=(l=o.contentMatchAt(o.childCount)).findWrapping(r.type))&&l.matchType(i[0]||r.type).validEnd){if(t){let p=e.pos+r.nodeSize,b=Q.empty;for(let M=i.length-1;M>=0;M--)b=Q.from(i[M].create(null,b));b=Q.from(o.copy(b));let w=n.tr.step(new Xe(e.pos-1,p,e.pos,p,new le(b,1,0),i.length,!0)),C=w.doc.resolve(p+2*i.length);C.nodeAfter&&C.nodeAfter.type==o.type&&qn(w.doc,C.pos)&&w.join(C.pos),t(w.scrollIntoView())}return!0}let u=r.type.spec.isolating||s>0&&a?null:xe.findFrom(e,1),d=u&&u.$from.blockRange(u.$to),v=d&&Js(d);if(v!=null&&v>=e.depth)return t&&t(n.tr.lift(d,v).scrollIntoView()),!0;if(c&&Ws(r,"start",!0)&&Ws(o,"end")){let p=o,b=[];for(;b.push(p),!p.isTextblock;)p=p.lastChild;let w=r,C=1;for(;!w.isTextblock;w=w.firstChild)C++;if(p.canReplace(p.childCount,p.childCount,w.content)){if(t){let M=Q.empty;for(let L=b.length-1;L>=0;L--)M=Q.from(b[L].copy(M));let X=n.tr.step(new Xe(e.pos-b.length,e.pos+r.nodeSize,e.pos+C,e.pos+r.nodeSize-C,new le(M,b.length,0),0,!0));t(X.scrollIntoView())}return!0}}return!1}function cd(n){return function(e,t){let s=e.selection,o=n<0?s.$from:s.$to,r=o.depth;for(;o.node(r).isInline;){if(!r)return!1;r--}return o.node(r).isTextblock?(t&&t(e.tr.setSelection(ye.create(e.doc,n<0?o.start(r):o.end(r)))),!0):!1}}const e1=cd(-1),t1=cd(1);function n1(n,e=null){return function(t,s){let{$from:o,$to:r}=t.selection,i=o.blockRange(r),l=i&&il(i,n,e);return l?(s&&s(t.tr.wrap(i,l).scrollIntoView()),!0):!1}}function rc(n,e=null){return function(t,s){let o=!1;for(let r=0;r<t.selection.ranges.length&&!o;r++){let{$from:{pos:i},$to:{pos:l}}=t.selection.ranges[r];t.doc.nodesBetween(i,l,(a,c)=>{if(o)return!1;if(!(!a.isTextblock||a.hasMarkup(n,e)))if(a.type==n)o=!0;else{let u=t.doc.resolve(c),d=u.index();o=u.parent.canReplaceWith(d,d+1,n)}})}if(!o)return!1;if(s){let r=t.tr;for(let i=0;i<t.selection.ranges.length;i++){let{$from:{pos:l},$to:{pos:a}}=t.selection.ranges[i];r.setBlockType(l,a,n,e)}s(r.scrollIntoView())}return!0}}function kl(...n){return function(e,t,s){for(let o=0;o<n.length;o++)if(n[o](e,t,s))return!0;return!1}}kl(vl,Zu,td);kl(vl,sd,od);kl(rd,id,ld,Gy);typeof navigator<"u"?/Mac|iP(hone|[oa]d)/.test(navigator.platform):typeof os<"u"&&os.platform&&os.platform()=="darwin";function s1(n,e=null){return function(t,s){let{$from:o,$to:r}=t.selection,i=o.blockRange(r);if(!i)return!1;let l=s?t.tr:null;return o1(l,i,n,e)?(s&&s(l.scrollIntoView()),!0):!1}}function o1(n,e,t,s=null){let o=!1,r=e,i=e.$from.doc;if(e.depth>=2&&e.$from.node(e.depth-1).type.compatibleContent(t)&&e.startIndex==0){if(e.$from.index(e.depth-1)==0)return!1;let a=i.resolve(e.start-2);r=new lr(a,a,e.depth),e.endIndex<e.parent.childCount&&(e=new lr(e.$from,i.resolve(e.$to.end(e.depth)),e.depth)),o=!0}let l=il(r,t,s,e);return l?(n&&r1(n,e,l,o,t),!0):!1}function r1(n,e,t,s,o){let r=Q.empty;for(let u=t.length-1;u>=0;u--)r=Q.from(t[u].type.create(t[u].attrs,r));n.step(new Xe(e.start-(s?2:0),e.end,e.start,e.end,new le(r,0,0),t.length,!0));let i=0;for(let u=0;u<t.length;u++)t[u].type==o&&(i=u+1);let l=t.length-i,a=e.start+t.length-(s?2:0),c=e.parent;for(let u=e.startIndex,d=e.endIndex,v=!0;u<d;u++,v=!1)!v&&gn(n.doc,a,l)&&(n.split(a,l),a+=2*l),a+=c.child(u).nodeSize;return n}function i1(n){return function(e,t){let{$from:s,$to:o}=e.selection,r=s.blockRange(o,i=>i.childCount>0&&i.firstChild.type==n);return r?t?s.node(r.depth-1).type==n?l1(e,t,n,r):a1(e,t,r):!0:!1}}function l1(n,e,t,s){let o=n.tr,r=s.end,i=s.$to.end(s.depth);r<i&&(o.step(new Xe(r-1,i,r,i,new le(Q.from(t.create(null,s.parent.copy())),1,0),1,!0)),s=new lr(o.doc.resolve(s.$from.pos),o.doc.resolve(i),s.depth));const l=Js(s);if(l==null)return!1;o.lift(s,l);let a=o.doc.resolve(o.mapping.map(r,-1)-1);return qn(o.doc,a.pos)&&a.nodeBefore.type==a.nodeAfter.type&&o.join(a.pos),e(o.scrollIntoView()),!0}function a1(n,e,t){let s=n.tr,o=t.parent;for(let p=t.end,b=t.endIndex-1,w=t.startIndex;b>w;b--)p-=o.child(b).nodeSize,s.delete(p-1,p+1);let r=s.doc.resolve(t.start),i=r.nodeAfter;if(s.mapping.map(t.end)!=t.start+r.nodeAfter.nodeSize)return!1;let l=t.startIndex==0,a=t.endIndex==o.childCount,c=r.node(-1),u=r.index(-1);if(!c.canReplace(u+(l?0:1),u+1,i.content.append(a?Q.empty:Q.from(o))))return!1;let d=r.pos,v=d+i.nodeSize;return s.step(new Xe(d-(l?1:0),v+(a?1:0),d+1,v-1,new le((l?Q.empty:Q.from(o.copy(Q.empty))).append(a?Q.empty:Q.from(o.copy(Q.empty))),l?0:1,a?0:1),l?0:1)),e(s.scrollIntoView()),!0}function c1(n){return function(e,t){let{$from:s,$to:o}=e.selection,r=s.blockRange(o,c=>c.childCount>0&&c.firstChild.type==n);if(!r)return!1;let i=r.startIndex;if(i==0)return!1;let l=r.parent,a=l.child(i-1);if(a.type!=n)return!1;if(t){let c=a.lastChild&&a.lastChild.type==l.type,u=Q.from(c?n.create():null),d=new le(Q.from(n.create(null,Q.from(l.type.create(null,u)))),c?3:1,0),v=r.start,p=r.end;t(e.tr.step(new Xe(v-(c?3:1),p,v,p,d,1,!0)).scrollIntoView())}return!0}}function Dr(n){const{state:e,transaction:t}=n;let{selection:s}=t,{doc:o}=t,{storedMarks:r}=t;return{...e,apply:e.apply.bind(e),applyTransaction:e.applyTransaction.bind(e),plugins:e.plugins,schema:e.schema,reconfigure:e.reconfigure.bind(e),toJSON:e.toJSON.bind(e),get storedMarks(){return r},get selection(){return s},get doc(){return o},get tr(){return s=t.selection,o=t.doc,r=t.storedMarks,t}}}class Ar{constructor(e){this.editor=e.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=e.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){const{rawCommands:e,editor:t,state:s}=this,{view:o}=t,{tr:r}=s,i=this.buildProps(r);return Object.fromEntries(Object.entries(e).map(([l,a])=>[l,(...u)=>{const d=a(...u)(i);return!r.getMeta("preventDispatch")&&!this.hasCustomState&&o.dispatch(r),d}]))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(e,t=!0){const{rawCommands:s,editor:o,state:r}=this,{view:i}=o,l=[],a=!!e,c=e||r.tr,u=()=>(!a&&t&&!c.getMeta("preventDispatch")&&!this.hasCustomState&&i.dispatch(c),l.every(v=>v===!0)),d={...Object.fromEntries(Object.entries(s).map(([v,p])=>[v,(...w)=>{const C=this.buildProps(c,t),M=p(...w)(C);return l.push(M),d}])),run:u};return d}createCan(e){const{rawCommands:t,state:s}=this,o=!1,r=e||s.tr,i=this.buildProps(r,o);return{...Object.fromEntries(Object.entries(t).map(([a,c])=>[a,(...u)=>c(...u)({...i,dispatch:void 0})])),chain:()=>this.createChain(r,o)}}buildProps(e,t=!0){const{rawCommands:s,editor:o,state:r}=this,{view:i}=o,l={tr:e,editor:o,view:i,state:Dr({state:r,transaction:e}),dispatch:t?()=>{}:void 0,chain:()=>this.createChain(e,t),can:()=>this.createCan(e),get commands(){return Object.fromEntries(Object.entries(s).map(([a,c])=>[a,(...u)=>c(...u)(l)]))}};return l}}class u1{constructor(){this.callbacks={}}on(e,t){return this.callbacks[e]||(this.callbacks[e]=[]),this.callbacks[e].push(t),this}emit(e,...t){const s=this.callbacks[e];return s&&s.forEach(o=>o.apply(this,t)),this}off(e,t){const s=this.callbacks[e];return s&&(t?this.callbacks[e]=s.filter(o=>o!==t):delete this.callbacks[e]),this}once(e,t){const s=(...o)=>{this.off(e,s),t.apply(this,o)};return this.on(e,s)}removeAllListeners(){this.callbacks={}}}function ue(n,e,t){return n.config[e]===void 0&&n.parent?ue(n.parent,e,t):typeof n.config[e]=="function"?n.config[e].bind({...t,parent:n.parent?ue(n.parent,e,t):null}):n.config[e]}function $r(n){const e=n.filter(o=>o.type==="extension"),t=n.filter(o=>o.type==="node"),s=n.filter(o=>o.type==="mark");return{baseExtensions:e,nodeExtensions:t,markExtensions:s}}function ud(n){const e=[],{nodeExtensions:t,markExtensions:s}=$r(n),o=[...t,...s],r={default:null,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return n.forEach(i=>{const l={name:i.name,options:i.options,storage:i.storage,extensions:o},a=ue(i,"addGlobalAttributes",l);if(!a)return;a().forEach(u=>{u.types.forEach(d=>{Object.entries(u.attributes).forEach(([v,p])=>{e.push({type:d,name:v,attribute:{...r,...p}})})})})}),o.forEach(i=>{const l={name:i.name,options:i.options,storage:i.storage},a=ue(i,"addAttributes",l);if(!a)return;const c=a();Object.entries(c).forEach(([u,d])=>{const v={...r,...d};typeof v?.default=="function"&&(v.default=v.default()),v?.isRequired&&v?.default===void 0&&delete v.default,e.push({type:i.name,name:u,attribute:v})})}),e}function Ge(n,e){if(typeof n=="string"){if(!e.nodes[n])throw Error(`There is no node type named '${n}'. Maybe you forgot to add the extension?`);return e.nodes[n]}return n}function bt(...n){return n.filter(e=>!!e).reduce((e,t)=>{const s={...e};return Object.entries(t).forEach(([o,r])=>{if(!s[o]){s[o]=r;return}if(o==="class"){const l=r?String(r).split(" "):[],a=s[o]?s[o].split(" "):[],c=l.filter(u=>!a.includes(u));s[o]=[...a,...c].join(" ")}else if(o==="style"){const l=r?r.split(";").map(u=>u.trim()).filter(Boolean):[],a=s[o]?s[o].split(";").map(u=>u.trim()).filter(Boolean):[],c=new Map;a.forEach(u=>{const[d,v]=u.split(":").map(p=>p.trim());c.set(d,v)}),l.forEach(u=>{const[d,v]=u.split(":").map(p=>p.trim());c.set(d,v)}),s[o]=Array.from(c.entries()).map(([u,d])=>`${u}: ${d}`).join("; ")}else s[o]=r}),s},{})}function Ji(n,e){return e.filter(t=>t.type===n.type.name).filter(t=>t.attribute.rendered).map(t=>t.attribute.renderHTML?t.attribute.renderHTML(n.attrs)||{}:{[t.name]:n.attrs[t.name]}).reduce((t,s)=>bt(t,s),{})}function dd(n){return typeof n=="function"}function Se(n,e=void 0,...t){return dd(n)?e?n.bind(e)(...t):n(...t):n}function d1(n={}){return Object.keys(n).length===0&&n.constructor===Object}function f1(n){return typeof n!="string"?n:n.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(n):n==="true"?!0:n==="false"?!1:n}function ic(n,e){return"style"in n?n:{...n,getAttrs:t=>{const s=n.getAttrs?n.getAttrs(t):n.attrs;if(s===!1)return!1;const o=e.reduce((r,i)=>{const l=i.attribute.parseHTML?i.attribute.parseHTML(t):f1(t.getAttribute(i.name));return l==null?r:{...r,[i.name]:l}},{});return{...s,...o}}}}function lc(n){return Object.fromEntries(Object.entries(n).filter(([e,t])=>e==="attrs"&&d1(t)?!1:t!=null))}function p1(n,e){var t;const s=ud(n),{nodeExtensions:o,markExtensions:r}=$r(n),i=(t=o.find(c=>ue(c,"topNode")))===null||t===void 0?void 0:t.name,l=Object.fromEntries(o.map(c=>{const u=s.filter(M=>M.type===c.name),d={name:c.name,options:c.options,storage:c.storage,editor:e},v=n.reduce((M,X)=>{const L=ue(X,"extendNodeSchema",d);return{...M,...L?L(c):{}}},{}),p=lc({...v,content:Se(ue(c,"content",d)),marks:Se(ue(c,"marks",d)),group:Se(ue(c,"group",d)),inline:Se(ue(c,"inline",d)),atom:Se(ue(c,"atom",d)),selectable:Se(ue(c,"selectable",d)),draggable:Se(ue(c,"draggable",d)),code:Se(ue(c,"code",d)),whitespace:Se(ue(c,"whitespace",d)),linebreakReplacement:Se(ue(c,"linebreakReplacement",d)),defining:Se(ue(c,"defining",d)),isolating:Se(ue(c,"isolating",d)),attrs:Object.fromEntries(u.map(M=>{var X;return[M.name,{default:(X=M?.attribute)===null||X===void 0?void 0:X.default}]}))}),b=Se(ue(c,"parseHTML",d));b&&(p.parseDOM=b.map(M=>ic(M,u)));const w=ue(c,"renderHTML",d);w&&(p.toDOM=M=>w({node:M,HTMLAttributes:Ji(M,u)}));const C=ue(c,"renderText",d);return C&&(p.toText=C),[c.name,p]})),a=Object.fromEntries(r.map(c=>{const u=s.filter(C=>C.type===c.name),d={name:c.name,options:c.options,storage:c.storage,editor:e},v=n.reduce((C,M)=>{const X=ue(M,"extendMarkSchema",d);return{...C,...X?X(c):{}}},{}),p=lc({...v,inclusive:Se(ue(c,"inclusive",d)),excludes:Se(ue(c,"excludes",d)),group:Se(ue(c,"group",d)),spanning:Se(ue(c,"spanning",d)),code:Se(ue(c,"code",d)),attrs:Object.fromEntries(u.map(C=>{var M;return[C.name,{default:(M=C?.attribute)===null||M===void 0?void 0:M.default}]}))}),b=Se(ue(c,"parseHTML",d));b&&(p.parseDOM=b.map(C=>ic(C,u)));const w=ue(c,"renderHTML",d);return w&&(p.toDOM=C=>w({mark:C,HTMLAttributes:Ji(C,u)})),[c.name,p]}));return new eu({topNode:i,nodes:l,marks:a})}function ki(n,e){return e.nodes[n]||e.marks[n]||null}function ac(n,e){return Array.isArray(e)?e.some(t=>(typeof t=="string"?t:t.name)===n.name):e}function xl(n,e){const t=ys.fromSchema(e).serializeFragment(n),o=document.implementation.createHTMLDocument().createElement("div");return o.appendChild(t),o.innerHTML}const h1=(n,e=500)=>{let t="";const s=n.parentOffset;return n.parent.nodesBetween(Math.max(0,s-e),s,(o,r,i,l)=>{var a,c;const u=((c=(a=o.type.spec).toText)===null||c===void 0?void 0:c.call(a,{node:o,pos:r,parent:i,index:l}))||o.textContent||"%leaf%";t+=o.isAtom&&!o.isText?u:u.slice(0,Math.max(0,s-r))}),t};function Sl(n){return Object.prototype.toString.call(n)==="[object RegExp]"}class Pr{constructor(e){this.find=e.find,this.handler=e.handler}}const m1=(n,e)=>{if(Sl(e))return e.exec(n);const t=e(n);if(!t)return null;const s=[t.text];return s.index=t.index,s.input=n,s.data=t.data,t.replaceWith&&(t.text.includes(t.replaceWith)||console.warn('[tiptap warn]: "inputRuleMatch.replaceWith" must be part of "inputRuleMatch.text".'),s.push(t.replaceWith)),s};function Ho(n){var e;const{editor:t,from:s,to:o,text:r,rules:i,plugin:l}=n,{view:a}=t;if(a.composing)return!1;const c=a.state.doc.resolve(s);if(c.parent.type.spec.code||!((e=c.nodeBefore||c.nodeAfter)===null||e===void 0)&&e.marks.find(v=>v.type.spec.code))return!1;let u=!1;const d=h1(c)+r;return i.forEach(v=>{if(u)return;const p=m1(d,v.find);if(!p)return;const b=a.state.tr,w=Dr({state:a.state,transaction:b}),C={from:s-(p[0].length-r.length),to:o},{commands:M,chain:X,can:L}=new Ar({editor:t,state:w});v.handler({state:w,range:C,match:p,commands:M,chain:X,can:L})===null||!b.steps.length||(b.setMeta(l,{transform:b,from:s,to:o,text:r}),a.dispatch(b),u=!0)}),u}function g1(n){const{editor:e,rules:t}=n,s=new nt({state:{init(){return null},apply(o,r,i){const l=o.getMeta(s);if(l)return l;const a=o.getMeta("applyInputRules");return!!a&&setTimeout(()=>{let{text:u}=a;typeof u=="string"?u=u:u=xl(Q.from(u),i.schema);const{from:d}=a,v=d+u.length;Ho({editor:e,from:d,to:v,text:u,rules:t,plugin:s})}),o.selectionSet||o.docChanged?null:r}},props:{handleTextInput(o,r,i,l){return Ho({editor:e,from:r,to:i,text:l,rules:t,plugin:s})},handleDOMEvents:{compositionend:o=>(setTimeout(()=>{const{$cursor:r}=o.state.selection;r&&Ho({editor:e,from:r.pos,to:r.pos,text:"",rules:t,plugin:s})}),!1)},handleKeyDown(o,r){if(r.key!=="Enter")return!1;const{$cursor:i}=o.state.selection;return i?Ho({editor:e,from:i.pos,to:i.pos,text:`
`,rules:t,plugin:s}):!1}},isInputRules:!0});return s}function v1(n){return Object.prototype.toString.call(n).slice(8,-1)}function qo(n){return v1(n)!=="Object"?!1:n.constructor===Object&&Object.getPrototypeOf(n)===Object.prototype}function Rr(n,e){const t={...n};return qo(n)&&qo(e)&&Object.keys(e).forEach(s=>{qo(e[s])&&qo(n[s])?t[s]=Rr(n[s],e[s]):t[s]=e[s]}),t}class Hn{constructor(e={}){this.type="mark",this.name="mark",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=Se(ue(this,"addOptions",{name:this.name}))),this.storage=Se(ue(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new Hn(e)}configure(e={}){const t=this.extend({...this.config,addOptions:()=>Rr(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){const t=new Hn(e);return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=Se(ue(t,"addOptions",{name:t.name})),t.storage=Se(ue(t,"addStorage",{name:t.name,options:t.options})),t}static handleExit({editor:e,mark:t}){const{tr:s}=e.state,o=e.state.selection.$from;if(o.pos===o.end()){const i=o.marks();if(!!!i.find(c=>c?.type.name===t.name))return!1;const a=i.find(c=>c?.type.name===t.name);return a&&s.removeStoredMark(a),s.insertText(" ",o.pos),e.view.dispatch(s),!0}return!1}}function y1(n){return typeof n=="number"}class b1{constructor(e){this.find=e.find,this.handler=e.handler}}const w1=(n,e,t)=>{if(Sl(e))return[...n.matchAll(e)];const s=e(n,t);return s?s.map(o=>{const r=[o.text];return r.index=o.index,r.input=n,r.data=o.data,o.replaceWith&&(o.text.includes(o.replaceWith)||console.warn('[tiptap warn]: "pasteRuleMatch.replaceWith" must be part of "pasteRuleMatch.text".'),r.push(o.replaceWith)),r}):[]};function k1(n){const{editor:e,state:t,from:s,to:o,rule:r,pasteEvent:i,dropEvent:l}=n,{commands:a,chain:c,can:u}=new Ar({editor:e,state:t}),d=[];return t.doc.nodesBetween(s,o,(p,b)=>{if(!p.isTextblock||p.type.spec.code)return;const w=Math.max(s,b),C=Math.min(o,b+p.content.size),M=p.textBetween(w-b,C-b,void 0,"￼");w1(M,r.find,i).forEach(L=>{if(L.index===void 0)return;const D=w+L.index+1,E=D+L[0].length,B={from:t.tr.mapping.map(D),to:t.tr.mapping.map(E)},N=r.handler({state:t,range:B,match:L,commands:a,chain:c,can:u,pasteEvent:i,dropEvent:l});d.push(N)})}),d.every(p=>p!==null)}let Jo=null;const x1=n=>{var e;const t=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return(e=t.clipboardData)===null||e===void 0||e.setData("text/html",n),t};function S1(n){const{editor:e,rules:t}=n;let s=null,o=!1,r=!1,i=typeof ClipboardEvent<"u"?new ClipboardEvent("paste"):null,l;try{l=typeof DragEvent<"u"?new DragEvent("drop"):null}catch{l=null}const a=({state:u,from:d,to:v,rule:p,pasteEvt:b})=>{const w=u.tr,C=Dr({state:u,transaction:w});if(!(!k1({editor:e,state:C,from:Math.max(d-1,0),to:v.b-1,rule:p,pasteEvent:b,dropEvent:l})||!w.steps.length)){try{l=typeof DragEvent<"u"?new DragEvent("drop"):null}catch{l=null}return i=typeof ClipboardEvent<"u"?new ClipboardEvent("paste"):null,w}};return t.map(u=>new nt({view(d){const v=b=>{var w;s=!((w=d.dom.parentElement)===null||w===void 0)&&w.contains(b.target)?d.dom.parentElement:null,s&&(Jo=e)},p=()=>{Jo&&(Jo=null)};return window.addEventListener("dragstart",v),window.addEventListener("dragend",p),{destroy(){window.removeEventListener("dragstart",v),window.removeEventListener("dragend",p)}}},props:{handleDOMEvents:{drop:(d,v)=>{if(r=s===d.dom.parentElement,l=v,!r){const p=Jo;p&&setTimeout(()=>{const b=p.state.selection;b&&p.commands.deleteRange({from:b.from,to:b.to})},10)}return!1},paste:(d,v)=>{var p;const b=(p=v.clipboardData)===null||p===void 0?void 0:p.getData("text/html");return i=v,o=!!b?.includes("data-pm-slice"),!1}}},appendTransaction:(d,v,p)=>{const b=d[0],w=b.getMeta("uiEvent")==="paste"&&!o,C=b.getMeta("uiEvent")==="drop"&&!r,M=b.getMeta("applyPasteRules"),X=!!M;if(!w&&!C&&!X)return;if(X){let{text:E}=M;typeof E=="string"?E=E:E=xl(Q.from(E),p.schema);const{from:B}=M,N=B+E.length,V=x1(E);return a({rule:u,state:p,from:B,to:{b:N},pasteEvt:V})}const L=v.doc.content.findDiffStart(p.doc.content),D=v.doc.content.findDiffEnd(p.doc.content);if(!(!y1(L)||!D||L===D.b))return a({rule:u,state:p,from:L,to:D,pasteEvt:i})}}))}function C1(n){const e=n.filter((t,s)=>n.indexOf(t)!==s);return Array.from(new Set(e))}class Ds{constructor(e,t){this.splittableMarks=[],this.editor=t,this.extensions=Ds.resolve(e),this.schema=p1(this.extensions,t),this.setupExtensions()}static resolve(e){const t=Ds.sort(Ds.flatten(e)),s=C1(t.map(o=>o.name));return s.length&&console.warn(`[tiptap warn]: Duplicate extension names found: [${s.map(o=>`'${o}'`).join(", ")}]. This can lead to issues.`),t}static flatten(e){return e.map(t=>{const s={name:t.name,options:t.options,storage:t.storage},o=ue(t,"addExtensions",s);return o?[t,...this.flatten(o())]:t}).flat(10)}static sort(e){return e.sort((s,o)=>{const r=ue(s,"priority")||100,i=ue(o,"priority")||100;return r>i?-1:r<i?1:0})}get commands(){return this.extensions.reduce((e,t)=>{const s={name:t.name,options:t.options,storage:t.storage,editor:this.editor,type:ki(t.name,this.schema)},o=ue(t,"addCommands",s);return o?{...e,...o()}:e},{})}get plugins(){const{editor:e}=this,t=Ds.sort([...this.extensions].reverse()),s=[],o=[],r=t.map(i=>{const l={name:i.name,options:i.options,storage:i.storage,editor:e,type:ki(i.name,this.schema)},a=[],c=ue(i,"addKeyboardShortcuts",l);let u={};if(i.type==="mark"&&ue(i,"exitable",l)&&(u.ArrowRight=()=>Hn.handleExit({editor:e,mark:i})),c){const w=Object.fromEntries(Object.entries(c()).map(([C,M])=>[C,()=>M({editor:e})]));u={...u,...w}}const d=Hy(u);a.push(d);const v=ue(i,"addInputRules",l);ac(i,e.options.enableInputRules)&&v&&s.push(...v());const p=ue(i,"addPasteRules",l);ac(i,e.options.enablePasteRules)&&p&&o.push(...p());const b=ue(i,"addProseMirrorPlugins",l);if(b){const w=b();a.push(...w)}return a}).flat();return[g1({editor:e,rules:s}),...S1({editor:e,rules:o}),...r]}get attributes(){return ud(this.extensions)}get nodeViews(){const{editor:e}=this,{nodeExtensions:t}=$r(this.extensions);return Object.fromEntries(t.filter(s=>!!ue(s,"addNodeView")).map(s=>{const o=this.attributes.filter(a=>a.type===s.name),r={name:s.name,options:s.options,storage:s.storage,editor:e,type:Ge(s.name,this.schema)},i=ue(s,"addNodeView",r);if(!i)return[];const l=(a,c,u,d,v)=>{const p=Ji(a,o);return i()({node:a,view:c,getPos:u,decorations:d,innerDecorations:v,editor:e,extension:s,HTMLAttributes:p})};return[s.name,l]}))}setupExtensions(){this.extensions.forEach(e=>{var t;this.editor.extensionStorage[e.name]=e.storage;const s={name:e.name,options:e.options,storage:e.storage,editor:this.editor,type:ki(e.name,this.schema)};e.type==="mark"&&(!((t=Se(ue(e,"keepOnSplit",s)))!==null&&t!==void 0)||t)&&this.splittableMarks.push(e.name);const o=ue(e,"onBeforeCreate",s),r=ue(e,"onCreate",s),i=ue(e,"onUpdate",s),l=ue(e,"onSelectionUpdate",s),a=ue(e,"onTransaction",s),c=ue(e,"onFocus",s),u=ue(e,"onBlur",s),d=ue(e,"onDestroy",s);o&&this.editor.on("beforeCreate",o),r&&this.editor.on("create",r),i&&this.editor.on("update",i),l&&this.editor.on("selectionUpdate",l),a&&this.editor.on("transaction",a),c&&this.editor.on("focus",c),u&&this.editor.on("blur",u),d&&this.editor.on("destroy",d)})}}class Qe{constructor(e={}){this.type="extension",this.name="extension",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=Se(ue(this,"addOptions",{name:this.name}))),this.storage=Se(ue(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new Qe(e)}configure(e={}){const t=this.extend({...this.config,addOptions:()=>Rr(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){const t=new Qe({...this.config,...e});return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=Se(ue(t,"addOptions",{name:t.name})),t.storage=Se(ue(t,"addStorage",{name:t.name,options:t.options})),t}}function fd(n,e,t){const{from:s,to:o}=e,{blockSeparator:r=`

`,textSerializers:i={}}=t||{};let l="";return n.nodesBetween(s,o,(a,c,u,d)=>{var v;a.isBlock&&c>s&&(l+=r);const p=i?.[a.type.name];if(p)return u&&(l+=p({node:a,pos:c,parent:u,index:d,range:e})),!1;a.isText&&(l+=(v=a?.text)===null||v===void 0?void 0:v.slice(Math.max(s,c)-c,o-c))}),l}function pd(n){return Object.fromEntries(Object.entries(n.nodes).filter(([,e])=>e.spec.toText).map(([e,t])=>[e,t.spec.toText]))}const _1=Qe.create({name:"clipboardTextSerializer",addOptions(){return{blockSeparator:void 0}},addProseMirrorPlugins(){return[new nt({key:new Nt("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{const{editor:n}=this,{state:e,schema:t}=n,{doc:s,selection:o}=e,{ranges:r}=o,i=Math.min(...r.map(u=>u.$from.pos)),l=Math.max(...r.map(u=>u.$to.pos)),a=pd(t);return fd(s,{from:i,to:l},{...this.options.blockSeparator!==void 0?{blockSeparator:this.options.blockSeparator}:{},textSerializers:a})}}})]}}),M1=()=>({editor:n,view:e})=>(requestAnimationFrame(()=>{var t;n.isDestroyed||(e.dom.blur(),(t=window?.getSelection())===null||t===void 0||t.removeAllRanges())}),!0),T1=(n=!1)=>({commands:e})=>e.setContent("",n),E1=()=>({state:n,tr:e,dispatch:t})=>{const{selection:s}=e,{ranges:o}=s;return t&&o.forEach(({$from:r,$to:i})=>{n.doc.nodesBetween(r.pos,i.pos,(l,a)=>{if(l.type.isText)return;const{doc:c,mapping:u}=e,d=c.resolve(u.map(a)),v=c.resolve(u.map(a+l.nodeSize)),p=d.blockRange(v);if(!p)return;const b=Js(p);if(l.type.isTextblock){const{defaultType:w}=d.parent.contentMatchAt(d.index());e.setNodeMarkup(p.start,w)}(b||b===0)&&e.lift(p,b)})}),!0},I1=n=>e=>n(e),O1=()=>({state:n,dispatch:e})=>id(n,e),N1=(n,e)=>({editor:t,tr:s})=>{const{state:o}=t,r=o.doc.slice(n.from,n.to);s.deleteRange(n.from,n.to);const i=s.mapping.map(e);return s.insert(i,r.content),s.setSelection(new ye(s.doc.resolve(i-1))),!0},D1=()=>({tr:n,dispatch:e})=>{const{selection:t}=n,s=t.$anchor.node();if(s.content.size>0)return!1;const o=n.selection.$anchor;for(let r=o.depth;r>0;r-=1)if(o.node(r).type===s.type){if(e){const l=o.before(r),a=o.after(r);n.delete(l,a).scrollIntoView()}return!0}return!1},A1=n=>({tr:e,state:t,dispatch:s})=>{const o=Ge(n,t.schema),r=e.selection.$anchor;for(let i=r.depth;i>0;i-=1)if(r.node(i).type===o){if(s){const a=r.before(i),c=r.after(i);e.delete(a,c).scrollIntoView()}return!0}return!1},$1=n=>({tr:e,dispatch:t})=>{const{from:s,to:o}=n;return t&&e.delete(s,o),!0},P1=()=>({state:n,dispatch:e})=>vl(n,e),R1=()=>({commands:n})=>n.keyboardShortcut("Enter"),V1=()=>({state:n,dispatch:e})=>Xy(n,e);function mr(n,e,t={strict:!0}){const s=Object.keys(e);return s.length?s.every(o=>t.strict?e[o]===n[o]:Sl(e[o])?e[o].test(n[o]):e[o]===n[o]):!0}function hd(n,e,t={}){return n.find(s=>s.type===e&&mr(Object.fromEntries(Object.keys(t).map(o=>[o,s.attrs[o]])),t))}function cc(n,e,t={}){return!!hd(n,e,t)}function Cl(n,e,t){var s;if(!n||!e)return;let o=n.parent.childAfter(n.parentOffset);if((!o.node||!o.node.marks.some(u=>u.type===e))&&(o=n.parent.childBefore(n.parentOffset)),!o.node||!o.node.marks.some(u=>u.type===e)||(t=t||((s=o.node.marks[0])===null||s===void 0?void 0:s.attrs),!hd([...o.node.marks],e,t)))return;let i=o.index,l=n.start()+o.offset,a=i+1,c=l+o.node.nodeSize;for(;i>0&&cc([...n.parent.child(i-1).marks],e,t);)i-=1,l-=n.parent.child(i).nodeSize;for(;a<n.parent.childCount&&cc([...n.parent.child(a).marks],e,t);)c+=n.parent.child(a).nodeSize,a+=1;return{from:l,to:c}}function Un(n,e){if(typeof n=="string"){if(!e.marks[n])throw Error(`There is no mark type named '${n}'. Maybe you forgot to add the extension?`);return e.marks[n]}return n}const L1=(n,e={})=>({tr:t,state:s,dispatch:o})=>{const r=Un(n,s.schema),{doc:i,selection:l}=t,{$from:a,from:c,to:u}=l;if(o){const d=Cl(a,r,e);if(d&&d.from<=c&&d.to>=u){const v=ye.create(i,d.from,d.to);t.setSelection(v)}}return!0},B1=n=>e=>{const t=typeof n=="function"?n(e):n;for(let s=0;s<t.length;s+=1)if(t[s](e))return!0;return!1};function md(n){return n instanceof ye}function is(n=0,e=0,t=0){return Math.min(Math.max(n,e),t)}function gd(n,e=null){if(!e)return null;const t=xe.atStart(n),s=xe.atEnd(n);if(e==="start"||e===!0)return t;if(e==="end")return s;const o=t.from,r=s.to;return e==="all"?ye.create(n,is(0,o,r),is(n.content.size,o,r)):ye.create(n,is(e,o,r),is(e,o,r))}function z1(){return navigator.platform==="Android"||/android/i.test(navigator.userAgent)}function _l(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}const F1=(n=null,e={})=>({editor:t,view:s,tr:o,dispatch:r})=>{e={scrollIntoView:!0,...e};const i=()=>{(_l()||z1())&&s.dom.focus(),requestAnimationFrame(()=>{t.isDestroyed||(s.focus(),e?.scrollIntoView&&t.commands.scrollIntoView())})};if(s.hasFocus()&&n===null||n===!1)return!0;if(r&&n===null&&!md(t.state.selection))return i(),!0;const l=gd(o.doc,n)||t.state.selection,a=t.state.selection.eq(l);return r&&(a||o.setSelection(l),a&&o.storedMarks&&o.setStoredMarks(o.storedMarks),i()),!0},W1=(n,e)=>t=>n.every((s,o)=>e(s,{...t,index:o})),H1=(n,e)=>({tr:t,commands:s})=>s.insertContentAt({from:t.selection.from,to:t.selection.to},n,e),vd=n=>{const e=n.childNodes;for(let t=e.length-1;t>=0;t-=1){const s=e[t];s.nodeType===3&&s.nodeValue&&/^(\n\s\s|\n)$/.test(s.nodeValue)?n.removeChild(s):s.nodeType===1&&vd(s)}return n};function Uo(n){const e=`<body>${n}</body>`,t=new window.DOMParser().parseFromString(e,"text/html").body;return vd(t)}function gr(n,e,t){if(n instanceof Rn||n instanceof Q)return n;t={slice:!0,parseOptions:{},...t};const s=typeof n=="object"&&n!==null,o=typeof n=="string";if(s)try{if(Array.isArray(n)&&n.length>0)return Q.fromArray(n.map(l=>e.nodeFromJSON(l)));const i=e.nodeFromJSON(n);return t.errorOnInvalidContent&&i.check(),i}catch(r){if(t.errorOnInvalidContent)throw new Error("[tiptap error]: Invalid JSON content",{cause:r});return console.warn("[tiptap warn]: Invalid content.","Passed value:",n,"Error:",r),gr("",e,t)}if(o){if(t.errorOnInvalidContent){let i=!1,l="";const a=new eu({topNode:e.spec.topNode,marks:e.spec.marks,nodes:e.spec.nodes.append({__tiptap__private__unknown__catch__all__node:{content:"inline*",group:"block",parseDOM:[{tag:"*",getAttrs:c=>(i=!0,l=typeof c=="string"?c:c.outerHTML,null)}]}})});if(t.slice?Vn.fromSchema(a).parseSlice(Uo(n),t.parseOptions):Vn.fromSchema(a).parse(Uo(n),t.parseOptions),t.errorOnInvalidContent&&i)throw new Error("[tiptap error]: Invalid HTML content",{cause:new Error(`Invalid element found: ${l}`)})}const r=Vn.fromSchema(e);return t.slice?r.parseSlice(Uo(n),t.parseOptions).content:r.parse(Uo(n),t.parseOptions)}return gr("",e,t)}function q1(n,e,t){const s=n.steps.length-1;if(s<e)return;const o=n.steps[s];if(!(o instanceof Ke||o instanceof Xe))return;const r=n.mapping.maps[s];let i=0;r.forEach((l,a,c,u)=>{i===0&&(i=u)}),n.setSelection(xe.near(n.doc.resolve(i),t))}const J1=n=>!("type"in n),U1=(n,e,t)=>({tr:s,dispatch:o,editor:r})=>{var i;if(o){t={parseOptions:r.options.parseOptions,updateSelection:!0,applyInputRules:!1,applyPasteRules:!1,...t};let l;try{l=gr(e,r.schema,{parseOptions:{preserveWhitespace:"full",...t.parseOptions},errorOnInvalidContent:(i=t.errorOnInvalidContent)!==null&&i!==void 0?i:r.options.enableContentCheck})}catch(b){return r.emit("contentError",{editor:r,error:b,disableCollaboration:()=>{r.storage.collaboration&&(r.storage.collaboration.isDisabled=!0)}}),!1}let{from:a,to:c}=typeof n=="number"?{from:n,to:n}:{from:n.from,to:n.to},u=!0,d=!0;if((J1(l)?l:[l]).forEach(b=>{b.check(),u=u?b.isText&&b.marks.length===0:!1,d=d?b.isBlock:!1}),a===c&&d){const{parent:b}=s.doc.resolve(a);b.isTextblock&&!b.type.spec.code&&!b.childCount&&(a-=1,c+=1)}let p;if(u){if(Array.isArray(e))p=e.map(b=>b.text||"").join("");else if(e instanceof Q){let b="";e.forEach(w=>{w.text&&(b+=w.text)}),p=b}else typeof e=="object"&&e&&e.text?p=e.text:p=e;s.insertText(p,a,c)}else p=l,s.replaceWith(a,c,p);t.updateSelection&&q1(s,s.steps.length-1,-1),t.applyInputRules&&s.setMeta("applyInputRules",{from:a,text:p}),t.applyPasteRules&&s.setMeta("applyPasteRules",{from:a,text:p})}return!0},j1=()=>({state:n,dispatch:e})=>Uy(n,e),K1=()=>({state:n,dispatch:e})=>jy(n,e),X1=()=>({state:n,dispatch:e})=>Zu(n,e),Y1=()=>({state:n,dispatch:e})=>sd(n,e),G1=()=>({state:n,dispatch:e,tr:t})=>{try{const s=Mr(n.doc,n.selection.$from.pos,-1);return s==null?!1:(t.join(s,2),e&&e(t),!0)}catch{return!1}},Q1=()=>({state:n,dispatch:e,tr:t})=>{try{const s=Mr(n.doc,n.selection.$from.pos,1);return s==null?!1:(t.join(s,2),e&&e(t),!0)}catch{return!1}},Z1=()=>({state:n,dispatch:e})=>qy(n,e),e0=()=>({state:n,dispatch:e})=>Jy(n,e);function yd(){return typeof navigator<"u"?/Mac/.test(navigator.platform):!1}function t0(n){const e=n.split(/-(?!$)/);let t=e[e.length-1];t==="Space"&&(t=" ");let s,o,r,i;for(let l=0;l<e.length-1;l+=1){const a=e[l];if(/^(cmd|meta|m)$/i.test(a))i=!0;else if(/^a(lt)?$/i.test(a))s=!0;else if(/^(c|ctrl|control)$/i.test(a))o=!0;else if(/^s(hift)?$/i.test(a))r=!0;else if(/^mod$/i.test(a))_l()||yd()?i=!0:o=!0;else throw new Error(`Unrecognized modifier name: ${a}`)}return s&&(t=`Alt-${t}`),o&&(t=`Ctrl-${t}`),i&&(t=`Meta-${t}`),r&&(t=`Shift-${t}`),t}const n0=n=>({editor:e,view:t,tr:s,dispatch:o})=>{const r=t0(n).split(/-(?!$)/),i=r.find(c=>!["Alt","Ctrl","Meta","Shift"].includes(c)),l=new KeyboardEvent("keydown",{key:i==="Space"?" ":i,altKey:r.includes("Alt"),ctrlKey:r.includes("Ctrl"),metaKey:r.includes("Meta"),shiftKey:r.includes("Shift"),bubbles:!0,cancelable:!0}),a=e.captureTransaction(()=>{t.someProp("handleKeyDown",c=>c(t,l))});return a?.steps.forEach(c=>{const u=c.map(s.mapping);u&&o&&s.maybeStep(u)}),!0};function go(n,e,t={}){const{from:s,to:o,empty:r}=n.selection,i=e?Ge(e,n.schema):null,l=[];n.doc.nodesBetween(s,o,(d,v)=>{if(d.isText)return;const p=Math.max(s,v),b=Math.min(o,v+d.nodeSize);l.push({node:d,from:p,to:b})});const a=o-s,c=l.filter(d=>i?i.name===d.node.type.name:!0).filter(d=>mr(d.node.attrs,t,{strict:!1}));return r?!!c.length:c.reduce((d,v)=>d+v.to-v.from,0)>=a}const s0=(n,e={})=>({state:t,dispatch:s})=>{const o=Ge(n,t.schema);return go(t,o,e)?Ky(t,s):!1},o0=()=>({state:n,dispatch:e})=>ld(n,e),r0=n=>({state:e,dispatch:t})=>{const s=Ge(n,e.schema);return i1(s)(e,t)},i0=()=>({state:n,dispatch:e})=>rd(n,e);function Vr(n,e){return e.nodes[n]?"node":e.marks[n]?"mark":null}function uc(n,e){const t=typeof e=="string"?[e]:e;return Object.keys(n).reduce((s,o)=>(t.includes(o)||(s[o]=n[o]),s),{})}const l0=(n,e)=>({tr:t,state:s,dispatch:o})=>{let r=null,i=null;const l=Vr(typeof n=="string"?n:n.name,s.schema);return l?(l==="node"&&(r=Ge(n,s.schema)),l==="mark"&&(i=Un(n,s.schema)),o&&t.selection.ranges.forEach(a=>{s.doc.nodesBetween(a.$from.pos,a.$to.pos,(c,u)=>{r&&r===c.type&&t.setNodeMarkup(u,void 0,uc(c.attrs,e)),i&&c.marks.length&&c.marks.forEach(d=>{i===d.type&&t.addMark(u,u+c.nodeSize,i.create(uc(d.attrs,e)))})})}),!0):!1},a0=()=>({tr:n,dispatch:e})=>(e&&n.scrollIntoView(),!0),c0=()=>({tr:n,dispatch:e})=>{if(e){const t=new Ot(n.doc);n.setSelection(t)}return!0},u0=()=>({state:n,dispatch:e})=>td(n,e),d0=()=>({state:n,dispatch:e})=>od(n,e),f0=()=>({state:n,dispatch:e})=>Qy(n,e),p0=()=>({state:n,dispatch:e})=>t1(n,e),h0=()=>({state:n,dispatch:e})=>e1(n,e);function Ui(n,e,t={},s={}){return gr(n,e,{slice:!1,parseOptions:t,errorOnInvalidContent:s.errorOnInvalidContent})}const m0=(n,e=!1,t={},s={})=>({editor:o,tr:r,dispatch:i,commands:l})=>{var a,c;const{doc:u}=r;if(t.preserveWhitespace!=="full"){const d=Ui(n,o.schema,t,{errorOnInvalidContent:(a=s.errorOnInvalidContent)!==null&&a!==void 0?a:o.options.enableContentCheck});return i&&r.replaceWith(0,u.content.size,d).setMeta("preventUpdate",!e),!0}return i&&r.setMeta("preventUpdate",!e),l.insertContentAt({from:0,to:u.content.size},n,{parseOptions:t,errorOnInvalidContent:(c=s.errorOnInvalidContent)!==null&&c!==void 0?c:o.options.enableContentCheck})};function bd(n,e){const t=Un(e,n.schema),{from:s,to:o,empty:r}=n.selection,i=[];r?(n.storedMarks&&i.push(...n.storedMarks),i.push(...n.selection.$head.marks())):n.doc.nodesBetween(s,o,a=>{i.push(...a.marks)});const l=i.find(a=>a.type.name===t.name);return l?{...l.attrs}:{}}function g0(n){for(let e=0;e<n.edgeCount;e+=1){const{type:t}=n.edge(e);if(t.isTextblock&&!t.hasRequiredAttrs())return t}return null}function v0(n,e){for(let t=n.depth;t>0;t-=1){const s=n.node(t);if(e(s))return{pos:t>0?n.before(t):0,start:n.start(t),depth:t,node:s}}}function Ml(n){return e=>v0(e.$from,n)}function y0(n,e){const t={from:0,to:n.content.size};return fd(n,t,e)}function b0(n,e){const t=Ge(e,n.schema),{from:s,to:o}=n.selection,r=[];n.doc.nodesBetween(s,o,l=>{r.push(l)});const i=r.reverse().find(l=>l.type.name===t.name);return i?{...i.attrs}:{}}function w0(n,e){const t=Vr(typeof e=="string"?e:e.name,n.schema);return t==="node"?b0(n,e):t==="mark"?bd(n,e):{}}function wd(n,e,t){const s=[];return n===e?t.resolve(n).marks().forEach(o=>{const r=t.resolve(n),i=Cl(r,o.type);i&&s.push({mark:o,...i})}):t.nodesBetween(n,e,(o,r)=>{!o||o?.nodeSize===void 0||s.push(...o.marks.map(i=>({from:r,to:r+o.nodeSize,mark:i})))}),s}function Zo(n,e,t){return Object.fromEntries(Object.entries(t).filter(([s])=>{const o=n.find(r=>r.type===e&&r.name===s);return o?o.attribute.keepOnSplit:!1}))}function ji(n,e,t={}){const{empty:s,ranges:o}=n.selection,r=e?Un(e,n.schema):null;if(s)return!!(n.storedMarks||n.selection.$from.marks()).filter(d=>r?r.name===d.type.name:!0).find(d=>mr(d.attrs,t,{strict:!1}));let i=0;const l=[];if(o.forEach(({$from:d,$to:v})=>{const p=d.pos,b=v.pos;n.doc.nodesBetween(p,b,(w,C)=>{if(!w.isText&&!w.marks.length)return;const M=Math.max(p,C),X=Math.min(b,C+w.nodeSize),L=X-M;i+=L,l.push(...w.marks.map(D=>({mark:D,from:M,to:X})))})}),i===0)return!1;const a=l.filter(d=>r?r.name===d.mark.type.name:!0).filter(d=>mr(d.mark.attrs,t,{strict:!1})).reduce((d,v)=>d+v.to-v.from,0),c=l.filter(d=>r?d.mark.type!==r&&d.mark.type.excludes(r):!0).reduce((d,v)=>d+v.to-v.from,0);return(a>0?a+c:a)>=i}function k0(n,e,t={}){if(!e)return go(n,null,t)||ji(n,null,t);const s=Vr(e,n.schema);return s==="node"?go(n,e,t):s==="mark"?ji(n,e,t):!1}function dc(n,e){const{nodeExtensions:t}=$r(e),s=t.find(i=>i.name===n);if(!s)return!1;const o={name:s.name,options:s.options,storage:s.storage},r=Se(ue(s,"group",o));return typeof r!="string"?!1:r.split(" ").includes("list")}function Lr(n,{checkChildren:e=!0,ignoreWhitespace:t=!1}={}){var s;if(t){if(n.type.name==="hardBreak")return!0;if(n.isText)return/^\s*$/m.test((s=n.text)!==null&&s!==void 0?s:"")}if(n.isText)return!n.text;if(n.isAtom||n.isLeaf)return!1;if(n.content.childCount===0)return!0;if(e){let o=!0;return n.content.forEach(r=>{o!==!1&&(Lr(r,{ignoreWhitespace:t,checkChildren:e})||(o=!1))}),o}return!1}function x0(n){return n instanceof de}function S0(n,e,t){var s;const{selection:o}=e;let r=null;if(md(o)&&(r=o.$cursor),r){const l=(s=n.storedMarks)!==null&&s!==void 0?s:r.marks();return!!t.isInSet(l)||!l.some(a=>a.type.excludes(t))}const{ranges:i}=o;return i.some(({$from:l,$to:a})=>{let c=l.depth===0?n.doc.inlineContent&&n.doc.type.allowsMarkType(t):!1;return n.doc.nodesBetween(l.pos,a.pos,(u,d,v)=>{if(c)return!1;if(u.isInline){const p=!v||v.type.allowsMarkType(t),b=!!t.isInSet(u.marks)||!u.marks.some(w=>w.type.excludes(t));c=p&&b}return!c}),c})}const C0=(n,e={})=>({tr:t,state:s,dispatch:o})=>{const{selection:r}=t,{empty:i,ranges:l}=r,a=Un(n,s.schema);if(o)if(i){const c=bd(s,a);t.addStoredMark(a.create({...c,...e}))}else l.forEach(c=>{const u=c.$from.pos,d=c.$to.pos;s.doc.nodesBetween(u,d,(v,p)=>{const b=Math.max(p,u),w=Math.min(p+v.nodeSize,d);v.marks.find(M=>M.type===a)?v.marks.forEach(M=>{a===M.type&&t.addMark(b,w,a.create({...M.attrs,...e}))}):t.addMark(b,w,a.create(e))})});return S0(s,t,a)},_0=(n,e)=>({tr:t})=>(t.setMeta(n,e),!0),M0=(n,e={})=>({state:t,dispatch:s,chain:o})=>{const r=Ge(n,t.schema);let i;return t.selection.$anchor.sameParent(t.selection.$head)&&(i=t.selection.$anchor.parent.attrs),r.isTextblock?o().command(({commands:l})=>rc(r,{...i,...e})(t)?!0:l.clearNodes()).command(({state:l})=>rc(r,{...i,...e})(l,s)).run():(console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.'),!1)},T0=n=>({tr:e,dispatch:t})=>{if(t){const{doc:s}=e,o=is(n,0,s.content.size),r=de.create(s,o);e.setSelection(r)}return!0},E0=n=>({tr:e,dispatch:t})=>{if(t){const{doc:s}=e,{from:o,to:r}=typeof n=="number"?{from:n,to:n}:n,i=ye.atStart(s).from,l=ye.atEnd(s).to,a=is(o,i,l),c=is(r,i,l),u=ye.create(s,a,c);e.setSelection(u)}return!0},I0=n=>({state:e,dispatch:t})=>{const s=Ge(n,e.schema);return c1(s)(e,t)};function fc(n,e){const t=n.storedMarks||n.selection.$to.parentOffset&&n.selection.$from.marks();if(t){const s=t.filter(o=>e?.includes(o.type.name));n.tr.ensureMarks(s)}}const O0=({keepMarks:n=!0}={})=>({tr:e,state:t,dispatch:s,editor:o})=>{const{selection:r,doc:i}=e,{$from:l,$to:a}=r,c=o.extensionManager.attributes,u=Zo(c,l.node().type.name,l.node().attrs);if(r instanceof de&&r.node.isBlock)return!l.parentOffset||!gn(i,l.pos)?!1:(s&&(n&&fc(t,o.extensionManager.splittableMarks),e.split(l.pos).scrollIntoView()),!0);if(!l.parent.isBlock)return!1;const d=a.parentOffset===a.parent.content.size,v=l.depth===0?void 0:g0(l.node(-1).contentMatchAt(l.indexAfter(-1)));let p=d&&v?[{type:v,attrs:u}]:void 0,b=gn(e.doc,e.mapping.map(l.pos),1,p);if(!p&&!b&&gn(e.doc,e.mapping.map(l.pos),1,v?[{type:v}]:void 0)&&(b=!0,p=v?[{type:v,attrs:u}]:void 0),s){if(b&&(r instanceof ye&&e.deleteSelection(),e.split(e.mapping.map(l.pos),1,p),v&&!d&&!l.parentOffset&&l.parent.type!==v)){const w=e.mapping.map(l.before()),C=e.doc.resolve(w);l.node(-1).canReplaceWith(C.index(),C.index()+1,v)&&e.setNodeMarkup(e.mapping.map(l.before()),v)}n&&fc(t,o.extensionManager.splittableMarks),e.scrollIntoView()}return b},N0=(n,e={})=>({tr:t,state:s,dispatch:o,editor:r})=>{var i;const l=Ge(n,s.schema),{$from:a,$to:c}=s.selection,u=s.selection.node;if(u&&u.isBlock||a.depth<2||!a.sameParent(c))return!1;const d=a.node(-1);if(d.type!==l)return!1;const v=r.extensionManager.attributes;if(a.parent.content.size===0&&a.node(-1).childCount===a.indexAfter(-1)){if(a.depth===2||a.node(-3).type!==l||a.index(-2)!==a.node(-2).childCount-1)return!1;if(o){let M=Q.empty;const X=a.index(-1)?1:a.index(-2)?2:3;for(let V=a.depth-X;V>=a.depth-3;V-=1)M=Q.from(a.node(V).copy(M));const L=a.indexAfter(-1)<a.node(-2).childCount?1:a.indexAfter(-2)<a.node(-3).childCount?2:3,D={...Zo(v,a.node().type.name,a.node().attrs),...e},E=((i=l.contentMatch.defaultType)===null||i===void 0?void 0:i.createAndFill(D))||void 0;M=M.append(Q.from(l.createAndFill(null,E)||void 0));const B=a.before(a.depth-(X-1));t.replace(B,a.after(-L),new le(M,4-X,0));let N=-1;t.doc.nodesBetween(B,t.doc.content.size,(V,_)=>{if(N>-1)return!1;V.isTextblock&&V.content.size===0&&(N=_+1)}),N>-1&&t.setSelection(ye.near(t.doc.resolve(N))),t.scrollIntoView()}return!0}const p=c.pos===a.end()?d.contentMatchAt(0).defaultType:null,b={...Zo(v,d.type.name,d.attrs),...e},w={...Zo(v,a.node().type.name,a.node().attrs),...e};t.delete(a.pos,c.pos);const C=p?[{type:l,attrs:b},{type:p,attrs:w}]:[{type:l,attrs:b}];if(!gn(t.doc,a.pos,2))return!1;if(o){const{selection:M,storedMarks:X}=s,{splittableMarks:L}=r.extensionManager,D=X||M.$to.parentOffset&&M.$from.marks();if(t.split(a.pos,2,C).scrollIntoView(),!D||!o)return!0;const E=D.filter(B=>L.includes(B.type.name));t.ensureMarks(E)}return!0},xi=(n,e)=>{const t=Ml(i=>i.type===e)(n.selection);if(!t)return!0;const s=n.doc.resolve(Math.max(0,t.pos-1)).before(t.depth);if(s===void 0)return!0;const o=n.doc.nodeAt(s);return t.node.type===o?.type&&qn(n.doc,t.pos)&&n.join(t.pos),!0},Si=(n,e)=>{const t=Ml(i=>i.type===e)(n.selection);if(!t)return!0;const s=n.doc.resolve(t.start).after(t.depth);if(s===void 0)return!0;const o=n.doc.nodeAt(s);return t.node.type===o?.type&&qn(n.doc,s)&&n.join(s),!0},D0=(n,e,t,s={})=>({editor:o,tr:r,state:i,dispatch:l,chain:a,commands:c,can:u})=>{const{extensions:d,splittableMarks:v}=o.extensionManager,p=Ge(n,i.schema),b=Ge(e,i.schema),{selection:w,storedMarks:C}=i,{$from:M,$to:X}=w,L=M.blockRange(X),D=C||w.$to.parentOffset&&w.$from.marks();if(!L)return!1;const E=Ml(B=>dc(B.type.name,d))(w);if(L.depth>=1&&E&&L.depth-E.depth<=1){if(E.node.type===p)return c.liftListItem(b);if(dc(E.node.type.name,d)&&p.validContent(E.node.content)&&l)return a().command(()=>(r.setNodeMarkup(E.pos,p),!0)).command(()=>xi(r,p)).command(()=>Si(r,p)).run()}return!t||!D||!l?a().command(()=>u().wrapInList(p,s)?!0:c.clearNodes()).wrapInList(p,s).command(()=>xi(r,p)).command(()=>Si(r,p)).run():a().command(()=>{const B=u().wrapInList(p,s),N=D.filter(V=>v.includes(V.type.name));return r.ensureMarks(N),B?!0:c.clearNodes()}).wrapInList(p,s).command(()=>xi(r,p)).command(()=>Si(r,p)).run()},A0=(n,e={},t={})=>({state:s,commands:o})=>{const{extendEmptyMarkRange:r=!1}=t,i=Un(n,s.schema);return ji(s,i,e)?o.unsetMark(i,{extendEmptyMarkRange:r}):o.setMark(i,e)},$0=(n,e,t={})=>({state:s,commands:o})=>{const r=Ge(n,s.schema),i=Ge(e,s.schema),l=go(s,r,t);let a;return s.selection.$anchor.sameParent(s.selection.$head)&&(a=s.selection.$anchor.parent.attrs),l?o.setNode(i,a):o.setNode(r,{...a,...t})},P0=(n,e={})=>({state:t,commands:s})=>{const o=Ge(n,t.schema);return go(t,o,e)?s.lift(o):s.wrapIn(o,e)},R0=()=>({state:n,dispatch:e})=>{const t=n.plugins;for(let s=0;s<t.length;s+=1){const o=t[s];let r;if(o.spec.isInputRules&&(r=o.getState(n))){if(e){const i=n.tr,l=r.transform;for(let a=l.steps.length-1;a>=0;a-=1)i.step(l.steps[a].invert(l.docs[a]));if(r.text){const a=i.doc.resolve(r.from).marks();i.replaceWith(r.from,r.to,n.schema.text(r.text,a))}else i.delete(r.from,r.to)}return!0}}return!1},V0=()=>({tr:n,dispatch:e})=>{const{selection:t}=n,{empty:s,ranges:o}=t;return s||e&&o.forEach(r=>{n.removeMark(r.$from.pos,r.$to.pos)}),!0},L0=(n,e={})=>({tr:t,state:s,dispatch:o})=>{var r;const{extendEmptyMarkRange:i=!1}=e,{selection:l}=t,a=Un(n,s.schema),{$from:c,empty:u,ranges:d}=l;if(!o)return!0;if(u&&i){let{from:v,to:p}=l;const b=(r=c.marks().find(C=>C.type===a))===null||r===void 0?void 0:r.attrs,w=Cl(c,a,b);w&&(v=w.from,p=w.to),t.removeMark(v,p,a)}else d.forEach(v=>{t.removeMark(v.$from.pos,v.$to.pos,a)});return t.removeStoredMark(a),!0},B0=(n,e={})=>({tr:t,state:s,dispatch:o})=>{let r=null,i=null;const l=Vr(typeof n=="string"?n:n.name,s.schema);return l?(l==="node"&&(r=Ge(n,s.schema)),l==="mark"&&(i=Un(n,s.schema)),o&&t.selection.ranges.forEach(a=>{const c=a.$from.pos,u=a.$to.pos;let d,v,p,b;t.selection.empty?s.doc.nodesBetween(c,u,(w,C)=>{r&&r===w.type&&(p=Math.max(C,c),b=Math.min(C+w.nodeSize,u),d=C,v=w)}):s.doc.nodesBetween(c,u,(w,C)=>{C<c&&r&&r===w.type&&(p=Math.max(C,c),b=Math.min(C+w.nodeSize,u),d=C,v=w),C>=c&&C<=u&&(r&&r===w.type&&t.setNodeMarkup(C,void 0,{...w.attrs,...e}),i&&w.marks.length&&w.marks.forEach(M=>{if(i===M.type){const X=Math.max(C,c),L=Math.min(C+w.nodeSize,u);t.addMark(X,L,i.create({...M.attrs,...e}))}}))}),v&&(d!==void 0&&t.setNodeMarkup(d,void 0,{...v.attrs,...e}),i&&v.marks.length&&v.marks.forEach(w=>{i===w.type&&t.addMark(p,b,i.create({...w.attrs,...e}))}))}),!0):!1},z0=(n,e={})=>({state:t,dispatch:s})=>{const o=Ge(n,t.schema);return n1(o,e)(t,s)},F0=(n,e={})=>({state:t,dispatch:s})=>{const o=Ge(n,t.schema);return s1(o,e)(t,s)};var W0=Object.freeze({__proto__:null,blur:M1,clearContent:T1,clearNodes:E1,command:I1,createParagraphNear:O1,cut:N1,deleteCurrentNode:D1,deleteNode:A1,deleteRange:$1,deleteSelection:P1,enter:R1,exitCode:V1,extendMarkRange:L1,first:B1,focus:F1,forEach:W1,insertContent:H1,insertContentAt:U1,joinBackward:X1,joinDown:K1,joinForward:Y1,joinItemBackward:G1,joinItemForward:Q1,joinTextblockBackward:Z1,joinTextblockForward:e0,joinUp:j1,keyboardShortcut:n0,lift:s0,liftEmptyBlock:o0,liftListItem:r0,newlineInCode:i0,resetAttributes:l0,scrollIntoView:a0,selectAll:c0,selectNodeBackward:u0,selectNodeForward:d0,selectParentNode:f0,selectTextblockEnd:p0,selectTextblockStart:h0,setContent:m0,setMark:C0,setMeta:_0,setNode:M0,setNodeSelection:T0,setTextSelection:E0,sinkListItem:I0,splitBlock:O0,splitListItem:N0,toggleList:D0,toggleMark:A0,toggleNode:$0,toggleWrap:P0,undoInputRule:R0,unsetAllMarks:V0,unsetMark:L0,updateAttributes:B0,wrapIn:z0,wrapInList:F0});const H0=Qe.create({name:"commands",addCommands(){return{...W0}}}),q0=Qe.create({name:"drop",addProseMirrorPlugins(){return[new nt({key:new Nt("tiptapDrop"),props:{handleDrop:(n,e,t,s)=>{this.editor.emit("drop",{editor:this.editor,event:e,slice:t,moved:s})}}})]}}),J0=Qe.create({name:"editable",addProseMirrorPlugins(){return[new nt({key:new Nt("editable"),props:{editable:()=>this.editor.options.editable}})]}}),U0=Qe.create({name:"focusEvents",addProseMirrorPlugins(){const{editor:n}=this;return[new nt({key:new Nt("focusEvents"),props:{handleDOMEvents:{focus:(e,t)=>{n.isFocused=!0;const s=n.state.tr.setMeta("focus",{event:t}).setMeta("addToHistory",!1);return e.dispatch(s),!1},blur:(e,t)=>{n.isFocused=!1;const s=n.state.tr.setMeta("blur",{event:t}).setMeta("addToHistory",!1);return e.dispatch(s),!1}}}})]}}),j0=Qe.create({name:"keymap",addKeyboardShortcuts(){const n=()=>this.editor.commands.first(({commands:i})=>[()=>i.undoInputRule(),()=>i.command(({tr:l})=>{const{selection:a,doc:c}=l,{empty:u,$anchor:d}=a,{pos:v,parent:p}=d,b=d.parent.isTextblock&&v>0?l.doc.resolve(v-1):d,w=b.parent.type.spec.isolating,C=d.pos-d.parentOffset,M=w&&b.parent.childCount===1?C===d.pos:xe.atStart(c).from===v;return!u||!p.type.isTextblock||p.textContent.length||!M||M&&d.parent.type.name==="paragraph"?!1:i.clearNodes()}),()=>i.deleteSelection(),()=>i.joinBackward(),()=>i.selectNodeBackward()]),e=()=>this.editor.commands.first(({commands:i})=>[()=>i.deleteSelection(),()=>i.deleteCurrentNode(),()=>i.joinForward(),()=>i.selectNodeForward()]),s={Enter:()=>this.editor.commands.first(({commands:i})=>[()=>i.newlineInCode(),()=>i.createParagraphNear(),()=>i.liftEmptyBlock(),()=>i.splitBlock()]),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:n,"Mod-Backspace":n,"Shift-Backspace":n,Delete:e,"Mod-Delete":e,"Mod-a":()=>this.editor.commands.selectAll()},o={...s},r={...s,"Ctrl-h":n,"Alt-Backspace":n,"Ctrl-d":e,"Ctrl-Alt-Backspace":e,"Alt-Delete":e,"Alt-d":e,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return _l()||yd()?r:o},addProseMirrorPlugins(){return[new nt({key:new Nt("clearDocument"),appendTransaction:(n,e,t)=>{if(n.some(w=>w.getMeta("composition")))return;const s=n.some(w=>w.docChanged)&&!e.doc.eq(t.doc),o=n.some(w=>w.getMeta("preventClearDocument"));if(!s||o)return;const{empty:r,from:i,to:l}=e.selection,a=xe.atStart(e.doc).from,c=xe.atEnd(e.doc).to;if(r||!(i===a&&l===c)||!Lr(t.doc))return;const v=t.tr,p=Dr({state:t,transaction:v}),{commands:b}=new Ar({editor:this.editor,state:p});if(b.clearNodes(),!!v.steps.length)return v}})]}}),K0=Qe.create({name:"paste",addProseMirrorPlugins(){return[new nt({key:new Nt("tiptapPaste"),props:{handlePaste:(n,e,t)=>{this.editor.emit("paste",{editor:this.editor,event:e,slice:t})}}})]}}),X0=Qe.create({name:"tabindex",addProseMirrorPlugins(){return[new nt({key:new Nt("tabindex"),props:{attributes:()=>this.editor.isEditable?{tabindex:"0"}:{}}})]}});class ns{get name(){return this.node.type.name}constructor(e,t,s=!1,o=null){this.currentNode=null,this.actualDepth=null,this.isBlock=s,this.resolvedPos=e,this.editor=t,this.currentNode=o}get node(){return this.currentNode||this.resolvedPos.node()}get element(){return this.editor.view.domAtPos(this.pos).node}get depth(){var e;return(e=this.actualDepth)!==null&&e!==void 0?e:this.resolvedPos.depth}get pos(){return this.resolvedPos.pos}get content(){return this.node.content}set content(e){let t=this.from,s=this.to;if(this.isBlock){if(this.content.size===0){console.error(`You can’t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`);return}t=this.from+1,s=this.to-1}this.editor.commands.insertContentAt({from:t,to:s},e)}get attributes(){return this.node.attrs}get textContent(){return this.node.textContent}get size(){return this.node.nodeSize}get from(){return this.isBlock?this.pos:this.resolvedPos.start(this.resolvedPos.depth)}get range(){return{from:this.from,to:this.to}}get to(){return this.isBlock?this.pos+this.size:this.resolvedPos.end(this.resolvedPos.depth)+(this.node.isText?0:1)}get parent(){if(this.depth===0)return null;const e=this.resolvedPos.start(this.resolvedPos.depth-1),t=this.resolvedPos.doc.resolve(e);return new ns(t,this.editor)}get before(){let e=this.resolvedPos.doc.resolve(this.from-(this.isBlock?1:2));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.from-3)),new ns(e,this.editor)}get after(){let e=this.resolvedPos.doc.resolve(this.to+(this.isBlock?2:1));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.to+3)),new ns(e,this.editor)}get children(){const e=[];return this.node.content.forEach((t,s)=>{const o=t.isBlock&&!t.isTextblock,r=t.isAtom&&!t.isText,i=this.pos+s+(r?0:1),l=this.resolvedPos.doc.resolve(i);if(!o&&l.depth<=this.depth)return;const a=new ns(l,this.editor,o,o?t:null);o&&(a.actualDepth=this.depth+1),e.push(new ns(l,this.editor,o,o?t:null))}),e}get firstChild(){return this.children[0]||null}get lastChild(){const e=this.children;return e[e.length-1]||null}closest(e,t={}){let s=null,o=this.parent;for(;o&&!s;){if(o.node.type.name===e)if(Object.keys(t).length>0){const r=o.node.attrs,i=Object.keys(t);for(let l=0;l<i.length;l+=1){const a=i[l];if(r[a]!==t[a])break}}else s=o;o=o.parent}return s}querySelector(e,t={}){return this.querySelectorAll(e,t,!0)[0]||null}querySelectorAll(e,t={},s=!1){let o=[];if(!this.children||this.children.length===0)return o;const r=Object.keys(t);return this.children.forEach(i=>{s&&o.length>0||(i.node.type.name===e&&r.every(a=>t[a]===i.node.attrs[a])&&o.push(i),!(s&&o.length>0)&&(o=o.concat(i.querySelectorAll(e,t,s))))}),o}setAttribute(e){const{tr:t}=this.editor.state;t.setNodeMarkup(this.from,void 0,{...this.node.attrs,...e}),this.editor.view.dispatch(t)}}const Y0=`.ProseMirror {
  position: relative;
}

.ProseMirror {
  word-wrap: break-word;
  white-space: pre-wrap;
  white-space: break-spaces;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0; /* the above doesn't seem to work in Edge */
}

.ProseMirror [contenteditable="false"] {
  white-space: normal;
}

.ProseMirror [contenteditable="false"] [contenteditable="true"] {
  white-space: pre-wrap;
}

.ProseMirror pre {
  white-space: pre-wrap;
}

img.ProseMirror-separator {
  display: inline !important;
  border: none !important;
  margin: 0 !important;
  width: 0 !important;
  height: 0 !important;
}

.ProseMirror-gapcursor {
  display: none;
  pointer-events: none;
  position: absolute;
  margin: 0;
}

.ProseMirror-gapcursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid black;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

.ProseMirror-hideselection *::selection {
  background: transparent;
}

.ProseMirror-hideselection *::-moz-selection {
  background: transparent;
}

.ProseMirror-hideselection * {
  caret-color: transparent;
}

.ProseMirror-focused .ProseMirror-gapcursor {
  display: block;
}

.tippy-box[data-animation=fade][data-state=hidden] {
  opacity: 0
}`;function G0(n,e,t){const s=document.querySelector("style[data-tiptap-style]");if(s!==null)return s;const o=document.createElement("style");return e&&o.setAttribute("nonce",e),o.setAttribute("data-tiptap-style",""),o.innerHTML=n,document.getElementsByTagName("head")[0].appendChild(o),o}let Q0=class extends u1{constructor(e={}){super(),this.isFocused=!1,this.isInitialized=!1,this.extensionStorage={},this.options={element:document.createElement("div"),content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},coreExtensionOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,enableContentCheck:!1,onBeforeCreate:()=>null,onCreate:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null,onContentError:({error:t})=>{throw t},onPaste:()=>null,onDrop:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(e),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.on("contentError",this.options.onContentError),this.createView(),this.injectCSS(),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),this.on("drop",({event:t,slice:s,moved:o})=>this.options.onDrop(t,s,o)),this.on("paste",({event:t,slice:s})=>this.options.onPaste(t,s)),window.setTimeout(()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}),this.isInitialized=!0)},0)}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&document&&(this.css=G0(Y0,this.options.injectNonce))}setOptions(e={}){this.options={...this.options,...e},!(!this.view||!this.state||this.isDestroyed)&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(e,t=!0){this.setOptions({editable:e}),t&&this.emit("update",{editor:this,transaction:this.state.tr})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get state(){return this.view.state}registerPlugin(e,t){const s=dd(t)?t(e,[...this.state.plugins]):[...this.state.plugins,e],o=this.state.reconfigure({plugins:s});return this.view.updateState(o),o}unregisterPlugin(e){if(this.isDestroyed)return;const t=this.state.plugins;let s=t;if([].concat(e).forEach(r=>{const i=typeof r=="string"?`${r}$`:r.key;s=t.filter(l=>!l.key.startsWith(i))}),t.length===s.length)return;const o=this.state.reconfigure({plugins:s});return this.view.updateState(o),o}createExtensionManager(){var e,t;const o=[...this.options.enableCoreExtensions?[J0,_1.configure({blockSeparator:(t=(e=this.options.coreExtensionOptions)===null||e===void 0?void 0:e.clipboardTextSerializer)===null||t===void 0?void 0:t.blockSeparator}),H0,U0,j0,X0,q0,K0].filter(r=>typeof this.options.enableCoreExtensions=="object"?this.options.enableCoreExtensions[r.name]!==!1:!0):[],...this.options.extensions].filter(r=>["extension","node","mark"].includes(r?.type));this.extensionManager=new Ds(o,this)}createCommandManager(){this.commandManager=new Ar({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createView(){var e;let t;try{t=Ui(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:this.options.enableContentCheck})}catch(i){if(!(i instanceof Error)||!["[tiptap error]: Invalid JSON content","[tiptap error]: Invalid HTML content"].includes(i.message))throw i;this.emit("contentError",{editor:this,error:i,disableCollaboration:()=>{this.storage.collaboration&&(this.storage.collaboration.isDisabled=!0),this.options.extensions=this.options.extensions.filter(l=>l.name!=="collaboration"),this.createExtensionManager()}}),t=Ui(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:!1})}const s=gd(t,this.options.autofocus);this.view=new $y(this.options.element,{...this.options.editorProps,attributes:{role:"textbox",...(e=this.options.editorProps)===null||e===void 0?void 0:e.attributes},dispatchTransaction:this.dispatchTransaction.bind(this),state:Ns.create({doc:t,selection:s||void 0})});const o=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(o),this.createNodeViews(),this.prependClass();const r=this.view.dom;r.editor=this}createNodeViews(){this.view.isDestroyed||this.view.setProps({nodeViews:this.extensionManager.nodeViews})}prependClass(){this.view.dom.className=`tiptap ${this.view.dom.className}`}captureTransaction(e){this.isCapturingTransaction=!0,e(),this.isCapturingTransaction=!1;const t=this.capturedTransaction;return this.capturedTransaction=null,t}dispatchTransaction(e){if(this.view.isDestroyed)return;if(this.isCapturingTransaction){if(!this.capturedTransaction){this.capturedTransaction=e;return}e.steps.forEach(i=>{var l;return(l=this.capturedTransaction)===null||l===void 0?void 0:l.step(i)});return}const t=this.state.apply(e),s=!this.state.selection.eq(t.selection);this.emit("beforeTransaction",{editor:this,transaction:e,nextState:t}),this.view.updateState(t),this.emit("transaction",{editor:this,transaction:e}),s&&this.emit("selectionUpdate",{editor:this,transaction:e});const o=e.getMeta("focus"),r=e.getMeta("blur");o&&this.emit("focus",{editor:this,event:o.event,transaction:e}),r&&this.emit("blur",{editor:this,event:r.event,transaction:e}),!(!e.docChanged||e.getMeta("preventUpdate"))&&this.emit("update",{editor:this,transaction:e})}getAttributes(e){return w0(this.state,e)}isActive(e,t){const s=typeof e=="string"?e:null,o=typeof e=="string"?t:e;return k0(this.state,s,o)}getJSON(){return this.state.doc.toJSON()}getHTML(){return xl(this.state.doc.content,this.schema)}getText(e){const{blockSeparator:t=`

`,textSerializers:s={}}=e||{};return y0(this.state.doc,{blockSeparator:t,textSerializers:{...pd(this.schema),...s}})}get isEmpty(){return Lr(this.state.doc)}getCharacterCount(){return console.warn('[tiptap warn]: "editor.getCharacterCount()" is deprecated. Please use "editor.storage.characterCount.characters()" instead.'),this.state.doc.content.size-2}destroy(){if(this.emit("destroy"),this.view){const e=this.view.dom;e&&e.editor&&delete e.editor,this.view.destroy()}this.removeAllListeners()}get isDestroyed(){var e;return!(!((e=this.view)===null||e===void 0)&&e.docView)}$node(e,t){var s;return((s=this.$doc)===null||s===void 0?void 0:s.querySelector(e,t))||null}$nodes(e,t){var s;return((s=this.$doc)===null||s===void 0?void 0:s.querySelectorAll(e,t))||null}$pos(e){const t=this.state.doc.resolve(e);return new ns(t,this)}get $doc(){return this.$pos(0)}};function Hs(n){return new Pr({find:n.find,handler:({state:e,range:t,match:s})=>{const o=Se(n.getAttributes,void 0,s);if(o===!1||o===null)return null;const{tr:r}=e,i=s[s.length-1],l=s[0];if(i){const a=l.search(/\S/),c=t.from+l.indexOf(i),u=c+i.length;if(wd(t.from,t.to,e.doc).filter(p=>p.mark.type.excluded.find(w=>w===n.type&&w!==p.mark.type)).filter(p=>p.to>c).length)return null;u<t.to&&r.delete(u,t.to),c>t.from&&r.delete(t.from+a,c);const v=t.from+a+i.length;r.addMark(t.from+a,v,n.type.create(o||{})),r.removeStoredMark(n.type)}}})}function Z0(n){return new Pr({find:n.find,handler:({state:e,range:t,match:s})=>{const o=Se(n.getAttributes,void 0,s)||{},{tr:r}=e,i=t.from;let l=t.to;const a=n.type.create(o);if(s[1]){const c=s[0].lastIndexOf(s[1]);let u=i+c;u>l?u=l:l=u+s[1].length;const d=s[0][s[0].length-1];r.insertText(d,i+s[0].length-1),r.replaceWith(u,l,a)}else if(s[0]){const c=n.type.isInline?i:i-1;r.insert(c,n.type.create(o)).delete(r.mapping.map(i),r.mapping.map(l))}r.scrollIntoView()}})}function Ki(n){return new Pr({find:n.find,handler:({state:e,range:t,match:s})=>{const o=e.doc.resolve(t.from),r=Se(n.getAttributes,void 0,s)||{};if(!o.node(-1).canReplaceWith(o.index(-1),o.indexAfter(-1),n.type))return null;e.tr.delete(t.from,t.to).setBlockType(t.from,t.from,n.type,r)}})}function vo(n){return new Pr({find:n.find,handler:({state:e,range:t,match:s,chain:o})=>{const r=Se(n.getAttributes,void 0,s)||{},i=e.tr.delete(t.from,t.to),a=i.doc.resolve(t.from).blockRange(),c=a&&il(a,n.type,r);if(!c)return null;if(i.wrap(a,c),n.keepMarks&&n.editor){const{selection:d,storedMarks:v}=e,{splittableMarks:p}=n.editor.extensionManager,b=v||d.$to.parentOffset&&d.$from.marks();if(b){const w=b.filter(C=>p.includes(C.type.name));i.ensureMarks(w)}}if(n.keepAttributes){const d=n.type.name==="bulletList"||n.type.name==="orderedList"?"listItem":"taskList";o().updateAttributes(d,r).run()}const u=i.doc.resolve(t.from-1).nodeBefore;u&&u.type===n.type&&qn(i.doc,t.from-1)&&(!n.joinPredicate||n.joinPredicate(s,u))&&i.join(t.from-1)}})}let Kt=class Xi{constructor(e={}){this.type="node",this.name="node",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=Se(ue(this,"addOptions",{name:this.name}))),this.storage=Se(ue(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new Xi(e)}configure(e={}){const t=this.extend({...this.config,addOptions:()=>Rr(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){const t=new Xi(e);return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=Se(ue(t,"addOptions",{name:t.name})),t.storage=Se(ue(t,"addStorage",{name:t.name,options:t.options})),t}};function qs(n){return new b1({find:n.find,handler:({state:e,range:t,match:s,pasteEvent:o})=>{const r=Se(n.getAttributes,void 0,s,o);if(r===!1||r===null)return null;const{tr:i}=e,l=s[s.length-1],a=s[0];let c=t.to;if(l){const u=a.search(/\S/),d=t.from+a.indexOf(l),v=d+l.length;if(wd(t.from,t.to,e.doc).filter(b=>b.mark.type.excluded.find(C=>C===n.type&&C!==b.mark.type)).filter(b=>b.to>d).length)return null;v<t.to&&i.delete(v,t.to),d>t.from&&i.delete(t.from+u,d),c=t.from+u+l.length,i.addMark(t.from+u,c,n.type.create(r||{})),i.removeStoredMark(n.type)}}})}function pc(n){return Cf((e,t)=>({get(){return e(),n},set(s){n=s,requestAnimationFrame(()=>{requestAnimationFrame(()=>{t()})})}}))}class eb extends Q0{constructor(e={}){return super(e),this.contentComponent=null,this.appContext=null,this.reactiveState=pc(this.view.state),this.reactiveExtensionStorage=pc(this.extensionStorage),this.on("beforeTransaction",({nextState:t})=>{this.reactiveState.value=t,this.reactiveExtensionStorage.value=this.extensionStorage}),Sf(this)}get state(){return this.reactiveState?this.reactiveState.value:this.view.state}get storage(){return this.reactiveExtensionStorage?this.reactiveExtensionStorage.value:super.storage}registerPlugin(e,t){const s=super.registerPlugin(e,t);return this.reactiveState&&(this.reactiveState.value=s),s}unregisterPlugin(e){const t=super.unregisterPlugin(e);return this.reactiveState&&t&&(this.reactiveState.value=t),t}}const tb=_f({name:"EditorContent",props:{editor:{default:null,type:Object}},setup(n){const e=I(),t=Ef();return Tf(()=>{const s=n.editor;s&&s.options.element&&e.value&&Le(()=>{if(!e.value||!s.options.element.firstChild)return;const o=j(e.value);e.value.append(...s.options.element.childNodes),s.contentComponent=t.ctx._,t&&(s.appContext={...t.appContext,provides:t.provides}),s.setOptions({element:o}),s.createNodeViews()})}),Mc(()=>{const s=n.editor;s&&(s.contentComponent=null,s.appContext=null)}),{rootEl:e}},render(){return Mf("div",{ref:n=>{this.rootEl=n}})}}),nb=/^\s*>\s$/,sb=Kt.create({name:"blockquote",addOptions(){return{HTMLAttributes:{}}},content:"block+",group:"block",defining:!0,parseHTML(){return[{tag:"blockquote"}]},renderHTML({HTMLAttributes:n}){return["blockquote",bt(this.options.HTMLAttributes,n),0]},addCommands(){return{setBlockquote:()=>({commands:n})=>n.wrapIn(this.name),toggleBlockquote:()=>({commands:n})=>n.toggleWrap(this.name),unsetBlockquote:()=>({commands:n})=>n.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[vo({find:nb,type:this.type})]}}),ob=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,rb=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,ib=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,lb=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,ab=Hn.create({name:"bold",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:n=>n.style.fontWeight!=="normal"&&null},{style:"font-weight=400",clearMark:n=>n.type.name===this.name},{style:"font-weight",getAttrs:n=>/^(bold(er)?|[5-9]\d{2,})$/.test(n)&&null}]},renderHTML({HTMLAttributes:n}){return["strong",bt(this.options.HTMLAttributes,n),0]},addCommands(){return{setBold:()=>({commands:n})=>n.setMark(this.name),toggleBold:()=>({commands:n})=>n.toggleMark(this.name),unsetBold:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[Hs({find:ob,type:this.type}),Hs({find:ib,type:this.type})]},addPasteRules(){return[qs({find:rb,type:this.type}),qs({find:lb,type:this.type})]}}),cb="listItem",hc="textStyle",mc=/^\s*([-+*])\s$/,ub=Kt.create({name:"bulletList",addOptions(){return{itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}},group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML(){return[{tag:"ul"}]},renderHTML({HTMLAttributes:n}){return["ul",bt(this.options.HTMLAttributes,n),0]},addCommands(){return{toggleBulletList:()=>({commands:n,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes(cb,this.editor.getAttributes(hc)).run():n.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let n=vo({find:mc,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(n=vo({find:mc,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(hc),editor:this.editor})),[n]}}),db=/(^|[^`])`([^`]+)`(?!`)/,fb=/(^|[^`])`([^`]+)`(?!`)/g,pb=Hn.create({name:"code",addOptions(){return{HTMLAttributes:{}}},excludes:"_",code:!0,exitable:!0,parseHTML(){return[{tag:"code"}]},renderHTML({HTMLAttributes:n}){return["code",bt(this.options.HTMLAttributes,n),0]},addCommands(){return{setCode:()=>({commands:n})=>n.setMark(this.name),toggleCode:()=>({commands:n})=>n.toggleMark(this.name),unsetCode:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[Hs({find:db,type:this.type})]},addPasteRules(){return[qs({find:fb,type:this.type})]}}),hb=/^```([a-z]+)?[\s\n]$/,mb=/^~~~([a-z]+)?[\s\n]$/,gb=Kt.create({name:"codeBlock",addOptions(){return{languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}},content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:this.options.defaultLanguage,parseHTML:n=>{var e;const{languageClassPrefix:t}=this.options,r=[...((e=n.firstElementChild)===null||e===void 0?void 0:e.classList)||[]].filter(i=>i.startsWith(t)).map(i=>i.replace(t,""))[0];return r||null},rendered:!1}}},parseHTML(){return[{tag:"pre",preserveWhitespace:"full"}]},renderHTML({node:n,HTMLAttributes:e}){return["pre",bt(this.options.HTMLAttributes,e),["code",{class:n.attrs.language?this.options.languageClassPrefix+n.attrs.language:null},0]]},addCommands(){return{setCodeBlock:n=>({commands:e})=>e.setNode(this.name,n),toggleCodeBlock:n=>({commands:e})=>e.toggleNode(this.name,"paragraph",n)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{const{empty:n,$anchor:e}=this.editor.state.selection,t=e.pos===1;return!n||e.parent.type.name!==this.name?!1:t||!e.parent.textContent.length?this.editor.commands.clearNodes():!1},Enter:({editor:n})=>{if(!this.options.exitOnTripleEnter)return!1;const{state:e}=n,{selection:t}=e,{$from:s,empty:o}=t;if(!o||s.parent.type!==this.type)return!1;const r=s.parentOffset===s.parent.nodeSize-2,i=s.parent.textContent.endsWith(`

`);return!r||!i?!1:n.chain().command(({tr:l})=>(l.delete(s.pos-2,s.pos),!0)).exitCode().run()},ArrowDown:({editor:n})=>{if(!this.options.exitOnArrowDown)return!1;const{state:e}=n,{selection:t,doc:s}=e,{$from:o,empty:r}=t;if(!r||o.parent.type!==this.type||!(o.parentOffset===o.parent.nodeSize-2))return!1;const l=o.after();return l===void 0?!1:s.nodeAt(l)?n.commands.command(({tr:c})=>(c.setSelection(xe.near(s.resolve(l))),!0)):n.commands.exitCode()}}},addInputRules(){return[Ki({find:hb,type:this.type,getAttributes:n=>({language:n[1]})}),Ki({find:mb,type:this.type,getAttributes:n=>({language:n[1]})})]},addProseMirrorPlugins(){return[new nt({key:new Nt("codeBlockVSCodeHandler"),props:{handlePaste:(n,e)=>{if(!e.clipboardData||this.editor.isActive(this.type.name))return!1;const t=e.clipboardData.getData("text/plain"),s=e.clipboardData.getData("vscode-editor-data"),o=s?JSON.parse(s):void 0,r=o?.mode;if(!t||!r)return!1;const{tr:i,schema:l}=n.state,a=l.text(t.replace(/\r\n?/g,`
`));return i.replaceSelectionWith(this.type.create({language:r},a)),i.selection.$from.parent.type!==this.type&&i.setSelection(ye.near(i.doc.resolve(Math.max(0,i.selection.from-2)))),i.setMeta("paste",!0),n.dispatch(i),!0}}})]}}),vb=Kt.create({name:"doc",topNode:!0,content:"block+"});function yb(n={}){return new nt({view(e){return new bb(e,n)}})}class bb{constructor(e,t){var s;this.editorView=e,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=(s=t.width)!==null&&s!==void 0?s:1,this.color=t.color===!1?void 0:t.color||"black",this.class=t.class,this.handlers=["dragover","dragend","drop","dragleave"].map(o=>{let r=i=>{this[o](i)};return e.dom.addEventListener(o,r),{name:o,handler:r}})}destroy(){this.handlers.forEach(({name:e,handler:t})=>this.editorView.dom.removeEventListener(e,t))}update(e,t){this.cursorPos!=null&&t.doc!=e.state.doc&&(this.cursorPos>e.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(e){e!=this.cursorPos&&(this.cursorPos=e,e==null?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let e=this.editorView.state.doc.resolve(this.cursorPos),t=!e.parent.inlineContent,s;if(t){let l=e.nodeBefore,a=e.nodeAfter;if(l||a){let c=this.editorView.nodeDOM(this.cursorPos-(l?l.nodeSize:0));if(c){let u=c.getBoundingClientRect(),d=l?u.bottom:u.top;l&&a&&(d=(d+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2),s={left:u.left,right:u.right,top:d-this.width/2,bottom:d+this.width/2}}}}if(!s){let l=this.editorView.coordsAtPos(this.cursorPos);s={left:l.left-this.width/2,right:l.left+this.width/2,top:l.top,bottom:l.bottom}}let o=this.editorView.dom.offsetParent;this.element||(this.element=o.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none;",this.color&&(this.element.style.backgroundColor=this.color)),this.element.classList.toggle("prosemirror-dropcursor-block",t),this.element.classList.toggle("prosemirror-dropcursor-inline",!t);let r,i;if(!o||o==document.body&&getComputedStyle(o).position=="static")r=-pageXOffset,i=-pageYOffset;else{let l=o.getBoundingClientRect();r=l.left-o.scrollLeft,i=l.top-o.scrollTop}this.element.style.left=s.left-r+"px",this.element.style.top=s.top-i+"px",this.element.style.width=s.right-s.left+"px",this.element.style.height=s.bottom-s.top+"px"}scheduleRemoval(e){clearTimeout(this.timeout),this.timeout=setTimeout(()=>this.setCursor(null),e)}dragover(e){if(!this.editorView.editable)return;let t=this.editorView.posAtCoords({left:e.clientX,top:e.clientY}),s=t&&t.inside>=0&&this.editorView.state.doc.nodeAt(t.inside),o=s&&s.type.spec.disableDropCursor,r=typeof o=="function"?o(this.editorView,t,e):o;if(t&&!r){let i=t.pos;if(this.editorView.dragging&&this.editorView.dragging.slice){let l=du(this.editorView.state.doc,i,this.editorView.dragging.slice);l!=null&&(i=l)}this.setCursor(i),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(e){(e.target==this.editorView.dom||!this.editorView.dom.contains(e.relatedTarget))&&this.setCursor(null)}}const wb=Qe.create({name:"dropCursor",addOptions(){return{color:"currentColor",width:1,class:void 0}},addProseMirrorPlugins(){return[yb(this.options)]}});class Je extends xe{constructor(e){super(e,e)}map(e,t){let s=e.resolve(t.map(this.head));return Je.valid(s)?new Je(s):xe.near(s)}content(){return le.empty}eq(e){return e instanceof Je&&e.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for GapCursor.fromJSON");return new Je(e.resolve(t.pos))}getBookmark(){return new Tl(this.anchor)}static valid(e){let t=e.parent;if(t.isTextblock||!kb(e)||!xb(e))return!1;let s=t.type.spec.allowGapCursor;if(s!=null)return s;let o=t.contentMatchAt(e.index()).defaultType;return o&&o.isTextblock}static findGapCursorFrom(e,t,s=!1){e:for(;;){if(!s&&Je.valid(e))return e;let o=e.pos,r=null;for(let i=e.depth;;i--){let l=e.node(i);if(t>0?e.indexAfter(i)<l.childCount:e.index(i)>0){r=l.child(t>0?e.indexAfter(i):e.index(i)-1);break}else if(i==0)return null;o+=t;let a=e.doc.resolve(o);if(Je.valid(a))return a}for(;;){let i=t>0?r.firstChild:r.lastChild;if(!i){if(r.isAtom&&!r.isText&&!de.isSelectable(r)){e=e.doc.resolve(o+r.nodeSize*t),s=!1;continue e}break}r=i,o+=t;let l=e.doc.resolve(o);if(Je.valid(l))return l}return null}}}Je.prototype.visible=!1;Je.findFrom=Je.findGapCursorFrom;xe.jsonID("gapcursor",Je);class Tl{constructor(e){this.pos=e}map(e){return new Tl(e.map(this.pos))}resolve(e){let t=e.resolve(this.pos);return Je.valid(t)?new Je(t):xe.near(t)}}function kb(n){for(let e=n.depth;e>=0;e--){let t=n.index(e),s=n.node(e);if(t==0){if(s.type.spec.isolating)return!0;continue}for(let o=s.child(t-1);;o=o.lastChild){if(o.childCount==0&&!o.inlineContent||o.isAtom||o.type.spec.isolating)return!0;if(o.inlineContent)return!1}}return!0}function xb(n){for(let e=n.depth;e>=0;e--){let t=n.indexAfter(e),s=n.node(e);if(t==s.childCount){if(s.type.spec.isolating)return!0;continue}for(let o=s.child(t);;o=o.firstChild){if(o.childCount==0&&!o.inlineContent||o.isAtom||o.type.spec.isolating)return!0;if(o.inlineContent)return!1}}return!0}function Sb(){return new nt({props:{decorations:Tb,createSelectionBetween(n,e,t){return e.pos==t.pos&&Je.valid(t)?new Je(t):null},handleClick:_b,handleKeyDown:Cb,handleDOMEvents:{beforeinput:Mb}}})}const Cb=Gu({ArrowLeft:jo("horiz",-1),ArrowRight:jo("horiz",1),ArrowUp:jo("vert",-1),ArrowDown:jo("vert",1)});function jo(n,e){const t=n=="vert"?e>0?"down":"up":e>0?"right":"left";return function(s,o,r){let i=s.selection,l=e>0?i.$to:i.$from,a=i.empty;if(i instanceof ye){if(!r.endOfTextblock(t)||l.depth==0)return!1;a=!1,l=s.doc.resolve(e>0?l.after():l.before())}let c=Je.findGapCursorFrom(l,e,a);return c?(o&&o(s.tr.setSelection(new Je(c))),!0):!1}}function _b(n,e,t){if(!n||!n.editable)return!1;let s=n.state.doc.resolve(e);if(!Je.valid(s))return!1;let o=n.posAtCoords({left:t.clientX,top:t.clientY});return o&&o.inside>-1&&de.isSelectable(n.state.doc.nodeAt(o.inside))?!1:(n.dispatch(n.state.tr.setSelection(new Je(s))),!0)}function Mb(n,e){if(e.inputType!="insertCompositionText"||!(n.state.selection instanceof Je))return!1;let{$from:t}=n.state.selection,s=t.parent.contentMatchAt(t.index()).findWrapping(n.state.schema.nodes.text);if(!s)return!1;let o=Q.empty;for(let i=s.length-1;i>=0;i--)o=Q.from(s[i].createAndFill(null,o));let r=n.state.tr.replace(t.pos,t.pos,new le(o,0,0));return r.setSelection(ye.near(r.doc.resolve(t.pos+1))),n.dispatch(r),!1}function Tb(n){if(!(n.selection instanceof Je))return null;let e=document.createElement("div");return e.className="ProseMirror-gapcursor",Be.create(n.doc,[mt.widget(n.selection.head,e,{key:"gapcursor"})])}const Eb=Qe.create({name:"gapCursor",addProseMirrorPlugins(){return[Sb()]},extendNodeSchema(n){var e;const t={name:n.name,options:n.options,storage:n.storage};return{allowGapCursor:(e=Se(ue(n,"allowGapCursor",t)))!==null&&e!==void 0?e:null}}}),Ib=Kt.create({name:"hardBreak",addOptions(){return{keepMarks:!0,HTMLAttributes:{}}},inline:!0,group:"inline",selectable:!1,linebreakReplacement:!0,parseHTML(){return[{tag:"br"}]},renderHTML({HTMLAttributes:n}){return["br",bt(this.options.HTMLAttributes,n)]},renderText(){return`
`},addCommands(){return{setHardBreak:()=>({commands:n,chain:e,state:t,editor:s})=>n.first([()=>n.exitCode(),()=>n.command(()=>{const{selection:o,storedMarks:r}=t;if(o.$from.parent.type.spec.isolating)return!1;const{keepMarks:i}=this.options,{splittableMarks:l}=s.extensionManager,a=r||o.$to.parentOffset&&o.$from.marks();return e().insertContent({type:this.name}).command(({tr:c,dispatch:u})=>{if(u&&a&&i){const d=a.filter(v=>l.includes(v.type.name));c.ensureMarks(d)}return!0}).run()})])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),Ob=Kt.create({name:"heading",addOptions(){return{levels:[1,2,3,4,5,6],HTMLAttributes:{}}},content:"inline*",group:"block",defining:!0,addAttributes(){return{level:{default:1,rendered:!1}}},parseHTML(){return this.options.levels.map(n=>({tag:`h${n}`,attrs:{level:n}}))},renderHTML({node:n,HTMLAttributes:e}){return[`h${this.options.levels.includes(n.attrs.level)?n.attrs.level:this.options.levels[0]}`,bt(this.options.HTMLAttributes,e),0]},addCommands(){return{setHeading:n=>({commands:e})=>this.options.levels.includes(n.level)?e.setNode(this.name,n):!1,toggleHeading:n=>({commands:e})=>this.options.levels.includes(n.level)?e.toggleNode(this.name,"paragraph",n):!1}},addKeyboardShortcuts(){return this.options.levels.reduce((n,e)=>({...n,[`Mod-Alt-${e}`]:()=>this.editor.commands.toggleHeading({level:e})}),{})},addInputRules(){return this.options.levels.map(n=>Ki({find:new RegExp(`^(#{${Math.min(...this.options.levels)},${n}})\\s$`),type:this.type,getAttributes:{level:n}}))}});var vr=200,Ye=function(){};Ye.prototype.append=function(e){return e.length?(e=Ye.from(e),!this.length&&e||e.length<vr&&this.leafAppend(e)||this.length<vr&&e.leafPrepend(this)||this.appendInner(e)):this};Ye.prototype.prepend=function(e){return e.length?Ye.from(e).append(this):this};Ye.prototype.appendInner=function(e){return new Nb(this,e)};Ye.prototype.slice=function(e,t){return e===void 0&&(e=0),t===void 0&&(t=this.length),e>=t?Ye.empty:this.sliceInner(Math.max(0,e),Math.min(this.length,t))};Ye.prototype.get=function(e){if(!(e<0||e>=this.length))return this.getInner(e)};Ye.prototype.forEach=function(e,t,s){t===void 0&&(t=0),s===void 0&&(s=this.length),t<=s?this.forEachInner(e,t,s,0):this.forEachInvertedInner(e,t,s,0)};Ye.prototype.map=function(e,t,s){t===void 0&&(t=0),s===void 0&&(s=this.length);var o=[];return this.forEach(function(r,i){return o.push(e(r,i))},t,s),o};Ye.from=function(e){return e instanceof Ye?e:e&&e.length?new kd(e):Ye.empty};var kd=function(n){function e(s){n.call(this),this.values=s}n&&(e.__proto__=n),e.prototype=Object.create(n&&n.prototype),e.prototype.constructor=e;var t={length:{configurable:!0},depth:{configurable:!0}};return e.prototype.flatten=function(){return this.values},e.prototype.sliceInner=function(o,r){return o==0&&r==this.length?this:new e(this.values.slice(o,r))},e.prototype.getInner=function(o){return this.values[o]},e.prototype.forEachInner=function(o,r,i,l){for(var a=r;a<i;a++)if(o(this.values[a],l+a)===!1)return!1},e.prototype.forEachInvertedInner=function(o,r,i,l){for(var a=r-1;a>=i;a--)if(o(this.values[a],l+a)===!1)return!1},e.prototype.leafAppend=function(o){if(this.length+o.length<=vr)return new e(this.values.concat(o.flatten()))},e.prototype.leafPrepend=function(o){if(this.length+o.length<=vr)return new e(o.flatten().concat(this.values))},t.length.get=function(){return this.values.length},t.depth.get=function(){return 0},Object.defineProperties(e.prototype,t),e}(Ye);Ye.empty=new kd([]);var Nb=function(n){function e(t,s){n.call(this),this.left=t,this.right=s,this.length=t.length+s.length,this.depth=Math.max(t.depth,s.depth)+1}return n&&(e.__proto__=n),e.prototype=Object.create(n&&n.prototype),e.prototype.constructor=e,e.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},e.prototype.getInner=function(s){return s<this.left.length?this.left.get(s):this.right.get(s-this.left.length)},e.prototype.forEachInner=function(s,o,r,i){var l=this.left.length;if(o<l&&this.left.forEachInner(s,o,Math.min(r,l),i)===!1||r>l&&this.right.forEachInner(s,Math.max(o-l,0),Math.min(this.length,r)-l,i+l)===!1)return!1},e.prototype.forEachInvertedInner=function(s,o,r,i){var l=this.left.length;if(o>l&&this.right.forEachInvertedInner(s,o-l,Math.max(r,l)-l,i+l)===!1||r<l&&this.left.forEachInvertedInner(s,Math.min(o,l),r,i)===!1)return!1},e.prototype.sliceInner=function(s,o){if(s==0&&o==this.length)return this;var r=this.left.length;return o<=r?this.left.slice(s,o):s>=r?this.right.slice(s-r,o-r):this.left.slice(s,r).append(this.right.slice(0,o-r))},e.prototype.leafAppend=function(s){var o=this.right.leafAppend(s);if(o)return new e(this.left,o)},e.prototype.leafPrepend=function(s){var o=this.left.leafPrepend(s);if(o)return new e(o,this.right)},e.prototype.appendInner=function(s){return this.left.depth>=Math.max(this.right.depth,s.depth)+1?new e(this.left,new e(this.right,s)):new e(this,s)},e}(Ye);const Db=500;class Jt{constructor(e,t){this.items=e,this.eventCount=t}popEvent(e,t){if(this.eventCount==0)return null;let s=this.items.length;for(;;s--)if(this.items.get(s-1).selection){--s;break}let o,r;t&&(o=this.remapping(s,this.items.length),r=o.maps.length);let i=e.tr,l,a,c=[],u=[];return this.items.forEach((d,v)=>{if(!d.step){o||(o=this.remapping(s,v+1),r=o.maps.length),r--,u.push(d);return}if(o){u.push(new tn(d.map));let p=d.step.map(o.slice(r)),b;p&&i.maybeStep(p).doc&&(b=i.mapping.maps[i.mapping.maps.length-1],c.push(new tn(b,void 0,void 0,c.length+u.length))),r--,b&&o.appendMap(b,r)}else i.maybeStep(d.step);if(d.selection)return l=o?d.selection.map(o.slice(r)):d.selection,a=new Jt(this.items.slice(0,s).append(u.reverse().concat(c)),this.eventCount-1),!1},this.items.length,0),{remaining:a,transform:i,selection:l}}addTransform(e,t,s,o){let r=[],i=this.eventCount,l=this.items,a=!o&&l.length?l.get(l.length-1):null;for(let u=0;u<e.steps.length;u++){let d=e.steps[u].invert(e.docs[u]),v=new tn(e.mapping.maps[u],d,t),p;(p=a&&a.merge(v))&&(v=p,u?r.pop():l=l.slice(0,l.length-1)),r.push(v),t&&(i++,t=void 0),o||(a=v)}let c=i-s.depth;return c>$b&&(l=Ab(l,c),i-=c),new Jt(l.append(r),i)}remapping(e,t){let s=new uo;return this.items.forEach((o,r)=>{let i=o.mirrorOffset!=null&&r-o.mirrorOffset>=e?s.maps.length-o.mirrorOffset:void 0;s.appendMap(o.map,i)},e,t),s}addMaps(e){return this.eventCount==0?this:new Jt(this.items.append(e.map(t=>new tn(t))),this.eventCount)}rebased(e,t){if(!this.eventCount)return this;let s=[],o=Math.max(0,this.items.length-t),r=e.mapping,i=e.steps.length,l=this.eventCount;this.items.forEach(v=>{v.selection&&l--},o);let a=t;this.items.forEach(v=>{let p=r.getMirror(--a);if(p==null)return;i=Math.min(i,p);let b=r.maps[p];if(v.step){let w=e.steps[p].invert(e.docs[p]),C=v.selection&&v.selection.map(r.slice(a+1,p));C&&l++,s.push(new tn(b,w,C))}else s.push(new tn(b))},o);let c=[];for(let v=t;v<i;v++)c.push(new tn(r.maps[v]));let u=this.items.slice(0,o).append(c).append(s),d=new Jt(u,l);return d.emptyItemCount()>Db&&(d=d.compress(this.items.length-s.length)),d}emptyItemCount(){let e=0;return this.items.forEach(t=>{t.step||e++}),e}compress(e=this.items.length){let t=this.remapping(0,e),s=t.maps.length,o=[],r=0;return this.items.forEach((i,l)=>{if(l>=e)o.push(i),i.selection&&r++;else if(i.step){let a=i.step.map(t.slice(s)),c=a&&a.getMap();if(s--,c&&t.appendMap(c,s),a){let u=i.selection&&i.selection.map(t.slice(s));u&&r++;let d=new tn(c.invert(),a,u),v,p=o.length-1;(v=o.length&&o[p].merge(d))?o[p]=v:o.push(d)}}else i.map&&s--},this.items.length,0),new Jt(Ye.from(o.reverse()),r)}}Jt.empty=new Jt(Ye.empty,0);function Ab(n,e){let t;return n.forEach((s,o)=>{if(s.selection&&e--==0)return t=o,!1}),n.slice(t)}class tn{constructor(e,t,s,o){this.map=e,this.step=t,this.selection=s,this.mirrorOffset=o}merge(e){if(this.step&&e.step&&!e.selection){let t=e.step.merge(this.step);if(t)return new tn(t.getMap().invert(),t,this.selection)}}}class On{constructor(e,t,s,o,r){this.done=e,this.undone=t,this.prevRanges=s,this.prevTime=o,this.prevComposition=r}}const $b=20;function Pb(n,e,t,s){let o=t.getMeta(ds),r;if(o)return o.historyState;t.getMeta(Lb)&&(n=new On(n.done,n.undone,null,0,-1));let i=t.getMeta("appendedTransaction");if(t.steps.length==0)return n;if(i&&i.getMeta(ds))return i.getMeta(ds).redo?new On(n.done.addTransform(t,void 0,s,er(e)),n.undone,gc(t.mapping.maps),n.prevTime,n.prevComposition):new On(n.done,n.undone.addTransform(t,void 0,s,er(e)),null,n.prevTime,n.prevComposition);if(t.getMeta("addToHistory")!==!1&&!(i&&i.getMeta("addToHistory")===!1)){let l=t.getMeta("composition"),a=n.prevTime==0||!i&&n.prevComposition!=l&&(n.prevTime<(t.time||0)-s.newGroupDelay||!Rb(t,n.prevRanges)),c=i?Ci(n.prevRanges,t.mapping):gc(t.mapping.maps);return new On(n.done.addTransform(t,a?e.selection.getBookmark():void 0,s,er(e)),Jt.empty,c,t.time,l??n.prevComposition)}else return(r=t.getMeta("rebased"))?new On(n.done.rebased(t,r),n.undone.rebased(t,r),Ci(n.prevRanges,t.mapping),n.prevTime,n.prevComposition):new On(n.done.addMaps(t.mapping.maps),n.undone.addMaps(t.mapping.maps),Ci(n.prevRanges,t.mapping),n.prevTime,n.prevComposition)}function Rb(n,e){if(!e)return!1;if(!n.docChanged)return!0;let t=!1;return n.mapping.maps[0].forEach((s,o)=>{for(let r=0;r<e.length;r+=2)s<=e[r+1]&&o>=e[r]&&(t=!0)}),t}function gc(n){let e=[];for(let t=n.length-1;t>=0&&e.length==0;t--)n[t].forEach((s,o,r,i)=>e.push(r,i));return e}function Ci(n,e){if(!n)return null;let t=[];for(let s=0;s<n.length;s+=2){let o=e.map(n[s],1),r=e.map(n[s+1],-1);o<=r&&t.push(o,r)}return t}function Vb(n,e,t){let s=er(e),o=ds.get(e).spec.config,r=(t?n.undone:n.done).popEvent(e,s);if(!r)return null;let i=r.selection.resolve(r.transform.doc),l=(t?n.done:n.undone).addTransform(r.transform,e.selection.getBookmark(),o,s),a=new On(t?l:r.remaining,t?r.remaining:l,null,0,-1);return r.transform.setSelection(i).setMeta(ds,{redo:t,historyState:a})}let _i=!1,vc=null;function er(n){let e=n.plugins;if(vc!=e){_i=!1,vc=e;for(let t=0;t<e.length;t++)if(e[t].spec.historyPreserveItems){_i=!0;break}}return _i}const ds=new Nt("history"),Lb=new Nt("closeHistory");function Bb(n={}){return n={depth:n.depth||100,newGroupDelay:n.newGroupDelay||500},new nt({key:ds,state:{init(){return new On(Jt.empty,Jt.empty,null,0,-1)},apply(e,t,s){return Pb(t,s,e,n)}},config:n,props:{handleDOMEvents:{beforeinput(e,t){let s=t.inputType,o=s=="historyUndo"?Sd:s=="historyRedo"?Cd:null;return o?(t.preventDefault(),o(e.state,e.dispatch)):!1}}}})}function xd(n,e){return(t,s)=>{let o=ds.getState(t);if(!o||(n?o.undone:o.done).eventCount==0)return!1;if(s){let r=Vb(o,t,n);r&&s(e?r.scrollIntoView():r)}return!0}}const Sd=xd(!1,!0),Cd=xd(!0,!0),zb=Qe.create({name:"history",addOptions(){return{depth:100,newGroupDelay:500}},addCommands(){return{undo:()=>({state:n,dispatch:e})=>Sd(n,e),redo:()=>({state:n,dispatch:e})=>Cd(n,e)}},addProseMirrorPlugins(){return[Bb(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}}),Fb=Kt.create({name:"horizontalRule",addOptions(){return{HTMLAttributes:{}}},group:"block",parseHTML(){return[{tag:"hr"}]},renderHTML({HTMLAttributes:n}){return["hr",bt(this.options.HTMLAttributes,n)]},addCommands(){return{setHorizontalRule:()=>({chain:n,state:e})=>{const{selection:t}=e,{$from:s,$to:o}=t,r=n();return s.parentOffset===0?r.insertContentAt({from:Math.max(s.pos-1,0),to:o.pos},{type:this.name}):x0(t)?r.insertContentAt(o.pos,{type:this.name}):r.insertContent({type:this.name}),r.command(({tr:i,dispatch:l})=>{var a;if(l){const{$to:c}=i.selection,u=c.end();if(c.nodeAfter)c.nodeAfter.isTextblock?i.setSelection(ye.create(i.doc,c.pos+1)):c.nodeAfter.isBlock?i.setSelection(de.create(i.doc,c.pos)):i.setSelection(ye.create(i.doc,c.pos));else{const d=(a=c.parent.type.contentMatch.defaultType)===null||a===void 0?void 0:a.create();d&&(i.insert(u,d),i.setSelection(ye.create(i.doc,u+1)))}i.scrollIntoView()}return!0}).run()}}},addInputRules(){return[Z0({find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type})]}}),Wb=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,Hb=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,qb=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,Jb=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,Ub=Hn.create({name:"italic",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:n=>n.style.fontStyle!=="normal"&&null},{style:"font-style=normal",clearMark:n=>n.type.name===this.name},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:n}){return["em",bt(this.options.HTMLAttributes,n),0]},addCommands(){return{setItalic:()=>({commands:n})=>n.setMark(this.name),toggleItalic:()=>({commands:n})=>n.toggleMark(this.name),unsetItalic:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[Hs({find:Wb,type:this.type}),Hs({find:qb,type:this.type})]},addPasteRules(){return[qs({find:Hb,type:this.type}),qs({find:Jb,type:this.type})]}}),jb=Kt.create({name:"listItem",addOptions(){return{HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}},content:"paragraph block*",defining:!0,parseHTML(){return[{tag:"li"}]},renderHTML({HTMLAttributes:n}){return["li",bt(this.options.HTMLAttributes,n),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),Kb="listItem",yc="textStyle",bc=/^(\d+)\.\s$/,Xb=Kt.create({name:"orderedList",addOptions(){return{itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}},group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes(){return{start:{default:1,parseHTML:n=>n.hasAttribute("start")?parseInt(n.getAttribute("start")||"",10):1},type:{default:void 0,parseHTML:n=>n.getAttribute("type")}}},parseHTML(){return[{tag:"ol"}]},renderHTML({HTMLAttributes:n}){const{start:e,...t}=n;return e===1?["ol",bt(this.options.HTMLAttributes,t),0]:["ol",bt(this.options.HTMLAttributes,n),0]},addCommands(){return{toggleOrderedList:()=>({commands:n,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes(Kb,this.editor.getAttributes(yc)).run():n.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let n=vo({find:bc,type:this.type,getAttributes:e=>({start:+e[1]}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(n=vo({find:bc,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:e=>({start:+e[1],...this.editor.getAttributes(yc)}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1],editor:this.editor})),[n]}}),Yb=Kt.create({name:"paragraph",priority:1e3,addOptions(){return{HTMLAttributes:{}}},group:"block",content:"inline*",parseHTML(){return[{tag:"p"}]},renderHTML({HTMLAttributes:n}){return["p",bt(this.options.HTMLAttributes,n),0]},addCommands(){return{setParagraph:()=>({commands:n})=>n.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),Gb=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,Qb=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,Zb=Hn.create({name:"strike",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:n=>n.includes("line-through")?{}:!1}]},renderHTML({HTMLAttributes:n}){return["s",bt(this.options.HTMLAttributes,n),0]},addCommands(){return{setStrike:()=>({commands:n})=>n.setMark(this.name),toggleStrike:()=>({commands:n})=>n.toggleMark(this.name),unsetStrike:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[Hs({find:Gb,type:this.type})]},addPasteRules(){return[qs({find:Qb,type:this.type})]}}),ew=Kt.create({name:"text",group:"inline"}),tw=Qe.create({name:"starterKit",addExtensions(){var n,e,t,s,o,r,i,l,a,c,u,d,v,p,b,w,C,M;const X=[];return this.options.bold!==!1&&X.push(ab.configure((n=this.options)===null||n===void 0?void 0:n.bold)),this.options.blockquote!==!1&&X.push(sb.configure((e=this.options)===null||e===void 0?void 0:e.blockquote)),this.options.bulletList!==!1&&X.push(ub.configure((t=this.options)===null||t===void 0?void 0:t.bulletList)),this.options.code!==!1&&X.push(pb.configure((s=this.options)===null||s===void 0?void 0:s.code)),this.options.codeBlock!==!1&&X.push(gb.configure((o=this.options)===null||o===void 0?void 0:o.codeBlock)),this.options.document!==!1&&X.push(vb.configure((r=this.options)===null||r===void 0?void 0:r.document)),this.options.dropcursor!==!1&&X.push(wb.configure((i=this.options)===null||i===void 0?void 0:i.dropcursor)),this.options.gapcursor!==!1&&X.push(Eb.configure((l=this.options)===null||l===void 0?void 0:l.gapcursor)),this.options.hardBreak!==!1&&X.push(Ib.configure((a=this.options)===null||a===void 0?void 0:a.hardBreak)),this.options.heading!==!1&&X.push(Ob.configure((c=this.options)===null||c===void 0?void 0:c.heading)),this.options.history!==!1&&X.push(zb.configure((u=this.options)===null||u===void 0?void 0:u.history)),this.options.horizontalRule!==!1&&X.push(Fb.configure((d=this.options)===null||d===void 0?void 0:d.horizontalRule)),this.options.italic!==!1&&X.push(Ub.configure((v=this.options)===null||v===void 0?void 0:v.italic)),this.options.listItem!==!1&&X.push(jb.configure((p=this.options)===null||p===void 0?void 0:p.listItem)),this.options.orderedList!==!1&&X.push(Xb.configure((b=this.options)===null||b===void 0?void 0:b.orderedList)),this.options.paragraph!==!1&&X.push(Yb.configure((w=this.options)===null||w===void 0?void 0:w.paragraph)),this.options.strike!==!1&&X.push(Zb.configure((C=this.options)===null||C===void 0?void 0:C.strike)),this.options.text!==!1&&X.push(ew.configure((M=this.options)===null||M===void 0?void 0:M.text)),X}});Qe.create({name:"placeholder",addOptions(){return{emptyEditorClass:"is-editor-empty",emptyNodeClass:"is-empty",placeholder:"Write something …",showOnlyWhenEditable:!0,showOnlyCurrent:!0,includeChildren:!1}},addProseMirrorPlugins(){return[new nt({key:new Nt("placeholder"),props:{decorations:({doc:n,selection:e})=>{const t=this.editor.isEditable||!this.options.showOnlyWhenEditable,{anchor:s}=e,o=[];if(!t)return null;const r=this.editor.isEmpty;return n.descendants((i,l)=>{const a=s>=l&&s<=l+i.nodeSize,c=!i.isLeaf&&Lr(i);if((a||!this.options.showOnlyCurrent)&&c){const u=[this.options.emptyNodeClass];r&&u.push(this.options.emptyEditorClass);const d=mt.node(l,l+i.nodeSize,{class:u.join(" "),"data-placeholder":typeof this.options.placeholder=="function"?this.options.placeholder({editor:this.editor,node:i,pos:l,hasAnchor:a}):this.options.placeholder});o.push(d)}return this.options.includeChildren}),Be.create(n,o)}}})]}});const nw=["onClick"],sw={key:0,class:"menu-item-icon"},ow={class:"menu-item-text"},rw={key:1,class:"menu-item-shortcut"},iw={__name:"ContextMenu",props:{menuItems:{type:Array,required:!0,default:()=>[]},x:{type:Number,required:!0},y:{type:Number,required:!0},visible:{type:Boolean,required:!0}},emits:["update:visible","select","close"],setup(n,{emit:e}){const t=e,s=I(null),o=l=>{s.value&&!s.value.contains(l.target)&&t("close")},r=l=>{l.key==="Escape"&&t("close")},i=l=>{l.disabled||(t("select",l),t("close"))};return ct(()=>{document.addEventListener("click",o),document.addEventListener("keydown",r)}),lt(()=>{document.removeEventListener("click",o),document.removeEventListener("keydown",r)}),(l,a)=>Fn((P(),q("div",{ref_key:"menuRef",ref:s,class:"context-menu",style:Et({left:`${n.x}px`,top:`${n.y}px`}),onClick:a[0]||(a[0]=Oe(()=>{},["stop"]))},[(P(!0),q(_e,null,Ae(n.menuItems,c=>(P(),q("div",{key:c.id,class:we(["menu-item",{disabled:c.disabled}]),onClick:Oe(u=>i(c),["stop"])},[c.icon?(P(),q("span",sw,[(P(),Ne(Tc(c.icon)))])):ge("",!0),g("span",ow,ee(c.name),1),c.shortcut?(P(),q("span",rw,ee(c.shortcut),1)):ge("",!0)],10,nw))),128))],4)),[[bo,n.visible]])}},lw=ut(iw,[["__scopeId","data-v-36ca75df"]]),aw={class:"window-header"},cw={class:"window-title"},uw={class:"window-controls"},dw={class:"window-content"},fw={__name:"FloatingWindow",props:{title:{type:String,default:""},visible:{type:Boolean,required:!0},width:{type:[Number,String],default:400},height:{type:[Number,String],default:400},initialPosition:{type:Object,default:()=>({x:100,y:100})},pinnable:{type:Boolean,default:!1},onResize:{type:Function,default:null}},emits:["update:visible","close","move","resize"],setup(n,{emit:e}){const t=n,s=e,o=I(null),r=I(!1),i=I(!1),l=I(!0),a=I({...t.initialPosition}),c=I({width:typeof t.width=="number"?t.width:parseInt(t.width),height:typeof t.height=="number"?t.height:parseInt(t.height)}),u=I({x:0,y:0}),d=I({x:0,y:0}),v=I({width:0,height:0}),p=E=>{if(E.target.closest(".window-content")||E.target.closest(".resize-handle"))return;r.value=!0;const B=o.value.getBoundingClientRect();u.value={x:E.clientX-B.left,y:E.clientY-B.top},document.addEventListener("mousemove",b),document.addEventListener("mouseup",w)},b=E=>{if(!r.value)return;const B={x:E.clientX-u.value.x,y:E.clientY-u.value.y};a.value=B,s("move",B)},w=()=>{r.value=!1,document.removeEventListener("mousemove",b),document.removeEventListener("mouseup",w)},C=E=>{i.value=!0,d.value={x:E.clientX,y:E.clientY},v.value={...c.value},document.addEventListener("mousemove",M),document.addEventListener("mouseup",X)},M=E=>{if(!i.value)return;const B=E.clientX-d.value.x,N=E.clientY-d.value.y,V={width:Math.max(300,v.value.width+B),height:Math.max(400,v.value.height+N)};c.value=V,s("resize",V)},X=()=>{i.value=!1,document.removeEventListener("mousemove",M),document.removeEventListener("mouseup",X)},L=E=>{o.value&&!o.value.contains(E.target)&&!l.value&&s("close")},D=()=>{l.value=!l.value};return ct(()=>{document.addEventListener("mousedown",L)}),lt(()=>{document.removeEventListener("mousedown",L)}),(E,B)=>Fn((P(),q("div",{ref_key:"windowRef",ref:o,class:we(["floating-window",{"is-pinned":l.value}]),style:Et({left:`${a.value.x}px`,top:`${a.value.y}px`,width:`${c.value.width}px`,height:`${c.value.height}px`}),onMousedown:p},[g("div",aw,[g("div",cw,[g("span",null,ee(n.title),1),nr(E.$slots,"title-extra",{},void 0,!0)]),g("div",uw,[n.pinnable?(P(),q("button",{key:0,class:we(["control-btn pin-btn",{active:l.value}]),onClick:Oe(D,["stop"])},B[1]||(B[1]=[g("svg",{class:"pin-icon",viewBox:"0 0 24 24",width:"16",height:"16"},[g("path",{d:"M16,12V4H17V2H7V4H8V12L6,14V16H11.2V22H12.8V16H18V14L16,12Z"})],-1)]),2)):ge("",!0),g("button",{class:"control-btn close-btn",onClick:B[0]||(B[0]=Oe(N=>s("close"),["stop"]))},B[2]||(B[2]=[g("svg",{class:"close-icon",viewBox:"0 0 24 24",width:"16",height:"16"},[g("path",{d:"M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"})],-1)]))])]),g("div",dw,[nr(E.$slots,"default",{},void 0,!0)]),g("div",{class:"resize-handle",onMousedown:Oe(C,["stop"])},null,32)],38)),[[bo,n.visible]])}},El=ut(fw,[["__scopeId","data-v-6d6992c3"]]),pw={class:"title-extra"},hw=["disabled"],mw={class:"ai-chat-content"},gw={class:"input-area"},vw={class:"input-options"},yw=["title"],bw={class:"memory-icon"},ww=["title"],kw={class:"prompt-icon"},xw=["disabled"],Sw={class:"btn-icon"},Cw={class:"input-container"},_w=["disabled","onKeydown"],Mw=["disabled"],Tw={key:0,class:"btn-icon"},Ew={key:1,class:"btn-icon"},Iw={__name:"AIAssistantWindow",props:{visible:{type:Boolean,required:!0},initialPrompt:{type:String,default:""},initialContent:{type:String,default:""},model:{type:String,default:"gpt-3.5-turbo"},selectedText:{type:String,default:""}},emits:["update:visible","close","insert-text"],setup(n,{emit:e}){const t=n,s=e,o=I([]),r=I(""),i=I(null),l=I(null),a=I(""),c=I(!1),u=I(!1),d=I(!0),v=I(!0),p=wn(),b=Sr(),w=me(()=>p.state.config.editor?.aiAssistant||{position:{x:100,y:100},size:{width:400,height:600}}),C=S=>{try{console.log("AIAssistantWindow获取模型配置，模型ID:",S),console.log("可用模型数量:",b.allAvailableModels.length);const x=b.allAvailableModels.find(A=>A.uniqueId===S);return x&&x.config?(console.log("AIAssistantWindow获取到模型配置:",x.config),x.config):(console.log("AIAssistantWindow未找到模型配置，使用默认配置"),console.log("查找的模型ID:",S),b.allAvailableModels.length>0&&console.log("第一个可用模型:",b.allAvailableModels[0]),{temperature:.8,max_tokens:8192,top_p:.8,frequency_penalty:0,presence_penalty:0,stream:!0})}catch(x){return console.error("AIAssistantWindow获取模型配置失败:",x),{temperature:.8,max_tokens:8192,top_p:.8,frequency_penalty:0,presence_penalty:0,stream:!0}}},M=S=>{p.updateConfigItem("editor.aiAssistant.position",S)},X=S=>{p.updateConfigItem("editor.aiAssistant.size",S)},L=()=>{const S=window.receiveChunk,x=window.onMessageComplete,A=window.receiveChatError;return window.receiveChunk=Z=>{try{const G=atob(Z),se=new TextDecoder("utf-8").decode(new Uint8Array([...G].map(Ce=>Ce.charCodeAt(0)))),pe=JSON.parse(se),{chat_id:$e,content:he,reasoning:De}=pe;if($e===a.value){const Ce=o.value[o.value.length-1];if(!Ce||Ce.role!=="assistant"){const Pe={role:"assistant",content:he||"",timestamp:Date.now()};De&&(Pe.reasoning=De,Pe.reasoningCollapsed=!0,Pe.reasoningTime="思考中...",Pe.reasoningStartTime=Date.now()),o.value.push(Pe)}else he&&(Ce.content+=he),De&&(Ce.reasoning||(Ce.reasoning="",Ce.reasoningCollapsed=!0,Ce.reasoningTime="思考中...",Ce.reasoningStartTime=Date.now()),Ce.reasoning+=De);o.value=[...o.value],Le(T)}}catch(G){console.error("处理消息块失败:",G)}},window.onMessageComplete=Z=>{if(Z===a.value){c.value=!1;for(const G of o.value)if(G.role==="assistant"&&G.reasoning&&G.reasoningStartTime){const se=Date.now()-G.reasoningStartTime;G.reasoningTime=`${(se/1e3).toFixed(1)}秒`,delete G.reasoningStartTime}V(),Le(T)}},window.receiveChatError=Z=>{try{const G=atob(Z),se=new TextDecoder("utf-8").decode(new Uint8Array([...G].map(De=>De.charCodeAt(0)))),pe=JSON.parse(se),{chat_id:$e,error_message:he}=pe;console.log("AIAssistantWindow: 收到错误消息:",$e,he),$.error({message:`AI回复失败: ${he}`,duration:5e3}),$e===a.value&&(c.value=!1,o.value.push({role:"assistant",content:he,timestamp:Date.now(),isError:!0}),o.value=[...o.value],V(),Le(T))}catch(G){console.error("处理错误消息失败:",G),c.value=!1}},()=>{window.receiveChunk=S,window.onMessageComplete=x,window.receiveChatError=A}},D=async()=>{if(t.initialPrompt&&!u.value)try{a.value||(a.value=Nn());const S={role:"user",content:t.initialPrompt,timestamp:Date.now()};o.value=[S],await V(),Le(T),c.value=!0;const x=[{role:"user",content:t.initialPrompt}];try{const Z={stream:!0,...C(t.model)},G=await window.pywebview.api.model_controller.chat(a.value,t.model,x,Z),se=typeof G=="string"?JSON.parse(G):G;if(!se.data?.stream&&se.data?.content){const pe={role:"assistant",content:se.data.content,timestamp:Date.now()};o.value.push(pe),await V(),c.value=!1,Le(T)}}catch(A){console.error("发送对话请求失败:",A),$.error(A.message||"发送对话请求失败"),c.value=!1}u.value=!0}catch(S){console.error("初始化对话失败:",S),$.error(S.message||"初始化对话失败"),c.value=!1,u.value=!1}},E=async()=>{if(r.value.trim()){if(c.value){$.warning("请等待当前对话完成");return}try{a.value||(a.value=Nn()),c.value=!0;const S={role:"user",content:r.value,timestamp:Date.now()};o.value.push(S),await V();const x=r.value;r.value="",T();let A=[];d.value?A=o.value.map(pe=>({role:pe.role,content:pe.content})):(t.initialPrompt&&v.value&&A.push({role:"system",content:t.initialPrompt}),A.push({role:"user",content:x}));const G={stream:!0,...C(t.model)},se=await window.pywebview.api.model_controller.chat(a.value,t.model,A,G);if(se&&typeof se=="object"){const pe=typeof se=="string"?JSON.parse(se):se;if(!pe.data?.stream&&pe.data?.content){const $e={role:"assistant",content:pe.data.content,timestamp:Date.now()};o.value.push($e),await V(),c.value=!1}}}catch(S){console.error("发送消息失败:",S),$.error(S.message||"发送消息失败"),c.value=!1}}},B=async()=>{if(c.value){$.warning("请等待当前对话完成");return}o.value=[],u.value=!1,a.value=Nn(),await D()},N=async S=>{if(c.value){$.warning("请等待当前对话完成");return}r.value=S,await E()},V=async()=>{try{if(!a.value)return;const S=o.value.find(se=>se.role==="user"),x=S?.content.substring(0,50)+(S?.content.length>50?"...":"")||"无标题对话",A={id:a.value,model:t.model,messages:o.value,title:x,memory_enabled:d.value,created_at:Date.now(),updated_at:Date.now()},Z=await window.pywebview.api.model_controller.save_chat(a.value,A),G=typeof Z=="string"?JSON.parse(Z):Z;G.status!=="success"&&console.error("保存聊天历史失败:",G.message)}catch(S){console.error("保存聊天历史失败:",S)}},_=async S=>{try{if(console.log("AIAssistantWindow: Copying text:",S?S.substring(0,50)+"...":"undefined or empty"),!S){$.warning("没有内容可复制");return}await window.pywebview.api.copy_to_clipboard(S),$.success("复制成功")}catch(x){console.error("AIAssistantWindow: 复制失败:",x);try{const A=document.createElement("textarea");if(A.value=S,A.style.position="fixed",A.style.left="-999999px",A.style.top="-999999px",document.body.appendChild(A),A.focus(),A.select(),document.execCommand("copy"))$.success("复制成功");else throw new Error("execCommand failed");document.body.removeChild(A)}catch(A){console.error("AIAssistantWindow: 复制失败 (fallback):",A),$.error("复制失败: "+x.message)}}},T=()=>{Le(()=>{i.value&&(i.value.scrollTop=i.value.scrollHeight)})},U=async()=>{if(a.value&&c.value)try{const S=await window.pywebview.api.model_controller.stop_chat(a.value),x=typeof S=="string"?JSON.parse(S):S;if(x.status==="success")c.value=!1;else throw new Error(x.message||"停止失败")}catch(S){console.error("停止对话失败:",S),$.error(S.message||"停止失败")}finally{c.value=!1}},W=S=>{s("insert-text",S),$.success("内容已插入到编辑器")},z=()=>{t.selectedText&&(r.value=t.selectedText,$.success("已使用选中文本"))},re=()=>{r.value="",$({message:"输入框已清空",type:"info",duration:1500})},fe=()=>{d.value=!d.value,ke(d.value)},Te=()=>{v.value=!v.value,Fe(v.value)},ke=S=>{$({message:S?"已切换到记忆模式，AI将记住完整对话历史":"已切换到单次模式，AI将不会记住上下文，有效节省Token",type:S?"info":"success",duration:3e3})},Fe=S=>{$({message:S?"系统提示词已开启，将发送初始提示":"系统提示词已关闭，将不会发送初始提示，进一步节省Token",type:S?"success":"warning",duration:3e3})},be=S=>S.includes("gpt-4")?"model-gpt4":S.includes("gpt-3.5")?"model-gpt35":S.includes("claude")?"model-claude":"model-default",F=S=>S?S.replace("gpt-","").replace("claude-","").toUpperCase():"AI Model";return ct(async()=>{const S=L();if(Le(()=>{l.value?.focus()}),t.initialContent&&t.initialContent.chatId){a.value=t.initialContent.chatId;const x=await window.pywebview.api.model_controller.get_chat(a.value),A=typeof x=="string"?JSON.parse(x):x;A.status==="success"&&A.data?.messages?(o.value=A.data.messages,d.value=A.data.memory_enabled!==!1,u.value=!0,Le(T)):await D()}else await D();lt(()=>{S(),a.value&&c.value&&U().catch(x=>{console.error("停止对话失败:",x)})})}),(S,x)=>{const A=_t;return P(),Ne(El,{title:"AI助手",visible:n.visible,"initial-position":w.value.position,width:w.value.size.width,height:w.value.size.height,pinnable:!0,class:"ai-assistant-window",onClose:x[3]||(x[3]=Z=>S.$emit("close")),onMove:M,onResize:X},{"title-extra":y(()=>[g("div",pw,[g("div",{class:we(["model-tag",be(n.model)])},ee(F(n.model)),3),g("button",{class:"refresh-btn",disabled:c.value,onClick:B,title:"重新发送初始提示"},[f(A,null,{default:y(()=>[f(j(Nf))]),_:1})],8,hw)])]),default:y(()=>[g("div",mw,[g("div",{ref_key:"messagesRef",ref:i,class:"messages-list"},[(P(!0),q(_e,null,Ae(o.value,(Z,G)=>(P(),q("div",{key:`msg-${G}`,class:"message-container"},[f(Rc,{content:Z.content,isUser:Z.role==="user",isError:Z.isError,timestamp:Z.timestamp,selectedModel:n.model,reasoning:Z.reasoning,reasoningTime:Z.reasoningTime,disabled:c.value,hasEditor:!0,onResend:se=>N(Z.content),onInsert:se=>W(Z.content),onCopyFullMessage:x[0]||(x[0]=se=>_(se))},null,8,["content","isUser","isError","timestamp","selectedModel","reasoning","reasoningTime","disabled","onResend","onInsert"])]))),128))],512),g("div",gw,[g("div",vw,[g("div",{class:we(["memory-toggle",d.value?"memory-enabled":"memory-disabled"]),onClick:fe,title:d.value?"点击切换到单次模式":"点击切换到记忆模式"},[g("div",bw,[d.value?(P(),Ne(A,{key:0},{default:y(()=>[f(j(If))]),_:1})):(P(),Ne(A,{key:1},{default:y(()=>[f(j(Cc))]),_:1}))]),g("span",null,ee(d.value?"记忆模式":"单次模式"),1)],10,yw),d.value?ge("",!0):(P(),q("div",{key:0,class:we(["system-prompt-toggle",v.value?"enabled":"disabled"]),onClick:Te,title:v.value?"点击关闭系统提示词":"点击开启系统提示词"},[g("div",kw,[f(A,null,{default:y(()=>[f(j(Sc))]),_:1})]),x[4]||(x[4]=g("span",null,"系统提示词",-1))],10,ww)),t.selectedText?(P(),q("button",{key:1,class:"selected-text-btn",onClick:z,disabled:c.value,title:"使用编辑器中选中的文本"},[g("div",Sw,[f(A,null,{default:y(()=>[f(j(zt))]),_:1})]),x[5]||(x[5]=g("span",null,"使用选中文本",-1))],8,xw)):ge("",!0)]),g("div",Cw,[Fn(g("textarea",{ref_key:"inputRef",ref:l,"onUpdate:modelValue":x[1]||(x[1]=Z=>r.value=Z),class:"message-input",placeholder:"输入消息...",disabled:c.value,onKeydown:[St(Oe(E,["ctrl","prevent"]),["enter"]),St(Oe(E,["meta","prevent"]),["enter"])]},null,40,_w),[[Ec,r.value]]),r.value?(P(),q("button",{key:0,class:"clear-btn",onClick:re,title:"清空输入"},[f(A,null,{default:y(()=>[f(j(Vs))]),_:1})])):ge("",!0)]),g("button",{class:we(["send-btn",{"is-stopping":c.value}]),onClick:x[2]||(x[2]=Z=>c.value?U():E()),disabled:!r.value.trim()&&!c.value},[c.value?(P(),q("div",Tw,[f(A,null,{default:y(()=>[f(j(Gi))]),_:1})])):(P(),q("div",Ew,[f(A,null,{default:y(()=>[f(j(Of))]),_:1})])),g("span",null,ee(c.value?"停止生成":"发送"),1)],10,Mw)])])]),_:1},8,["visible","initial-position","width","height"])}}},Ut={ENTITY:"entity",ENTITY_ID:"entity_id",SELECTED_TEXT:"selected_text",CONTEXT:"context"},tr=[{key:Ut.ENTITY_ID,description:"插入指定实体的JSON数据，会弹出选择对话框",previewValue:'{ "name": "具体实体", "description": "实体描述" }'},{key:Ut.SELECTED_TEXT,description:"插入选中的文本内容",previewValue:"这是一段被选中的示例文本，用于预览效果。"},{key:Ut.CONTEXT,description:"插入当前章节的完整内容作为上下文",previewValue:"这是一段章节上下文的示例内容，包含了当前章节的相关信息..."}],Ow={class:"menu-settings-header"},Nw={class:"header-title"},Dw={class:"header-actions"},Aw={class:"menu-settings-content"},$w={class:"menu-list"},Pw={key:1,class:"text-placeholder"},Rw={class:"prompt-preview"},Vw={class:"table-actions"},Lw={class:"menu-settings-footer"},Bw={class:"footer-info"},zw={class:"footer-actions"},Fw={class:"dialog-header"},Ww={class:"dialog-title"},Hw={class:"dialog-actions"},qw={key:0,class:"dialog-body"},Jw={class:"left-panel"},Uw={class:"panel-content"},jw={class:"info-card"},Kw={class:"card-header"},Xw={class:"card-body"},Yw={class:"icon-option"},Gw={class:"info-card"},Qw={class:"card-header"},Zw={class:"card-body"},ek={class:"entity-option-content"},tk={class:"entity-template-tag"},nk={class:"selected-entities-info"},sk={class:"entity-name"},ok={class:"entity-variable"},rk={class:"right-panel"},ik={class:"panel-content"},lk={class:"info-card"},ak={class:"card-header"},ck={class:"card-body"},uk={class:"prompt-editor-container"},dk={class:"prompt-editor"},fk={class:"editor-header"},pk={class:"editor-tabs"},hk={class:"editor-actions"},mk={class:"editor-content"},gk={key:0,class:"edit-view"},vk={class:"prompt-toolbar"},yk={class:"params-list"},bk=["onClick"],wk={class:"entity-name"},kk=["onClick"],xk={class:"tooltip-wrapper"},Sk={key:1,class:"preview-view"},Ck={class:"preview-content"},_k={class:"preview-header"},Mk={class:"preview-text"},Tk={class:"dialog-footer"},Ek={class:"footer-left"},Ik={class:"footer-right"},Ok={class:"params-tooltip"},Nk={class:"dialog-footer"},Dk={__name:"ContextMenuSettings",props:{visible:{type:Boolean,required:!0},bookId:{type:String,required:!0},editor:{type:Object,required:!0},selectedText:{type:String,required:!0}},emits:["update:visible","close"],setup(n,{emit:e}){const t=n,s=wn(),o=Sr(),r=I([]),i=I(null),l=I(!1),a=I([]),c=I([]),u=I([]),d=I([]),v=I(!1),p=I(!1),b=I(!1),w=tr,C=me(()=>o.allAvailableModels),M=me(()=>tr.filter(S=>S.key===Ut.SELECTED_TEXT||S.key===Ut.CONTEXT)),X={id:"",name:"",icon:"",shortcut:"",aiPrompt:"",model:"",disabled:!1,order:0,includeContext:!1,entityInfo:null},L=[{name:"magic-wand",label:"魔法棒"},{name:"chat",label:"对话"},{name:"edit",label:"编辑"}],D=async S=>{try{const x=await window.pywebview.api.book_controller.get_templates(S),A=typeof x=="string"?JSON.parse(x):x;A.status==="success"?a.value=A.data||[]:$.error(A.message||"加载模板失败")}catch(x){$.error("加载模板失败："+x.message)}},E=S=>{try{if(console.log("开始复制提示语:",S),S.aiPrompt){let x=S.aiPrompt;const A=t.editor?.state.doc.textBetween(t.editor.state.selection.from,t.editor.state.selection.to),Z={[Ut.ENTITY]:S.entityInfoList?JSON.stringify(S.entityInfoList,null,2):"",[Ut.SELECTED_TEXT]:A||"",[Ut.CONTEXT]:S.includeContext?t.editor?.state.doc.textBetween(0,t.editor.state.doc.content.size):""};if(S.entityInfoList&&S.entityInfoList.length>0&&S.entityInfoList.forEach(G=>{G&&G.id&&(Z[`entity_${G.id}`]=JSON.stringify(G,null,2))}),tr.forEach(G=>{if(G&&G.key){const se=new RegExp(`\\$\\{${G.key}\\}`,"g");x=x.replace(se,Z[G.key]||"")}}),S.entityInfoList&&S.entityInfoList.length>0){const G=[];S.entityInfoList.forEach(se=>{if(se&&se.id){const pe=se.id,$e=`\\$\\{entity_${pe}\\}`,he=new RegExp($e,"g");let De;for(;(De=he.exec(x))!==null;)G.push({entityId:pe,index:De.index,length:De[0].length,replacement:Z[`entity_${pe}`]||""})}}),G.sort((se,pe)=>pe.index-se.index),G.forEach(se=>{const pe=x.substring(0,se.index),$e=x.substring(se.index+se.length);x=pe+se.replacement+$e})}console.log("处理后的提示语:",x),window.pywebview.api.copy_to_clipboard(x).then(()=>{console.log("复制成功！"),$.success("提示语已复制到剪贴板")}).catch(G=>{$.error("复制失败，请手动复制")})}}catch(x){console.error("处理复制提示词时出错:",x),$.error("处理复制时发生错误")}},B=async S=>{try{const x=await window.pywebview.api.book_controller.get_entities(S),A=typeof x=="string"?JSON.parse(x):x;A.status==="success"?c.value=A.data||[]:$.error(A.message||"加载实体失败")}catch(x){$.error("加载实体失败："+x.message)}};Ie(u,S=>{S.length>0&&i.value&&(d.value=[],i.value.entitySelection={...i.value.entitySelection,selectedTemplateIds:S,selectedEntityIds:[]},i.value.entityInfoList=[])}),Ie(d,async S=>{if(S.length>0&&i.value){const x=c.value.filter(A=>S.includes(A.id));if(x.length>0){const A=x.map(Z=>({id:Z.id,name:Z.name,description:Z.description,dimensions:Z.dimensions}));i.value.entityInfoList=A,i.value.entitySelection={enabled:!0,selectedTemplateIds:u.value,selectedEntityIds:S}}else $.warning("未找到选中的实体信息")}else S.length===0&&i.value&&(i.value.entityInfoList=[],i.value.entitySelection={enabled:!1,selectedTemplateIds:[],selectedEntityIds:[]})});const N=async()=>{try{o.initialized||(console.log("ContextMenuSettings: AI提供商配置未初始化，先加载提供商配置"),await o.loadProviders()),console.log("ContextMenuSettings: 可用模型数量:",C.value.length),C.value.length===0&&console.warn("ContextMenuSettings: 没有可用的模型，请检查AI提供商配置")}catch(S){console.error("ContextMenuSettings: 确保模型列表加载失败:",S)}},V=()=>{if(r.value=[...s.editor.contextMenus||[]],r.value=r.value.map(S=>(S.entitySelection||(S.entitySelection={enabled:!1,selectedTemplateIds:[],selectedEntityIds:[]}),S)),i.value){const S=r.value.find(x=>x.id===i.value.id);S?.entitySelection&&(v.value=S.entitySelection.enabled,u.value=S.entitySelection.selectedTemplateIds,d.value=S.entitySelection.selectedEntityIds)}},_=async()=>{await N(),i.value={...X,id:`menu-${Date.now()}`,order:r.value.length,model:s.selectedModel||C.value[0]?.id||"gpt-3.5-turbo"},l.value=!0},T=async S=>{await N(),i.value={...S},S.entitySelection?.enabled?(t.bookId&&(!a.value.length||!c.value.length)&&await Promise.all([D(t.bookId),B(t.bookId)]),v.value=S.entitySelection.enabled,S.entitySelection.selectedTemplateIds?u.value=S.entitySelection.selectedTemplateIds:S.entitySelection.selectedTemplateId?u.value=[S.entitySelection.selectedTemplateId]:u.value=[],d.value=S.entitySelection.selectedEntityIds||[],S.entityInfoList||(i.value.entityInfoList=[])):(v.value=!1,u.value=[],d.value=[],i.value.entitySelection={enabled:!1,selectedTemplateIds:[],selectedEntityIds:[]}),l.value=!0},U=async S=>{try{await qt.confirm(`确定要删除菜单项 "${S.name}" 吗？`,"确认删除",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning",customClass:"native-message-box"});const x=r.value.filter(A=>A.id!==S.id);await s.updateConfigItem("editor.contextMenus",x),V(),$.success("删除成功")}catch{}},W=async()=>{try{await s.updateConfigItem("editor.contextMenus",r.value),$.success("配置保存成功")}catch(S){console.error("保存配置失败:",S),$.error("保存配置失败")}},z=S=>{const x=C.value.find(A=>A.id===S);return x?x.name||S:S||"未知模型"},re=()=>{if(!i.value?.aiPrompt)return"请先输入AI提示语...";let S=i.value.aiPrompt;const x=/\$\{selected_text\}/g,A=t.selectedText||"这是选中的文本内容示例";S=S.replace(x,A);const Z=/\$\{context\}/g;let G="这是当前章节的上下文内容示例...";if(i.value.includeContext&&t.editor)try{const he=t.editor.state.doc.textBetween(0,t.editor.state.doc.content.size);he&&he.length>0&&(G=he.length>200?he.substring(0,200)+"...":he)}catch(he){console.warn("获取章节上下文失败:",he)}S=S.replace(Z,G);const se=/\$\{entity\}/g;let pe="这是实体信息的JSON数据示例";if(v.value&&d.value.length>0){const he=d.value.map(De=>{const Ce=c.value.find(Pe=>Pe.id===De);return Ce?{id:Ce.id,name:Ce.name,description:Ce.description||"",dimensions:Ce.dimensions||{}}:{id:De,name:"未知实体"}});pe=JSON.stringify(he,null,2)}S=S.replace(se,pe),v.value&&d.value.length>0&&d.value.forEach(he=>{const De=new RegExp(`\\$\\{entity_${he}\\}`,"g"),Ce=c.value.find(wt=>wt.id===he);let Pe=`{ "id": "${he}", "name": "未知实体" }`;if(Ce){const wt={id:Ce.id,name:Ce.name,description:Ce.description||"",template_id:Ce.template_id||""};Ce.dimensions&&Object.keys(Ce.dimensions).length>0&&(wt.dimensions=Ce.dimensions);const Ft=JSON.stringify(wt,null,2);Pe=Ft.length>300?Ft.substring(0,300)+`
  ...
}`:Ft}S=S.replace(De,Pe)});const $e=/\$\{([^}]+)\}/g;return S=S.replace($e,(he,De)=>["selected_text","context","entity"].includes(De)||De.startsWith("entity_")?he:`[参数: ${De}]`),S},fe=async()=>{if(!i.value.name||!i.value.aiPrompt){$.warning("请填写必要的信息");return}const S=[];v.value&&d.value.length>0&&d.value.forEach(Z=>{const G=c.value.find(se=>se.id===Z);if(G){const{id:se,name:pe,description:$e,dimensions:he,template_id:De}=G;S.push({id:se,name:pe,description:$e,dimensions:he,template_id:De})}}),i.value.entitySelection={enabled:v.value,selectedTemplateIds:u.value,selectedEntityIds:d.value},i.value.entityInfoList=S,console.log("保存的实体信息列表:",i.value.entityInfoList);const x=[...r.value],A=x.findIndex(Z=>Z.id===i.value.id);A===-1?x.push(i.value):x[A]=i.value,x.sort((Z,G)=>Z.order-G.order),await s.updateConfigItem("editor.contextMenus",x),V(),l.value=!1,$.success("保存成功")},Te=S=>{if(!i.value)return;const x=document.querySelector(".prompt-editor textarea");if(!x)return;const A=x.selectionStart,Z=i.value.aiPrompt.substring(0,A),G=i.value.aiPrompt.substring(x.selectionEnd);let se=`\${${S}}`;if(S==="entity_id"&&d.value.length>0){qt.confirm(h("div",{class:"entity-insert-dialog"},[h("p","请选择要插入的实体:"),h("div",{class:"entity-list"},d.value.map(pe=>{const $e=c.value.find(he=>he.id===pe);return h("div",{class:"entity-option",onClick:()=>{const he=`\${entity_${pe}}`;i.value.aiPrompt=`${Z}${he}${G}`,qt.close(),Le(()=>{x.focus();const De=A+he.length;x.setSelectionRange(De,De)})}},[h("span",$e?.name||`实体 ${pe}`)])}))]),"选择实体",{confirmButtonText:"取消",cancelButtonText:"插入全部实体",type:"info",showCancelButton:!0,distinguishCancelAndClose:!0,callback:pe=>{pe==="cancel"&&(i.value.aiPrompt=`${Z}${se}${G}`,Le(()=>{x.focus();const $e=A+se.length;x.setSelectionRange($e,$e)}))}}).catch(()=>{});return}i.value.aiPrompt=`${Z}${se}${G}`,Le(()=>{x.focus();const pe=A+se.length;x.setSelectionRange(pe,pe)})},ke=S=>`\${${S}}`,Fe=S=>{try{if(!S)return"未知实体";const x=c.value?.find(A=>A&&A.id===S);return x?x.name:`实体 ${S.substring(0,8)}...`}catch(x){return console.error("获取实体名称失败:",x),"未知实体"}},be=me(()=>!u.value.length||!c.value?.length?[]:u.value.map(S=>{const x=a.value.find(Z=>Z.id===S),A=c.value.filter(Z=>Z.template_id===S);return{id:S,name:x?.name||"未命名模板",entities:A}}).filter(S=>S.entities.length>0));Ie(u,S=>{if(S.length>0&&i.value){const x=c.value.filter(A=>S.includes(A.template_id)).map(A=>A.id);d.value=d.value.filter(A=>x.includes(A)),i.value.entitySelection={...i.value.entitySelection,selectedTemplateIds:S}}}),me(()=>!u.value.length||!c.value?.length?[]:c.value.filter(S=>u.value.includes(S.template_id)));const F=S=>{if(!i.value)return;const x=document.querySelector(".prompt-editor textarea");if(!x)return;const A=x.selectionStart,Z=i.value.aiPrompt.substring(0,A),G=i.value.aiPrompt.substring(x.selectionEnd),se=`\${entity_${S}}`;i.value.aiPrompt=`${Z}${se}${G}`,Le(()=>{x.focus();const pe=A+se.length;x.setSelectionRange(pe,pe)})};return ct(async()=>{s.isModelsLoaded||await s.loadModels(),V(),t.bookId&&await Promise.all([D(t.bookId),B(t.bookId)])}),Ie(()=>t.bookId,async S=>{S&&await Promise.all([D(S),B(S)])}),(S,x)=>{const A=_t,Z=kn,G=Df,se=br,pe=$f,$e=bn,he=ko,De=kr,Ce=wr,Pe=Vf,wt=Rf,Ft=Lf,jn=tl,cn=wo,Wt=Oc,bs=Pf,Us=vs,Kn=zf,st=xr;return P(),q("div",{class:"menu-settings",onContextmenu:x[24]||(x[24]=Oe(()=>{},["prevent"])),onSelectstart:x[25]||(x[25]=Oe(()=>{},["prevent"])),onDragstart:x[26]||(x[26]=Oe(()=>{},["prevent"]))},[g("div",Ow,[g("div",Nw,[f(A,null,{default:y(()=>[f(j(yr))]),_:1}),x[27]||(x[27]=g("span",null,"右键菜单配置",-1))]),g("div",Dw,[f(Z,{type:"primary",size:"small",onClick:_},{default:y(()=>[f(A,null,{default:y(()=>[f(j(on))]),_:1}),x[28]||(x[28]=ne(" 添加菜单项 "))]),_:1})])]),g("div",Aw,[g("div",$w,[f(pe,{data:r.value,style:{width:"100%"},height:"100%"},{default:y(()=>[f(G,{prop:"name",label:"名称",width:"140","show-overflow-tooltip":""}),f(G,{prop:"icon",label:"图标",width:"80",align:"center"},{default:y(({row:te})=>[f(se,{size:"small",type:"info"},{default:y(()=>[ne(ee(te.icon),1)]),_:2},1024)]),_:1}),f(G,{prop:"model",label:"AI模型",width:"120","show-overflow-tooltip":""},{default:y(({row:te})=>[te.model?(P(),Ne(se,{key:0,size:"small",type:"success"},{default:y(()=>[ne(ee(z(te.model)),1)]),_:2},1024)):(P(),q("span",Pw,"未设置"))]),_:1}),f(G,{prop:"aiPrompt",label:"提示语","min-width":"200","show-overflow-tooltip":""},{default:y(({row:te})=>[g("div",Rw,ee(te.aiPrompt?te.aiPrompt.substring(0,50)+(te.aiPrompt.length>50?"...":""):"未设置"),1)]),_:1}),f(G,{label:"操作",width:"200",fixed:"right"},{default:y(({row:te})=>[g("div",Vw,[f(Z,{size:"small",type:"primary",link:"",onClick:ft=>T(te)},{default:y(()=>[f(A,null,{default:y(()=>[f(j(zt))]),_:1}),x[29]||(x[29]=ne(" 编辑 "))]),_:2},1032,["onClick"]),f(Z,{size:"small",type:"info",link:"",onClick:ft=>E(te)},{default:y(()=>[f(A,null,{default:y(()=>[f(j(Af))]),_:1}),x[30]||(x[30]=ne(" 复制 "))]),_:2},1032,["onClick"]),f(Z,{size:"small",type:"danger",link:"",onClick:ft=>U(te)},{default:y(()=>[f(A,null,{default:y(()=>[f(j(Vs))]),_:1}),x[31]||(x[31]=ne(" 删除 "))]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"])])]),g("div",Lw,[g("div",Bw,[f(A,null,{default:y(()=>[f(j(sr))]),_:1}),g("span",null,"共 "+ee(r.value.length)+" 个菜单项",1)]),g("div",zw,[f(Z,{onClick:x[0]||(x[0]=te=>S.$emit("close"))},{default:y(()=>x[32]||(x[32]=[ne("关闭")])),_:1}),f(Z,{type:"primary",onClick:W},{default:y(()=>x[33]||(x[33]=[ne("保存配置")])),_:1})])]),f(st,{modelValue:l.value,"onUpdate:modelValue":x[18]||(x[18]=te=>l.value=te),width:"90%",top:"5vh","lock-scroll":!0,"modal-append-to-body":!1,"destroy-on-close":"","show-close":!1,class:"menu-item-dialog native-dialog fullscreen-dialog",onContextmenu:x[19]||(x[19]=Oe(()=>{},["prevent"])),onSelectstart:x[20]||(x[20]=Oe(()=>{},["prevent"])),onDragstart:x[21]||(x[21]=Oe(()=>{},["prevent"]))},{header:y(()=>[g("div",Fw,[g("div",Ww,[f(A,null,{default:y(()=>[f(j(zt))]),_:1}),g("span",null,ee(i.value?.id?"编辑菜单项":"新增菜单项"),1)]),g("div",Hw,[f(Z,{size:"small",onClick:x[1]||(x[1]=te=>l.value=!1)},{default:y(()=>[f(A,null,{default:y(()=>[f(j(ps))]),_:1}),x[34]||(x[34]=ne(" 关闭 "))]),_:1})])])]),footer:y(()=>[g("div",Tk,[g("div",Ek,[f(Z,{size:"small",onClick:x[16]||(x[16]=te=>b.value=!0)},{default:y(()=>[f(A,null,{default:y(()=>[f(j(Xl))]),_:1}),x[41]||(x[41]=ne(" 参数说明 "))]),_:1})]),g("div",Ik,[f(Z,{onClick:x[17]||(x[17]=te=>l.value=!1)},{default:y(()=>x[42]||(x[42]=[ne("取消")])),_:1}),f(Z,{type:"primary",onClick:fe},{default:y(()=>[f(A,null,{default:y(()=>[f(j(Rs))]),_:1}),x[43]||(x[43]=ne(" 保存 "))]),_:1})])])]),default:y(()=>[i.value?(P(),q("div",qw,[g("div",Jw,[f(bs,{height:"calc(80vh - 120px)"},{default:y(()=>[g("div",Uw,[g("div",jw,[g("div",Kw,[f(A,null,{default:y(()=>[f(j(Rs))]),_:1}),x[35]||(x[35]=g("span",null,"基本信息",-1))]),g("div",Xw,[f(cn,{"label-width":"80px"},{default:y(()=>[f(he,{label:"名称",required:""},{default:y(()=>[f($e,{modelValue:i.value.name,"onUpdate:modelValue":x[2]||(x[2]=te=>i.value.name=te),placeholder:"请输入菜单名称"},null,8,["modelValue"])]),_:1}),f(wt,{gutter:16},{default:y(()=>[f(Pe,{span:12},{default:y(()=>[f(he,{label:"图标"},{default:y(()=>[f(Ce,{modelValue:i.value.icon,"onUpdate:modelValue":x[3]||(x[3]=te=>i.value.icon=te),placeholder:"请选择图标",class:"w-full"},{default:y(()=>[(P(),q(_e,null,Ae(L,te=>f(De,{key:te.name,label:te.label,value:te.name},{default:y(()=>[g("span",Yw,[f(A,null,{default:y(()=>[(P(),Ne(Tc(te.name)))]),_:2},1024),g("span",null,ee(te.label),1)])]),_:2},1032,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1}),f(Pe,{span:12},{default:y(()=>[f(he,{label:"快捷键"},{default:y(()=>[f($e,{modelValue:i.value.shortcut,"onUpdate:modelValue":x[4]||(x[4]=te=>i.value.shortcut=te),placeholder:"例如：Ctrl+O"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),f(wt,{gutter:16},{default:y(()=>[f(Pe,{span:16},{default:y(()=>[f(he,{label:"AI模型",required:""},{default:y(()=>[f(Ce,{modelValue:i.value.model,"onUpdate:modelValue":x[5]||(x[5]=te=>i.value.model=te),placeholder:"请选择模型",class:"w-full"},{default:y(()=>[(P(!0),q(_e,null,Ae(C.value,te=>(P(),Ne(De,{key:te.id,label:`${te.name} (${te.providerName})`,value:te.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),f(Pe,{span:8},{default:y(()=>[f(he,{label:"排序"},{default:y(()=>[f(Ft,{modelValue:i.value.order,"onUpdate:modelValue":x[6]||(x[6]=te=>i.value.order=te),min:0,class:"w-full"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),f(he,{label:"状态"},{default:y(()=>[f(jn,{modelValue:i.value.disabled,"onUpdate:modelValue":x[7]||(x[7]=te=>i.value.disabled=te),"active-text":"禁用","inactive-text":"启用","active-value":!0,"inactive-value":!1},null,8,["modelValue"])]),_:1})]),_:1})])]),g("div",Gw,[g("div",Qw,[f(A,null,{default:y(()=>[f(j(Ic))]),_:1}),x[36]||(x[36]=g("span",null,"实体选择配置",-1))]),g("div",Zw,[f(cn,{"label-width":"80px"},{default:y(()=>[f(he,{label:"启用实体"},{default:y(()=>[f(jn,{modelValue:v.value,"onUpdate:modelValue":x[8]||(x[8]=te=>v.value=te)},null,8,["modelValue"])]),_:1}),v.value?(P(),q(_e,{key:0},[f(he,{label:"选择模板"},{default:y(()=>[f(Ce,{modelValue:u.value,"onUpdate:modelValue":x[9]||(x[9]=te=>u.value=te),placeholder:"请选择模板(可多选)",filterable:"",multiple:"","collapse-tags":"","collapse-tags-tooltip":"",class:"w-full"},{default:y(()=>[(P(!0),q(_e,null,Ae(a.value,te=>(P(),Ne(De,{key:te.id,label:te.name,value:te.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u.value.length>0?(P(),Ne(he,{key:0,label:"选择实体"},{default:y(()=>[f(Ce,{modelValue:d.value,"onUpdate:modelValue":x[10]||(x[10]=te=>d.value=te),placeholder:"请选择实体(可多选)",filterable:"",multiple:"","collapse-tags":"","collapse-tags-tooltip":"",class:"w-full"},{default:y(()=>[(P(!0),q(_e,null,Ae(be.value,te=>(P(),Ne(Wt,{key:te.id,label:te.name},{default:y(()=>[(P(!0),q(_e,null,Ae(te.entities,ft=>(P(),Ne(De,{key:ft.id,label:ft.name,value:ft.id},{default:y(()=>[g("div",ek,[g("span",null,ee(ft.name),1),g("span",tk,ee(te.name),1)])]),_:2},1032,["label","value"]))),128))]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})):ge("",!0),d.value.length>0?(P(),Ne(he,{key:1,label:"已选实体"},{default:y(()=>[g("div",nk,[(P(!0),q(_e,null,Ae(d.value,te=>(P(),q("div",{key:te,class:"entity-item"},[g("div",sk,ee(Fe(te)),1),g("div",ok,[x[37]||(x[37]=ne(" 可用变量: ")),g("code",null,"$"+ee(`entity_${te}`),1)])]))),128))])]),_:1})):ge("",!0)],64)):ge("",!0)]),_:1})])])])]),_:1})]),g("div",rk,[f(bs,{height:"calc(80vh - 120px)"},{default:y(()=>[g("div",ik,[g("div",lk,[g("div",ak,[f(A,null,{default:y(()=>[f(j(Bf))]),_:1}),x[38]||(x[38]=g("span",null,"AI 提示配置",-1))]),g("div",ck,[g("div",uk,[g("div",dk,[g("div",fk,[g("div",pk,[g("span",{class:we(["tab-item",{active:!p.value}]),onClick:x[11]||(x[11]=te=>p.value=!1)},"编辑",2),g("span",{class:we(["tab-item",{active:p.value}]),onClick:x[12]||(x[12]=te=>p.value=!0)},"预览",2)]),g("div",hk,[f(Kn,{modelValue:i.value.includeContext,"onUpdate:modelValue":x[13]||(x[13]=te=>i.value.includeContext=te),class:"context-checkbox"},{default:y(()=>[x[39]||(x[39]=ne(" 包含章节上下文 ")),f(Us,{content:"启用后，会将当前章节的完整内容作为上下文添加到提示语中",placement:"top"},{default:y(()=>[f(A,null,{default:y(()=>[f(j(Xl))]),_:1})]),_:1})]),_:1},8,["modelValue"])])]),g("div",mk,[p.value?(P(),q("div",Sk,[g("div",Ck,[g("div",_k,[f(A,null,{default:y(()=>[f(j(Ff))]),_:1}),x[40]||(x[40]=g("span",null,"预览效果",-1))]),g("div",Mk,ee(re()),1)])])):(P(),q("div",gk,[f($e,{modelValue:i.value.aiPrompt,"onUpdate:modelValue":x[14]||(x[14]=te=>i.value.aiPrompt=te),type:"textarea",rows:12,placeholder:"请输入AI提示语...",class:"prompt-textarea"},null,8,["modelValue"]),g("div",vk,[g("div",yk,[v.value&&d.value.length>0?(P(!0),q(_e,{key:0},Ae(d.value,te=>(P(),q("span",{key:`entity_${te}`,class:"param-tag entity-param-tag",onClick:ft=>F(te)},[g("code",null,"$"+ee(`entity_${te}`),1),g("span",wk,ee(Fe(te)),1)],8,bk))),128)):ge("",!0),(P(!0),q(_e,null,Ae(M.value,te=>(P(),q("span",{key:te.key,class:"param-tag",onClick:ft=>Te(te.key)},[g("code",null,ee(ke(te.key)),1)],8,kk))),128))]),g("div",xk,[f(Z,{link:"",size:"small",onClick:x[15]||(x[15]=te=>b.value=!0),class:"help-button"},{default:y(()=>[f(A,{class:"help-icon"},{default:y(()=>[f(j(sr))]),_:1})]),_:1})])])]))])])])])])])]),_:1})])])):ge("",!0)]),_:1},8,["modelValue"]),f(st,{modelValue:b.value,"onUpdate:modelValue":x[23]||(x[23]=te=>b.value=te),title:"参数说明",width:"400px","destroy-on-close":"","append-to-body":""},{footer:y(()=>[g("span",Nk,[f(Z,{onClick:x[22]||(x[22]=te=>b.value=!1)},{default:y(()=>x[44]||(x[44]=[ne("关闭")])),_:1})])]),default:y(()=>[g("div",Ok,[(P(!0),q(_e,null,Ae(j(w),te=>(P(),q("div",{key:te.key,class:"param-item"},[g("code",null,ee(ke(te.key)),1),g("span",null,ee(te.description),1)]))),128))])]),_:1},8,["modelValue"])],32)}}},Ak=ut(Dk,[["__scopeId","data-v-d38f6465"]]),$k={class:"title-extra"},Pk={class:"window-content"},Rk={class:"template-option"},Vk={class:"template-name"},Lk={class:"template-info"},Bk={class:"divider-content"},zk={class:"import-content"},Fk={class:"format-hint-title"},Wk={class:"dialog-footer"},Hk={__name:"EntityCreateWindow",props:{visible:Boolean,bookId:{type:[String,Number],required:!0}},emits:["close","created","update:visible"],setup(n,{emit:e}){const t=n,s=e,o=I(null),r=wn(),i=I([]),l=I({template_id:"",name:"",description:"",dimensions:{}}),a=I(null),c=I(!1),u=I(""),d=me(()=>r.state.config.editor?.entityWindow||{position:{x:120,y:120},size:{width:600,height:500}}),v=D=>{r.updateConfigItem("editor.entityWindow.position",D)},p=D=>{r.updateConfigItem("editor.entityWindow.size",D)},b=async()=>{try{const D=await window.pywebview.api.book_controller.get_templates(t.bookId),E=typeof D=="string"?JSON.parse(D):D;E.status==="success"?i.value=E.data||[]:$.error(E.message||"加载模板失败")}catch(D){console.error("加载模板失败:",D),$.error("加载模板失败："+D.message)}},w=D=>{a.value=i.value.find(E=>E.id===D),a.value&&(l.value.dimensions=a.value.dimensions.reduce((E,B)=>(E[B.name]="",E),{}))},C=async()=>{try{if(!l.value.template_id){$.error("请选择模板");return}if(!l.value.name){$.error("请输入实体名称");return}a.value&&a.value.dimensions.forEach(B=>{(!l.value.dimensions[B.name]||l.value.dimensions[B.name].trim()==="")&&(l.value.dimensions[B.name]="未设定")});const D=await window.pywebview.api.book_controller.save_entity({...l.value,book_id:t.bookId}),E=typeof D=="string"?JSON.parse(D):D;if(E.status==="success"){s("created"),$.success("实体创建成功");const B=l.value.template_id;l.value={template_id:B,name:"",description:"",dimensions:{}},a.value&&(l.value.dimensions=a.value.dimensions.reduce((N,V)=>(N[V.name]="",N),{}))}else throw new Error(E.message||"保存失败")}catch(D){console.error("保存实体失败:",D),$.error("保存失败："+D.message)}},M=()=>{if(!l.value.template_id){$.error("请先选择模板");return}c.value=!0,u.value=""},X=async()=>{try{if(!u.value.trim()){$.error("请输入JSON字符串");return}const D=JSON.parse(u.value);if(!D.name){$.error("导入失败：缺少实体名称");return}const E=l.value.template_id;if(!E){$.error("请先选择一个模板");return}const B=i.value.find(z=>z.id===E);if(!B){$.error("模板不存在");return}const N=Object.keys(D.dimensions).filter(z=>!B.dimensions.some(re=>re.name===z));N.length>0&&($.warning(`以下维度在模板中未定义，将被忽略: [${N.join(", ")}]`),N.forEach(z=>{delete D.dimensions[z]}));const V=await window.pywebview.api.book_controller.get_entities(t.bookId),_=typeof V=="string"?JSON.parse(V):V;if(_.status==="success"&&(_.data||[]).some(fe=>fe.name===D.name&&fe.template_id===E)){$.error("导入失败：该模板下已存在同名实体");return}const T={name:D.name,description:D.description||"",dimensions:{},template_id:E,book_id:t.bookId};B.dimensions.forEach(z=>{const re=D.dimensions[z.name];T.dimensions[z.name]=re!=null?String(re):"未设定"});const U=await window.pywebview.api.book_controller.save_entity(T),W=typeof U=="string"?JSON.parse(U):U;if(W.status==="success")$.success("导入成功"),c.value=!1,s("created"),l.value={template_id:E,name:"",description:"",dimensions:{}},a.value&&(l.value.dimensions=a.value.dimensions.reduce((z,re)=>(z[re.name]="",z),{}));else throw new Error(W.message||"导入失败")}catch(D){D instanceof SyntaxError?$.error("导入失败：JSON格式不正确"):(console.error("导入失败:",D),$.error("导入失败："+D.message))}};Ie(()=>t.visible,async D=>{D&&i.value.length===0&&await b()});const L=D=>{if(!t.visible)return;const E=document.activeElement,B=o.value?.$el;!B||!B.contains(E)||D.key==="Enter"&&(D.ctrlKey||D.metaKey)&&(D.preventDefault(),D.stopPropagation(),C())};return Ie(()=>t.visible,D=>{D?window.addEventListener("keydown",L):window.removeEventListener("keydown",L)}),ct(()=>{r.state.config.editor.entityWindow||r.updateConfigItem("editor.entityWindow",{position:{x:120,y:120},size:{width:600,height:500}}),t.visible&&window.addEventListener("keydown",L)}),lt(()=>{window.removeEventListener("keydown",L)}),(D,E)=>{const B=kn,N=vs,V=kr,_=wr,T=ko,U=bn,W=br,z=Nc,re=wo,fe=Es("InfoFilled"),Te=_t,ke=Ac,Fe=Dc,be=xr;return P(),q("div",null,[f(El,{ref_key:"windowRef",ref:o,title:"新建实体",visible:n.visible,"initial-position":d.value.position,width:d.value.size.width,height:d.value.size.height,pinnable:!0,class:"entity-create-window",onClose:E[3]||(E[3]=F=>s("update:visible",!1)),onMove:v,onResize:p},{"title-extra":y(()=>[g("div",$k,[f(N,{content:"从JSON导入实体",placement:"bottom",effect:"light"},{default:y(()=>[f(B,{type:"success",size:"small",disabled:!l.value.template_id,onClick:M,class:"import-btn"},{default:y(()=>E[7]||(E[7]=[ne(" 导入 ")])),_:1},8,["disabled"])]),_:1}),f(N,{content:"Ctrl + Enter 快速创建",placement:"bottom",effect:"light"},{default:y(()=>[f(B,{type:"primary",size:"small",disabled:!l.value.template_id,onClick:C,class:"create-btn"},{default:y(()=>E[8]||(E[8]=[ne(" 创建 ")])),_:1},8,["disabled"])]),_:1})])]),footer:y(()=>E[10]||(E[10]=[])),default:y(()=>[g("div",Pk,[f(re,{model:l.value,"label-width":"100px"},{default:y(()=>[f(T,{label:"选择模板",required:""},{default:y(()=>[f(_,{modelValue:l.value.template_id,"onUpdate:modelValue":E[0]||(E[0]=F=>l.value.template_id=F),placeholder:"请选择模板",class:"template-select",onChange:w},{default:y(()=>[(P(!0),q(_e,null,Ae(i.value,F=>(P(),Ne(V,{key:F.id,label:F.name,value:F.id},{default:y(()=>[g("div",Rk,[g("span",Vk,ee(F.name),1),g("span",Lk,ee(F.dimensions?.length||0)+"个维度 ",1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),f(T,{label:"实体名称",required:""},{default:y(()=>[f(U,{modelValue:l.value.name,"onUpdate:modelValue":E[1]||(E[1]=F=>l.value.name=F),placeholder:"输入实体名称",clearable:"",onKeyup:St(C,["enter"])},null,8,["modelValue"])]),_:1}),f(T,{label:"描述"},{default:y(()=>[f(U,{modelValue:l.value.description,"onUpdate:modelValue":E[2]||(E[2]=F=>l.value.description=F),type:"textarea",rows:3,placeholder:"输入实体描述"},null,8,["modelValue"])]),_:1}),a.value?(P(),q(_e,{key:0},[f(z,{"content-position":"left"},{default:y(()=>[g("div",Bk,[E[9]||(E[9]=g("span",null,"维度信息",-1)),f(W,{size:"small",type:"info",effect:"plain",class:"dimension-count"},{default:y(()=>[ne(ee(a.value.dimensions.length)+"个维度 ",1)]),_:1})])]),_:1}),(P(!0),q(_e,null,Ae(a.value.dimensions,F=>(P(),Ne(T,{key:F.name,label:F.name},{default:y(()=>[f(U,{modelValue:l.value.dimensions[F.name],"onUpdate:modelValue":S=>l.value.dimensions[F.name]=S,placeholder:"输入"+F.name,type:"textarea",rows:2,class:"dimension-input"},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),_:2},1032,["label"]))),128))],64)):ge("",!0)]),_:1},8,["model"])])]),_:1},8,["visible","initial-position","width","height"]),f(be,{modelValue:c.value,"onUpdate:modelValue":E[6]||(E[6]=F=>c.value=F),title:"导入实体",width:"600px",class:"import-dialog","append-to-body":!0},{footer:y(()=>[g("span",Wk,[f(B,{onClick:E[5]||(E[5]=F=>c.value=!1)},{default:y(()=>E[13]||(E[13]=[ne("取消")])),_:1}),f(B,{type:"primary",onClick:X},{default:y(()=>E[14]||(E[14]=[ne("确认导入")])),_:1})])]),default:y(()=>[g("div",zk,[f(Fe,null,{default:y(()=>[f(ke,null,{title:y(()=>[g("div",Fk,[f(Te,null,{default:y(()=>[f(fe)]),_:1}),E[11]||(E[11]=g("span",null,"查看JSON格式示例",-1))])]),default:y(()=>[E[12]||(E[12]=g("div",{class:"format-hint-content"},[g("pre",null,`{
  "name": "实体名称",
  "description": "实体描述",
  "dimensions": {
    "维度1": "值1",
    "维度2": "值2"
  }
}`)],-1))]),_:1})]),_:1}),f(U,{modelValue:u.value,"onUpdate:modelValue":E[4]||(E[4]=F=>u.value=F),type:"textarea",rows:10,placeholder:"请输入JSON字符串",class:"import-input"},null,8,["modelValue"])])]),_:1},8,["modelValue"])])}}},qk=ut(Hk,[["__scopeId","data-v-863a74bd"]]),Jk={class:"title-extra"},Uk={class:"window-content"},jk={class:"pool-option"},Kk={class:"pool-name"},Xk={class:"pool-info"},Yk={class:"update-time"},Gk={class:"tag-option"},Qk={class:"import-content"},Zk={class:"format-hint-title"},ex={class:"dialog-footer"},tx={class:"create-pool-content"},nx={class:"dialog-footer"},sx={__name:"SceneCreateWindow",props:{visible:Boolean,bookId:{type:[String,Number],required:!0}},emits:["close","created","update:visible"],setup(n,{emit:e}){const t=n,s=e,o=I(null),r=wn(),i=I([]),l=I({poolId:"",title:"",description:"",tags:[]}),a=I(["战斗","对话","探索","追逐","相遇","告别","冲突","和解","发现","选择"]),c=me(()=>r.state.config.editor?.sceneWindow||{position:{x:120,y:120},size:{width:600,height:600}}),u=_=>{r.updateConfigItem("editor.sceneWindow.position",_)},d=_=>{r.updateConfigItem("editor.sceneWindow.size",_)},v=async()=>{try{const _=await window.pywebview.api.book_controller.get_scene_events(t.bookId),T=typeof _=="string"?JSON.parse(_):_;T.status==="success"?i.value=(T.data?.pools||[]).filter(U=>!U.isVirtual):$.error(T.message||"加载卡池失败")}catch(_){console.error("加载卡池失败:",_),$.error("加载卡池失败："+_.message)}},p=_=>{i.value.find(T=>T.id===_)},b=_=>new Date(_).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}),w=async()=>{try{if(!l.value.poolId){$.error("请选择场景卡池");return}if(!l.value.title){$.error("请输入场景标题");return}const _=i.value.find(z=>z.id===l.value.poolId);if(!_){$.error("卡池不存在");return}Array.isArray(_.scenes)||(_.scenes=[]);const T={id:Date.now().toString(),title:l.value.title,description:l.value.description,tags:l.value.tags,position:{left:Math.random()*560,top:Math.random()*320},zIndex:1};_.scenes.push(T),_.updateTime=Date.now();const U=await window.pywebview.api.book_controller.save_scene_events(t.bookId,{pools:i.value,currentPoolId:l.value.poolId}),W=typeof U=="string"?JSON.parse(U):U;if(W.status==="success"){s("created"),$.success("场景创建成功");const z=l.value.poolId;l.value={poolId:z,title:"",description:"",tags:[]}}else throw new Error(W.message||"保存失败")}catch(_){console.error("保存场景失败:",_),$.error("保存失败："+_.message)}};Ie(()=>t.visible,async _=>{_&&i.value.length===0&&await v()});const C=_=>{if(!t.visible)return;const T=document.activeElement,U=o.value?.$el;!U||!U.contains(T)||_.key==="Enter"&&(_.ctrlKey||_.metaKey)&&(_.preventDefault(),_.stopPropagation(),w())};Ie(()=>t.visible,_=>{_?window.addEventListener("keydown",C):window.removeEventListener("keydown",C)}),ct(()=>{r.state.config.editor.sceneWindow||r.updateConfigItem("editor.sceneWindow",{position:{x:120,y:120},size:{width:600,height:600}}),t.visible&&window.addEventListener("keydown",C)}),lt(()=>{window.removeEventListener("keydown",C)});const M=I(!1),X=I(""),L=I(!1),D=I({name:""}),E=()=>{M.value=!0,X.value=""},B=()=>{L.value=!0,D.value.name=""},N=async()=>{try{if(!X.value.trim()){$.error("请输入JSON字符串");return}const _=JSON.parse(X.value);if(!_.name){$.error("导入失败：缺少卡池名称");return}if(!Array.isArray(_.scenes)){$.error("导入失败：场景列表格式不正确");return}const T={id:Date.now().toString(),name:_.name,scenes:_.scenes.map(z=>({id:Date.now().toString()+Math.random().toString(36).slice(2),title:z.title,description:z.description||"",tags:z.tags||[],position:z.position||{left:Math.random()*560,top:Math.random()*320},zIndex:1})),updateTime:Date.now()};i.value.push(T);const U=await window.pywebview.api.book_controller.save_scene_events(t.bookId,{pools:i.value,currentPoolId:T.id}),W=typeof U=="string"?JSON.parse(U):U;if(W.status==="success")$.success("卡池导入成功"),M.value=!1,l.value.poolId=T.id,p(T.id),s("created");else throw new Error(W.message||"导入失败")}catch(_){_ instanceof SyntaxError?$.error("导入失败：JSON格式不正确"):(console.error("导入失败:",_),$.error("导入失败："+_.message))}},V=async()=>{try{if(!D.value.name.trim()){$.error("请输入卡池名称");return}const _={id:Date.now().toString(),name:D.value.name,scenes:[],updateTime:Date.now()};i.value.push(_);const T=await window.pywebview.api.book_controller.save_scene_events(t.bookId,{pools:i.value,currentPoolId:_.id}),U=typeof T=="string"?JSON.parse(T):T;if(U.status==="success")$.success("卡池创建成功"),L.value=!1,l.value.poolId=_.id,p(_.id);else throw new Error(U.message||"创建失败")}catch(_){console.error("创建卡池失败:",_),$.error("创建失败："+_.message)}};return(_,T)=>{const U=kn,W=vs,z=br,re=kr,fe=wr,Te=ko,ke=bn,Fe=wo,be=_t,F=Ac,S=Dc,x=xr;return P(),q("div",null,[f(El,{ref_key:"windowRef",ref:o,title:"新建场景",visible:n.visible,"initial-position":c.value.position,width:c.value.size.width,height:c.value.size.height,pinnable:!0,class:"scene-create-window",onClose:T[4]||(T[4]=A=>s("update:visible",!1)),onMove:u,onResize:d},{"title-extra":y(()=>[g("div",Jk,[f(W,{content:"新建卡池",placement:"bottom",effect:"light"},{default:y(()=>[f(U,{type:"success",size:"small",onClick:B,class:"pool-btn"},{default:y(()=>T[11]||(T[11]=[ne(" 新建卡池 ")])),_:1})]),_:1}),f(W,{content:"从JSON导入场景",placement:"bottom",effect:"light"},{default:y(()=>[f(U,{type:"success",size:"small",disabled:!l.value.poolId,onClick:E,class:"import-btn"},{default:y(()=>T[12]||(T[12]=[ne(" 导入 ")])),_:1},8,["disabled"])]),_:1}),f(W,{content:"Ctrl + Enter 快速创建",placement:"bottom",effect:"light"},{default:y(()=>[f(U,{type:"primary",size:"small",disabled:!l.value.poolId,onClick:w,class:"create-btn"},{default:y(()=>T[13]||(T[13]=[ne(" 创建 ")])),_:1},8,["disabled"])]),_:1})])]),default:y(()=>[g("div",Uk,[f(Fe,{model:l.value,"label-width":"100px"},{default:y(()=>[f(Te,{label:"选择卡池",required:""},{default:y(()=>[f(fe,{modelValue:l.value.poolId,"onUpdate:modelValue":T[0]||(T[0]=A=>l.value.poolId=A),placeholder:"请选择场景卡池",class:"pool-select",onChange:p},{default:y(()=>[(P(!0),q(_e,null,Ae(i.value,A=>(P(),Ne(re,{key:A.id,label:A.name,value:A.id},{default:y(()=>[g("div",jk,[g("span",Kk,ee(A.name),1),g("span",Xk,[f(z,{size:"small",type:"success",effect:"light",class:"scene-count"},{default:y(()=>[ne(ee(A.scenes?.length||0)+"个场景 ",1)]),_:2},1024),g("span",Yk,ee(b(A.updateTime)),1)])])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),f(Te,{label:"场景标题",required:""},{default:y(()=>[f(ke,{modelValue:l.value.title,"onUpdate:modelValue":T[1]||(T[1]=A=>l.value.title=A),placeholder:"输入场景标题",clearable:"",onKeyup:St(w,["enter"])},null,8,["modelValue"])]),_:1}),f(Te,{label:"场景描述"},{default:y(()=>[f(ke,{modelValue:l.value.description,"onUpdate:modelValue":T[2]||(T[2]=A=>l.value.description=A),type:"textarea",rows:4,placeholder:"输入场景描述",class:"description-input"},null,8,["modelValue"])]),_:1}),f(Te,{label:"场景标签"},{default:y(()=>[f(fe,{modelValue:l.value.tags,"onUpdate:modelValue":T[3]||(T[3]=A=>l.value.tags=A),multiple:"",filterable:"","allow-create":"","default-first-option":"",placeholder:"请选择或创建标签",class:"tags-select"},{default:y(()=>[(P(!0),q(_e,null,Ae(a.value,A=>(P(),Ne(re,{key:A,label:A,value:A},{default:y(()=>[g("span",Gk,ee(A),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])])]),_:1},8,["visible","initial-position","width","height"]),f(x,{modelValue:M.value,"onUpdate:modelValue":T[7]||(T[7]=A=>M.value=A),title:"导入场景卡池",width:"600px",class:"import-dialog","append-to-body":!0},{footer:y(()=>[g("span",ex,[f(U,{onClick:T[6]||(T[6]=A=>M.value=!1)},{default:y(()=>T[16]||(T[16]=[ne("取消")])),_:1}),f(U,{type:"primary",onClick:N},{default:y(()=>T[17]||(T[17]=[ne("确认导入")])),_:1})])]),default:y(()=>[g("div",Qk,[f(S,null,{default:y(()=>[f(F,null,{title:y(()=>[g("div",Zk,[f(be,null,{default:y(()=>[f(j(sr))]),_:1}),T[14]||(T[14]=g("span",null,"查看JSON格式示例",-1))])]),default:y(()=>[T[15]||(T[15]=g("div",{class:"format-hint-content"},[g("pre",null,`{
  "name": "卡池名称",
  "scenes": [
    {
      "title": "场景标题1",
      "description": "场景描述1",
      "tags": ["标签1", "标签2"],
      "position": {
        "left": 100,
        "top": 100
      }
    },
    {
      "title": "场景标题2",
      "description": "场景描述2",
      "tags": ["标签3", "标签4"],
      "position": {
        "left": 300,
        "top": 200
      }
    }
  ]
}`)],-1))]),_:1})]),_:1}),f(ke,{modelValue:X.value,"onUpdate:modelValue":T[5]||(T[5]=A=>X.value=A),type:"textarea",rows:12,placeholder:"请输入JSON字符串",class:"import-input"},null,8,["modelValue"])])]),_:1},8,["modelValue"]),f(x,{modelValue:L.value,"onUpdate:modelValue":T[10]||(T[10]=A=>L.value=A),title:"新建卡池",width:"500px",class:"create-pool-dialog","append-to-body":!0},{footer:y(()=>[g("span",nx,[f(U,{onClick:T[9]||(T[9]=A=>L.value=!1)},{default:y(()=>T[18]||(T[18]=[ne("取消")])),_:1}),f(U,{type:"primary",onClick:V},{default:y(()=>T[19]||(T[19]=[ne("确认创建")])),_:1})])]),default:y(()=>[g("div",tx,[f(Fe,{model:D.value,"label-width":"80px"},{default:y(()=>[f(Te,{label:"卡池名称",required:""},{default:y(()=>[f(ke,{modelValue:D.value.name,"onUpdate:modelValue":T[8]||(T[8]=A=>D.value.name=A),placeholder:"请输入卡池名称",clearable:""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])])]),_:1},8,["modelValue"])])}}},ox=ut(sx,[["__scopeId","data-v-39eea2bf"]]),rx={class:"search-row"},ix={class:"replace-row"},lx={class:"match-stats"},ax={__name:"FindReplacePanel",props:{visible:{type:Boolean,default:!1},editor:{type:Object,required:!1,default:null}},emits:["close","update:visible"],setup(n,{emit:e}){const t=n,s=e,o=I(""),r=I(""),i=I(!1),l=I(null),a=I(null),c=I(!1),u=I({x:0,y:0}),d=I({x:null,y:null});Ie(()=>t.editor?.storage?.findAndReplace?.isCaseSensitive,_=>{_!==void 0&&(i.value=_)},{immediate:!0});const v=_=>{if(_.target.closest("button")||_.target.closest("input"))return;c.value=!0;const T=a.value.getBoundingClientRect();u.value={x:_.clientX-T.left,y:_.clientY-T.top},d.value.x===null&&(d.value={x:T.left,y:T.top}),document.addEventListener("mousemove",p),document.addEventListener("mouseup",b)},p=_=>{c.value&&(d.value={x:_.clientX-u.value.x,y:_.clientY-u.value.y},a.value&&(a.value.style.right="auto",a.value.style.left=`${d.value.x}px`,a.value.style.top=`${d.value.y}px`))},b=()=>{c.value=!1,document.removeEventListener("mousemove",p),document.removeEventListener("mouseup",b)};Ie(()=>t.visible,_=>{_?Le(()=>{l.value?.focus(),t.editor?.commands&&t.editor.commands.startFindAndReplace(),d.value.x!==null&&a.value?(a.value.style.right="auto",a.value.style.left=`${d.value.x}px`,a.value.style.top=`${d.value.y}px`):(d.value={x:null,y:null},a.value&&(a.value.style.right="20px",a.value.style.top="70px",a.value.style.left="auto"))}):t.editor?.commands&&t.editor.commands.endFindAndReplace()},{immediate:!0}),Ie(()=>t.editor?.storage?.findAndReplace?.findText,_=>{_!==void 0&&_!==o.value&&(o.value=_,_&&t.visible&&Le(()=>{L()}))},{immediate:!0}),lt(()=>{document.removeEventListener("mousemove",p),document.removeEventListener("mouseup",b)});const w=me(()=>t.editor?.storage?.findAndReplace?.currentIndex||0),C=me(()=>t.editor?.storage?.findAndReplace?.totalMatches||0),M=me(()=>C.value>0),X=()=>{s("update:visible",!1),s("close")},L=()=>{if(t.editor?.commands)try{t.editor.commands.setFindTerm(o.value),t.editor.commands.updateFindResults()}catch(_){console.error("更新搜索时出错:",_)}},D=()=>{if(t.editor?.commands)try{t.editor.commands.updateFindResults(),t.editor.commands.findNext()}catch(_){console.error("查找下一项时发生错误:",_)}},E=()=>{if(t.editor?.commands)try{t.editor.commands.updateFindResults(),t.editor.commands.findPrevious()}catch(_){console.error("查找上一项时发生错误:",_)}},B=()=>{if(!(!M.value||!t.editor?.commands))try{t.editor.commands.setReplaceTerm(r.value),t.editor.commands.replaceCurrentMatch(),setTimeout(()=>{try{o.value&&t.editor?.commands&&t.editor.commands.setFindTerm(o.value)}catch(_){console.warn("恢复查找失败",_)}},100)}catch(_){console.error("替换时发生错误:",_),setTimeout(()=>{try{o.value&&t.editor?.commands&&t.editor.commands.setFindTerm(o.value)}catch(T){console.warn("恢复查找失败",T)}},100)}},N=()=>{if(!M.value||!t.editor?.commands)return;const _=C.value;try{t.editor.commands.setReplaceTerm(r.value),t.editor.commands.replaceAllMatches(),$({message:`已替换 ${_} 处匹配内容`,type:"success",duration:2e3}),setTimeout(()=>{try{o.value&&t.editor?.commands&&t.editor.commands.setFindTerm(o.value)}catch(T){console.warn("恢复查找失败",T)}},100)}catch(T){console.error("替换所有匹配项时发生错误:",T),$({message:`替换操作已执行，可能已替换了 ${_} 处匹配内容`,type:"info",duration:2e3}),setTimeout(()=>{try{o.value&&t.editor?.commands&&t.editor.commands.setFindTerm(o.value)}catch(U){console.warn("恢复查找失败",U)}},100)}},V=()=>{t.editor?.commands&&(i.value=!i.value,t.editor.commands.toggleCaseSensitivity(),setTimeout(()=>{o.value&&L()},10))};return Ie(()=>t.editor,_=>{_?.commands&&t.visible&&_.commands.startFindAndReplace()},{immediate:!0}),ct(()=>{t.visible&&t.editor?.commands&&t.editor.commands.startFindAndReplace()}),(_,T)=>{const U=_t,W=kn,z=vs,re=bn;return Fn((P(),q("div",{class:we(["find-replace-panel",{"is-active":n.visible}]),ref_key:"panel",ref:a},[g("div",{class:"panel-content",onMousedown:v},[g("div",rx,[f(re,{modelValue:o.value,"onUpdate:modelValue":T[0]||(T[0]=fe=>o.value=fe),placeholder:"查找内容",clearable:"",onInput:L,onKeydown:St(D,["enter"]),ref_key:"findInput",ref:l,class:"search-input",onMousedown:T[1]||(T[1]=Oe(()=>{},["stop"]))},{prefix:y(()=>[f(U,null,{default:y(()=>[f(j(fs))]),_:1})]),append:y(()=>[f(z,{content:"区分大小写",placement:"top","hide-after":1e3},{default:y(()=>[f(W,{class:we(["case-button",{"is-active":i.value}]),onClick:Oe(V,["stop"]),link:""},{default:y(()=>T[5]||(T[5]=[g("span",{class:"case-icon"},"Aa",-1)])),_:1},8,["class"])]),_:1}),f(W,{disabled:!M.value,onClick:Oe(E,["stop"])},{default:y(()=>[f(U,null,{default:y(()=>[f(j(Yi))]),_:1})]),_:1},8,["disabled"]),f(W,{disabled:!M.value,onClick:Oe(D,["stop"])},{default:y(()=>[f(U,null,{default:y(()=>[f(j($c))]),_:1})]),_:1},8,["disabled"])]),_:1},8,["modelValue"]),f(W,{class:"close-button",link:"",onClick:Oe(X,["stop"])},{default:y(()=>[f(U,null,{default:y(()=>[f(j(ps))]),_:1})]),_:1})]),g("div",ix,[f(re,{modelValue:r.value,"onUpdate:modelValue":T[2]||(T[2]=fe=>r.value=fe),placeholder:"替换为",clearable:"",onKeydown:St(B,["enter"]),class:"replace-input",onMousedown:T[3]||(T[3]=Oe(()=>{},["stop"]))},{prefix:y(()=>[f(U,null,{default:y(()=>[f(j(zt))]),_:1})]),append:y(()=>[f(W,{disabled:!M.value,onClick:Oe(B,["stop"])},{default:y(()=>T[6]||(T[6]=[ne(" 替换 ")])),_:1},8,["disabled"]),f(W,{disabled:!M.value,onClick:Oe(N,["stop"])},{default:y(()=>T[7]||(T[7]=[ne(" 全部替换 ")])),_:1},8,["disabled"])]),_:1},8,["modelValue"])]),o.value&&M.value?(P(),q("div",{key:0,class:"options-row",onMousedown:T[4]||(T[4]=Oe(()=>{},["stop"]))},[g("div",lx,ee(w.value+1)+"/"+ee(C.value),1)],32)):ge("",!0)],32)],2)),[[bo,n.visible]])}}},cx=ut(ax,[["__scopeId","data-v-aec7b35b"]]);class ux{constructor(){this.findText="",this.replaceText="",this.isActive=!1,this.isCaseSensitive=!1,this.currentIndex=0,this.totalMatches=0,this.decorationSet=Be.empty,this.results=[]}}function Mi(n,e,t,s){const o=[],r=[];if(!e||e.length===0)return{results:o,decorations:r,decorationSet:Be.empty};const i=t?e:e.toLowerCase();return n.descendants((l,a)=>{if(!l.isText)return;const c=l.text||"",u=t?c:c.toLowerCase();let d=0;for(;(d=u.indexOf(i,d))!==-1;){const v=a+d,p=v+i.length;o.push({from:v,to:p,text:c.slice(d,d+i.length)}),r.push(mt.inline(v,p,{class:s})),d+=i.length}}),{results:o,decorations:r,decorationSet:Be.create(n,r)}}function to(n,e,t,s,o){if(!e||e.length===0)return Be.empty;const r=[];return e.forEach((i,l)=>{const a=l===t;r.push(mt.inline(i.from,i.to,{class:a?`${s} ${o}`:s}))}),Be.create(n.state.doc,r)}const dx=Qe.create({name:"findAndReplace",addOptions(){return{findClass:"find-result",findActiveClass:"find-result-active"}},addStorage(){return new ux},addCommands(){return{startFindAndReplace:()=>({editor:n})=>{try{this.storage.isActive=!0,this.storage.findText="",this.storage.replaceText="",this.storage.currentIndex=0,this.storage.totalMatches=0,this.storage.results=[],n.view.dispatch(n.state.tr)}catch(e){console.error("Error in startFindAndReplace:",e)}return!0},endFindAndReplace:()=>({editor:n})=>{try{this.storage.isActive=!1,this.storage.findText="",this.storage.decorationSet=Be.empty,this.storage.results=[],n.view.dispatch(n.state.tr)}catch(e){console.error("Error in endFindAndReplace:",e)}return!0},setFindTerm:n=>({editor:e})=>{try{const t=n||"";this.storage.findText=t;const s=Mi(e.state.doc,t,this.storage.isCaseSensitive,this.options.findClass);this.storage.results=s.results,this.storage.totalMatches=s.results.length,this.storage.results.length>0&&(this.storage.currentIndex<0||this.storage.currentIndex>=this.storage.results.length)&&(this.storage.currentIndex=0),this.storage.decorationSet=to(e,this.storage.results,this.storage.currentIndex,this.options.findClass,this.options.findActiveClass),e.view.dispatch(e.state.tr)}catch(t){console.error("Error in setFindTerm:",t)}return!0},setReplaceTerm:n=>({editor:e})=>{try{this.storage.replaceText=n||""}catch(t){console.error("Error in setReplaceTerm:",t)}return!0},findNext:()=>({editor:n})=>{try{if(this.storage.results.length===0)return!1;this.storage.currentIndex=(this.storage.currentIndex+1)%this.storage.results.length;const e=this.storage.results[this.storage.currentIndex];if(!e)return!1;this.storage.decorationSet=to(n,this.storage.results,this.storage.currentIndex,this.options.findClass,this.options.findActiveClass);const t=n.state.tr;return t.setSelection(n.state.selection.constructor.near(n.state.doc.resolve(e.from))),t.scrollIntoView().setMeta("findOperation",!0),n.view.dispatch(t),setTimeout(()=>{try{const s=n.view.dom,o=s.closest(".editor-content")||s.parentElement,r=s.querySelector(`.${this.options.findActiveClass}`);if(r&&o){const i=o.getBoundingClientRect(),l=r.getBoundingClientRect(),a=i.top+i.height/2,u=l.top+l.height/2-a;o.scrollBy({top:u,behavior:"smooth"})}}catch(s){console.error("额外滚动调整失败:",s)}},50),!0}catch(e){return console.error("Error in findNext:",e),!1}},findPrevious:()=>({editor:n})=>{try{if(this.storage.results.length===0)return!1;this.storage.currentIndex=(this.storage.currentIndex-1+this.storage.results.length)%this.storage.results.length;const e=this.storage.results[this.storage.currentIndex];if(!e)return!1;this.storage.decorationSet=to(n,this.storage.results,this.storage.currentIndex,this.options.findClass,this.options.findActiveClass);const t=n.state.tr;return t.setSelection(n.state.selection.constructor.near(n.state.doc.resolve(e.from))),t.scrollIntoView().setMeta("findOperation",!0),n.view.dispatch(t),setTimeout(()=>{try{const s=n.view.dom,o=s.closest(".editor-content")||s.parentElement,r=s.querySelector(`.${this.options.findActiveClass}`);if(r&&o){const i=o.getBoundingClientRect(),l=r.getBoundingClientRect(),a=i.top+i.height/2,u=l.top+l.height/2-a;o.scrollBy({top:u,behavior:"smooth"})}}catch(s){console.error("额外滚动调整失败:",s)}},50),!0}catch(e){return console.error("Error in findPrevious:",e),!1}},replaceCurrentMatch:()=>({editor:n,chain:e})=>{try{if(this.storage.results.length===0||this.storage.currentIndex<0||this.storage.currentIndex>=this.storage.results.length)return!1;const t=this.storage.results[this.storage.currentIndex];if(!t)return!1;const s=this.storage.replaceText||"";return e().insertContentAt({from:t.from,to:t.to},s).run()}catch(t){return console.error("替换时发生错误:",t),!1}},replaceAllMatches:()=>({editor:n,chain:e,commands:t})=>{try{if(this.storage.results.length===0)return 0;const s=[...this.storage.results].sort((a,c)=>c.from-a.from),o=this.storage.replaceText||"";let r=0,i=e();for(const a of s)i=i.insertContentAt({from:a.from,to:a.to},o);return i.run()?s.length:0}catch(s){console.error("批量替换时发生错误:",s),console.log("尝试逐个替换...");let o=0;const r=[...this.storage.results].sort((l,a)=>a.from-l.from),i=this.storage.replaceText||"";for(const l of r)try{n.chain().insertContentAt({from:l.from,to:l.to},i).run()&&o++}catch(a){console.error("单个替换失败:",a)}return o}},toggleCaseSensitivity:()=>({editor:n})=>{try{this.storage.isCaseSensitive=!this.storage.isCaseSensitive;const e=Mi(n.state.doc,this.storage.findText,this.storage.isCaseSensitive,this.options.findClass);this.storage.results=e.results,this.storage.totalMatches=e.results.length,this.storage.results.length>0&&(this.storage.currentIndex<0||this.storage.currentIndex>=this.storage.results.length)&&(this.storage.currentIndex=0),this.storage.decorationSet=to(n,this.storage.results,this.storage.currentIndex,this.options.findClass,this.options.findActiveClass),n.view.dispatch(n.state.tr)}catch(e){console.error("Error in toggleCaseSensitivity:",e)}return!0},updateFindResults:()=>({editor:n,chain:e})=>{try{if(!n||!n.state||!n.state.doc)return console.warn("编辑器状态无效，无法更新查找结果"),!1;const t=Mi(n.state.doc,this.storage.findText,this.storage.isCaseSensitive,this.options.findClass);return this.storage.results=t.results,this.storage.totalMatches=t.results.length,this.storage.results.length>0?this.storage.currentIndex=Math.min(Math.max(0,this.storage.currentIndex),this.storage.results.length-1):this.storage.currentIndex=-1,this.storage.decorationSet=to(n,this.storage.results,this.storage.currentIndex,this.options.findClass,this.options.findActiveClass),e().focus(!1).run(),!0}catch(t){return console.error("更新查找结果时出错:",t),!1}},resetFindState:()=>({editor:n})=>{try{this.storage.findText="",this.storage.replaceText="",this.storage.results=[],this.storage.totalMatches=0,this.storage.currentIndex=-1,this.storage.decorationSet=Be.empty;const e=n.state.tr;return n.view.dispatch(e),!0}catch(e){return console.error("重置查找状态时出错:",e),!1}}}},addProseMirrorPlugins(){const n=this;return[new nt({key:new Nt("findAndReplace"),state:{init(){return Be.empty},apply(e,t){try{return n.storage.isActive&&n.storage.decorationSet||Be.empty}catch(s){return console.error("装饰处理出错:",s),Be.empty}}},props:{decorations(e){try{return this.getState(e)}catch(t){return console.error("获取装饰时出错:",t),Be.empty}}}})]}}),fx={class:"search-panel-header no-select"},px={class:"actions"},hx={class:"fixed-content"},mx={class:"search-input-container"},gx={class:"input-row"},vx={class:"history-dropdown"},yx={class:"dropdown-header no-select"},bx={class:"dropdown-list"},wx=["onClick"],kx={class:"term-text"},xx={class:"scroll-content"},Sx={key:0,class:"search-providers"},Cx={class:"provider-list"},_x={class:"provider-info"},Mx={class:"provider-name no-select"},Tx={class:"provider-url no-select"},Ex={class:"provider-actions"},wc=20,Ix={__name:"SearchPanel",props:{visible:{type:Boolean,default:!1},bookId:{type:String,required:!0},selectedText:{type:String,default:""}},emits:["close","show-config"],setup(n,{emit:e}){const t=n,s=e,o=wn(),r=I(""),i=I([]);I(!1);const l=I(null),a=I(!1);Ie(()=>t.selectedText,N=>{N&&t.visible&&(r.value=N.trim())}),Ie(()=>t.visible,N=>{N&&t.selectedText&&(r.value=t.selectedText.trim())});const c=N=>{const V=document.querySelector(".search-wrapper");V&&!V.contains(N.target)&&(a.value=!1)};ct(()=>{document.addEventListener("click",c);const N=localStorage.getItem("searchHistory");if(N)try{i.value=JSON.parse(N)}catch(V){console.error("解析搜索历史失败:",V)}});const u=me(()=>o.search?.providers||[]),d=me(()=>{const N=u.value;return N.find(V=>V.isDefault)||(N.length>0?N[0]:null)}),v=N=>N.length>40?N.substring(0,37)+"...":N,p=async N=>{try{if(console.log("SearchPanel: Copying text:",N?N.substring(0,50)+"...":"undefined or empty"),!N){$.warning("没有内容可复制");return}await window.pywebview.api.copy_to_clipboard(N),$.success("复制成功")}catch(V){console.error("SearchPanel: 复制失败:",V);try{const _=document.createElement("textarea");if(_.value=N,_.style.position="fixed",_.style.left="-999999px",_.style.top="-999999px",document.body.appendChild(_),_.focus(),_.select(),document.execCommand("copy"))$.success("复制成功");else throw new Error("execCommand failed");document.body.removeChild(_)}catch(_){console.error("SearchPanel: 复制失败 (fallback):",_),$.error("复制失败: "+V.message)}}},b=(N,V)=>{let _=N;return _&&(_.includes("{query}")&&V?_=_.replace("{query}",encodeURIComponent(V)):_.includes("{q}")&&V&&(_=_.replace("{q}",encodeURIComponent(V))),_)},w=async N=>{if(!N){console.log("无法执行搜索，提供商为空");return}console.log("准备打开浏览器搜索:",r.value),console.log("搜索提供商:",N.name);try{r.value&&(await p(r.value),M(r.value));const V=b(N.url,r.value);console.log("搜索URL:",V),console.log("调用后端API打开系统浏览器"),await window.pywebview.api.open_browser_window(V,N.name),console.log("浏览器已打开"),r.value?$.success(`已复制搜索内容并在浏览器中打开"${N.name}"`):$.success(`已在浏览器中打开"${N.name}"`)}catch(V){console.error("打开浏览器失败:",V),$.error("打开浏览器失败: "+(V.message||"未知错误"))}},C=()=>{if(console.log("执行搜索:",r.value),u.value.length===0){console.log("没有配置搜索提供商"),$.warning("请先配置搜索提供商"),L();return}const N=d.value;if(!N){console.log("未找到默认提供商"),$.warning("未找到默认提供商");return}console.log("使用默认提供商进行搜索:",N.name),w(N)},M=N=>{if(!N.trim())return;const V=i.value.indexOf(N);V!==-1&&i.value.splice(V,1),i.value.unshift(N),i.value.length>wc&&(i.value=i.value.slice(0,wc)),localStorage.setItem("searchHistory",JSON.stringify(i.value))},X=()=>{i.value=[],localStorage.removeItem("searchHistory")},L=()=>{s("show-config")},D=N=>{console.log("使用特定提供商搜索:",N.name),w(N)},E=N=>{r.value=N,a.value=!1},B=async()=>{if(console.log("执行一键多搜:",r.value),u.value.length===0){console.log("没有配置搜索提供商"),$.warning("请先配置搜索提供商"),L();return}r.value&&(await p(r.value),M(r.value));const N=5,V=u.value.slice(0,N);V.length<u.value.length&&$.warning(`为避免浏览器拦截，最多同时打开${N}个搜索窗口`),V.forEach((_,T)=>{setTimeout(()=>{const U=b(_.url,r.value);console.log(`打开搜索提供商 ${_.name}: ${U}`),window.pywebview.api.open_browser_window(U,_.name).catch(W=>{console.error(`打开搜索提供商 ${_.name} 失败:`,W)})},T*300)}),r.value?$.success(`已复制搜索内容，正在打开${V.length}个搜索结果`):$.success(`正在打开${V.length}个搜索结果`)};return(N,V)=>{const _=_t,T=kn,U=bn,W=yo;return P(),q("div",{class:we(["search-panel-container",{visible:n.visible}])},[g("div",fx,[V[6]||(V[6]=g("div",{class:"title"},"网络查询",-1)),g("div",px,[f(T,{type:"primary",onClick:L,class:"config-button",size:"default"},{default:y(()=>[f(_,{class:"config-icon"},{default:y(()=>[f(j(yr))]),_:1}),V[5]||(V[5]=g("span",null,"配置",-1))]),_:1}),f(T,{plain:"",onClick:V[0]||(V[0]=z=>N.$emit("close")),class:"close-button"},{default:y(()=>[f(_,null,{default:y(()=>[f(j(ps))]),_:1})]),_:1})])]),g("div",hx,[g("div",mx,[g("div",gx,[g("div",{class:"search-wrapper",onMouseenter:V[3]||(V[3]=z=>a.value=!0),onMouseleave:V[4]||(V[4]=z=>a.value=!1)},[f(U,{modelValue:r.value,"onUpdate:modelValue":V[1]||(V[1]=z=>r.value=z),placeholder:"输入搜索内容...",size:"large",clearable:"",onKeyup:St(C,["enter"]),onFocus:V[2]||(V[2]=z=>a.value=!0),class:"search-input",ref_key:"searchInputRef",ref:l},{append:y(()=>[f(T,{onClick:C,type:"primary"},{default:y(()=>[f(_,null,{default:y(()=>[f(j(fs))]),_:1})]),_:1})]),_:1},8,["modelValue"]),Fn(g("div",vx,[g("div",yx,[V[8]||(V[8]=g("span",null,"最近搜索",-1)),f(T,{link:"",onClick:Oe(X,["stop"]),size:"small",type:"danger"},{default:y(()=>V[7]||(V[7]=[ne("清空")])),_:1})]),g("div",bx,[(P(!0),q(_e,null,Ae(i.value,(z,re)=>(P(),q("div",{key:re,class:"history-item no-select",onClick:fe=>E(z)},[f(_,null,{default:y(()=>[f(j(Wf))]),_:1}),g("span",kx,ee(z),1)],8,wx))),128))])],512),[[bo,a.value&&i.value.length>0]])],32),u.value.length>1?(P(),Ne(T,{key:0,type:"success",onClick:B,class:"multi-search-button"},{default:y(()=>[f(_,null,{default:y(()=>[f(j(Ic))]),_:1}),V[9]||(V[9]=ne(" 多搜 "))]),_:1})):ge("",!0)])])]),g("div",xx,[u.value.length>0?(P(),q("div",Sx,[g("div",Cx,[(P(!0),q(_e,null,Ae(u.value,z=>(P(),q("div",{key:z.id,class:"provider-item"},[g("div",_x,[g("div",Mx,ee(z.name),1),g("div",Tx,ee(v(z.url)),1)]),g("div",Ex,[f(T,{type:"primary",onClick:re=>D(z),class:"open-button"},{default:y(()=>[f(_,null,{default:y(()=>[f(j(Hf))]),_:1}),V[10]||(V[10]=ne(" 打开 "))]),_:2},1032,["onClick"])])]))),128))]),u.value.length===0?(P(),Ne(W,{key:0,description:"暂无配置的搜索提供商"})):ge("",!0)])):ge("",!0)])],2)}}},Ox=ut(Ix,[["__scopeId","data-v-96f4c3b7"]]),Nx={class:"dialog-header"},Dx={class:"header-content"},Ax={class:"header-icon"},$x={class:"dialog-actions"},Px={class:"actions-left"},Rx={class:"provider-count"},Vx={class:"dialog-content"},Lx={key:0,class:"empty-state"},Bx={class:"empty-icon"},zx={key:1,class:"provider-cards"},Fx={class:"provider-content"},Wx={class:"provider-header-row"},Hx={class:"provider-info"},qx={class:"provider-name no-select"},Jx={class:"provider-url no-select"},Ux={class:"provider-actions"},jx={class:"action-buttons"},Kx={class:"dialog-footer"},Xx={class:"edit-dialog-header"},Yx={class:"edit-header-content"},Gx={class:"edit-header-icon"},Qx={class:"edit-header-text"},Zx={class:"edit-dialog-title"},eS={class:"edit-dialog-content"},tS={class:"url-examples"},nS={class:"examples-grid"},sS={class:"preview-container"},oS={class:"preview-url"},rS={class:"edit-dialog-footer"},iS={__name:"SearchConfigWindow",props:{visible:{type:Boolean,default:!1},bookId:{type:String,required:!0}},emits:["update:visible","config-updated"],setup(n,{emit:e}){const t=n,s=e,o=wn(),r=I(!1),i=I(!1),l=I(!1),a=I(-1),c=I([]),u=I({id:"",name:"",url:"",isDefault:!1}),d=W=>W?W.length>50?W.substring(0,47)+"...":W:"",v=me(()=>{if(!u.value.url)return"输入URL后将显示预览效果";let W=u.value.url;const z="示例搜索词";return W.includes("{query}")?W.replace("{query}",z):W.includes("{q}")?W.replace("{q}",z):W+" (将复制搜索内容到剪贴板)"}),p=me(()=>u.value.name.trim()!==""&&u.value.url.trim()!=="");Ie(()=>t.visible,W=>{r.value=W,W&&X()}),Ie(r,W=>{s("update:visible",W)});const b=()=>{r.value=!1},w=()=>{},C=()=>{i.value=!1},M=()=>{},X=()=>{const W=o.search||{providers:[]};c.value=JSON.parse(JSON.stringify(W.providers||[]))},L=async()=>{try{c.value.length>0&&!c.value.some(W=>W.isDefault)&&(c.value[0].isDefault=!0),await o.updateConfig({search:{providers:c.value}}),s("config-updated"),$.success("搜索配置已保存"),r.value=!1}catch(W){console.error("保存搜索配置失败:",W),$.error("保存配置失败: "+W.message)}},D=()=>{l.value=!1,a.value=-1,u.value={id:T(),name:"",url:"",isDefault:c.value.length===0},i.value=!0},E=W=>{l.value=!0,a.value=W,u.value=JSON.parse(JSON.stringify(c.value[W])),i.value=!0},B=async W=>{try{await qt.confirm("确定要删除该搜索提供商吗？","删除提供商",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const z=c.value[W].isDefault;c.value.splice(W,1),z&&c.value.length>0&&(c.value[0].isDefault=!0)}catch{}},N=()=>{p.value&&(l.value?(c.value[a.value]={...u.value},u.value.isDefault&&V(a.value)):(c.value.push({...u.value}),u.value.isDefault&&V(c.value.length-1)),i.value=!1)},V=W=>{c.value.forEach((z,re)=>{z.isDefault=re===W})},_=(W,z)=>{const re=z==="up"?W-1:W+1;if(re<0||re>=c.value.length)return;const fe=c.value[re];c.value[re]=c.value[W],c.value[W]=fe,c.value[re].isDefault?V(re):c.value[W].isDefault&&V(W)},T=()=>Date.now().toString(36)+Math.random().toString(36).substring(2),U=W=>{u.value.url=W};return(W,z)=>{const re=_t,fe=kn,Te=br,ke=vs,Fe=bn,be=ko,F=tl,S=wo;return r.value?(P(),q("div",{key:0,class:"custom-dialog-overlay no-select",onClick:w},[g("div",{class:"custom-dialog-container no-select gpu-accelerated",onClick:z[0]||(z[0]=Oe(()=>{},["stop"]))},[g("div",Nx,[g("div",Dx,[g("div",Ax,[f(re,null,{default:y(()=>[f(j(fs))]),_:1})]),z[11]||(z[11]=g("div",{class:"header-text"},[g("h2",{class:"dialog-title"},"网络查询配置"),g("p",{class:"dialog-subtitle"},"管理您的搜索引擎和查询提供商")],-1))]),g("button",{onClick:b,class:"close-btn"},[f(re,null,{default:y(()=>[f(j(ps))]),_:1})])]),g("div",$x,[g("div",Px,[z[12]||(z[12]=g("h3",{class:"section-title"},"查询提供商",-1)),g("span",Rx,ee(c.value.length)+" 个提供商",1)]),f(fe,{type:"primary",onClick:D,class:"add-button"},{default:y(()=>[f(re,null,{default:y(()=>[f(j(on))]),_:1}),z[13]||(z[13]=g("span",null,"添加提供商",-1))]),_:1})]),g("div",Vx,[c.value.length===0?(P(),q("div",Lx,[g("div",Bx,[f(re,null,{default:y(()=>[f(j(fs))]),_:1})]),z[15]||(z[15]=g("h4",null,"暂无搜索提供商",-1)),z[16]||(z[16]=g("p",null,"添加您常用的搜索引擎，快速进行网络查询",-1)),f(fe,{type:"primary",onClick:D,class:"empty-add-btn"},{default:y(()=>[f(re,null,{default:y(()=>[f(j(on))]),_:1}),z[14]||(z[14]=ne(" 添加第一个提供商 "))]),_:1})])):(P(),q("div",zx,[(P(!0),q(_e,null,Ae(c.value,(x,A)=>(P(),q("div",{key:x.id||A,class:we(["provider-item",{"is-default":x.isDefault}])},[g("div",Fx,[g("div",Wx,[g("div",Hx,[g("div",qx,[ne(ee(x.name)+" ",1),x.isDefault?(P(),Ne(Te,{key:0,size:"small",type:"success",class:"default-tag"},{default:y(()=>z[17]||(z[17]=[ne(" 默认 ")])),_:1})):ge("",!0)]),g("div",Jx,ee(d(x.url)),1)]),g("div",Ux,[g("div",jx,[x.isDefault?ge("",!0):(P(),Ne(ke,{key:0,content:"设为默认",placement:"top"},{default:y(()=>[f(fe,{size:"small",onClick:Z=>V(A),class:"action-btn default-btn"},{default:y(()=>[f(re,null,{default:y(()=>[f(j(qf))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)),f(ke,{content:"上移",placement:"top"},{default:y(()=>[f(fe,{size:"small",disabled:A===0,onClick:Z=>_(A,"up"),class:"action-btn"},{default:y(()=>[f(re,null,{default:y(()=>[f(j(Yi))]),_:1})]),_:2},1032,["disabled","onClick"])]),_:2},1024),f(ke,{content:"下移",placement:"top"},{default:y(()=>[f(fe,{size:"small",disabled:A===c.value.length-1,onClick:Z=>_(A,"down"),class:"action-btn"},{default:y(()=>[f(re,null,{default:y(()=>[f(j($c))]),_:1})]),_:2},1032,["disabled","onClick"])]),_:2},1024),f(ke,{content:"编辑",placement:"top"},{default:y(()=>[f(fe,{size:"small",onClick:Z=>E(A),class:"action-btn edit-btn"},{default:y(()=>[f(re,null,{default:y(()=>[f(j(zt))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024),f(ke,{content:"删除",placement:"top"},{default:y(()=>[f(fe,{size:"small",type:"danger",onClick:Z=>B(A),class:"action-btn delete-btn"},{default:y(()=>[f(re,null,{default:y(()=>[f(j(Vs))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)])])])])],2))),128))]))]),g("div",Kx,[f(fe,{onClick:b,class:"cancel-btn"},{default:y(()=>z[18]||(z[18]=[ne("取消")])),_:1}),f(fe,{type:"primary",onClick:L,class:"save-btn"},{default:y(()=>z[19]||(z[19]=[ne("保存")])),_:1})])]),i.value?(P(),q("div",{key:0,class:"edit-dialog-overlay no-select",onClick:M},[g("div",{class:"edit-dialog-container no-select gpu-accelerated",onClick:z[10]||(z[10]=Oe(()=>{},["stop"]))},[g("div",Xx,[g("div",Yx,[g("div",Gx,[f(re,null,{default:y(()=>[l.value?(P(),Ne(j(zt),{key:0})):(P(),Ne(j(on),{key:1}))]),_:1})]),g("div",Qx,[g("h3",Zx,ee(l.value?"编辑提供商":"添加提供商"),1),z[20]||(z[20]=g("p",{class:"edit-dialog-subtitle"},"配置搜索引擎的名称和URL模板",-1))])]),g("button",{onClick:C,class:"edit-close-btn"},[f(re,null,{default:y(()=>[f(j(ps))]),_:1})])]),g("div",eS,[f(S,{model:u.value,"label-width":"120px",class:"provider-form"},{default:y(()=>[f(be,{label:"提供商名称",required:""},{default:y(()=>[f(Fe,{modelValue:u.value.name,"onUpdate:modelValue":z[1]||(z[1]=x=>u.value.name=x),placeholder:"例如：Google、百度、知乎等",size:"large",class:"allow-select"},null,8,["modelValue"])]),_:1}),f(be,{label:"URL模板",required:""},{default:y(()=>[f(Fe,{modelValue:u.value.url,"onUpdate:modelValue":z[2]||(z[2]=x=>u.value.url=x),placeholder:"输入搜索引擎的URL地址",size:"large",class:"allow-select"},{append:y(()=>[f(ke,{placement:"top","hide-after":0},{content:y(()=>z[21]||(z[21]=[g("div",{class:"url-help-content"},[g("h4",null,"URL格式说明"),g("div",{class:"help-section"},[g("p",null,[g("strong",null,"使用占位符（推荐）：")]),g("ul",null,[g("li",null,[ne("使用 "),g("code",null,"{query}"),ne(" 或 "),g("code",null,"{q}"),ne(" 作为搜索词占位符")]),g("li",null,"搜索时会自动替换为实际的搜索内容")])]),g("div",{class:"help-section"},[g("p",null,[g("strong",null,"不使用占位符：")]),g("ul",null,[g("li",null,[ne("直接输入网站URL，如 "),g("code",null,"https://www.google.com")]),g("li",null,"点击打开时会复制搜索内容到剪贴板，方便手动粘贴")])])],-1)])),default:y(()=>[f(fe,{class:"help-btn"},{default:y(()=>[f(re,null,{default:y(()=>[f(j(sr))]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"]),g("div",tS,[z[28]||(z[28]=g("div",{class:"examples-header"},[g("span",{class:"examples-label"},"常用模板：")],-1)),g("div",nS,[f(Te,{size:"small",class:"example-tag",onClick:z[3]||(z[3]=x=>U("https://www.google.com/search?q={query}"))},{default:y(()=>z[22]||(z[22]=[ne(" Google ")])),_:1}),f(Te,{size:"small",class:"example-tag",onClick:z[4]||(z[4]=x=>U("https://www.baidu.com/s?wd={query}"))},{default:y(()=>z[23]||(z[23]=[ne(" 百度 ")])),_:1}),f(Te,{size:"small",class:"example-tag",onClick:z[5]||(z[5]=x=>U("https://www.bing.com/search?q={query}"))},{default:y(()=>z[24]||(z[24]=[ne(" Bing ")])),_:1}),f(Te,{size:"small",class:"example-tag",onClick:z[6]||(z[6]=x=>U("https://www.zhihu.com/search?q={query}"))},{default:y(()=>z[25]||(z[25]=[ne(" 知乎 ")])),_:1}),f(Te,{size:"small",class:"example-tag",onClick:z[7]||(z[7]=x=>U("https://github.com/search?q={query}"))},{default:y(()=>z[26]||(z[26]=[ne(" GitHub ")])),_:1}),f(Te,{size:"small",class:"example-tag",onClick:z[8]||(z[8]=x=>U("https://stackoverflow.com/search?q={query}"))},{default:y(()=>z[27]||(z[27]=[ne(" Stack Overflow ")])),_:1})])])]),_:1}),f(be,{label:"设为默认"},{default:y(()=>[f(F,{modelValue:u.value.isDefault,"onUpdate:modelValue":z[9]||(z[9]=x=>u.value.isDefault=x),"active-text":"是","inactive-text":"否"},null,8,["modelValue"]),z[29]||(z[29]=g("span",{class:"switch-tip"},"默认提供商将在按回车键时使用",-1))]),_:1}),f(be,{label:"URL预览"},{default:y(()=>[g("div",sS,[g("div",oS,ee(v.value),1)])]),_:1})]),_:1},8,["model"])]),g("div",rS,[f(fe,{onClick:C,size:"large",class:"cancel-btn"},{default:y(()=>z[30]||(z[30]=[ne(" 取消 ")])),_:1}),f(fe,{type:"primary",onClick:N,disabled:!p.value,size:"large",class:"save-btn"},{default:y(()=>[ne(ee(l.value?"保存更改":"添加提供商"),1)]),_:1},8,["disabled"])])])])):ge("",!0)])):ge("",!0)}}},lS=ut(iS,[["__scopeId","data-v-f457bd92"]]),aS={class:"panel-header"},cS={class:"panel-title"},uS={class:"panel-controls"},dS={class:"panel-content"},fS={class:"punctuation-category"},pS={class:"punctuation-grid"},hS=["title","onClick"],mS={class:"punctuation-category"},gS={class:"punctuation-grid"},vS=["title","onClick"],yS={__name:"PunctuationPanel",props:{visible:{type:Boolean,default:!1},editor:{type:Object,default:null}},emits:["close","insert"],setup(n,{emit:e}){const t=n,s=e,o=I({x:100,y:100}),r=I(!1),i=I({x:0,y:0}),l=I([{symbol:"，",name:"逗号",type:"single"},{symbol:"。",name:"句号",type:"single"},{symbol:"？",name:"问号",type:"single"},{symbol:"！",name:"感叹号",type:"single"},{symbol:"；",name:"分号",type:"single"},{symbol:"：",name:"冒号",type:"single"},{symbol:"、",name:"顿号",type:"single"},{symbol:"…",name:"省略号",type:"single"},{symbol:"“”",name:"中文双引号",type:"pair",left:"“",right:"”"},{symbol:"'’",name:"中文单引号",type:"pair",left:"‘",right:"’"},{symbol:"（）",name:"圆括号",type:"pair",left:"（",right:"）"},{symbol:"【】",name:"方括号",type:"pair",left:"【",right:"】"},{symbol:"《》",name:"书名号",type:"pair",left:"《",right:"》"},{symbol:"〈〉",name:"单书名号",type:"pair",left:"〈",right:"〉"},{symbol:"——",name:"破折号",type:"single"},{symbol:"·",name:"间隔号",type:"single"}]),a=I([{symbol:"删除",name:"删除键 (Backspace)",type:"function",action:"backspace"},{symbol:"回车",name:"回车键 (Enter)",type:"function",action:"enter"}]),c=me(()=>({left:`${o.value.x}px`,top:`${o.value.y}px`})),u=C=>{if(!(!t.editor||!t.editor.commands)){if(C.type==="pair"){const M=C.left+C.right;t.editor.commands.insertContent(M);const{state:X,dispatch:L}=t.editor.view,{tr:D}=X,E=X.selection.head-1,B=X.selection.constructor.near(X.doc.resolve(E));L(D.setSelection(B)),s("insert",M)}else t.editor.commands.insertContent(C.symbol),s("insert",C.symbol);t.editor.commands.focus()}},d=C=>{if(!(!t.editor||!t.editor.commands)){switch(C.action){case"backspace":const{from:M,empty:X}=t.editor.state.selection;X?t.editor.commands.joinBackward()||M>0&&t.editor.commands.deleteRange({from:M-1,to:M}):t.editor.commands.deleteSelection();break;case"enter":t.editor.commands.splitBlock();break}t.editor.commands.focus(),s("insert",C.symbol)}},v=C=>{C.preventDefault(),r.value=!0;const M=C.currentTarget.closest(".punctuation-panel").getBoundingClientRect();i.value={x:C.clientX-M.left,y:C.clientY-M.top},document.addEventListener("mousemove",p),document.addEventListener("mouseup",b)},p=C=>{r.value&&(o.value={x:C.clientX-i.value.x,y:C.clientY-i.value.y})},b=()=>{r.value=!1,document.removeEventListener("mousemove",p),document.removeEventListener("mouseup",b)},w=C=>{C.preventDefault()};return lt(()=>{document.removeEventListener("mousemove",p),document.removeEventListener("mouseup",b)}),ct(()=>{o.value={x:window.innerWidth-320,y:150}}),(C,M)=>{const X=_t,L=vs;return n.visible?(P(),q("div",{key:0,class:"punctuation-panel",style:Et(c.value),onMousedown:w},[g("div",aS,[g("div",cS,[f(X,null,{default:y(()=>[f(j(zt))]),_:1}),M[1]||(M[1]=g("span",null,"标点符号",-1))]),g("div",uS,[f(L,{content:"拖拽移动",placement:"top"},{default:y(()=>[f(X,{class:"drag-handle",onMousedown:v},{default:y(()=>[f(j(nl))]),_:1})]),_:1}),f(L,{content:"关闭面板",placement:"top"},{default:y(()=>[f(X,{class:"close-btn",onClick:M[0]||(M[0]=D=>C.$emit("close"))},{default:y(()=>[f(j(ps))]),_:1})]),_:1})])]),g("div",dS,[g("div",fS,[M[2]||(M[2]=g("div",{class:"category-title"},"中文常用",-1)),g("div",pS,[(P(!0),q(_e,null,Ae(l.value,D=>(P(),q("button",{key:D.symbol,class:we(["punctuation-btn",{"pair-punctuation":D.type==="pair"}]),title:D.name+(D.type==="pair"?" (成对插入)":""),onClick:E=>u(D)},ee(D.symbol),11,hS))),128))])]),g("div",mS,[M[3]||(M[3]=g("div",{class:"category-title"},"功能按键",-1)),g("div",gS,[(P(!0),q(_e,null,Ae(a.value,D=>(P(),q("button",{key:D.symbol,class:we(["punctuation-btn","function-key"]),title:D.name,onClick:E=>d(D)},ee(D.symbol),9,vS))),128))])])])],36)):ge("",!0)}}},bS=ut(yS,[["__scopeId","data-v-c3c14026"]]),wS=["onClick"],kS={key:0,class:"virtual-scroll-loading"},xS={key:1,class:"virtual-scroll-empty"},SS={__name:"VirtualScrollList",props:{items:{type:Array,default:()=>[]},itemHeight:{type:Number,default:40},containerHeight:{type:Number,default:400},bufferSize:{type:Number,default:5},keyField:{type:String,default:"id"},textField:{type:String,default:"title"},classField:{type:[String,Function],default:""},loading:{type:Boolean,default:!1},infiniteScroll:{type:Boolean,default:!1},loadMoreThreshold:{type:Number,default:100}},emits:["item-click","load-more","scroll"],setup(n,{expose:e,emit:t}){const s=n,o=t,r=I(null),i=I(0),l=me(()=>s.items.length*s.itemHeight),a=me(()=>Math.ceil(s.containerHeight/s.itemHeight)),c=me(()=>{const E=Math.floor(i.value/s.itemHeight)-s.bufferSize;return Math.max(0,E)}),u=me(()=>{const E=c.value+a.value+s.bufferSize*2;return Math.min(s.items.length,E)}),d=me(()=>s.items.slice(c.value,u.value).map((E,B)=>({...E,index:c.value+B}))),v=me(()=>c.value*s.itemHeight),p=E=>E[s.keyField]||E.index,b=E=>E[s.textField]||"",w=E=>typeof s.classField=="function"?s.classField(E):s.classField?E[s.classField]:"",C=E=>{if(i.value=E.target.scrollTop,o("scroll",{scrollTop:i.value,scrollHeight:E.target.scrollHeight,clientHeight:E.target.clientHeight}),s.infiniteScroll&&!s.loading){const{scrollTop:B,scrollHeight:N,clientHeight:V}=E.target;N-B-V<=s.loadMoreThreshold&&o("load-more")}},M=(E,B)=>{o("item-click",E,B)},X=E=>{if(r.value){const B=E*s.itemHeight;r.value.scrollTop=B}},L=E=>{r.value&&(r.value.scrollTop=E)};return e({scrollToItem:X,scrollTo:L,getScrollTop:()=>r.value?r.value.scrollTop:0}),Ie(()=>s.items.length,(E,B)=>{B>0&&E===0&&Le(()=>{L(0)})}),(E,B)=>{const N=_t,V=yo;return P(),q("div",{ref_key:"containerRef",ref:r,class:"virtual-scroll-container",style:Et({height:n.containerHeight+"px"}),onScroll:C},[g("div",{class:"virtual-scroll-content",style:Et({height:l.value+"px"})},[g("div",{class:"virtual-scroll-items",style:Et({transform:`translateY(${v.value}px)`})},[(P(!0),q(_e,null,Ae(d.value,_=>(P(),q("div",{key:p(_),class:we(["virtual-scroll-item",w(_)]),style:Et({height:n.itemHeight+"px"}),onClick:T=>M(_,T)},[nr(E.$slots,"default",{item:_,index:_.index},()=>[ne(ee(b(_)),1)],!0)],14,wS))),128))],4)],4),n.loading?(P(),q("div",kS,[f(N,{class:"is-loading"},{default:y(()=>[f(j(Ti))]),_:1}),B[0]||(B[0]=g("span",null,"加载中...",-1))])):ge("",!0),!n.loading&&n.items.length===0?(P(),q("div",xS,[nr(E.$slots,"empty",{},()=>[f(V,{description:"暂无数据"})],!0)])):ge("",!0)],36)}}},kc=ut(SS,[["__scopeId","data-v-fc7562bd"]]),CS={class:"virtual-chapter-list"},_S={key:0,class:"search-results"},MS={class:"search-result-item"},TS={class:"volume-info"},ES={class:"volume-title"},IS={class:"chapter-info"},OS={class:"chapter-title"},NS={class:"chapter-meta"},DS={key:1,class:"volumes-list"},AS={key:0,class:"tree-node volume-node"},$S={class:"node-content"},PS={class:"expand-icon"},RS={class:"node-icon"},VS={class:"node-label"},LS={class:"node-badge"},BS={class:"action-button"},zS={class:"node-content"},FS={class:"node-icon"},WS={class:"node-label"},HS={key:0,class:"node-badge"},qS={class:"action-button"},JS={key:2,class:"loading-item"},US={class:"empty-state"},jS=36,KS={__name:"VirtualChapterList",props:{volumes:{type:Array,default:()=>[]},expandedVolumeIds:{type:Set,default:()=>new Set},activeChapterId:{type:String,default:""},searchResults:{type:Array,default:()=>[]},showSearchResults:{type:Boolean,default:!1},searchLoading:{type:Boolean,default:!1},containerHeight:{type:Number,default:600},loading:{type:Boolean,default:!1}},emits:["chapter-click","volume-command","chapter-command","toggle-volume","create-volume","load-more"],setup(n,{emit:e}){const t=n,s=e,o=me(()=>{const b=[];return t.volumes.forEach(w=>{b.push({key:`volume-${w.id}`,type:"volume",data:w}),t.expandedVolumeIds.has(w.id)&&w.chapters&&w.chapters.length>0&&w.chapters.forEach(C=>{b.push({key:`chapter-${C.id}`,type:"chapter",data:C,volumeId:w.id})})}),b}),r=b=>{const w=[b.type];return b.type==="chapter"&&b.data.id===t.activeChapterId&&w.push("active"),w.join(" ")},i=b=>b?b>=1e4?`${(b/1e4).toFixed(1)}万字`:`${b}字`:"0字",l=b=>t.expandedVolumeIds.has(b),a=(b,w)=>{w?.target?.closest(".node-actions")||(b.type==="volume"?c(b.data.id):b.type==="chapter"&&s("chapter-click",b.volumeId,b.data.id))},c=b=>{s("toggle-volume",b)},u=(b,w)=>{s("volume-command",b,w)},d=(b,w,C)=>{s("chapter-command",b,w,C)},v=b=>{s("chapter-click",b.volume.id,b.chapter.id)},p=()=>{s("load-more")};return(b,w)=>{const C=_t,M=yo,X=el,L=Zi,D=Qi,E=Jf,B=kn;return P(),q("div",CS,[n.showSearchResults?(P(),q("div",_S,[f(kc,{items:n.searchResults,"item-height":60,"container-height":n.containerHeight,"key-field":"id",loading:n.searchLoading,onItemClick:v},{default:y(({item:N})=>[g("div",MS,[g("div",TS,[f(C,null,{default:y(()=>[f(j(ri))]),_:1}),g("span",ES,ee(N.volume.title),1)]),g("div",IS,[f(C,null,{default:y(()=>[f(j(Rs))]),_:1}),g("span",OS,ee(N.chapter.title),1),g("span",NS,ee(i(N.chapter.word_count)),1)])])]),empty:y(()=>[f(M,{description:"未找到匹配的章节"})]),_:1},8,["items","container-height","loading"])])):(P(),q("div",DS,[f(kc,{items:o.value,"item-height":jS,"container-height":n.containerHeight,"key-field":"key","class-field":r,loading:n.loading,"infinite-scroll":!0,onItemClick:a,onLoadMore:p},{default:y(({item:N})=>[N.type==="volume"?(P(),q("div",AS,[g("div",$S,[g("div",PS,[f(C,{class:we({expanded:l(N.data.id)})},{default:y(()=>[f(j(Pc))]),_:2},1032,["class"])]),g("div",RS,[f(C,null,{default:y(()=>[f(j(ri))]),_:1})]),g("div",VS,ee(N.data.title),1),g("div",LS,ee(N.data.chapter_count||0),1),g("div",{class:"node-actions",onClick:w[0]||(w[0]=Oe(()=>{},["stop"]))},[f(D,{trigger:"click",onCommand:V=>u(V,N.data),placement:"bottom-end"},{dropdown:y(()=>[f(L,null,{default:y(()=>[f(X,{command:"rename"},{default:y(()=>[f(C,null,{default:y(()=>[f(j(zt))]),_:1}),w[3]||(w[3]=ne("重命名 "))]),_:1}),f(X,{command:"add-chapter"},{default:y(()=>[f(C,null,{default:y(()=>[f(j(on))]),_:1}),w[4]||(w[4]=ne("新建章节 "))]),_:1}),f(X,{command:"delete",class:"danger"},{default:y(()=>[f(C,null,{default:y(()=>[f(j(Vs))]),_:1}),w[5]||(w[5]=ne("删除 "))]),_:1})]),_:1})]),default:y(()=>[g("div",BS,[f(C,null,{default:y(()=>[f(j(Ei))]),_:1})])]),_:2},1032,["onCommand"])])])])):N.type==="chapter"?(P(),q("div",{key:1,class:we(["tree-node chapter-node",{active:N.data.id===n.activeChapterId}])},[g("div",zS,[w[10]||(w[10]=g("div",{class:"indent-spacer"},null,-1)),g("div",FS,[f(C,null,{default:y(()=>[f(j(Rs))]),_:1})]),g("div",WS,ee(N.data.title||"未命名章节"),1),N.data.word_count?(P(),q("div",HS,ee(i(N.data.word_count)),1)):ge("",!0),g("div",{class:"node-actions",onClick:w[1]||(w[1]=Oe(()=>{},["stop"]))},[f(D,{trigger:"click",onCommand:V=>d(V,N.volumeId,N.data),placement:"bottom-end"},{dropdown:y(()=>[f(L,null,{default:y(()=>[f(X,{command:"rename"},{default:y(()=>[f(C,null,{default:y(()=>[f(j(zt))]),_:1}),w[6]||(w[6]=ne("重命名 "))]),_:1}),f(X,{command:"duplicate"},{default:y(()=>[f(C,null,{default:y(()=>[f(j(Uf))]),_:1}),w[7]||(w[7]=ne("复制 "))]),_:1}),f(X,{command:"move"},{default:y(()=>[f(C,null,{default:y(()=>[f(j(nl))]),_:1}),w[8]||(w[8]=ne("移动 "))]),_:1}),f(X,{command:"delete",class:"danger"},{default:y(()=>[f(C,null,{default:y(()=>[f(j(Vs))]),_:1}),w[9]||(w[9]=ne("删除 "))]),_:1})]),_:1})]),default:y(()=>[g("div",qS,[f(C,null,{default:y(()=>[f(j(Ei))]),_:1})])]),_:2},1032,["onCommand"])])])],2)):N.type==="loading"?(P(),q("div",JS,[f(E,{rows:1,animated:""})])):ge("",!0)]),empty:y(()=>[g("div",US,[f(C,null,{default:y(()=>[f(j(ri))]),_:1}),w[12]||(w[12]=g("span",null,"暂无卷册",-1)),f(B,{type:"primary",link:"",size:"small",onClick:w[2]||(w[2]=N=>b.$emit("create-volume"))},{default:y(()=>w[11]||(w[11]=[ne(" 新建卷 ")])),_:1})])]),_:1},8,["items","container-height","loading"])]))])}}},XS=ut(KS,[["__scopeId","data-v-b689a3b9"]]),YS={class:"editor-wrapper"},GS={class:"editor-toolbar"},QS={class:"left-tools"},ZS={class:"tts-controls"},eC={class:"right-tools"},tC={class:"toolbar-dropdown"},nC={class:"editor-container"},sC={class:"sidebar-header"},oC={class:"search-container"},rC={class:"actions"},iC={class:"dialog-footer"},lC={class:"dialog-footer"},aC={class:"chapter-header"},cC={class:"editor-status-bar"},uC={class:"status-left"},dC={class:"status-item"},fC={class:"status-value"},pC={class:"status-item"},hC={class:"status-value"},mC={class:"status-item"},gC={class:"status-value"},vC={class:"status-right"},yC={class:"status-item"},bC={class:"status-value"},wC={class:"stats-header"},kC={class:"stats-content"},xC={class:"stat-row"},SC={class:"stat-item"},CC={class:"stat-value"},_C={class:"stat-row"},MC={class:"stat-item"},TC={class:"stat-value"},EC={class:"stat-row"},IC={class:"stat-item"},OC={class:"stat-value"},NC={class:"stat-row"},DC={class:"stat-item"},AC={class:"stat-value"},$C={class:"settings-content"},PC={class:"settings-section"},RC={class:"setting-group"},VC={class:"setting-item"},LC={class:"setting-header"},BC={class:"setting-value"},zC={class:"setting-item"},FC={class:"setting-header"},WC={class:"setting-value"},HC={class:"color-settings"},qC={key:0,class:"recent-colors"},JC={class:"recent-colors-list"},UC=["title","onClick"],jC={class:"setting-item"},KC={class:"setting-header"},XC={class:"setting-value"},YC={class:"setting-item"},GC={class:"setting-header"},QC={class:"setting-value"},ZC={class:"setting-item"},e_={class:"setting-header"},t_={class:"setting-value"},n_={class:"setting-item"},s_={class:"setting-header"},o_={class:"setting-value"},r_={class:"drawer-footer"},i_={class:"bg-history-wrapper"},l_={key:0,class:"loading-state"},a_={key:1,class:"empty-state"},c_={key:2,class:"bg-list-container"},u_={class:"bg-list"},d_=["onClick"],f_=["src"],p_={key:0,class:"loading-placeholder"},h_={class:"bg-info"},m_={class:"bg-date"},g_={class:"bg-actions"},v_={class:"bg-preview-wrapper"},y_={key:0,class:"bg-preview"},b_=["src"],w_={key:1,class:"no-bg-selected"},k_={class:"dialog-footer"},x_={class:"tts-settings-panel"},S_={class:"voice-select-container"},C_={key:0,class:"empty-voices-tip"},__={class:"global-popup-container"},Ko=3e3,M_={__name:"editor",setup(n){const e=k=>{if(k.includes(":")&&!k.startsWith("gpt-")&&!k.startsWith("claude-")){const Y=k.split(":");if(Y.length>=2&&!isNaN(Y[0]))return k}const O=Sr().allAvailableModels,R=O.find(Y=>Y.uniqueId&&Y.uniqueId.endsWith(":"+k));return R?(console.log("转换模型ID:",k,"->",R.uniqueId),R.uniqueId):O.length>0?(console.log("使用默认模型:",O[0].uniqueId),O[0].uniqueId):"gpt-3.5-turbo"},t=Qe.create({name:"customPasteHandler",addProseMirrorPlugins(){return[new nt({key:new Nt("customPasteHandler"),props:{handlePaste(k,m){const O=m.clipboardData;if(O&&O.types.includes("text/plain")){const R=O.getData("text/plain");if(R&&R.trim()){if(!/\n\s*\n/.test(R))return!1;try{m.preventDefault();let ie;R.includes(`
`)?ie=R.split(/\n\s*\n/).map(Ee=>Ee.trim()).filter(Ee=>Ee).map(Ee=>{let ze=Ee.replace(/\r\n/g,`
`);const Ue=/^[\s\u3000]{2,}/.test(ze),ht=ze.split(`
`);if(ht.length>2&&ht.every(Zt=>Zt.trim().length<50))ze=ht.map(Zt=>Zt.trim()).join("<br>");else if(Ue){const Zt=ht[0],Tn=ht.slice(1).join(" ").trim();ze=Tn?`${Zt} ${Tn}`:Zt}else ze=ze.replace(/\n/g," ").trim();return ze}):ie=[R.trim()];const ae=ie.map((Ee,ze)=>{const Ue=ze>0?"<p><br></p>":"";if(Ee.includes("<br>"))return`${Ue}<p style="line-height: 2; margin-bottom: 1.5em; text-align: left;">${Ee}</p>`;{const Zn=/^[\s\u3000]{2,}/.test(Ee)?"line-height: 1.8; margin-bottom: 1em;":"text-indent: 2em; line-height: 1.8; margin-bottom: 1em;";return`${Ue}<p style="${Zn}">${Ee}</p>`}}).join(""),Me=window.editorInstance||L.value;if(Me&&Me.commands)return k.state.selection.empty||Me.commands.deleteSelection(),Me.commands.insertContent(ae,{parseOptions:{preserveWhitespace:!1}}),!0;{if(!k.state.selection.empty){const Ue=k.state.tr.deleteSelection();k.dispatch(Ue)}let Ee=k.state.tr,ze=Ee.selection.from;return ie.forEach((Ue,ht)=>{Ee=Ee.insertText(Ue,ze),ze+=Ue.length,ht<ie.length-1&&(Ee=Ee.insertText(`

`,ze),ze+=2)}),k.dispatch(Ee),!0}}catch(ie){return console.error("粘贴处理出错:",ie),!1}}}return!1}}})]}}),s=I(!0),o=wn(),r=me(()=>o.editor),i=k=>{if(!k)return;r.value.recentFontColors||(r.value.recentFontColors=[]);const m=r.value.recentFontColors.indexOf(k);m>-1&&r.value.recentFontColors.splice(m,1),r.value.recentFontColors.unshift(k),r.value.recentFontColors.length>10&&(r.value.recentFontColors=r.value.recentFontColors.slice(0,10)),ks({...r.value})},l=I(""),a=I(""),u=Bo(async()=>{const k=r.value?.bgImage||"";if(!(r.value?.bgEnabled||!1)){l.value="",a.value="";return}if(!k){l.value="",a.value="";return}if(k===a.value&&l.value){console.log("背景图片已缓存，跳过加载:",k);return}console.log("加载背景图片:",k);try{const O=await window.pywebview.api.get_background_image_data(k),R=typeof O=="string"?JSON.parse(O):O;R.status==="success"?(l.value=R.data,a.value=k):(console.error("加载背景图片失败:",R.message),l.value="",a.value="")}catch(O){console.error("加载背景图片失败:",O),l.value="",a.value=""}},500);Ie(()=>[r.value?.bgEnabled,r.value?.bgImage],(k,m)=>{JSON.stringify(k)!==JSON.stringify(m)&&u()},{immediate:!0,deep:!1});const d=jf(),v=Gf(),p=d.params.id,b=I({}),w=I([]),C=I(null),M=I({});I("");const X=I(0),L=I(null),D=I(!1),E=I(!1),B=I(!1),N=I(!1),V=I(null),_=I(!1),T=I({title:""}),U=I(!1),W=I({volumeId:"",title:""}),z=I(!1),re=I(!1),fe=I(!1),Te=I(!1),ke=I({voice:"zh-CN-XiaoxiaoNeural",rate:0,volume:0,pitch:0}),Fe=I([]),be=I(!1),F=I(!1),S=I(!1),x=I(null),A=I(null),Z=I([]),G=I(!1);I(!1);const se=I([]);I({template_id:"",name:"",description:"",dimensions:{}}),I(null);const pe=async()=>{try{const k=await window.pywebview.api.book_controller.get_templates(p),m=typeof k=="string"?JSON.parse(k):k;m.status==="success"?se.value=m.data||[]:$.error(m.message||"加载模板失败")}catch(k){console.error("加载模板失败:",k),$.error("加载模板失败："+k.message)}};async function $e(){try{S.value=!0;let k;try{const O=new Promise((Y,ie)=>{setTimeout(()=>ie(new Error("请求超时，请检查网络连接")),1e4)}),R=window.pywebview.api.get_voices();k=await Promise.race([R,O])}catch(O){console.error("获取语音列表失败:",O);let R="网络连接问题";throw typeof O.message=="string"&&(O.message.includes("timeout")?R="请求超时，请检查网络连接":O.message.includes("network")&&(R="网络连接问题，请检查网络后重试")),new Error(R)}const m=typeof k=="string"?JSON.parse(k):k;if(m.status==="success"){Fe.value=m.data||[],s.value=Fe.value.length>0,Fe.value.length===0&&(console.warn("警告：语音列表为空"),s.value=!1);try{const O=await window.pywebview.api.get_tts_config(),R=typeof O=="string"?JSON.parse(O):O;R&&R.status==="success"&&(ke.value=R.data||ke.value,console.log("已加载TTS配置",ke.value))}catch(O){console.warn("获取TTS默认配置失败，使用当前配置",O)}}else console.error("语音列表加载失败:",m.message),s.value=!1,$.error("加载TTS配置失败: "+(m.message||"请检查网络连接"))}catch(k){console.error("加载TTS配置失败:",k),$.error("加载TTS配置失败: "+k.message),Fe.value=[],s.value=!1}finally{S.value=!1}}async function he(k,m){try{F.value=!0,G.value&&(await Ce(),await new Promise(R=>setTimeout(R,200)));let O=m;k!=="voice"&&(O=parseInt(m)||0),await o.updateTTSConfig({[k]:O}),$.success("配置已更新")}catch(O){console.error("更新TTS配置失败:",O),x.value=O.message,$.error("更新TTS配置失败: "+O.message)}finally{F.value=!1}}Ie(be,async k=>{k&&await $e()}),ct(async()=>{A.value=new(window.AudioContext||window.webkitAudioContext),window.ttsAudioChunkCallback=k=>{try{const m=Md(k);Z.value.push(m),G.value||De()}catch(m){console.error("处理音频数据失败:",m),x.value=m.message,F.value=!1}},window.ttsAudioCompleteCallback=()=>{console.log("TTS播放完成"),F.value=!1,G.value=!1},window.ttsAudioErrorCallback=k=>{console.error("TTS错误:",k),x.value=k,F.value=!1,G.value=!1,$.error(k)},await $e(),await pe(),Io(),_n=setInterval(Io,1e3)}),lt(()=>{A.value&&A.value.close(),delete window.ttsAudioChunkCallback,delete window.ttsAudioCompleteCallback,delete window.ttsAudioErrorCallback,_n&&(clearInterval(_n),_n=null)});async function De(){if(Z.value.length===0||!A.value){G.value=!1,F.value=!1;return}G.value=!0;const k=Z.value.shift();try{const m=await A.value.decodeAudioData(k),O=A.value.createBufferSource();O.buffer=m,O.connect(A.value.destination),O.onended=()=>{Z.value.length>0?De():(G.value=!1,F.value=!1)},O.start(0)}catch(m){console.error("音频解码失败:",m),G.value=!1,F.value=!1,x.value=m.message}}function Ce(){if(console.log("停止TTS播放"),G.value=!1,F.value=!1,Z.value=[],A.value)try{A.value.suspend().catch(k=>{console.error("暂停AudioContext失败:",k)});try{A.value.close().then(()=>{try{A.value=new(window.AudioContext||window.webkitAudioContext)}catch(k){console.error("创建新AudioContext失败:",k)}}).catch(k=>{console.error("关闭AudioContext失败:",k);try{A.value=new(window.AudioContext||window.webkitAudioContext)}catch(m){console.error("创建新的AudioContext失败:",m)}})}catch(k){console.error("重置AudioContext失败:",k);try{A.value=new(window.AudioContext||window.webkitAudioContext)}catch(m){console.error("创建新的AudioContext失败:",m)}}}catch(k){console.error("停止音频失败:",k)}try{const k=new Promise(O=>{setTimeout(()=>{O({status:"timeout",message:"TTS停止操作超时，可能已经停止"})},2e3)}),m=window.pywebview.api.stop_tts_stream();Promise.race([m,k]).then(O=>{O.status==="timeout"?console.warn(O.message):console.log("后端TTS流已停止")}).catch(O=>{console.error("停止后端TTS流失败:",O)})}catch(k){console.error("调用后端停止TTS流方法失败:",k)}}const Pe=I(null),wt=I([]),Ft=(k,m)=>{Pe.value=m,k&&!wt.value.includes(k)&&(wt.value=[k])},jn=async()=>{try{await ft();const k=await window.pywebview.api.book_controller.get_book(p),m=typeof k=="string"?JSON.parse(k):k;if(m.status==="success"){b.value=m.data,X.value=m.data.word_count,await st();const O=r.value?.lastState;O&&O.bookId===p&&O.volumeId&&O.chapterId&&(Ft(O.volumeId,O.chapterId),await Jr(O.volumeId,O.chapterId))}}catch(k){console.error("加载书籍信息失败:",k),$.error("加载书籍信息失败")}},cn=I(!1),Wt=I(1),bs=I(50),Us=I(0),Kn=I(!0),st=async(k=1,m=!1)=>{try{cn.value=!0;const O=await window.pywebview.api.book_controller.get_volumes(p,k,bs.value,!0),R=typeof O=="string"?JSON.parse(O):O;if(R.status==="success"){const Y=R.data;m&&k>1?w.value=[...w.value,...Y.volumes]:w.value=Y.volumes||Y,Y.total!==void 0?(Us.value=Y.total,Wt.value=Y.page,Kn.value=Y.page<Y.total_pages):Kn.value=!1,!C.value&&w.value.length>0&&(C.value=w.value[0])}}catch(O){console.error("加载卷列表失败:",O),$.error("加载卷列表失败")}finally{cn.value=!1}},te=async()=>{!Kn.value||cn.value||await st(Wt.value+1,!0)},ft=async()=>{o.loaded||await o.loadConfig()};Ie(d,async()=>{M.value?.id&&await Wr()}),Mc(async()=>{Cs.value=!1,Mn.value=null,ot&&typeof ot.cancel=="function"&&ot.cancel();try{if(M.value?.id&&L.value&&L.value.getHTML()!==Ro.value){console.log("组件卸载前保存最终内容");const k=Zs();if(k.isBookEncrypted(p)&&!k.getBookPassword(p))console.log("加密书籍无可用密码，跳过卸载前保存");else{const m=L.value.getHTML(),O=m.replace(/<[^>]+>/g,"").length;await window.pywebview.api.book_controller.update_chapter(p,C.value.id,M.value.id,{content:m,word_count:O,title:M.value.title,save_timestamp:Date.now()})}}}catch(k){console.error("卸载前保存失败:",k)}M.value?.id&&await Wr(),await Zs().loadBooks()});const Br=()=>{Le(()=>{volumeTitleInput.value?.input?.focus()})},zr=()=>{Le(()=>{chapterTitleInput.value?.input?.focus()})},_o=()=>{T.value={title:""},_.value=!0},xn=async()=>{if(!T.value.title){$.warning("请输入卷标题");return}try{const k=await window.pywebview.api.book_controller.create_volume(p,T.value),m=typeof k=="string"?JSON.parse(k):k;m.status==="success"?($.success("创建成功"),_.value=!1,await st(),C.value=m.data,Ft(m.data.id,null)):$.error(m.message||"创建卷失败")}catch(k){console.error("创建卷失败:",k),$.error("创建卷失败")}},Xn=k=>{const m=w.value.find(ae=>ae.chapters?.find(Me=>Me.id===M.value.id));if(!m||!m.chapters)return"第一章";const O=m.chapters;if(O.length===0)return"第一章";const R=O.map(ae=>{const Me=ae.title.match(/第([一二三四五六七八九十百千万\d]+)章/);if(Me){const Ee=Me[1];return/^\d+$/.test(Ee)?parseInt(Ee):(Ue=>{const ht={一:1,二:2,三:3,四:4,五:5,六:6,七:7,八:8,九:9,十:10,百:100,千:1e3,万:1e4};if(Ue==="十")return 10;if(Ue.startsWith("十"))return 10+(ht[Ue.charAt(1)]||0);if(Ue.includes("十")){const Zn=Ue.split("十"),Zt=ht[Zn[0]]||0,Tn=ht[Zn[1]]||0;return Zt*10+Tn}return ht[Ue]||0})(Ee)}return 0}).filter(ae=>ae>0);if(R.length===0)return"第一章";const Y=Math.max(...R);return`第${(ae=>{const Me=["零","一","二","三","四","五","六","七","八","九","十"];if(ae<=10)return Me[ae];if(ae<20)return"十"+(ae%10===0?"":Me[ae%10]);if(ae<100){const Ee=Math.floor(ae/10),ze=ae%10;return Me[Ee]+"十"+(ze===0?"":Me[ze])}return ae.toString()})(Y+1)}章`},H=()=>{if(!C.value){$.warning("请先选择一个卷");return}W.value={volumeId:C.value.id,title:Xn(C.value.id)},U.value=!0},J=async()=>{if(!W.value.title.trim()){$.warning("章节标题不能为空");return}try{const k=await window.pywebview.api.book_controller.create_chapter(p,W.value.volumeId,{title:W.value.title.trim(),content:""}),m=typeof k=="string"?JSON.parse(k):k;m.status==="success"?($.success("创建成功"),U.value=!1,await st(),await v.push({query:{...d.query,volumeId:W.value.volumeId,chapterId:m.data.id}})):$.error(m.message||"创建失败")}catch(k){console.error("创建失败:",k),$.error("创建失败")}},oe=async(k,m)=>{if(k==="rename"){const{value:O}=await qt.prompt("请输入新的卷标题","重命名卷",{inputValue:m.title,confirmButtonText:"确定",cancelButtonText:"取消"});if(O)try{const R=await window.pywebview.api.book_controller.update_volume(p,m.id,{...m,title:O}),Y=typeof R=="string"?JSON.parse(R):R;Y.status==="success"?($.success("重命名成功"),st()):$.error(Y.message||"重命名失败")}catch(R){console.error("重命名失败:",R),$.error("重命名失败")}}else if(k==="delete"){if(m.chapters&&m.chapters.length>0){$.warning("请先删除卷内的所有章节");return}try{await qt.confirm("确定要删除这个卷吗？","删除卷",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const O=await window.pywebview.api.book_controller.delete_volume(p,m.id),R=typeof O=="string"?JSON.parse(O):O;R.status==="success"?($.success("删除成功"),st()):$.error(R.message||"删除失败")}catch(O){O!=="cancel"&&(console.error("删除失败:",O),$.error("删除失败"))}}},ce=async(k,m,O)=>{if(k==="rename"){const{value:R}=await qt.prompt("请输入新的章节标题","重命名章节",{inputValue:O.title,confirmButtonText:"确定",cancelButtonText:"取消",inputValidator:Y=>Y.trim()?!0:"章节标题不能为空"});if(R)try{const Y=R.trim(),ie=await window.pywebview.api.book_controller.update_chapter(p,m,O.id,{...O,title:Y}),ae=typeof ie=="string"?JSON.parse(ie):ie;ae.status==="success"?(O.title=Y,M.value?.id===O.id&&(M.value.title=Y,await ve()),$.success("重命名成功"),st()):$.error(ae.message||"重命名失败")}catch(Y){console.error("重命名失败:",Y),$.error("重命名失败")}}else if(k==="delete")try{await qt.confirm("确定要删除这个章节吗？","删除章节",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const R=await window.pywebview.api.book_controller.delete_chapter(p,m,O.id),Y=typeof R=="string"?JSON.parse(R):R;if(Y.status==="success"){if(M.value?.id===O.id){L.value?.commands.setContent(""),M.value=null;const ie=w.value.find(ae=>ae.id===m);if(ie&&ie.chapters&&ie.chapters.length>0){const ae=ie.chapters.findIndex(Ee=>Ee.id===O.id);let Me;ae>0?Me=ie.chapters[ae-1]:ie.chapters.length>1&&(Me=ie.chapters[1]),Me?(await v.replace({query:{...d.query,volumeId:m,chapterId:Me.id}}),await Jr(m,Me.id)):await v.replace({query:{...d.query,chapterId:void 0}})}}await st(),$.success("删除成功")}else $.error(Y.message||"删除失败")}catch(R){R!=="cancel"&&(console.error("删除失败:",R),$.error("删除失败"))}},ve=async(k=!1)=>{if(!M.value?.id||!L.value)return;const m=L.value.getHTML();if(m===Ro.value&&!k)return;if(!Cs.value){const R=Zs();if(R.isBookEncrypted(p)&&!R.getBookPassword(p)){console.log("禁止密码验证，且加密书籍无可用密码，跳过保存");return}}const O=++Ss.value;if(Po.value){console.log(`保存请求已排队，版本: ${O}`);return}try{Po.value=!0;const R=m.replace(/<[^>]+>/g,"").length;console.log(`开始保存章节, 版本: ${O}`);const Y=Zs();let ie=m;if(Y.isBookEncrypted(p))try{if(Mn.value!==p){if(!Cs.value)throw new Error("当前状态不允许密码验证");if(!await Y.forcePromptBookPassword(p))throw new Error("需要密码才能保存加密书籍");Mn.value=p}ie=await Y.encryptChapterContent(p,C.value.id,M.value.id,m)}catch(Ee){throw Mn.value=null,Ee}const ae=await window.pywebview.api.book_controller.update_chapter(p,C.value.id,M.value.id,{content:ie,word_count:R,title:M.value.title,save_timestamp:Date.now()}),Me=typeof ae=="string"?JSON.parse(ae):ae;if(O<Ss.value){console.log(`忽略旧版本(${O})的保存响应`);return}Me.status==="success"?(k&&$.success("保存成功"),Ro.value=m):$.error(Me.message||"保存失败")}catch(R){console.error("保存失败:",R),Cs.value&&$.error("保存失败: "+(R.message||"未知错误"))}finally{Po.value=!1,O<Ss.value&&Cs.value&&(console.log(`检测到更新的内容，重新保存(版本: ${Ss.value})`),Le(()=>ve(!1)))}},Re=async()=>{if(M.value?.id){M.value.title?.trim()||(M.value.title="未命名章节");try{const k=w.value.find(R=>R.chapters?.find(Y=>Y.id===M.value.id));if(!k)return;const m=await window.pywebview.api.book_controller.update_chapter(p,k.id,M.value.id,{...M.value}),O=typeof m=="string"?JSON.parse(m):m;if(O.status==="success")$.success("保存成功"),st();else{$.error(O.message||"保存失败");const R=w.value.find(Y=>Y.chapters?.find(ie=>ie.id===M.value.id))?.chapters?.find(Y=>Y.id===M.value.id);R&&(M.value.title=R.title)}}catch(k){console.error("保存失败:",k),$.error("保存失败");const m=w.value.find(O=>O.chapters?.find(R=>R.id===M.value.id))?.chapters?.find(O=>O.id===M.value.id);m&&(M.value.title=m.title)}}},Ve=async()=>{await ft(),r.value.bgEnabled===void 0&&(r.value.bgEnabled=!1),r.value.bgOpacity===void 0&&(r.value.bgOpacity=100),r.value.bgImage===void 0&&(r.value.bgImage=""),r.value.paragraphSpace===void 0&&(r.value.paragraphSpace=!1),r.value.recentFontColors===void 0&&(r.value.recentFontColors=[]),L.value=new eb({extensions:[tw,t,dx.configure({findClass:"find-result",findActiveClass:"find-result-active"})],content:"",autofocus:"start",editorProps:{attributes:{spellcheck:"false"},handleDOMEvents:{contextmenu:(k,m)=>(m.preventDefault(),To(m),!0),keydown:(k,m)=>m.ctrlKey&&!m.shiftKey&&m.key==="f"?(m.preventDefault(),Zr(),!0):m.ctrlKey&&m.shiftKey&&m.key==="F"?(m.preventDefault(),contextMenuVisible.value=!0,!0):!1},scrollThreshold:{top:40,bottom:window.innerHeight/3},scrollMargin:{bottom:window.innerHeight/3}},onUpdate:({editor:k})=>{qr(),ot()}})},We=()=>{const k=document.querySelector(".ProseMirror");k&&(k.style.scrollbarWidth="thin")};ct(async()=>{await ft(),Kf(".editor-toolbar .drag-area"),window.addEventListener("keydown",xt),jn(),st(),Ve(),Le(()=>{We()});const k=setInterval(()=>ve(!1),1e3*30);V.value=k,await pe();try{await $e()}catch(m){console.error("初始TTS服务检查失败:",m),s.value=!1}}),lt(()=>{window.removeEventListener("keydown",xt),L.value&&L.value.destroy(),V.value&&(clearInterval(V.value),V.value=null),ot&&typeof ot.cancel=="function"&&ot.cancel()});const qe={getStyle(){return{"--editor-font-family":r.value.fontFamily,"--editor-font-size":`${r.value.fontSize}px`,"--editor-line-height":r.value.lineHeight,"--editor-content-width":`${r.value.contentWidth}%`,"--editor-theme":r.value.theme,"--editor-font-color":r.value.fontColor||"#303133"}},getClass(){return{"auto-spacing":r.value.paragraphSpace,"dark-theme":r.value.theme==="dark","light-theme":r.value.theme==="light"}},updateStyle(){const k=document.querySelector(".book-editor");if(!k)return;const m=this.getStyle();Object.entries(m).forEach(([O,R])=>{k.style.setProperty(O,R)})}},Dt=me(()=>qe.getStyle()),Xt=me(()=>qe.getClass()),At=me(()=>{const k=r.value?.bgEnabled,m=k?r.value.bgOpacity/100:0;return{backgroundImage:k&&l.value?`url('${l.value}')`:"none",opacity:m,backgroundSize:"cover",backgroundPosition:"center",zIndex:-999}}),Mo=async k=>{k||(r.value.bgImage="",ks({...r.value}))},ws=async()=>{try{const k=await window.pywebview.api.select_file_path(),m=typeof k=="string"?JSON.parse(k):k;if(m.status==="success"){const O=m.data[0],R=await window.pywebview.api.process_background_image(O),Y=typeof R=="string"?JSON.parse(R):R;if(Y.status==="success")r.value.bgImage=Y.data,ks({...r.value}),await Gr(),$.success("背景图片设置成功");else throw new Error(Y.message)}}catch(k){console.error("选择背景图片失败:",k),$.error("选择背景图片失败："+k.message)}};Ie(r,()=>{qe.updateStyle()},{deep:!0});const pt=I(!1),un=I(0),Yn=I(0),Mt=I(""),dn=I(!1),Ht=I(""),kt=I("gpt-3.5-turbo"),To=k=>{k.preventDefault(),k.stopPropagation(),Mt.value=L.value?.state.doc.textBetween(L.value.state.selection.from,L.value.state.selection.to);const m=window.innerWidth,O=window.innerHeight,R=200,Y=300;let ie=k.clientX,ae=k.clientY;ie+R>m&&(ie=m-R-10),ae+Y>O&&(ae=O-Y-10),un.value=Math.max(10,ie),Yn.value=Math.max(10,ae),pt.value=!0},fn=async k=>{try{if(k.aiPrompt){console.log("处理菜单项:",k),console.log("实体信息列表:",k.entityInfoList);let m=k.aiPrompt;const O=L.value?.state.doc.textBetween(L.value.state.selection.from,L.value.state.selection.to),R={[Ut.ENTITY]:k.entityInfoList?JSON.stringify(k.entityInfoList,null,2):"",[Ut.SELECTED_TEXT]:O||"",[Ut.CONTEXT]:k.includeContext?L.value?.state.doc.textBetween(0,L.value.state.doc.content.size):""};if(k.entityInfoList&&k.entityInfoList.length>0&&k.entityInfoList.forEach(Y=>{Y&&Y.id&&(R[`entity_${Y.id}`]=JSON.stringify(Y,null,2))}),tr.forEach(Y=>{if(Y&&Y.key){const ie=new RegExp(`\\$\\{${Y.key}\\}`,"g");m=m.replace(ie,R[Y.key]||"")}}),k.entityInfoList&&k.entityInfoList.length>0){const Y=[];k.entityInfoList.forEach(ie=>{if(ie&&ie.id){const ae=ie.id,Me=`\\$\\{entity_${ae}\\}`,Ee=new RegExp(Me,"g");let ze;for(;(ze=Ee.exec(m))!==null;)Y.push({entityId:ae,index:ze.index,length:ze[0].length,replacement:R[`entity_${ae}`]||""})}}),Y.sort((ie,ae)=>ae.index-ie.index),Y.forEach(ie=>{const ae=m.substring(0,ie.index),Me=m.substring(ie.index+ie.length);m=ae+ie.replacement+Me})}Le(()=>{Ht.value=m;const Y=k.model||"gpt-3.5-turbo",ie=e(Y);kt.value=ie,console.log("右键菜单设置模型:",Y,"->",ie),dn.value=!0,pt.value=!1})}else k.action==="playText"&&(await Nl(),pt.value=!1)}catch(m){console.error("处理菜单选择时出错:",m),$.error("处理菜单项时发生错误"),pt.value=!1}},xt=k=>{const m=document.querySelector(".entity-create-window, .scene-create-window, .el-dialog__wrapper");if(k.key==="Escape"){if(E.value){E.value=!1,k.preventDefault();return}if(m)return;ql(),k.preventDefault();return}k.ctrlKey&&k.key==="s"&&(k.preventDefault(),ve(!0)),k.ctrlKey&&k.key==="f"&&(k.preventDefault(),Zr())},js=()=>{L.value?.commands.focus("end")},Fr=()=>{Cs.value=!1,Mn.value=null,ot&&typeof ot.cancel=="function"&&ot.cancel(),v.push("/book/writing")},Il=()=>{window.pywebview.api.toggle_fullscreen()},Wr=async()=>{try{await o.updateConfigItem("editor.lastState",{bookId:p,volumeId:C.value?.id,chapterId:M.value?.id})}catch(k){console.error("保存最后编辑状态失败:",k)}},ks=Bo(async k=>{try{await o.updateConfigItem("editor",k)}catch(m){console.error("保存编辑器设置失败:",m)}},300);Ie(r,(k,m)=>{m&&ks(k)},{deep:!0});const Ol=me(()=>{if(!w.value)return[];const k=[...w.value];return k.sort((m,O)=>{const R=r.value.volumeSortOrder==="asc"?1:-1;return(m.created_at>O.created_at?1:-1)*R}),k.forEach(m=>{m.chapters&&m.chapters.sort((O,R)=>{const Y=r.value.chapterSortOrder==="asc"?1:-1;return(O.created_at>R.created_at?1:-1)*Y})}),k}),_d=async()=>{ks({...r.value}),re.value=!1,$.success("排序设置已保存")},Md=k=>{const m=window.atob(k),O=m.length,R=new Uint8Array(O);for(let Y=0;Y<O;Y++)R[Y]=m.charCodeAt(Y);return R.buffer};async function Nl(){try{let k="";if(L.value){const{from:Me,to:Ee,empty:ze}=L.value.state.selection;if(!ze)k=L.value.state.doc.textBetween(Me,Ee).trim();else{const Ue=L.value.state.doc.content.size;k=L.value.state.doc.textBetween(Me,Ue).trim()}}if(!k){$.warning("无法获取文本内容");return}Ce(),F.value=!0,Z.value=[];const m=ke.value,O=parseInt(m.rate)||0,R=parseInt(m.volume)||0,Y=parseInt(m.pitch)||0,ie=await window.pywebview.api.stream_speak_sync(k,m.voice,`${O>=0?"+":""}${O}%`,`${R>=0?"+":""}${R}%`,`${Y>=0?"+":""}${Y}Hz`),ae=typeof ie=="string"?JSON.parse(ie):ae;if(ae.status!=="success")throw new Error(ae.message)}catch(k){console.error("TTS播放失败:",k),$.error("TTS播放失败: "+k.message),F.value=!1}}async function Td(){try{let k="";if(L.value&&(k=L.value.state.doc.textBetween(0,L.value.state.doc.content.size).trim()),!k){$.warning("文档内容为空");return}Ce(),F.value=!0,Z.value=[];const m=ke.value,O=parseInt(m.rate)||0,R=parseInt(m.volume)||0,Y=parseInt(m.pitch)||0,ie=await window.pywebview.api.stream_speak_sync(k,m.voice,`${O>=0?"+":""}${O}%`,`${R>=0?"+":""}${R}%`,`${Y>=0?"+":""}${Y}Hz`),ae=typeof ie=="string"?JSON.parse(ie):ae;if(ae.status!=="success")throw new Error(ae.message)}catch(k){console.error("TTS播放失败:",k),$.error("TTS播放失败: "+k.message),F.value=!1}}const Ks=I(!1),Gn=I(0),Sn=I(0),Qn=I({x:0,y:0}),Eo=I(!1),Hr=I({x:0,y:0}),Dl=()=>{const R=window.innerHeight,Y=window.innerWidth,ie=Math.max(30,Y-180-30),ae=Math.max(30,R-220-30);return{x:ie,y:ae}},Tt=I(null),Xs=I(0),Cn=I(0),Yt=I(0),$t=I(null),Gt=I(!1),Pt=I(null),Al=I(new Date().toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}));let _n=null;const Io=()=>{Al.value=new Date().toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1})};ct(()=>{Io(),_n=setInterval(Io,1e3)}),lt(()=>{_n&&(clearInterval(_n),_n=null)});const $l=k=>{if(k<=0)return"00:00:00";const m=Math.floor(k/(1e3*60*60)),O=Math.floor(k%(1e3*60*60)/(1e3*60)),R=Math.floor(k%(1e3*60)/1e3),Y=m.toString().padStart(2,"0"),ie=O.toString().padStart(2,"0"),ae=R.toString().padStart(2,"0");return`${Y}:${ie}:${ae}`},Ed=me(()=>{if(!Tt.value)return"0";const k=Gn.value+Sn.value;return Math.max(0,k-Cn.value).toString()}),Id=me(()=>{if(!Tt.value||Yt.value===0)return"0 字/小时";const k=Gn.value+Sn.value,m=Math.max(0,k-Cn.value),O=Yt.value/(1e3*60*60);return O===0?"0 字/小时":`${Math.round(m/O)} 字/小时`}),Oo=I(Date.now()),Od=me(()=>{if(!Tt.value)return"00:00:00";let k=Yt.value;if(Gt.value&&$t.value){const m=Oo.value-$t.value;m<Ko&&(k+=m)}return $l(k)}),Nd=me(()=>{if(!Tt.value)return"00:00:00";const k=Oo.value-Tt.value;let m=Yt.value;if(Gt.value&&$t.value){const R=Oo.value-$t.value;R<Ko&&(m+=R)}const O=Math.max(0,k-m);return $l(O)});me(()=>{if(!Tt.value)return 0;const k=Gn.value+Sn.value,m=Math.max(0,k-Xs.value),O=Yt.value/(1e3*60*60),R=O>0?Math.round(m/O):0;return Math.min(R/3e3*100,100)});const qr=(k=!1)=>{if(!L.value||!M.value)return;const R=L.value.getHTML().replace(/<[^>]+>/g,"").replace(/\s/g,""),Y=R.length,ie=/[，。！？；：""''（）【】《》、]/g,ae=R.match(ie);if(Sn.value=ae?ae.length:0,Gn.value=Y,k||!Tt.value)return;const Me=Y+Sn.value;Me<Cn.value&&(Cn.value=Me);const Ee=Date.now();$t.value=Ee,Gt.value||(Gt.value=!0,Dd())},Dd=()=>{Pt.value&&clearTimeout(Pt.value),Pt.value=setTimeout(()=>{Gt.value=!1},Ko)},Ad=()=>{if(!Tt.value||!Gt.value)return;const k=Date.now();if($t.value){const m=k-$t.value;m<Ko&&(Yt.value+=m)}$t.value=k},$d=()=>setInterval(()=>{Ad()},1e3),Pd=()=>setInterval(()=>{Oo.value=Date.now()},1e3),Pl=()=>{if(!L.value||!M.value)return;console.log("初始化写作统计..."),Qn.value=Dl();const k=Date.now();Tt.value=k,$t.value=null,Yt.value=0,Gt.value=!1,Pt.value&&(clearTimeout(Pt.value),Pt.value=null),qr(!0),Xs.value=Gn.value+Sn.value,Cn.value=Xs.value,window.activeTimeUpdateInterval&&clearInterval(window.activeTimeUpdateInterval),window.activeTimeUpdateInterval=$d(),window.realtimeUpdateInterval&&clearInterval(window.realtimeUpdateInterval),window.realtimeUpdateInterval=Pd();const m=()=>{if(!Eo.value){const O=Dl();Qn.value=O}};window.addEventListener("resize",m),window.writingStatsResizeHandler=m},Rl=()=>{Ks.value=!Ks.value,Ks.value?(console.log("打开写作统计面板"),Pl()):(console.log("关闭写作统计面板"),Pt.value&&(clearTimeout(Pt.value),Pt.value=null),window.activeTimeUpdateInterval&&(clearInterval(window.activeTimeUpdateInterval),window.activeTimeUpdateInterval=null),window.realtimeUpdateInterval&&(clearInterval(window.realtimeUpdateInterval),window.realtimeUpdateInterval=null),window.writingStatsResizeHandler&&(window.removeEventListener("resize",window.writingStatsResizeHandler),window.writingStatsResizeHandler=null),Tt.value=null,$t.value=null,Yt.value=0,Gt.value=!1,Xs.value=Gn.value+Sn.value,Cn.value=Xs.value)};lt(()=>{window.writingStatsUpdateInterval&&(clearInterval(window.writingStatsUpdateInterval),window.writingStatsUpdateInterval=null),Tt.value=null,$t.value=null,Yt.value=0,Gt.value=!1,Cn.value=0,L.value&&(L.value.off("selectionUpdate"),L.value.off("update"))});const Mn=I(null),Jr=async(k,m)=>{try{const O=w.value.find(ie=>ie.id===k);if(!O){$.error("找不到指定的卷");return}C.value=O;const R=await window.pywebview.api.book_controller.get_chapter(p,k,m),Y=typeof R=="string"?JSON.parse(R):R;if(Y.status==="success"){M.value=Y.data;const ie=Zs();if(ie.isBookEncrypted(p)&&M.value.content)try{const ae=Mn.value!==p,Me=await ie.decryptChapterContent(p,k,m,M.value.content,ae);Mn.value=p,M.value.content=Me}catch(ae){console.error("解密章节失败:",ae),$.error("解密章节失败: "+(ae.message||"密码错误")),ie.clearBookPassword(p),Mn.value=null,M.value.content="<p>加密内容，请输入密码查看</p>";return}L.value?.commands.setContent(M.value.content||""),Ft(k,m),await Wr(),qr(!0),Ks.value&&(Tt.value=null,Pl()),L.value&&(Ro.value=L.value.getHTML())}}catch(O){console.error("加载章节失败:",O),$.error("加载章节失败: "+(O.message||"未知错误"))}};lt(()=>{Pt.value&&(clearTimeout(Pt.value),Pt.value=null),window.activeTimeUpdateInterval&&(clearInterval(window.activeTimeUpdateInterval),window.activeTimeUpdateInterval=null),window.realtimeUpdateInterval&&(clearInterval(window.realtimeUpdateInterval),window.realtimeUpdateInterval=null),window.writingStatsResizeHandler&&(window.removeEventListener("resize",window.writingStatsResizeHandler),window.writingStatsResizeHandler=null),Tt.value=null,$t.value=null,Yt.value=0,Gt.value=!1,Cn.value=0});const Rd=me(()=>({transform:`translate(${Qn.value.x}px, ${Qn.value.y}px)`})),Vd=k=>{Eo.value=!0,Hr.value={x:k.clientX-Qn.value.x,y:k.clientY-Qn.value.y},document.addEventListener("mousemove",Vl),document.addEventListener("mouseup",Ll)},Vl=k=>{Eo.value&&(Qn.value={x:k.clientX-Hr.value.x,y:k.clientY-Hr.value.y})},Ll=()=>{Eo.value=!1,document.removeEventListener("mousemove",Vl),document.removeEventListener("mouseup",Ll)},Ld=me(()=>`${Gn.value+Sn.value} 字`),Ur=I(!1),Bd=()=>{},jr=I(!1),zd=()=>{},Kr=I(0),Xr=I(0),Fd=()=>{L.value&&(L.value.on("selectionUpdate",({editor:k})=>{const{from:m,to:O}=k.state.selection;if(m===O)Kr.value=0,Mt.value="";else{const R=k.state.doc.textBetween(m,O," ");Mt.value=R;const ie=R.replace(/\s/g,"").replace(/([，。！？；：、,.!?;:])\1+/g,"$1");Kr.value=ie.length}Bl(k,m)}),L.value.on("update",({editor:k})=>{const{from:m}=k.state.selection;Bl(k,m)}))},Bl=(k,m)=>{if(!k)return;const R=k.state.doc.resolve(m).parent;if(R.type.name!=="paragraph"){Xr.value=0;return}const ae=R.textContent.replace(/\s/g,"").replace(/([，。！？；：、,.!?;:])\1+/g,"$1");Xr.value=ae.length};Ie(()=>L.value,k=>{k&&Fd()},{immediate:!0});const zl=I("history"),No=I([]),Yr=I(!1),Ys=I({}),Gr=async()=>{try{Yr.value=!0;const k=await window.pywebview.api.get_background_images(),m=typeof k=="string"?JSON.parse(k):k;if(m.status==="success")No.value=m.data||[],No.value.forEach(O=>{qd(O.path)});else throw new Error(m.message)}catch(k){console.error("加载历史背景失败:",k),$.error("加载历史背景失败："+k.message)}finally{Yr.value=!1}},Wd=async k=>{r.value.bgImage=k,ks({...r.value}),$.success("背景图片设置成功")},Hd=k=>ap(k*1e3).format("YYYY-MM-DD");Ie(z,async k=>{k&&await Gr()});const qd=async k=>{if(!Ys.value[k])try{const m=await window.pywebview.api.get_background_image_data(k,!0),O=typeof m=="string"?JSON.parse(m):m;O.status==="success"&&(Ys.value[k]=O.data)}catch(m){console.error("加载图片缩略图失败:",m,k)}},Jd=async k=>{try{await qt.confirm("确定要删除这张背景图片吗？","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const m=await window.pywebview.api.delete_background_image(k.path),O=typeof m=="string"?JSON.parse(m):m;O.status==="success"?($.success("背景图片删除成功"),await Gr()):$.error(O.message||"删除背景图片失败")}catch(m){m!=="cancel"&&(console.error("删除背景图片失败:",m),$.error("删除背景图片失败"))}},Ud=k=>{if(!L.value)return;const{state:m,view:O}=L.value;m.selection.empty||L.value.commands.deleteSelection();let R;/<[a-z][\s\S]*>/i.test(k)?R=k:R=k.split(/\n\s*\n/).map(ae=>ae.trim()?`<p >${ae.trim()}</p>`:"").join(""),L.value.commands.insertContent(R,{parseOptions:{preserveWhitespace:!1}}),L.value.commands.focus(),$.success("AI内容已插入到编辑器")},jd=async()=>{try{$.info("正在重新加载语音列表..."),await $e(),Fe.value.length>0&&($.success("语音列表加载成功"),s.value=!0)}catch(k){$.error("重新加载失败: "+k.message),s.value=!1}},Fl=I(!1),Wl=I(!1);Ie(_,k=>{Fl.value=k}),Ie(Fl,k=>{_.value=k}),Ie(U,k=>{Wl.value=k}),Ie(Wl,k=>{U.value=k});const Do=I(""),Ao=I([]),$o=I(!1),Qr=I(!1),Hl=I(1),Kd=I(20),Xd=I(!0),Yd=Bo(async k=>{if(!k.trim()){Ao.value=[],$o.value=!1;return}try{Qr.value=!0,Hl.value=1;const m=await window.pywebview.api.book_controller.search_chapters(p,k,Hl.value,Kd.value),O=typeof m=="string"?JSON.parse(m):m;if(O.status==="success"){const R=O.data;Ao.value=R.results||[],Xd.value=R.page<R.total_pages,$o.value=!0}}catch(m){console.error("搜索失败:",m),$.error("搜索失败")}finally{Qr.value=!1}},300),Gd=()=>{Yd(Do.value)},Qd=()=>{Do.value="",Ao.value=[],$o.value=!1},xs=I(!1),ql=()=>{xs.value=!xs.value},Zd=()=>{N.value=!N.value},ef=k=>{if(!(!k||!L.value))try{const{from:m,to:O}=L.value.state.selection;L.value.chain().focus().insertContentAt(m,k).run(),$.success("已插入聊天内容到编辑器")}catch(m){console.error("插入聊天内容失败:",m),$.error("插入聊天内容失败")}};lt(()=>{window.removeEventListener("keydown",xt),L.value&&L.value.destroy(),ot&&typeof ot.cancel=="function"&&ot.cancel(),document.documentElement.style.removeProperty("--chat-sidebar-width")});const Zr=()=>{if(E.value=!E.value,E.value&&L.value){const{from:k,to:m,empty:O}=L.value.state.selection;if(!O){const R=L.value.state.doc.textBetween(k,m);setTimeout(()=>{L.value?.commands&&L.value.commands.setFindTerm(R)},10)}}},tf=()=>{B.value=!B.value},nf=k=>{console.log("标点符号已插入:",k)},Po=I(!1),Ss=I(0),Ro=I(""),ot=Bo(()=>{Po.value?(Ss.value++,console.log(`内容已更改，版本增加到: ${Ss.value}`)):ve(!1)},1500),sf=()=>{ot&&typeof ot.cancel=="function"&&ot.cancel(),ve(!0)},Cs=I(!0),_s=I(!1),ei=I(!1),of=()=>{_s.value=!_s.value,_s.value&&(xs.value=!1)},rf=()=>{$.success("搜索配置已更新")},Qt=I(new Set);I(null);const lf=k=>{Qt.value.has(k)?Qt.value.delete(k):Qt.value.add(k),Qt.value=new Set(Qt.value)},af=k=>{const m=w.value.find(O=>O.id===k);m&&(C.value=m,W.value={volumeId:k,title:Xn()},U.value=!0)},cf=async(k,m)=>{if(k==="add-chapter"){af(m.id);return}await oe(k,m)},uf=async(k,m,O)=>{if(k==="duplicate"){try{const R=await window.pywebview.api.book_controller.create_chapter(p,m,{title:O.title+" (副本)",content:O.content||""}),Y=typeof R=="string"?JSON.parse(R):R;Y.status==="success"?($.success("章节复制成功"),await st()):$.error(Y.message||"复制失败")}catch(R){console.error("复制章节失败:",R),$.error("复制章节失败")}return}if(k==="move"){$.info("章节移动功能开发中...");return}await ce(k,m,O)},df=()=>{if(Pe.value){const k=w.value.find(m=>m.chapters?.some(O=>O.id===Pe.value));k&&Qt.value.add(k.id)}};return Ie(Pe,k=>{if(k){const m=w.value.find(O=>O.chapters?.some(R=>R.id===k));m&&(Qt.value.add(m.id),Qt.value=new Set(Qt.value))}}),Ie(w,()=>{df()},{immediate:!0}),(k,m)=>{const O=_t,R=kn,Y=vs,ie=Xf,ae=Nc,Me=Es("ChatDotRound"),Ee=Es("Connection"),ze=el,Ue=Es("Sort"),ht=Es("Menu"),Zn=Zi,Zt=Qi,Tn=bn,Rt=ko,Gs=wo,Qs=xr,rt=kr,ti=wr,ff=ig,Vo=Oc,pf=tp,es=np,Jl=tl,Ul=Yf,jl=op,hf=sp,Lo=lp,Kl=ip,mf=Es("Warning");return P(),q("div",YS,[g("div",{class:"background-layer",style:Et(At.value)},null,4),D.value?(P(),q("div",{key:0,class:"fullscreen-bg-layer",style:Et(At.value)},null,4)):ge("",!0),g("div",{class:we(["book-editor",[{"is-fullscreen":D.value},Xt.value]]),style:Et(Dt.value)},[g("div",GS,[g("div",QS,[f(ie,null,{default:y(()=>[f(Y,{content:"返回"},{default:y(()=>[f(R,{onClick:Fr},{default:y(()=>[f(O,null,{default:y(()=>[f(j(ps))]),_:1})]),_:1})]),_:1}),f(Y,{content:"字体设置"},{default:y(()=>[f(R,{onClick:m[0]||(m[0]=K=>Te.value=!0)},{default:y(()=>m[58]||(m[58]=[ne("字体")])),_:1})]),_:1}),f(Y,{content:"背景设置"},{default:y(()=>[f(R,{onClick:m[1]||(m[1]=K=>z.value=!0)},{default:y(()=>m[59]||(m[59]=[ne("背景")])),_:1})]),_:1}),f(Y,{content:"排序设置"},{default:y(()=>[f(R,{onClick:m[2]||(m[2]=K=>re.value=!0)},{default:y(()=>m[60]||(m[60]=[ne("排序")])),_:1})]),_:1}),f(Y,{content:"菜单配置"},{default:y(()=>[f(R,{onClick:m[3]||(m[3]=K=>fe.value=!0)},{default:y(()=>m[61]||(m[61]=[ne("菜单配置")])),_:1})]),_:1}),f(Y,{content:"写作统计"},{default:y(()=>[f(R,{onClick:Rl},{default:y(()=>[f(O,null,{default:y(()=>[f(j(Yl))]),_:1})]),_:1})]),_:1})]),_:1}),f(ae,{direction:"vertical"}),g("div",ZS,[f(Y,{content:"播放选中文本或从光标处开始播放",placement:"bottom"},{default:y(()=>[f(R,{class:we(["tts-btn",{"is-active":G.value}]),onClick:Nl,disabled:G.value||!s.value||F.value,loading:F.value},{default:y(()=>[f(O,null,{default:y(()=>[f(j(Qf))]),_:1}),m[62]||(m[62]=g("span",null,"选中播放",-1))]),_:1},8,["class","disabled","loading"])]),_:1}),f(Y,{content:"播放全文",placement:"bottom"},{default:y(()=>[f(R,{class:we(["tts-btn",{"is-active":G.value}]),onClick:Td,disabled:G.value||!s.value||F.value,loading:F.value},{default:y(()=>[f(O,null,{default:y(()=>[f(j(Zf))]),_:1}),m[63]||(m[63]=g("span",null,"全文播放",-1))]),_:1},8,["class","disabled","loading"])]),_:1}),f(Y,{content:s.value?"停止播放":"语音服务不可用，请检查网络",placement:"bottom"},{default:y(()=>[f(R,{class:we(["tts-btn stop-btn",{"can-stop":G.value||F.value}]),onClick:Ce,disabled:!G.value&&!F.value},{default:y(()=>[f(O,null,{default:y(()=>[f(j(Gi))]),_:1}),m[64]||(m[64]=g("span",null,"停止",-1))]),_:1},8,["class","disabled"])]),_:1},8,["content"]),f(Y,{content:"TTS设置",placement:"bottom"},{default:y(()=>[f(R,{class:"tts-btn",disabled:G.value||F.value,onClick:m[4]||(m[4]=K=>be.value=!0)},{default:y(()=>[f(O,null,{default:y(()=>[f(j(yr))]),_:1}),m[65]||(m[65]=g("span",null,"设置",-1))]),_:1},8,["disabled"])]),_:1}),f(ae,{direction:"vertical"}),f(Y,{content:"新建实体",placement:"bottom"},{default:y(()=>[f(R,{class:"entity-btn",onClick:m[5]||(m[5]=K=>Ur.value=!0)},{default:y(()=>[f(O,null,{default:y(()=>[f(j(on))]),_:1}),m[66]||(m[66]=g("span",null,"实体",-1))]),_:1})]),_:1}),f(Y,{content:"新建场景",placement:"bottom"},{default:y(()=>[f(R,{class:"scene-create-btn",onClick:m[6]||(m[6]=K=>jr.value=!0)},{default:y(()=>[f(O,null,{default:y(()=>[f(j(on))]),_:1}),m[67]||(m[67]=g("span",null,"场景",-1))]),_:1})]),_:1})]),f(ae,{direction:"vertical"}),f(Y,{content:"聊天助手"},{default:y(()=>[f(R,{onClick:ql},{default:y(()=>[f(O,null,{default:y(()=>[f(Me)]),_:1}),m[68]||(m[68]=g("span",null,"聊天",-1))]),_:1})]),_:1}),f(Y,{content:"网络查询"},{default:y(()=>[f(R,{onClick:of},{default:y(()=>[f(O,null,{default:y(()=>[f(Ee)]),_:1}),m[69]||(m[69]=g("span",null,"查询",-1))]),_:1})]),_:1}),f(Y,{content:"查找和替换 (Ctrl+F)"},{default:y(()=>[f(R,{onClick:Zr},{default:y(()=>[f(O,null,{default:y(()=>[f(j(fs))]),_:1}),m[70]||(m[70]=g("span",null,"查找",-1))]),_:1})]),_:1}),f(Y,{content:"标点符号面板"},{default:y(()=>[f(R,{onClick:tf,class:we({"is-active":B.value})},{default:y(()=>[f(O,null,{default:y(()=>[f(j(zt))]),_:1}),m[71]||(m[71]=g("span",null,"标点",-1))]),_:1},8,["class"])]),_:1})]),g("div",{class:"drag-area",onDblclick:Il},null,32),g("div",eC,[f(R,{type:"primary",onClick:sf},{default:y(()=>m[72]||(m[72]=[ne("保存")])),_:1}),f(R,{onClick:Il},{default:y(()=>[f(O,null,{default:y(()=>[f(j(ep))]),_:1})]),_:1})]),g("div",tC,[f(Zt,{trigger:"click"},{dropdown:y(()=>[f(Zn,null,{default:y(()=>[f(ze,{onClick:m[7]||(m[7]=K=>Te.value=!0)},{default:y(()=>[f(O,null,{default:y(()=>[f(j(zt))]),_:1}),m[73]||(m[73]=ne("字体设置 "))]),_:1}),f(ze,{onClick:m[8]||(m[8]=K=>z.value=!0)},{default:y(()=>[f(O,null,{default:y(()=>[f(j(Gl))]),_:1}),m[74]||(m[74]=ne("背景设置 "))]),_:1}),f(ze,{onClick:m[9]||(m[9]=K=>re.value=!0)},{default:y(()=>[f(O,null,{default:y(()=>[f(Ue)]),_:1}),m[75]||(m[75]=ne("排序设置 "))]),_:1}),f(ze,{onClick:m[10]||(m[10]=K=>fe.value=!0)},{default:y(()=>[f(O,null,{default:y(()=>[f(ht)]),_:1}),m[76]||(m[76]=ne("菜单配置 "))]),_:1}),f(ze,{onClick:Rl},{default:y(()=>[f(O,null,{default:y(()=>[f(j(Yl))]),_:1}),m[77]||(m[77]=ne("写作统计 "))]),_:1})]),_:1})]),default:y(()=>[f(R,{class:"toolbar-dropdown-btn"},{default:y(()=>[f(O,null,{default:y(()=>[f(j(Ei))]),_:1})]),_:1})]),_:1})])]),g("div",nC,[Fn(g("div",{class:we(["chapters-sidebar chapter-list",{collapsed:N.value}])},[g("div",sC,[g("div",oC,[f(Tn,{modelValue:Do.value,"onUpdate:modelValue":m[11]||(m[11]=K=>Do.value=K),placeholder:"搜索章节...",clearable:"",onInput:Gd,onClear:Qd},{prefix:y(()=>[f(O,null,{default:y(()=>[f(j(fs))]),_:1})]),_:1},8,["modelValue"])]),g("div",rC,[f(ie,null,{default:y(()=>[f(R,{type:"primary",onClick:_o,icon:j(on)},{default:y(()=>m[78]||(m[78]=[ne(" 新建卷 ")])),_:1},8,["icon"]),f(R,{type:"primary",onClick:H,disabled:!C.value,icon:j(on)},{default:y(()=>m[79]||(m[79]=[ne(" 新建章节 ")])),_:1},8,["disabled","icon"])]),_:1})])]),f(XS,{volumes:Ol.value,"expanded-volume-ids":Qt.value,"active-chapter-id":Pe.value,"search-results":Ao.value,"show-search-results":$o.value,"search-loading":Qr.value,"container-height":500,loading:cn.value,onChapterClick:Jr,onVolumeCommand:cf,onChapterCommand:uf,onToggleVolume:lf,onCreateVolume:_o,onLoadMore:te},null,8,["volumes","expanded-volume-ids","active-chapter-id","search-results","show-search-results","search-loading","loading"]),f(Qs,{modelValue:_.value,"onUpdate:modelValue":m[14]||(m[14]=K=>_.value=K),title:"新建卷",width:"500px",class:"create-volume-dialog","append-to-body":!0,"destroy-on-close":!0,onOpened:Br},{footer:y(()=>[g("div",iC,[f(R,{onClick:m[13]||(m[13]=K=>_.value=!1)},{default:y(()=>m[80]||(m[80]=[ne("取消")])),_:1}),f(R,{type:"primary",onClick:xn},{default:y(()=>m[81]||(m[81]=[ne("确定")])),_:1})])]),default:y(()=>[f(Gs,{model:T.value,onSubmit:Oe(xn,["prevent"]),onKeyup:St(xn,["enter"])},{default:y(()=>[f(Rt,{label:"卷标题",required:""},{default:y(()=>[f(Tn,{modelValue:T.value.title,"onUpdate:modelValue":m[12]||(m[12]=K=>T.value.title=K),placeholder:"请输入卷标题",maxlength:"50","show-word-limit":"",ref:"volumeTitleInput",onKeyup:St(xn,["enter"])},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),f(Qs,{modelValue:U.value,"onUpdate:modelValue":m[18]||(m[18]=K=>U.value=K),title:"新建章节",width:"500px",class:"create-chapter-dialog","append-to-body":!0,"destroy-on-close":!0,onOpened:zr},{footer:y(()=>[g("div",lC,[f(R,{onClick:m[17]||(m[17]=K=>U.value=!1)},{default:y(()=>m[82]||(m[82]=[ne("取消")])),_:1}),f(R,{type:"primary",onClick:J},{default:y(()=>m[83]||(m[83]=[ne("确定")])),_:1})])]),default:y(()=>[f(Gs,{model:W.value,onSubmit:Oe(J,["prevent"]),onKeyup:St(J,["enter"])},{default:y(()=>[f(Rt,{label:"所属卷",required:""},{default:y(()=>[f(ti,{modelValue:W.value.volumeId,"onUpdate:modelValue":m[15]||(m[15]=K=>W.value.volumeId=K),placeholder:"请选择所属卷",style:{width:"100%"}},{default:y(()=>[(P(!0),q(_e,null,Ae(Ol.value,K=>(P(),Ne(rt,{key:K.id,label:K.title,value:K.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),f(Rt,{label:"章节标题",required:""},{default:y(()=>[f(Tn,{modelValue:W.value.title,"onUpdate:modelValue":m[16]||(m[16]=K=>W.value.title=K),placeholder:"请输入章节标题",maxlength:"50","show-word-limit":"",ref:"chapterTitleInput",onKeyup:St(J,["enter"])},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])],2),[[bo,!D.value]]),g("div",{class:we(["editor-main",{"with-chat-sidebar":xs.value,"with-search-panel":_s.value,"sidebar-collapsed":N.value}])},[g("div",{class:"sidebar-toggle-btn",onClick:Zd},[N.value?(P(),Ne(O,{key:1},{default:y(()=>[f(j(Pc))]),_:1})):(P(),Ne(O,{key:0},{default:y(()=>[f(j(xc))]),_:1}))]),g("div",{class:"editor-content",onContextmenu:Oe(To,["prevent"])},[g("div",aC,[Fn(g("input",{type:"text",class:"chapter-title-input","onUpdate:modelValue":m[19]||(m[19]=K=>M.value.title=K),placeholder:"请输入章节标题",onChange:Re,onKeydown:St(js,["enter"])},null,544),[[Ec,M.value.title]])]),f(j(tb),{editor:L.value},null,8,["editor"]),pt.value?(P(),Ne(lw,{key:0,visible:pt.value,x:un.value,y:Yn.value,menuItems:j(o).editor.contextMenus,selectedText:Mt.value,onSelect:fn,onClose:m[20]||(m[20]=K=>pt.value=!1)},null,8,["visible","x","y","menuItems","selectedText"])):ge("",!0),f(cx,{visible:E.value,editor:L.value,"onUpdate:visible":m[21]||(m[21]=K=>E.value=K),onClose:m[22]||(m[22]=K=>E.value=!1)},null,8,["visible","editor"]),f(bS,{visible:B.value,editor:L.value,onClose:m[23]||(m[23]=K=>B.value=!1),onInsert:nf},null,8,["visible","editor"])],32),g("div",cC,[g("div",uC,[g("div",dC,[m[84]||(m[84]=g("span",{class:"status-label"},"总字数：",-1)),g("span",fC,ee(Ld.value),1)]),g("div",pC,[m[85]||(m[85]=g("span",{class:"status-label"},"选中字数：",-1)),g("span",hC,ee(Kr.value),1)]),g("div",mC,[m[86]||(m[86]=g("span",{class:"status-label"},"当前段落：",-1)),g("span",gC,ee(Xr.value)+" 字",1)])]),g("div",vC,[g("div",yC,[m[87]||(m[87]=g("span",{class:"status-label"},"当前时间：",-1)),g("span",bC,ee(Al.value),1)])])])],2),f(ff,{visible:xs.value,"book-id":j(p),"selected-text":Mt.value,editor:L.value,onClose:m[24]||(m[24]=K=>xs.value=!1),onInsertText:ef},null,8,["visible","book-id","selected-text","editor"]),f(Ox,{visible:_s.value,"book-id":j(p),"selected-text":Mt.value,editor:L.value,onClose:m[25]||(m[25]=K=>_s.value=!1),onShowConfig:m[26]||(m[26]=K=>ei.value=!0)},null,8,["visible","book-id","selected-text","editor"]),f(_c,{name:"fade"},{default:y(()=>[Ks.value?(P(),q("div",{key:0,class:"writing-stats-panel",style:Et(Rd.value)},[g("div",wC,[m[88]||(m[88]=g("span",{class:"title"},"写作统计",-1)),f(O,{class:"drag-handle",onMousedown:Vd},{default:y(()=>[f(j(nl))]),_:1})]),g("div",kC,[g("div",xC,[g("div",SC,[m[89]||(m[89]=g("div",{class:"stat-label"},"本次码字",-1)),g("div",CC,ee(Ed.value),1)])]),g("div",_C,[g("div",MC,[m[90]||(m[90]=g("div",{class:"stat-label"},"码字速率(字/时)",-1)),g("div",TC,ee(Id.value),1)])]),g("div",EC,[g("div",IC,[m[91]||(m[91]=g("div",{class:"stat-label"},"码字时间",-1)),g("div",OC,ee(Od.value),1)])]),g("div",NC,[g("div",DC,[m[92]||(m[92]=g("div",{class:"stat-label"},"空闲时间",-1)),g("div",AC,ee(Nd.value),1)])])])],4)):ge("",!0)]),_:1})]),f(Ul,{modelValue:Te.value,"onUpdate:modelValue":m[34]||(m[34]=K=>Te.value=K),title:"字体设置",direction:"rtl",size:"400px","destroy-on-close":!1,"append-to-body":!0,"custom-class":"font-settings-drawer"},{footer:y(()=>[g("div",r_,[f(R,{onClick:m[33]||(m[33]=K=>Te.value=!1)},{default:y(()=>m[100]||(m[100]=[ne("关闭")])),_:1})])]),default:y(()=>[g("div",$C,[g("div",PC,[g("div",RC,[g("div",VC,[g("div",LC,[m[93]||(m[93]=g("span",{class:"setting-label"},"字体",-1)),g("span",BC,ee(r.value.fontFamily),1)]),f(ti,{modelValue:r.value.fontFamily,"onUpdate:modelValue":m[27]||(m[27]=K=>r.value.fontFamily=K),placeholder:"选择字体",class:"font-select"},{default:y(()=>[f(Vo,{label:"无衬线字体"},{default:y(()=>[f(rt,{label:"思源黑体",value:"'Noto Sans SC', sans-serif"}),f(rt,{label:"微软雅黑",value:"'Microsoft YaHei', sans-serif"}),f(rt,{label:"黑体",value:"SimHei, sans-serif"}),f(rt,{label:"阿里巴巴普惠体",value:"'AlibabaPuHuiTi', sans-serif"}),f(rt,{label:"苹方",value:"'PingFang SC', 'Hiragino Sans GB', sans-serif"})]),_:1}),f(Vo,{label:"衬线字体"},{default:y(()=>[f(rt,{label:"思源宋体",value:"'Noto Serif SC', serif"}),f(rt,{label:"宋体",value:"SimSun, serif"}),f(rt,{label:"方正书宋",value:"'FZShuSong-Z01', 'STSong', serif"}),f(rt,{label:"Georgia Pro",value:"'Georgia Pro', 'Times New Roman', SimSun, serif"})]),_:1}),f(Vo,{label:"等宽字体"},{default:y(()=>[f(rt,{label:"思源等宽",value:"'Source Han Mono SC', monospace"}),f(rt,{label:"等线",value:"'DengXian', 'Microsoft YaHei', sans-serif"})]),_:1}),f(Vo,{label:"手写/楷体"},{default:y(()=>[f(rt,{label:"楷体",value:"KaiTi, STKaiti, serif"}),f(rt,{label:"霞鹜文楷",value:"'LXGW WenKai', 'Noto Serif SC', serif"}),f(rt,{label:"隶书",value:"LiSu, STLiti, serif"})]),_:1})]),_:1},8,["modelValue"])]),g("div",zC,[g("div",FC,[m[94]||(m[94]=g("span",{class:"setting-label"},"字体颜色",-1)),g("span",WC,ee(r.value.fontColor||"#303133"),1)]),g("div",HC,[f(pf,{modelValue:r.value.fontColor,"onUpdate:modelValue":m[28]||(m[28]=K=>r.value.fontColor=K),predefine:["#303133","#606266","#909399","#2D2D2D","#000000","#1E1E1E","#494949","#5E0000","#4A2604","#214001","#004138","#012A4A","#171064","#3C0051"],"show-alpha":"",class:"font-color-picker",onChange:i},null,8,["modelValue"]),r.value.recentFontColors&&r.value.recentFontColors.length>0?(P(),q("div",qC,[m[95]||(m[95]=g("div",{class:"recent-colors-title"},"最近使用:",-1)),g("div",JC,[(P(!0),q(_e,null,Ae(r.value.recentFontColors,(K,ni)=>(P(),q("div",{key:ni,class:"recent-color-item",style:Et({backgroundColor:K}),title:K,onClick:T_=>r.value.fontColor=K},null,12,UC))),128))])])):ge("",!0)])]),g("div",jC,[g("div",KC,[m[96]||(m[96]=g("span",{class:"setting-label"},"字体大小",-1)),g("span",XC,ee(r.value.fontSize)+"px",1)]),f(es,{modelValue:r.value.fontSize,"onUpdate:modelValue":m[29]||(m[29]=K=>r.value.fontSize=K),min:16,max:58,step:1,"format-tooltip":K=>`${K}px`,class:"custom-slider"},null,8,["modelValue","format-tooltip"])]),g("div",YC,[g("div",GC,[m[97]||(m[97]=g("span",{class:"setting-label"},"行间距",-1)),g("span",QC,ee(r.value.lineHeight),1)]),f(es,{modelValue:r.value.lineHeight,"onUpdate:modelValue":m[30]||(m[30]=K=>r.value.lineHeight=K),min:1,max:3,step:.1,"format-tooltip":K=>K.toFixed(1),class:"custom-slider"},null,8,["modelValue","format-tooltip"])]),g("div",ZC,[g("div",e_,[m[98]||(m[98]=g("span",{class:"setting-label"},"正文宽度",-1)),g("span",t_,ee(r.value.contentWidth)+"%",1)]),f(es,{modelValue:r.value.contentWidth,"onUpdate:modelValue":m[31]||(m[31]=K=>r.value.contentWidth=K),min:20,max:100,step:1,"format-tooltip":K=>`${K}%`,class:"custom-slider"},null,8,["modelValue","format-tooltip"])]),g("div",n_,[g("div",s_,[m[99]||(m[99]=g("span",{class:"setting-label"},"段落间距",-1)),g("span",o_,ee(r.value.paragraphSpace?"开启":"关闭"),1)]),f(Jl,{modelValue:r.value.paragraphSpace,"onUpdate:modelValue":m[32]||(m[32]=K=>r.value.paragraphSpace=K),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])])])])])]),_:1},8,["modelValue"]),f(Qs,{modelValue:z.value,"onUpdate:modelValue":m[38]||(m[38]=K=>z.value=K),title:"背景设置",width:"800px",top:"5vh","append-to-body":!0,"destroy-on-close":"","close-on-click-modal":!1,class:"background-settings-dialog"},{default:y(()=>[f(Gs,{"label-position":"top",class:"bg-settings-form"},{default:y(()=>[f(Rt,{label:"启用背景图片"},{default:y(()=>[f(Jl,{modelValue:r.value.bgEnabled,"onUpdate:modelValue":m[35]||(m[35]=K=>r.value.bgEnabled=K),onChange:Mo},null,8,["modelValue"])]),_:1}),r.value.bgEnabled?(P(),q(_e,{key:0},[f(Rt,{label:"选择背景方式"},{default:y(()=>[f(hf,{modelValue:zl.value,"onUpdate:modelValue":m[36]||(m[36]=K=>zl.value=K)},{default:y(()=>[f(jl,{label:"历史背景",name:"history"},{default:y(()=>[g("div",i_,[Yr.value?(P(),q("div",l_,[f(O,{class:"is-loading"},{default:y(()=>[f(j(Ti))]),_:1}),m[101]||(m[101]=g("span",null,"加载中...",-1))])):No.value.length===0?(P(),q("div",a_,[f(O,null,{default:y(()=>[f(j(rp))]),_:1}),m[102]||(m[102]=g("span",null,"暂无历史背景图片",-1))])):(P(),q("div",c_,[g("div",u_,[(P(!0),q(_e,null,Ae(No.value,K=>(P(),q("div",{key:K.path,class:we(["bg-item",{active:r.value.bgImage===K.path}])},[g("div",{class:"bg-preview",onClick:ni=>Wd(K.path)},[g("img",{src:Ys.value[K.path]||"",alt:"历史背景",class:we(["bg-img",{loading:!Ys.value[K.path]}])},null,10,f_),Ys.value[K.path]?ge("",!0):(P(),q("div",p_,[f(O,{class:"is-loading"},{default:y(()=>[f(j(Ti))]),_:1})]))],8,d_),g("div",h_,[g("div",m_,ee(Hd(K.timestamp)),1),g("div",g_,[f(R,{type:"danger",size:"small",link:"",onClick:Oe(ni=>Jd(K),["stop"])},{default:y(()=>[f(O,null,{default:y(()=>[f(j(Vs))]),_:1})]),_:2},1032,["onClick"])])])],2))),128))])]))])]),_:1}),f(jl,{label:"上传新图片",name:"upload"},{default:y(()=>[g("div",v_,[l.value?(P(),q("div",y_,[g("img",{src:l.value,alt:"当前背景预览"},null,8,b_),m[103]||(m[103]=g("div",{class:"preview-label"},"当前背景预览",-1))])):(P(),q("div",w_,[f(O,null,{default:y(()=>[f(j(Gl))]),_:1}),m[104]||(m[104]=g("span",null,"未选择背景图片",-1))]))]),f(R,{type:"primary",onClick:ws},{default:y(()=>m[105]||(m[105]=[ne("选择本地图片")])),_:1}),m[106]||(m[106]=g("div",{class:"upload-tip"}," 支持 JPG、PNG、GIF、WEBP 格式，图片将保存至您的背景库 ",-1))]),_:1})]),_:1},8,["modelValue"])]),_:1}),f(Rt,{label:"背景透明度"},{default:y(()=>[f(es,{modelValue:r.value.bgOpacity,"onUpdate:modelValue":m[37]||(m[37]=K=>r.value.bgOpacity=K),min:0,max:100,step:1,"show-input":""},null,8,["modelValue"])]),_:1})],64)):ge("",!0)]),_:1})]),_:1},8,["modelValue"]),f(Qs,{modelValue:re.value,"onUpdate:modelValue":m[42]||(m[42]=K=>re.value=K),title:"目录排序设置",width:"400px",modal:!0,"close-on-click-modal":!1,"destroy-on-close":""},{footer:y(()=>[g("span",k_,[f(R,{onClick:m[41]||(m[41]=K=>re.value=!1)},{default:y(()=>m[111]||(m[111]=[ne("取消")])),_:1}),f(R,{type:"primary",onClick:_d},{default:y(()=>m[112]||(m[112]=[ne(" 确定 ")])),_:1})])]),default:y(()=>[f(Gs,{"label-position":"top"},{default:y(()=>[f(Rt,{label:"卷排序方式"},{default:y(()=>[f(Kl,{modelValue:r.value.volumeSortOrder,"onUpdate:modelValue":m[39]||(m[39]=K=>r.value.volumeSortOrder=K)},{default:y(()=>[f(Lo,{label:"asc"},{default:y(()=>m[107]||(m[107]=[ne("从旧到新")])),_:1}),f(Lo,{label:"desc"},{default:y(()=>m[108]||(m[108]=[ne("从新到旧")])),_:1})]),_:1},8,["modelValue"])]),_:1}),f(Rt,{label:"章节排序方式"},{default:y(()=>[f(Kl,{modelValue:r.value.chapterSortOrder,"onUpdate:modelValue":m[40]||(m[40]=K=>r.value.chapterSortOrder=K)},{default:y(()=>[f(Lo,{label:"asc"},{default:y(()=>m[109]||(m[109]=[ne("从旧到新")])),_:1}),f(Lo,{label:"desc"},{default:y(()=>m[110]||(m[110]=[ne("从新到旧")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),f(Ul,{modelValue:fe.value,"onUpdate:modelValue":m[44]||(m[44]=K=>fe.value=K),title:"右键菜单配置",direction:"rtl",size:"600px",modal:!0,"destroy-on-close":!1,"append-to-body":!0,"custom-class":"context-menu-settings-drawer"},{default:y(()=>[f(Ak,{visible:fe.value,selectedText:Mt.value,editor:L.value,"book-id":j(p),onClose:m[43]||(m[43]=K=>fe.value=!1)},null,8,["visible","selectedText","editor","book-id"])]),_:1},8,["modelValue"]),f(Qs,{modelValue:be.value,"onUpdate:modelValue":m[53]||(m[53]=K=>be.value=K),title:"TTS设置",width:"500px"},{default:y(()=>[g("div",x_,[f(Gs,{"label-position":"top"},{default:y(()=>[f(Rt,{label:"选择语音"},{default:y(()=>[g("div",S_,[f(ti,{modelValue:ke.value.voice,"onUpdate:modelValue":m[45]||(m[45]=K=>ke.value.voice=K),placeholder:"请选择语音",onChange:m[46]||(m[46]=K=>he("voice",K)),loading:S.value,filterable:""},{default:y(()=>[(P(!0),q(_e,null,Ae(Fe.value,K=>(P(),Ne(rt,{key:K.ShortName,label:K.LocalName,value:K.ShortName},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"]),!S.value&&Fe.value.length===0?(P(),q("div",C_,[f(O,null,{default:y(()=>[f(mf)]),_:1}),m[114]||(m[114]=g("span",null,"无法加载语音列表，请检查网络连接",-1)),f(R,{type:"primary",link:"",size:"small",onClick:jd},{default:y(()=>m[113]||(m[113]=[ne("重试")])),_:1})])):ge("",!0)])]),_:1}),f(Rt,{label:"语速"},{default:y(()=>[f(es,{modelValue:ke.value.rate,"onUpdate:modelValue":m[47]||(m[47]=K=>ke.value.rate=K),min:-100,max:100,"format-tooltip":K=>K+"%",onChange:m[48]||(m[48]=K=>he("rate",K))},null,8,["modelValue","format-tooltip"])]),_:1}),f(Rt,{label:"音量"},{default:y(()=>[f(es,{modelValue:ke.value.volume,"onUpdate:modelValue":m[49]||(m[49]=K=>ke.value.volume=K),min:-100,max:100,"format-tooltip":K=>K+"%",onChange:m[50]||(m[50]=K=>he("volume",K))},null,8,["modelValue","format-tooltip"])]),_:1}),f(Rt,{label:"音调"},{default:y(()=>[f(es,{modelValue:ke.value.pitch,"onUpdate:modelValue":m[51]||(m[51]=K=>ke.value.pitch=K),min:-100,max:100,"format-tooltip":K=>K+"Hz",onChange:m[52]||(m[52]=K=>he("pitch",K))},null,8,["modelValue","format-tooltip"])]),_:1})]),_:1})])]),_:1},8,["modelValue"]),g("div",__,[f(qk,{visible:Ur.value,"book-id":j(p),"onUpdate:visible":m[54]||(m[54]=K=>Ur.value=K),onCreated:Bd},null,8,["visible","book-id"]),f(ox,{visible:jr.value,"book-id":j(p),"onUpdate:visible":m[55]||(m[55]=K=>jr.value=K),onCreated:zd},null,8,["visible","book-id"]),f(lS,{visible:ei.value,"book-id":j(p),"onUpdate:visible":m[56]||(m[56]=K=>ei.value=K),onConfigUpdated:rf},null,8,["visible","book-id"]),dn.value?(P(),Ne(Iw,{key:0,visible:dn.value,"initial-prompt":Ht.value,model:kt.value,"selected-text":Mt.value,onClose:m[57]||(m[57]=K=>dn.value=!1),onInsertText:Ud},null,8,["visible","initial-prompt","model","selected-text"])):ge("",!0)])],6)])}}},tM=ut(M_,[["__scopeId","data-v-d2aef6fd"]]);export{tM as default};
