import{_ as ui,a as En,r as U,c as gt,w as Nt,b as ht,m as Z,e as w,p as It,a8 as ne,d as N,C as Q,aY as Li,t as Bi,aw as bi,N as Ar,s as Cn,g as z,cy as Rr,v as st,B as Nn,T as qn,F as dt,cz as Ir,af as Or,V as kr,R as ni,S as Tn,n as pe,M as oe,U as Di,h as ue,E as $,cg as zr,o as Sn,Y as xe,ao as Ln,ah as zs,aK as Fr,cA as Pr,a9 as si,cB as Br,b2 as $r,aS as Hr,a5 as Ur,cl as Kn,cx as Gr,cC as Yr,bo as Wr,W as nn,ap as fe,a4 as Zn,aM as Jn,aV as Qn,J as Vr,aF as Fs,aD as Ps,aE as Bs,ba as Xr,a1 as sn,aX as jr,bV as qr,bk as ts,k as $s,aR as Si,cD as Kr,cE as Zr,ad as Jr,ae as Qr,ab as to,j as eo,q as io,x as no}from"./entry-DxFfH4M0.js";/* empty css                *//* empty css               *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                   *//* empty css                 */import{useAIProvidersStore as Hs}from"./aiProviders-Zwr_VLaf.js";/* empty css                    *//* empty css                  */import{n as rn}from"./index-browser-OxPLOBIU.js";const so={class:"file-toolbar"},ro={class:"toolbar-actions"},oo={class:"toolbar-search"},ao={key:0,class:"current-path"},lo={class:"path-text"},ho={class:"file-list"},co={key:0,class:"empty-state"},uo={key:1,class:"empty-state"},fo={key:2,class:"file-items"},po=["onClick","onDblclick"],mo={class:"file-icon"},go={class:"file-info"},vo=["title"],yo={class:"file-meta"},xo={class:"file-size"},wo={class:"file-time"},_o={class:"file-actions"},Mo={key:1,class:"file-stats"},Eo={key:0},Co={__name:"FileManager",props:{selectedFile:{type:Object,default:null},lastSelectedDirectory:{type:String,default:""}},emits:["file-selected","file-opened","create-document","directory-selected"],setup(s,{expose:t,emit:e}){const i=s,n=e,r=En(),o=U(""),a=U([]),l=U(!1),h=U(""),d=gt(()=>r.theme==="dark"),c=gt(()=>a.value.filter(L=>!L.is_directory&&L.name.toLowerCase().endsWith(".md"))),f=gt(()=>{if(!h.value)return c.value;const L=h.value.toLowerCase();return c.value.filter(F=>F.name.toLowerCase().includes(L))});Nt(h,()=>{f.value.length>0&&i.selectedFile&&(f.value.some(F=>F.path===i.selectedFile.path)||n("file-selected",null))}),Nt(()=>i.lastSelectedDirectory,async L=>{L&&L!==o.value&&(o.value=L,await u())},{immediate:!0});const g=async()=>{try{const L=await window.pywebview.api.select_directory(),F=typeof L=="string"?JSON.parse(L):L;F.status==="success"?(o.value=F.data,await u(),n("directory-selected",F.data)):$.error("选择目录失败: "+F.message)}catch(L){console.error("选择目录失败:",L),$.error("选择目录失败: "+L.message)}},u=async()=>{if(o.value){l.value=!0;try{const L=await window.pywebview.api.list_directory(o.value),F=typeof L=="string"?JSON.parse(L):L;F.status==="success"?(a.value=F.data||[],console.log("加载的文件列表:",a.value),console.log("Markdown文件数量:",c.value.length),console.log("Markdown文件:",c.value),c.value.length>0&&p(c.value[0])):($.error("加载目录失败: "+F.message),a.value=[])}catch(L){console.error("加载目录失败:",L),$.error("加载目录失败: "+L.message),a.value=[]}finally{l.value=!1}}},m=async()=>{await u()},p=L=>{n("file-selected",L)},x=async L=>{!L.is_directory&&L.name.toLowerCase().endsWith(".md")&&n("file-opened",L)},E=async L=>{try{const W=L.substring(0,L.lastIndexOf("/"))||L.substring(0,L.lastIndexOf("\\"))||o.value;await window.pywebview.api.open_directory(W)}catch(F){console.error("打开文件管理器失败:",F),$.error("打开文件管理器失败")}},_=L=>L.split(/[/\\]/).pop()||L,T=L=>{if(L===0)return"0 B";const F=1024,W=["B","KB","MB","GB"],X=Math.floor(Math.log(L)/Math.log(F));return parseFloat((L/Math.pow(F,X)).toFixed(1))+" "+W[X]},C=L=>{const F=new Date(L*1e3);return F.toLocaleDateString()+" "+F.toLocaleTimeString()};return t({selectDirectory:g,refreshDirectory:m,currentDirectory:gt(()=>o.value),markdownFiles:gt(()=>c.value)}),(L,F)=>{const W=Bi,X=Cn,Mt=Nn,ct=Rr,Lt=Ir,bt=kr,Dt=Or;return Z(),ht("div",{class:pe(["file-manager",{dark:d.value}])},[w("div",so,[w("div",ro,[N(W,{onClick:g,icon:Q(Li),size:"small",text:"",class:"toolbar-btn"},null,8,["icon"]),N(W,{onClick:m,icon:Q(bi),size:"small",text:"",loading:l.value,disabled:!o.value,class:"toolbar-btn"},null,8,["icon","loading","disabled"])]),w("div",oo,[N(X,{modelValue:h.value,"onUpdate:modelValue":F[0]||(F[0]=A=>h.value=A),placeholder:"搜索文件...","prefix-icon":Q(Ar),size:"small",clearable:"",class:"search-input"},null,8,["modelValue","prefix-icon"])])]),o.value?(Z(),ht("div",ao,[N(Lt,{separator:"/"},{default:z(()=>[N(ct,null,{default:z(()=>[N(Mt,null,{default:z(()=>[N(Q(qn))]),_:1}),st(" "+dt(_(o.value)),1)]),_:1})]),_:1}),w("span",lo,dt(o.value),1)])):It("",!0),ne((Z(),ht("div",ho,[o.value?f.value.length===0?(Z(),ht("div",uo,[N(bt,{description:"当前目录没有找到Markdown文件"},{default:z(()=>[N(W,{onClick:m},{default:z(()=>F[2]||(F[2]=[st("刷新目录")])),_:1})]),_:1})])):(Z(),ht("div",fo,[(Z(!0),ht(ni,null,Tn(f.value,A=>(Z(),ht("div",{key:A.path,class:pe(["file-item",{selected:s.selectedFile?.path===A.path}]),onClick:O=>p(A),onDblclick:O=>x(A)},[w("div",mo,[N(Mt,null,{default:z(()=>[A.is_directory?(Z(),oe(Q(qn),{key:1})):(Z(),oe(Q(Di),{key:0}))]),_:2},1024)]),w("div",go,[w("div",{class:"file-name",title:A.name},dt(A.name),9,vo),w("div",yo,[w("span",xo,dt(T(A.size)),1),w("span",wo,dt(C(A.modified_time)),1)])]),w("div",_o,[N(W,{onClick:ue(O=>E(A.path),["stop"]),icon:Q(Li),size:"small",text:"",class:"file-action-btn"},null,8,["onClick","icon"])])],42,po))),128))])):(Z(),ht("div",co,[N(bt,{description:"请选择一个包含Markdown文件的目录"},{default:z(()=>[N(W,{type:"primary",onClick:g},{default:z(()=>F[1]||(F[1]=[st("选择目录")])),_:1})]),_:1})]))])),[[Dt,l.value]]),o.value?(Z(),ht("div",Mo,[w("span",null,"共 "+dt(c.value.length)+" 个Markdown文件",1),h.value?(Z(),ht("span",Eo,"，显示 "+dt(f.value.length)+" 个",1)):It("",!0)])):It("",!0)],2)}}},No=ui(Co,[["__scopeId","data-v-f7a8f1dd"]]),M={CHANGE_THEME:"changeTheme",CHANGE_LAYOUT:"changeLayout",MODE:{READONLY:"readonly",EDIT:"edit"},LAYOUT:{LOGICAL_STRUCTURE:"logicalStructure",LOGICAL_STRUCTURE_LEFT:"logicalStructureLeft",MIND_MAP:"mindMap",ORGANIZATION_STRUCTURE:"organizationStructure",CATALOG_ORGANIZATION:"catalogOrganization",TIMELINE:"timeline",TIMELINE2:"timeline2",FISHBONE:"fishbone",FISHBONE2:"fishbone2",RIGHT_FISHBONE:"rightFishbone",RIGHT_FISHBONE2:"rightFishbone2",VERTICAL_TIMELINE:"verticalTimeline",VERTICAL_TIMELINE2:"verticalTimeline2",VERTICAL_TIMELINE3:"verticalTimeline3"},DIR:{UP:"up",LEFT:"left",DOWN:"down",RIGHT:"right"},SHAPE:{RECTANGLE:"rectangle",DIAMOND:"diamond",PARALLELOGRAM:"parallelogram",ROUNDED_RECTANGLE:"roundedRectangle",OCTAGONAL_RECTANGLE:"octagonalRectangle",OUTER_TRIANGULAR_RECTANGLE:"outerTriangularRectangle",INNER_TRIANGULAR_RECTANGLE:"innerTriangularRectangle",ELLIPSE:"ellipse",CIRCLE:"circle"},MOUSE_WHEEL_ACTION:{ZOOM:"zoom",MOVE:"move"},INIT_ROOT_NODE_POSITION:{LEFT:"left",TOP:"top",RIGHT:"right",BOTTOM:"bottom",CENTER:"center"},LAYOUT_GROW_DIR:{LEFT:"left",TOP:"top",RIGHT:"right",BOTTOM:"bottom"},CREATE_NEW_NODE_BEHAVIOR:{DEFAULT:"default",NOT_ACTIVE:"notActive",ACTIVE_ONLY:"activeOnly"},TAG_PLACEMENT:{RIGHT:"right",BOTTOM:"bottom"},IMG_PLACEMENT:{LEFT:"left",TOP:"top",RIGHT:"right",BOTTOM:"bottom"}},es={[M.INIT_ROOT_NODE_POSITION.LEFT]:0,[M.INIT_ROOT_NODE_POSITION.TOP]:0,[M.INIT_ROOT_NODE_POSITION.RIGHT]:1,[M.INIT_ROOT_NODE_POSITION.BOTTOM]:1,[M.INIT_ROOT_NODE_POSITION.CENTER]:.5},is=[M.LAYOUT.LOGICAL_STRUCTURE,M.LAYOUT.LOGICAL_STRUCTURE_LEFT,M.LAYOUT.MIND_MAP,M.LAYOUT.CATALOG_ORGANIZATION,M.LAYOUT.ORGANIZATION_STRUCTURE,M.LAYOUT.TIMELINE,M.LAYOUT.TIMELINE2,M.LAYOUT.VERTICAL_TIMELINE,M.LAYOUT.VERTICAL_TIMELINE2,M.LAYOUT.VERTICAL_TIMELINE3,M.LAYOUT.FISHBONE,M.LAYOUT.FISHBONE2,M.LAYOUT.RIGHT_FISHBONE,M.LAYOUT.RIGHT_FISHBONE2],Ai=["text","image","imageTitle","imageSize","icon","tag","hyperlink","hyperlinkTitle","note","expand","isActive","generalization","richText","resetRichText","uid","activeStyle","associativeLineTargets","associativeLineTargetControlOffsets","associativeLinePoint","associativeLineText","attachmentUrl","attachmentName","notation","outerFrame","number","range","customLeft","customTop","customTextWidth","checkbox","dir","needUpdate","imgMap","nodeLink"],Ve={READ_CLIPBOARD_ERROR:"read_clipboard_error",PARSE_PASTE_DATA_ERROR:"parse_paste_data_error",CUSTOM_HANDLE_CLIPBOARD_TEXT_ERROR:"custom_handle_clipboard_text_error",LOAD_CLIPBOARD_IMAGE_ERROR:"load_clipboard_image_error",BEFORE_TEXT_EDIT_ERROR:"before_text_edit_error",EXPORT_ERROR:"export_error",EXPORT_LOAD_IMAGE_ERROR:"export_load_image_error",DATA_CHANGE_DETAIL_EVENT_ERROR:"data_change_detail_event_error"},To=`
  /* 鼠标hover和激活时渲染的矩形 */
  .smm-hover-node{
    display: none;
    opacity: 0.6;
    stroke-width: 1;
  }

  .smm-node:not(.smm-node-dragging):hover .smm-hover-node{
    display: block;
  }

  .smm-node.active .smm-hover-node, .smm-node-highlight .smm-hover-node{
    display: block;
    opacity: 1;
    stroke-width: 2;
  }

  .smm-text-node-wrap, .smm-expand-btn-text {
    user-select: none;
  }
`,Ri=1.2,So=["fontFamily","fontSize","fontWeight","fontStyle","textDecoration","color","textAlign"];class Lo{constructor(t={}){this.opt=t,this.mindMap=this.opt.mindMap,this.scale=1,this.sx=0,this.sy=0,this.x=0,this.y=0,this.firstDrag=!0,this.setTransformData(this.mindMap.opt.viewData),this.bind()}bind(){this.mindMap.keyCommand.addShortcut("Control+=",()=>{this.enlarge()}),this.mindMap.keyCommand.addShortcut("Control+-",()=>{this.narrow()}),this.mindMap.keyCommand.addShortcut("Control+i",()=>{this.fit()}),this.mindMap.event.on("mousedown",t=>{const{isDisableDrag:e,mousedownEventPreventDefault:i}=this.mindMap.opt;e||(i&&t.preventDefault(),this.sx=this.x,this.sy=this.y)}),this.mindMap.event.on("drag",(t,e)=>{t.ctrlKey||t.metaKey||this.mindMap.opt.isDisableDrag||(this.firstDrag&&(this.firstDrag=!1,this.mindMap.renderer.activeNodeList.length>0&&this.mindMap.execCommand("CLEAR_ACTIVE_NODE")),this.x=this.sx+e.mousemoveOffset.x,this.y=this.sy+e.mousemoveOffset.y,this.transform())}),this.mindMap.event.on("mouseup",()=>{this.firstDrag=!0}),this.mindMap.event.on("mousewheel",(t,e,i,n)=>{const{customHandleMousewheel:r,mousewheelAction:o,mouseScaleCenterUseMousePosition:a,mousewheelMoveStep:l,mousewheelZoomActionReverse:h,disableMouseWheelZoom:d,translateRatio:c}=this.mindMap.opt;if(r&&typeof r=="function")return r(t);if(o===M.MOUSE_WHEEL_ACTION.ZOOM||t.ctrlKey||t.metaKey){if(d)return;const{x:f,y:g}=this.mindMap.toPos(t.clientX,t.clientY),u=a?f:void 0,m=a?g:void 0;switch(n&&(e.includes(M.DIR.LEFT)||e.includes(M.DIR.RIGHT))&&(e=e.filter(p=>![M.DIR.LEFT,M.DIR.RIGHT].includes(p))),!0){case e.includes(M.DIR.UP):h?this.enlarge(u,m,n):this.narrow(u,m,n);break;case e.includes(M.DIR.DOWN):h?this.narrow(u,m,n):this.enlarge(u,m,n);break}}else{let f=0,g=0;n?(f=Math.abs(t.wheelDeltaX),g=Math.abs(t.wheelDeltaY)):f=g=l;let u=0,m=0;e.includes(M.DIR.DOWN)&&(m=-g),e.includes(M.DIR.UP)&&(m=g),e.includes(M.DIR.LEFT)&&(u=f),e.includes(M.DIR.RIGHT)&&(u=-f),this.translateXY(u*c,m*c)}}),this.mindMap.on("resize",()=>{this.checkNeedMindMapInCanvas()&&this.transform()})}getTransformData(){return{transform:this.mindMap.draw.transform(),state:{scale:this.scale,x:this.x,y:this.y,sx:this.sx,sy:this.sy}}}setTransformData(t){t&&(Object.keys(t.state).forEach(e=>{this[e]=t.state[e]}),this.mindMap.draw.transform({...t.transform}),this.mindMap.emit("view_data_change",this.getTransformData()),this.emitEvent("scale"),this.emitEvent("translate"))}translateXY(t,e){t===0&&e===0||(this.x+=t,this.y+=e,this.transform(),this.emitEvent("translate"))}translateX(t){t!==0&&(this.x+=t,this.transform(),this.emitEvent("translate"))}translateXTo(t){this.x=t,this.transform(),this.emitEvent("translate")}translateY(t){t!==0&&(this.y+=t,this.transform(),this.emitEvent("translate"))}translateYTo(t){this.y=t,this.transform(),this.emitEvent("translate")}transform(){try{this.limitMindMapInCanvas()}catch{}this.mindMap.draw.transform({origin:[0,0],scale:this.scale,translate:[this.x,this.y]}),this.mindMap.emit("view_data_change",this.getTransformData())}reset(){const t=this.scale!==1,e=this.x!==0||this.y!==0;this.scale=1,this.x=0,this.y=0,this.transform(),t&&this.emitEvent("scale"),e&&this.emitEvent("translate")}narrow(t,e,i){let{scaleRatio:n,minZoomRatio:r}=this.mindMap.opt;n=n/(i?5:1);const o=Math.max(this.scale-n,r/100);this.scaleInCenter(o,t,e),this.transform(),this.emitEvent("scale")}enlarge(t,e,i){let{scaleRatio:n,maxZoomRatio:r}=this.mindMap.opt;n=n/(i?5:1);let o=0;r===-1?o=this.scale+n:o=Math.min(this.scale+n,r/100),this.scaleInCenter(o,t,e),this.transform(),this.emitEvent("scale")}scaleInCenter(t,e,i){(e===void 0||i===void 0)&&(e=this.mindMap.width/2,i=this.mindMap.height/2);const n=this.scale,r=1-t/n,o=(e-this.x)*r,a=(i-this.y)*r;this.x+=o,this.y+=a,this.scale=t}setScale(t,e,i){e!==void 0&&i!==void 0?this.scaleInCenter(t,e,i):this.scale=t,this.transform(),this.emitEvent("scale")}fit(t=()=>{},e=!1,i){i=i===void 0?this.mindMap.opt.fitPadding:i;const n=this.mindMap.draw,r=n.transform(),o=t()||n.rbox(),a=o.width/r.scaleX,l=o.height/r.scaleY,h=a/l;let{width:d,height:c}=this.mindMap.elRect;d=d-i*2,c=c-i*2;const f=d/c;let g=0,u="";if(a<=d&&l<=c&&!e)g=1,u=1;else{let E=0;h>f?(E=d,u=2):(E=c*h,u=3),g=E/a}this.setScale(g);const m=t()||n.rbox();m.x-=this.mindMap.elRect.left,m.y-=this.mindMap.elRect.top;let p=0,x=0;u===1?(p=-m.x+i+(d-m.width)/2,x=-m.y+i+(c-m.height)/2):u===2?(p=-m.x+i,x=-m.y+i+(c-m.height)/2):u===3&&(p=-m.x+i+(d-m.width)/2,x=-m.y+i),this.translateXY(p,x)}checkNeedMindMapInCanvas(){if(this.mindMap.demonstrate&&this.mindMap.demonstrate.isInDemonstrate)return!1;const{isLimitMindMapInCanvasWhenHasScrollbar:t,isLimitMindMapInCanvas:e}=this.mindMap.opt;return this.mindMap.scrollbar?t:e}limitMindMapInCanvas(){if(!this.checkNeedMindMapInCanvas())return;let{scale:t,left:e,top:i,right:n,bottom:r}=this.getPositionLimit();const o=(this.mindMap.width-this.mindMap.initWidth)/2*t,a=(this.mindMap.height-this.mindMap.initHeight)/2*t,l=this.scale/t;e*=l,n*=l,i*=l,r*=l;const h=this.mindMap.width/2,d=this.mindMap.height/2,c=this.scale-1;e-=c*h-o,n-=c*h-o,i-=c*d-a,r-=c*d-a,this.x>e&&(this.x=e),this.x<n&&(this.x=n),this.y>i&&(this.y=i),this.y<r&&(this.y=r)}getPositionLimit(){const{scaleX:t,scaleY:e}=this.mindMap.draw.transform(),i=this.mindMap.draw.rbox(),n=this.mindMap.renderer.root.group.rbox(),r=this.mindMap.renderer.layout.getRootCenterOffset(n.width,n.height),o=n.x-i.x-r.x*t,a=n.x-i.x2-r.x*t,l=n.y-i.y-r.y*e,h=n.y-i.y2-r.y*e;return{scale:t,left:o,right:a,top:l,bottom:h}}emitEvent(t){switch(t){case"scale":this.mindMap.emit("scale",this.scale);case"translate":this.mindMap.emit("translate",this.x,this.y)}}}var Us={exports:{}};(function(s){var t=Object.prototype.hasOwnProperty,e="~";function i(){}Object.create&&(i.prototype=Object.create(null),new i().__proto__||(e=!1));function n(l,h,d){this.fn=l,this.context=h,this.once=d||!1}function r(l,h,d,c,f){if(typeof d!="function")throw new TypeError("The listener must be a function");var g=new n(d,c||l,f),u=e?e+h:h;return l._events[u]?l._events[u].fn?l._events[u]=[l._events[u],g]:l._events[u].push(g):(l._events[u]=g,l._eventsCount++),l}function o(l,h){--l._eventsCount===0?l._events=new i:delete l._events[h]}function a(){this._events=new i,this._eventsCount=0}a.prototype.eventNames=function(){var h=[],d,c;if(this._eventsCount===0)return h;for(c in d=this._events)t.call(d,c)&&h.push(e?c.slice(1):c);return Object.getOwnPropertySymbols?h.concat(Object.getOwnPropertySymbols(d)):h},a.prototype.listeners=function(h){var d=e?e+h:h,c=this._events[d];if(!c)return[];if(c.fn)return[c.fn];for(var f=0,g=c.length,u=new Array(g);f<g;f++)u[f]=c[f].fn;return u},a.prototype.listenerCount=function(h){var d=e?e+h:h,c=this._events[d];return c?c.fn?1:c.length:0},a.prototype.emit=function(h,d,c,f,g,u){var m=e?e+h:h;if(!this._events[m])return!1;var p=this._events[m],x=arguments.length,E,_;if(p.fn){switch(p.once&&this.removeListener(h,p.fn,void 0,!0),x){case 1:return p.fn.call(p.context),!0;case 2:return p.fn.call(p.context,d),!0;case 3:return p.fn.call(p.context,d,c),!0;case 4:return p.fn.call(p.context,d,c,f),!0;case 5:return p.fn.call(p.context,d,c,f,g),!0;case 6:return p.fn.call(p.context,d,c,f,g,u),!0}for(_=1,E=new Array(x-1);_<x;_++)E[_-1]=arguments[_];p.fn.apply(p.context,E)}else{var T=p.length,C;for(_=0;_<T;_++)switch(p[_].once&&this.removeListener(h,p[_].fn,void 0,!0),x){case 1:p[_].fn.call(p[_].context);break;case 2:p[_].fn.call(p[_].context,d);break;case 3:p[_].fn.call(p[_].context,d,c);break;case 4:p[_].fn.call(p[_].context,d,c,f);break;default:if(!E)for(C=1,E=new Array(x-1);C<x;C++)E[C-1]=arguments[C];p[_].fn.apply(p[_].context,E)}}return!0},a.prototype.on=function(h,d,c){return r(this,h,d,c,!1)},a.prototype.once=function(h,d,c){return r(this,h,d,c,!0)},a.prototype.removeListener=function(h,d,c,f){var g=e?e+h:h;if(!this._events[g])return this;if(!d)return o(this,g),this;var u=this._events[g];if(u.fn)u.fn===d&&(!f||u.once)&&(!c||u.context===c)&&o(this,g);else{for(var m=0,p=[],x=u.length;m<x;m++)(u[m].fn!==d||f&&!u[m].once||c&&u[m].context!==c)&&p.push(u[m]);p.length?this._events[g]=p.length===1?p[0]:p:o(this,g)}return this},a.prototype.removeAllListeners=function(h){var d;return h?(d=e?e+h:h,this._events[d]&&o(this,d)):(this._events=new i,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prefixed=e,a.EventEmitter=a,s.exports=a})(Us);var bo=Us.exports;const Do=zr(bo);class Ao extends Do{constructor(t={}){super(),this.opt=t,this.mindMap=t.mindMap,this.isLeftMousedown=!1,this.isRightMousedown=!1,this.isMiddleMousedown=!1,this.mousedownPos={x:0,y:0},this.mousemovePos={x:0,y:0},this.mousemoveOffset={x:0,y:0},this.bindFn(),this.bind()}bindFn(){this.onBodyMousedown=this.onBodyMousedown.bind(this),this.onBodyClick=this.onBodyClick.bind(this),this.onDrawClick=this.onDrawClick.bind(this),this.onMousedown=this.onMousedown.bind(this),this.onMousemove=this.onMousemove.bind(this),this.onMouseup=this.onMouseup.bind(this),this.onNodeMouseup=this.onNodeMouseup.bind(this),this.onMousewheel=this.onMousewheel.bind(this),this.onContextmenu=this.onContextmenu.bind(this),this.onSvgMousedown=this.onSvgMousedown.bind(this),this.onKeyup=this.onKeyup.bind(this),this.onMouseenter=this.onMouseenter.bind(this),this.onMouseleave=this.onMouseleave.bind(this)}bind(){document.body.addEventListener("mousedown",this.onBodyMousedown),document.body.addEventListener("click",this.onBodyClick),this.mindMap.svg.on("click",this.onDrawClick),this.mindMap.el.addEventListener("mousedown",this.onMousedown),this.mindMap.svg.on("mousedown",this.onSvgMousedown),window.addEventListener("mousemove",this.onMousemove),window.addEventListener("mouseup",this.onMouseup),this.on("node_mouseup",this.onNodeMouseup),this.mindMap.el.addEventListener("wheel",this.onMousewheel),this.mindMap.svg.on("contextmenu",this.onContextmenu),this.mindMap.svg.on("mouseenter",this.onMouseenter),this.mindMap.svg.on("mouseleave",this.onMouseleave),window.addEventListener("keyup",this.onKeyup)}unbind(){document.body.removeEventListener("mousedown",this.onBodyMousedown),document.body.removeEventListener("click",this.onBodyClick),this.mindMap.svg.off("click",this.onDrawClick),this.mindMap.el.removeEventListener("mousedown",this.onMousedown),window.removeEventListener("mousemove",this.onMousemove),window.removeEventListener("mouseup",this.onMouseup),this.off("node_mouseup",this.onNodeMouseup),this.mindMap.el.removeEventListener("wheel",this.onMousewheel),this.mindMap.svg.off("contextmenu",this.onContextmenu),this.mindMap.svg.off("mouseenter",this.onMouseenter),this.mindMap.svg.off("mouseleave",this.onMouseleave),window.removeEventListener("keyup",this.onKeyup)}onDrawClick(t){this.emit("draw_click",t)}onBodyMousedown(t){this.emit("body_mousedown",t)}onBodyClick(t){this.emit("body_click",t)}onSvgMousedown(t){this.emit("svg_mousedown",t)}onMousedown(t){t.which===1?this.isLeftMousedown=!0:t.which===3?this.isRightMousedown=!0:t.which===2&&(this.isMiddleMousedown=!0),this.mousedownPos.x=t.clientX,this.mousedownPos.y=t.clientY,this.emit("mousedown",t,this)}onMousemove(t){let{useLeftKeySelectionRightKeyDrag:e}=this.mindMap.opt;this.mousemovePos.x=t.clientX,this.mousemovePos.y=t.clientY,this.mousemoveOffset.x=t.clientX-this.mousedownPos.x,this.mousemoveOffset.y=t.clientY-this.mousedownPos.y,this.emit("mousemove",t,this),(this.isMiddleMousedown||(e?this.isRightMousedown:this.isLeftMousedown))&&(t.preventDefault(),this.emit("drag",t,this))}onMouseup(t){this.onNodeMouseup(),this.emit("mouseup",t,this)}onNodeMouseup(){this.isLeftMousedown=!1,this.isRightMousedown=!1,this.isMiddleMousedown=!1}onMousewheel(t){t.stopPropagation(),t.preventDefault();const e=[];t.deltaY<0&&e.push(M.DIR.UP),t.deltaY>0&&e.push(M.DIR.DOWN),t.deltaX<0&&e.push(M.DIR.LEFT),t.deltaX>0&&e.push(M.DIR.RIGHT);let i=!1;const{customCheckIsTouchPad:n}=this.mindMap.opt;typeof n=="function"?i=n(t):i=Math.abs(t.deltaY)<=10,this.emit("mousewheel",t,e,this,i)}onContextmenu(t){t.preventDefault(),!t.ctrlKey&&this.emit("contextmenu",t)}onKeyup(t){this.emit("keyup",t)}onMouseenter(t){this.emit("svg_mouseenter",t)}onMouseleave(t){this.emit("svg_mouseleave",t)}}var Ii=function(t){return Ro(t)&&!Io(t)};function Ro(s){return!!s&&typeof s=="object"}function Io(s){var t=Object.prototype.toString.call(s);return t==="[object RegExp]"||t==="[object Date]"||zo(s)}var Oo=typeof Symbol=="function"&&Symbol.for,ko=Oo?Symbol.for("react.element"):60103;function zo(s){return s.$$typeof===ko}function Fo(s){return Array.isArray(s)?[]:{}}function ri(s,t){var e=t&&t.clone===!0;return e&&Ii(s)?Xe(Fo(s),s,t):s}function ns(s,t,e){var i=s.slice();return t.forEach(function(n,r){typeof i[r]>"u"?i[r]=ri(n,e):Ii(n)?i[r]=Xe(s[r],n,e):s.indexOf(n)===-1&&i.push(ri(n,e))}),i}function Po(s,t,e){var i={};return Ii(s)&&Object.keys(s).forEach(function(n){i[n]=ri(s[n],e)}),Object.keys(t).forEach(function(n){!Ii(t[n])||!s[n]?i[n]=ri(t[n],e):i[n]=Xe(s[n],t[n],e)}),i}function Xe(s,t,e){var i=Array.isArray(t),n=Array.isArray(s),r=e||{arrayMerge:ns},o=i===n;if(o)if(i){var a=r.arrayMerge||ns;return a(s,t,e)}else return Po(s,t,e);else return ri(t,e)}Xe.all=function(t,e){if(!Array.isArray(t)||t.length<2)throw new Error("first argument should be an array with at least two elements");return t.reduce(function(i,n){return Xe(i,n,e)})};var Oi=Xe;let wi;const Bo=new Uint8Array(16);function $o(){if(!wi&&(wi=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!wi))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return wi(Bo)}const Ft=[];for(let s=0;s<256;++s)Ft.push((s+256).toString(16).slice(1));function Ho(s,t=0){return Ft[s[t+0]]+Ft[s[t+1]]+Ft[s[t+2]]+Ft[s[t+3]]+"-"+Ft[s[t+4]]+Ft[s[t+5]]+"-"+Ft[s[t+6]]+Ft[s[t+7]]+"-"+Ft[s[t+8]]+Ft[s[t+9]]+"-"+Ft[s[t+10]]+Ft[s[t+11]]+Ft[s[t+12]]+Ft[s[t+13]]+Ft[s[t+14]]+Ft[s[t+15]]}const Uo=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),ss={randomUUID:Uo};function Go(s,t,e){if(ss.randomUUID&&!s)return ss.randomUUID();s=s||{};const i=s.random||(s.rng||$o)();return i[6]=i[6]&15|64,i[8]=i[8]&63|128,Ho(i)}function bn(s){this.N=624,this.M=397,this.MATRIX_A=2567483615,this.UPPER_MASK=2147483648,this.LOWER_MASK=2147483647,this.mt=new Array(this.N),this.mti=this.N+1,this.init_genrand(s)}bn.prototype.init_genrand=function(s){for(this.mt[0]=s>>>0,this.mti=1;this.mti<this.N;this.mti++)s=this.mt[this.mti-1]^this.mt[this.mti-1]>>>30,this.mt[this.mti]=(((s&4294901760)>>>16)*1812433253<<16)+(s&65535)*1812433253+this.mti,this.mt[this.mti]>>>=0};bn.prototype.genrand_int32=function(){var s,t=new Array(0,this.MATRIX_A);if(this.mti>=this.N){var e;for(this.mti==this.N+1&&this.init_genrand(5489),e=0;e<this.N-this.M;e++)s=this.mt[e]&this.UPPER_MASK|this.mt[e+1]&this.LOWER_MASK,this.mt[e]=this.mt[e+this.M]^s>>>1^t[s&1];for(;e<this.N-1;e++)s=this.mt[e]&this.UPPER_MASK|this.mt[e+1]&this.LOWER_MASK,this.mt[e]=this.mt[e+(this.M-this.N)]^s>>>1^t[s&1];s=this.mt[this.N-1]&this.UPPER_MASK|this.mt[0]&this.LOWER_MASK,this.mt[this.N-1]=this.mt[this.M-1]^s>>>1^t[s&1],this.mti=0}return s=this.mt[this.mti++],s^=s>>>11,s^=s<<7&2636928640,s^=s<<15&4022730752,s^=s>>>18,s>>>0};const on={},Gs=[];function K(s,t){if(Array.isArray(s)){for(const e of s)K(e,t);return}if(typeof s=="object"){for(const e in s)K(e,s[e]);return}Ys(Object.getOwnPropertyNames(t)),on[s]=Object.assign(on[s]||{},t)}function jt(s){return on[s]||{}}function Yo(){return[...new Set(Gs)]}function Ys(s){Gs.push(...s)}function Dn(s,t){let e;const i=s.length,n=[];for(e=0;e<i;e++)n.push(t(s[e]));return n}function Wo(s,t){let e;const i=s.length,n=[];for(e=0;e<i;e++)t(s[e])&&n.push(s[e]);return n}function Vi(s){return s%360*Math.PI/180}function _i(s){return s.toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()})}function Vo(s){return s.replace(/([A-Z])/g,function(t,e){return"-"+e.toLowerCase()})}function Ws(s){return s.charAt(0).toUpperCase()+s.slice(1)}function qe(s,t,e,i){return(t==null||e==null)&&(i=i||s.bbox(),t==null?t=i.width/i.height*e:e==null&&(e=i.height/i.width*t)),{width:t,height:e}}function an(s,t){const e=s.origin;let i=s.ox!=null?s.ox:s.originX!=null?s.originX:"center",n=s.oy!=null?s.oy:s.originY!=null?s.originY:"center";e!=null&&([i,n]=Array.isArray(e)?e:typeof e=="object"?[e.x,e.y]:[e,e]);const r=typeof i=="string",o=typeof n=="string";if(r||o){const{height:a,width:l,x:h,y:d}=t.bbox();r&&(i=i.includes("left")?h:i.includes("right")?h+l:h+l/2),o&&(n=n.includes("top")?d:n.includes("bottom")?d+a:d+a/2)}return[i,n]}const An="http://www.w3.org/2000/svg",Xo="http://www.w3.org/1999/xhtml",Mi="http://www.w3.org/2000/xmlns/",fi="http://www.w3.org/1999/xlink",jo="http://svgjs.dev/svgjs",nt={window:typeof window>"u"?null:window,document:typeof document>"u"?null:document};let Rn=class{};const be={},In="___SYMBOL___ROOT___";function oi(s,t=An){return nt.document.createElementNS(t,s)}function Xt(s,t=!1){if(s instanceof Rn)return s;if(typeof s=="object")return Xi(s);if(s==null)return new be[In];if(typeof s=="string"&&s.charAt(0)!=="<")return Xi(nt.document.querySelector(s));const e=t?nt.document.createElement("div"):oi("svg");return e.innerHTML=s,s=Xi(e.firstChild),e.removeChild(e.firstChild),s}function pt(s,t){return t&&t.ownerDocument&&t instanceof t.ownerDocument.defaultView.Node?t:oi(s)}function ie(s){if(!s)return null;if(s.instance instanceof Rn)return s.instance;if(s.nodeName==="#document-fragment")return new be.Fragment(s);let t=Ws(s.nodeName||"Dom");return t==="LinearGradient"||t==="RadialGradient"?t="Gradient":be[t]||(t="Dom"),new be[t](s)}let Xi=ie;function et(s,t=s.name,e=!1){return be[t]=s,e&&(be[In]=s),Ys(Object.getOwnPropertyNames(s.prototype)),s}function qo(s){return be[s]}let Ko=1e3;function Vs(s){return"Svgjs"+Ws(s)+Ko++}function Xs(s){for(let t=s.children.length-1;t>=0;t--)Xs(s.children[t]);return s.id&&(s.id=Vs(s.nodeName)),s}function J(s,t){let e,i;for(s=Array.isArray(s)?s:[s],i=s.length-1;i>=0;i--)for(e in t)s[i].prototype[e]=t[e]}function ft(s){return function(...t){const e=t[t.length-1];return e&&e.constructor===Object&&!(e instanceof Array)?s.apply(this,t.slice(0,-1)).attr(e):s.apply(this,t)}}function Zo(){return this.parent().children()}function Jo(){return this.parent().index(this)}function Qo(){return this.siblings()[this.position()+1]}function ta(){return this.siblings()[this.position()-1]}function ea(){const s=this.position();return this.parent().add(this.remove(),s+1),this}function ia(){const s=this.position();return this.parent().add(this.remove(),s?s-1:0),this}function na(){return this.parent().add(this.remove()),this}function sa(){return this.parent().add(this.remove(),0),this}function ra(s){s=Xt(s),s.remove();const t=this.position();return this.parent().add(s,t),this}function oa(s){s=Xt(s),s.remove();const t=this.position();return this.parent().add(s,t+1),this}function aa(s){return s=Xt(s),s.before(this),this}function la(s){return s=Xt(s),s.after(this),this}K("Dom",{siblings:Zo,position:Jo,next:Qo,prev:ta,forward:ea,backward:ia,front:na,back:sa,before:ra,after:oa,insertBefore:aa,insertAfter:la});const js=/^([+-]?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?)([a-z%]*)$/i,ha=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,da=/rgb\((\d+),(\d+),(\d+)\)/,ca=/(#[a-z_][a-z0-9\-_]*)/i,ua=/\)\s*,?\s*/,fa=/\s/g,rs=/^#[a-f0-9]{3}$|^#[a-f0-9]{6}$/i,os=/^rgb\(/,as=/^(\s+)?$/,ls=/^[+-]?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,pa=/\.(jpg|jpeg|png|gif|svg)(\?[^=]+.*)?/i,me=/[\s,]+/,On=/[MLHVCSQTAZ]/i;function ma(){const s=this.attr("class");return s==null?[]:s.trim().split(me)}function ga(s){return this.classes().indexOf(s)!==-1}function va(s){if(!this.hasClass(s)){const t=this.classes();t.push(s),this.attr("class",t.join(" "))}return this}function ya(s){return this.hasClass(s)&&this.attr("class",this.classes().filter(function(t){return t!==s}).join(" ")),this}function xa(s){return this.hasClass(s)?this.removeClass(s):this.addClass(s)}K("Dom",{classes:ma,hasClass:ga,addClass:va,removeClass:ya,toggleClass:xa});function wa(s,t){const e={};if(arguments.length===0)return this.node.style.cssText.split(/\s*;\s*/).filter(function(i){return!!i.length}).forEach(function(i){const n=i.split(/\s*:\s*/);e[n[0]]=n[1]}),e;if(arguments.length<2){if(Array.isArray(s)){for(const i of s){const n=_i(i);e[i]=this.node.style[n]}return e}if(typeof s=="string")return this.node.style[_i(s)];if(typeof s=="object")for(const i in s)this.node.style[_i(i)]=s[i]==null||as.test(s[i])?"":s[i]}return arguments.length===2&&(this.node.style[_i(s)]=t==null||as.test(t)?"":t),this}function _a(){return this.css("display","")}function Ma(){return this.css("display","none")}function Ea(){return this.css("display")!=="none"}K("Dom",{css:wa,show:_a,hide:Ma,visible:Ea});function Ca(s,t,e){if(s==null)return this.data(Dn(Wo(this.node.attributes,i=>i.nodeName.indexOf("data-")===0),i=>i.nodeName.slice(5)));if(s instanceof Array){const i={};for(const n of s)i[n]=this.data(n);return i}else if(typeof s=="object")for(t in s)this.data(t,s[t]);else if(arguments.length<2)try{return JSON.parse(this.attr("data-"+s))}catch{return this.attr("data-"+s)}else this.attr("data-"+s,t===null?null:e===!0||typeof t=="string"||typeof t=="number"?t:JSON.stringify(t));return this}K("Dom",{data:Ca});function Na(s,t){if(typeof arguments[0]=="object")for(const e in s)this.remember(e,s[e]);else{if(arguments.length===1)return this.memory()[s];this.memory()[s]=t}return this}function Ta(){if(arguments.length===0)this._memory={};else for(let s=arguments.length-1;s>=0;s--)delete this.memory()[arguments[s]];return this}function Sa(){return this._memory=this._memory||{}}K("Dom",{remember:Na,forget:Ta,memory:Sa});function La(s){return s.length===4?["#",s.substring(1,2),s.substring(1,2),s.substring(2,3),s.substring(2,3),s.substring(3,4),s.substring(3,4)].join(""):s}function ba(s){const t=Math.round(s),i=Math.max(0,Math.min(255,t)).toString(16);return i.length===1?"0"+i:i}function Be(s,t){for(let e=t.length;e--;)if(s[t[e]]==null)return!1;return!0}function Da(s,t){const e=Be(s,"rgb")?{_a:s.r,_b:s.g,_c:s.b,_d:0,space:"rgb"}:Be(s,"xyz")?{_a:s.x,_b:s.y,_c:s.z,_d:0,space:"xyz"}:Be(s,"hsl")?{_a:s.h,_b:s.s,_c:s.l,_d:0,space:"hsl"}:Be(s,"lab")?{_a:s.l,_b:s.a,_c:s.b,_d:0,space:"lab"}:Be(s,"lch")?{_a:s.l,_b:s.c,_c:s.h,_d:0,space:"lch"}:Be(s,"cmyk")?{_a:s.c,_b:s.m,_c:s.y,_d:s.k,space:"cmyk"}:{_a:0,_b:0,_c:0,space:"rgb"};return e.space=t||e.space,e}function Aa(s){return s==="lab"||s==="xyz"||s==="lch"}function ji(s,t,e){return e<0&&(e+=1),e>1&&(e-=1),e<1/6?s+(t-s)*6*e:e<1/2?t:e<2/3?s+(t-s)*(2/3-e)*6:s}class lt{constructor(...t){this.init(...t)}static isColor(t){return t&&(t instanceof lt||this.isRgb(t)||this.test(t))}static isRgb(t){return t&&typeof t.r=="number"&&typeof t.g=="number"&&typeof t.b=="number"}static random(t="vibrant",e,i){const{random:n,round:r,sin:o,PI:a}=Math;if(t==="vibrant"){const l=24*n()+57,h=38*n()+45,d=360*n();return new lt(l,h,d,"lch")}else if(t==="sine"){e=e??n();const l=r(80*o(2*a*e/.5+.01)+150),h=r(50*o(2*a*e/.5****)+200),d=r(100*o(2*a*e/.5****)+150);return new lt(l,h,d)}else if(t==="pastel"){const l=8*n()+86,h=17*n()+9,d=360*n();return new lt(l,h,d,"lch")}else if(t==="dark"){const l=10+10*n(),h=50*n()+86,d=360*n();return new lt(l,h,d,"lch")}else if(t==="rgb"){const l=255*n(),h=255*n(),d=255*n();return new lt(l,h,d)}else if(t==="lab"){const l=100*n(),h=256*n()-128,d=256*n()-128;return new lt(l,h,d,"lab")}else if(t==="grey"){const l=255*n();return new lt(l,l,l)}else throw new Error("Unsupported random color mode")}static test(t){return typeof t=="string"&&(rs.test(t)||os.test(t))}cmyk(){const{_a:t,_b:e,_c:i}=this.rgb(),[n,r,o]=[t,e,i].map(f=>f/255),a=Math.min(1-n,1-r,1-o);if(a===1)return new lt(0,0,0,1,"cmyk");const l=(1-n-a)/(1-a),h=(1-r-a)/(1-a),d=(1-o-a)/(1-a);return new lt(l,h,d,a,"cmyk")}hsl(){const{_a:t,_b:e,_c:i}=this.rgb(),[n,r,o]=[t,e,i].map(m=>m/255),a=Math.max(n,r,o),l=Math.min(n,r,o),h=(a+l)/2,d=a===l,c=a-l,f=d?0:h>.5?c/(2-a-l):c/(a+l),g=d?0:a===n?((r-o)/c+(r<o?6:0))/6:a===r?((o-n)/c+2)/6:a===o?((n-r)/c+4)/6:0;return new lt(360*g,100*f,100*h,"hsl")}init(t=0,e=0,i=0,n=0,r="rgb"){if(t=t||0,this.space)for(const c in this.space)delete this[this.space[c]];if(typeof t=="number")r=typeof n=="string"?n:r,n=typeof n=="string"?0:n,Object.assign(this,{_a:t,_b:e,_c:i,_d:n,space:r});else if(t instanceof Array)this.space=e||(typeof t[3]=="string"?t[3]:t[4])||"rgb",Object.assign(this,{_a:t[0],_b:t[1],_c:t[2],_d:t[3]||0});else if(t instanceof Object){const c=Da(t,e);Object.assign(this,c)}else if(typeof t=="string")if(os.test(t)){const c=t.replace(fa,""),[f,g,u]=da.exec(c).slice(1,4).map(m=>parseInt(m));Object.assign(this,{_a:f,_b:g,_c:u,_d:0,space:"rgb"})}else if(rs.test(t)){const c=m=>parseInt(m,16),[,f,g,u]=ha.exec(La(t)).map(c);Object.assign(this,{_a:f,_b:g,_c:u,_d:0,space:"rgb"})}else throw Error("Unsupported string format, can't construct Color");const{_a:o,_b:a,_c:l,_d:h}=this,d=this.space==="rgb"?{r:o,g:a,b:l}:this.space==="xyz"?{x:o,y:a,z:l}:this.space==="hsl"?{h:o,s:a,l}:this.space==="lab"?{l:o,a,b:l}:this.space==="lch"?{l:o,c:a,h:l}:this.space==="cmyk"?{c:o,m:a,y:l,k:h}:{};Object.assign(this,d)}lab(){const{x:t,y:e,z:i}=this.xyz(),n=116*e-16,r=500*(t-e),o=200*(e-i);return new lt(n,r,o,"lab")}lch(){const{l:t,a:e,b:i}=this.lab(),n=Math.sqrt(e**2+i**2);let r=180*Math.atan2(i,e)/Math.PI;return r<0&&(r*=-1,r=360-r),new lt(t,n,r,"lch")}rgb(){if(this.space==="rgb")return this;if(Aa(this.space)){let{x:t,y:e,z:i}=this;if(this.space==="lab"||this.space==="lch"){let{l:g,a:u,b:m}=this;if(this.space==="lch"){const{c:L,h:F}=this,W=Math.PI/180;u=L*Math.cos(W*F),m=L*Math.sin(W*F)}const p=(g+16)/116,x=u/500+p,E=p-m/200,_=16/116,T=.008856,C=7.787;t=.95047*(x**3>T?x**3:(x-_)/C),e=1*(p**3>T?p**3:(p-_)/C),i=1.08883*(E**3>T?E**3:(E-_)/C)}const n=t*3.2406+e*-1.5372+i*-.4986,r=t*-.9689+e*1.8758+i*.0415,o=t*.0557+e*-.204+i*1.057,a=Math.pow,l=.0031308,h=n>l?1.055*a(n,1/2.4)-.055:12.92*n,d=r>l?1.055*a(r,1/2.4)-.055:12.92*r,c=o>l?1.055*a(o,1/2.4)-.055:12.92*o;return new lt(255*h,255*d,255*c)}else if(this.space==="hsl"){let{h:t,s:e,l:i}=this;if(t/=360,e/=100,i/=100,e===0)return i*=255,new lt(i,i,i);const n=i<.5?i*(1+e):i+e-i*e,r=2*i-n,o=255*ji(r,n,t+1/3),a=255*ji(r,n,t),l=255*ji(r,n,t-1/3);return new lt(o,a,l)}else if(this.space==="cmyk"){const{c:t,m:e,y:i,k:n}=this,r=255*(1-Math.min(1,t*(1-n)+n)),o=255*(1-Math.min(1,e*(1-n)+n)),a=255*(1-Math.min(1,i*(1-n)+n));return new lt(r,o,a)}else return this}toArray(){const{_a:t,_b:e,_c:i,_d:n,space:r}=this;return[t,e,i,n,r]}toHex(){const[t,e,i]=this._clamped().map(ba);return`#${t}${e}${i}`}toRgb(){const[t,e,i]=this._clamped();return`rgb(${t},${e},${i})`}toString(){return this.toHex()}xyz(){const{_a:t,_b:e,_c:i}=this.rgb(),[n,r,o]=[t,e,i].map(x=>x/255),a=n>.04045?Math.pow((n+.055)/1.055,2.4):n/12.92,l=r>.04045?Math.pow((r+.055)/1.055,2.4):r/12.92,h=o>.04045?Math.pow((o+.055)/1.055,2.4):o/12.92,d=(a*.4124+l*.3576+h*.1805)/.95047,c=(a*.2126+l*.7152+h*.0722)/1,f=(a*.0193+l*.1192+h*.9505)/1.08883,g=d>.008856?Math.pow(d,1/3):7.787*d+16/116,u=c>.008856?Math.pow(c,1/3):7.787*c+16/116,m=f>.008856?Math.pow(f,1/3):7.787*f+16/116;return new lt(g,u,m,"xyz")}_clamped(){const{_a:t,_b:e,_c:i}=this.rgb(),{max:n,min:r,round:o}=Math,a=l=>n(0,r(o(l),255));return[t,e,i].map(a)}}class _t{constructor(...t){this.init(...t)}clone(){return new _t(this)}init(t,e){const i={x:0,y:0},n=Array.isArray(t)?{x:t[0],y:t[1]}:typeof t=="object"?{x:t.x,y:t.y}:{x:t,y:e};return this.x=n.x==null?i.x:n.x,this.y=n.y==null?i.y:n.y,this}toArray(){return[this.x,this.y]}transform(t){return this.clone().transformO(t)}transformO(t){G.isMatrixLike(t)||(t=new G(t));const{x:e,y:i}=this;return this.x=t.a*e+t.c*i+t.e,this.y=t.b*e+t.d*i+t.f,this}}function Ra(s,t){return new _t(s,t).transformO(this.screenCTM().inverseO())}function $e(s,t,e){return Math.abs(t-s)<1e-6}class G{constructor(...t){this.init(...t)}static formatTransforms(t){const e=t.flip==="both"||t.flip===!0,i=t.flip&&(e||t.flip==="x")?-1:1,n=t.flip&&(e||t.flip==="y")?-1:1,r=t.skew&&t.skew.length?t.skew[0]:isFinite(t.skew)?t.skew:isFinite(t.skewX)?t.skewX:0,o=t.skew&&t.skew.length?t.skew[1]:isFinite(t.skew)?t.skew:isFinite(t.skewY)?t.skewY:0,a=t.scale&&t.scale.length?t.scale[0]*i:isFinite(t.scale)?t.scale*i:isFinite(t.scaleX)?t.scaleX*i:i,l=t.scale&&t.scale.length?t.scale[1]*n:isFinite(t.scale)?t.scale*n:isFinite(t.scaleY)?t.scaleY*n:n,h=t.shear||0,d=t.rotate||t.theta||0,c=new _t(t.origin||t.around||t.ox||t.originX,t.oy||t.originY),f=c.x,g=c.y,u=new _t(t.position||t.px||t.positionX||NaN,t.py||t.positionY||NaN),m=u.x,p=u.y,x=new _t(t.translate||t.tx||t.translateX,t.ty||t.translateY),E=x.x,_=x.y,T=new _t(t.relative||t.rx||t.relativeX,t.ry||t.relativeY),C=T.x,L=T.y;return{scaleX:a,scaleY:l,skewX:r,skewY:o,shear:h,theta:d,rx:C,ry:L,tx:E,ty:_,ox:f,oy:g,px:m,py:p}}static fromArray(t){return{a:t[0],b:t[1],c:t[2],d:t[3],e:t[4],f:t[5]}}static isMatrixLike(t){return t.a!=null||t.b!=null||t.c!=null||t.d!=null||t.e!=null||t.f!=null}static matrixMultiply(t,e,i){const n=t.a*e.a+t.c*e.b,r=t.b*e.a+t.d*e.b,o=t.a*e.c+t.c*e.d,a=t.b*e.c+t.d*e.d,l=t.e+t.a*e.e+t.c*e.f,h=t.f+t.b*e.e+t.d*e.f;return i.a=n,i.b=r,i.c=o,i.d=a,i.e=l,i.f=h,i}around(t,e,i){return this.clone().aroundO(t,e,i)}aroundO(t,e,i){const n=t||0,r=e||0;return this.translateO(-n,-r).lmultiplyO(i).translateO(n,r)}clone(){return new G(this)}decompose(t=0,e=0){const i=this.a,n=this.b,r=this.c,o=this.d,a=this.e,l=this.f,h=i*o-n*r,d=h>0?1:-1,c=d*Math.sqrt(i*i+n*n),f=Math.atan2(d*n,d*i),g=180/Math.PI*f,u=Math.cos(f),m=Math.sin(f),p=(i*r+n*o)/h,x=r*c/(p*i-n)||o*c/(p*n+i),E=a-t+t*u*c+e*(p*u*c-m*x),_=l-e+t*m*c+e*(p*m*c+u*x);return{scaleX:c,scaleY:x,shear:p,rotate:g,translateX:E,translateY:_,originX:t,originY:e,a:this.a,b:this.b,c:this.c,d:this.d,e:this.e,f:this.f}}equals(t){if(t===this)return!0;const e=new G(t);return $e(this.a,e.a)&&$e(this.b,e.b)&&$e(this.c,e.c)&&$e(this.d,e.d)&&$e(this.e,e.e)&&$e(this.f,e.f)}flip(t,e){return this.clone().flipO(t,e)}flipO(t,e){return t==="x"?this.scaleO(-1,1,e,0):t==="y"?this.scaleO(1,-1,0,e):this.scaleO(-1,-1,t,e||t)}init(t){const e=G.fromArray([1,0,0,1,0,0]);return t=t instanceof de?t.matrixify():typeof t=="string"?G.fromArray(t.split(me).map(parseFloat)):Array.isArray(t)?G.fromArray(t):typeof t=="object"&&G.isMatrixLike(t)?t:typeof t=="object"?new G().transform(t):arguments.length===6?G.fromArray([].slice.call(arguments)):e,this.a=t.a!=null?t.a:e.a,this.b=t.b!=null?t.b:e.b,this.c=t.c!=null?t.c:e.c,this.d=t.d!=null?t.d:e.d,this.e=t.e!=null?t.e:e.e,this.f=t.f!=null?t.f:e.f,this}inverse(){return this.clone().inverseO()}inverseO(){const t=this.a,e=this.b,i=this.c,n=this.d,r=this.e,o=this.f,a=t*n-e*i;if(!a)throw new Error("Cannot invert "+this);const l=n/a,h=-e/a,d=-i/a,c=t/a,f=-(l*r+d*o),g=-(h*r+c*o);return this.a=l,this.b=h,this.c=d,this.d=c,this.e=f,this.f=g,this}lmultiply(t){return this.clone().lmultiplyO(t)}lmultiplyO(t){const e=this,i=t instanceof G?t:new G(t);return G.matrixMultiply(i,e,this)}multiply(t){return this.clone().multiplyO(t)}multiplyO(t){const e=this,i=t instanceof G?t:new G(t);return G.matrixMultiply(e,i,this)}rotate(t,e,i){return this.clone().rotateO(t,e,i)}rotateO(t,e=0,i=0){t=Vi(t);const n=Math.cos(t),r=Math.sin(t),{a:o,b:a,c:l,d:h,e:d,f:c}=this;return this.a=o*n-a*r,this.b=a*n+o*r,this.c=l*n-h*r,this.d=h*n+l*r,this.e=d*n-c*r+i*r-e*n+e,this.f=c*n+d*r-e*r-i*n+i,this}scale(t,e,i,n){return this.clone().scaleO(...arguments)}scaleO(t,e=t,i=0,n=0){arguments.length===3&&(n=i,i=e,e=t);const{a:r,b:o,c:a,d:l,e:h,f:d}=this;return this.a=r*t,this.b=o*e,this.c=a*t,this.d=l*e,this.e=h*t-i*t+i,this.f=d*e-n*e+n,this}shear(t,e,i){return this.clone().shearO(t,e,i)}shearO(t,e=0,i=0){const{a:n,b:r,c:o,d:a,e:l,f:h}=this;return this.a=n+r*t,this.c=o+a*t,this.e=l+h*t-i*t,this}skew(t,e,i,n){return this.clone().skewO(...arguments)}skewO(t,e=t,i=0,n=0){arguments.length===3&&(n=i,i=e,e=t),t=Vi(t),e=Vi(e);const r=Math.tan(t),o=Math.tan(e),{a,b:l,c:h,d,e:c,f}=this;return this.a=a+l*r,this.b=l+a*o,this.c=h+d*r,this.d=d+h*o,this.e=c+f*r-n*r,this.f=f+c*o-i*o,this}skewX(t,e,i){return this.skew(t,0,e,i)}skewY(t,e,i){return this.skew(0,t,e,i)}toArray(){return[this.a,this.b,this.c,this.d,this.e,this.f]}toString(){return"matrix("+this.a+","+this.b+","+this.c+","+this.d+","+this.e+","+this.f+")"}transform(t){if(G.isMatrixLike(t))return new G(t).multiplyO(this);const e=G.formatTransforms(t),i=this,{x:n,y:r}=new _t(e.ox,e.oy).transform(i),o=new G().translateO(e.rx,e.ry).lmultiplyO(i).translateO(-n,-r).scaleO(e.scaleX,e.scaleY).skewO(e.skewX,e.skewY).shearO(e.shear).rotateO(e.theta).translateO(n,r);if(isFinite(e.px)||isFinite(e.py)){const a=new _t(n,r).transform(o),l=isFinite(e.px)?e.px-a.x:0,h=isFinite(e.py)?e.py-a.y:0;o.translateO(l,h)}return o.translateO(e.tx,e.ty),o}translate(t,e){return this.clone().translateO(t,e)}translateO(t,e){return this.e+=t||0,this.f+=e||0,this}valueOf(){return{a:this.a,b:this.b,c:this.c,d:this.d,e:this.e,f:this.f}}}function Ia(){return new G(this.node.getCTM())}function Oa(){if(typeof this.isRoot=="function"&&!this.isRoot()){const s=this.rect(1,1),t=s.node.getScreenCTM();return s.remove(),new G(t)}return new G(this.node.getScreenCTM())}et(G,"Matrix");function ye(){if(!ye.nodes){const s=Xt().size(2,0);s.node.style.cssText=["opacity: 0","position: absolute","left: -100%","top: -100%","overflow: hidden"].join(";"),s.attr("focusable","false"),s.attr("aria-hidden","true");const t=s.path().node;ye.nodes={svg:s,path:t}}if(!ye.nodes.svg.node.parentNode){const s=nt.document.body||nt.document.documentElement;ye.nodes.svg.addTo(s)}return ye.nodes}function qs(s){return!s.width&&!s.height&&!s.x&&!s.y}function ka(s){return s===nt.document||(nt.document.documentElement.contains||function(t){for(;t.parentNode;)t=t.parentNode;return t===nt.document}).call(nt.document.documentElement,s)}class Ut{constructor(...t){this.init(...t)}addOffset(){return this.x+=nt.window.pageXOffset,this.y+=nt.window.pageYOffset,new Ut(this)}init(t){const e=[0,0,0,0];return t=typeof t=="string"?t.split(me).map(parseFloat):Array.isArray(t)?t:typeof t=="object"?[t.left!=null?t.left:t.x,t.top!=null?t.top:t.y,t.width,t.height]:arguments.length===4?[].slice.call(arguments):e,this.x=t[0]||0,this.y=t[1]||0,this.width=this.w=t[2]||0,this.height=this.h=t[3]||0,this.x2=this.x+this.w,this.y2=this.y+this.h,this.cx=this.x+this.w/2,this.cy=this.y+this.h/2,this}isNulled(){return qs(this)}merge(t){const e=Math.min(this.x,t.x),i=Math.min(this.y,t.y),n=Math.max(this.x+this.width,t.x+t.width)-e,r=Math.max(this.y+this.height,t.y+t.height)-i;return new Ut(e,i,n,r)}toArray(){return[this.x,this.y,this.width,this.height]}toString(){return this.x+" "+this.y+" "+this.width+" "+this.height}transform(t){t instanceof G||(t=new G(t));let e=1/0,i=-1/0,n=1/0,r=-1/0;return[new _t(this.x,this.y),new _t(this.x2,this.y),new _t(this.x,this.y2),new _t(this.x2,this.y2)].forEach(function(a){a=a.transform(t),e=Math.min(e,a.x),i=Math.max(i,a.x),n=Math.min(n,a.y),r=Math.max(r,a.y)}),new Ut(e,n,i-e,r-n)}}function Ks(s,t,e){let i;try{if(i=t(s.node),qs(i)&&!ka(s.node))throw new Error("Element not in the dom")}catch{i=e(s)}return i}function za(){const e=Ks(this,n=>n.getBBox(),n=>{try{const r=n.clone().addTo(ye().svg).show(),o=r.node.getBBox();return r.remove(),o}catch(r){throw new Error(`Getting bbox of element "${n.node.nodeName}" is not possible: ${r.toString()}`)}});return new Ut(e)}function Fa(s){const i=Ks(this,r=>r.getBoundingClientRect(),r=>{throw new Error(`Getting rbox of element "${r.node.nodeName}" is not possible`)}),n=new Ut(i);return s?n.transform(s.screenCTM().inverseO()):n.addOffset()}function Pa(s,t){const e=this.bbox();return s>e.x&&t>e.y&&s<e.x+e.width&&t<e.y+e.height}K({viewbox:{viewbox(s,t,e,i){return s==null?new Ut(this.attr("viewBox")):this.attr("viewBox",new Ut(s,t,e,i))},zoom(s,t){let{width:e,height:i}=this.attr(["width","height"]);if((!e&&!i||typeof e=="string"||typeof i=="string")&&(e=this.node.clientWidth,i=this.node.clientHeight),!e||!i)throw new Error("Impossible to get absolute width and height. Please provide an absolute width and height attribute on the zooming element");const n=this.viewbox(),r=e/n.width,o=i/n.height,a=Math.min(r,o);if(s==null)return a;let l=a/s;l===1/0&&(l=Number.MAX_SAFE_INTEGER/100),t=t||new _t(e/2/r+n.x,i/2/o+n.y);const h=new Ut(n).transform(new G({scale:l,origin:t}));return this.viewbox(h)}}});et(Ut,"Box");class Ae extends Array{constructor(t=[],...e){if(super(t,...e),typeof t=="number")return this;this.length=0,this.push(...t)}}J([Ae],{each(s,...t){return typeof s=="function"?this.map((e,i,n)=>s.call(e,e,i,n)):this.map(e=>e[s](...t))},toArray(){return Array.prototype.concat.apply([],this)}});const Ba=["toArray","constructor","each"];Ae.extend=function(s){s=s.reduce((t,e)=>(Ba.includes(e)||e[0]==="_"||(t[e]=function(...i){return this.each(e,...i)}),t),{}),J([Ae],s)};function Ke(s,t){return new Ae(Dn((t||nt.document).querySelectorAll(s),function(e){return ie(e)}))}function $a(s){return Ke(s,this.node)}function Ha(s){return ie(this.node.querySelector(s))}let Ua=0;const Zs={};function Js(s){let t=s.getEventHolder();return t===nt.window&&(t=Zs),t.events||(t.events={}),t.events}function kn(s){return s.getEventTarget()}function Ga(s){let t=s.getEventHolder();t===nt.window&&(t=Zs),t.events&&(t.events={})}function ln(s,t,e,i,n){const r=e.bind(i||s),o=Xt(s),a=Js(o),l=kn(o);t=Array.isArray(t)?t:t.split(me),e._svgjsListenerId||(e._svgjsListenerId=++Ua),t.forEach(function(h){const d=h.split(".")[0],c=h.split(".")[1]||"*";a[d]=a[d]||{},a[d][c]=a[d][c]||{},a[d][c][e._svgjsListenerId]=r,l.addEventListener(d,r,n||!1)})}function He(s,t,e,i){const n=Xt(s),r=Js(n),o=kn(n);typeof e=="function"&&(e=e._svgjsListenerId,!e)||(t=Array.isArray(t)?t:(t||"").split(me),t.forEach(function(a){const l=a&&a.split(".")[0],h=a&&a.split(".")[1];let d,c;if(e)r[l]&&r[l][h||"*"]&&(o.removeEventListener(l,r[l][h||"*"][e],i||!1),delete r[l][h||"*"][e]);else if(l&&h){if(r[l]&&r[l][h]){for(c in r[l][h])He(o,[l,h].join("."),c);delete r[l][h]}}else if(h)for(a in r)for(d in r[a])h===d&&He(o,[a,h].join("."));else if(l){if(r[l]){for(d in r[l])He(o,[l,d].join("."));delete r[l]}}else{for(a in r)He(o,a);Ga(n)}}))}function Ya(s,t,e,i){const n=kn(s);return t instanceof nt.window.Event||(t=new nt.window.CustomEvent(t,{detail:e,cancelable:!0,...i})),n.dispatchEvent(t),t}class pi extends Rn{addEventListener(){}dispatch(t,e,i){return Ya(this,t,e,i)}dispatchEvent(t){const e=this.getEventHolder().events;if(!e)return!0;const i=e[t.type];for(const n in i)for(const r in i[n])i[n][r](t);return!t.defaultPrevented}fire(t,e,i){return this.dispatch(t,e,i),this}getEventHolder(){return this}getEventTarget(){return this}off(t,e,i){return He(this,t,e,i),this}on(t,e,i,n){return ln(this,t,e,i,n),this}removeEventListener(){}}et(pi,"EventTarget");function hs(){}const ti={duration:400,ease:">",delay:0},Wa={"fill-opacity":1,"stroke-opacity":1,"stroke-width":0,"stroke-linejoin":"miter","stroke-linecap":"butt",fill:"#000000",stroke:"#000000",opacity:1,x:0,y:0,cx:0,cy:0,width:0,height:0,r:0,rx:0,ry:0,offset:0,"stop-opacity":1,"stop-color":"#000000","text-anchor":"start"};class je extends Array{constructor(...t){super(...t),this.init(...t)}clone(){return new this.constructor(this)}init(t){return typeof t=="number"?this:(this.length=0,this.push(...this.parse(t)),this)}parse(t=[]){return t instanceof Array?t:t.trim().split(me).map(parseFloat)}toArray(){return Array.prototype.concat.apply([],this)}toSet(){return new Set(this)}toString(){return this.join(" ")}valueOf(){const t=[];return t.push(...this),t}}class V{constructor(...t){this.init(...t)}convert(t){return new V(this.value,t)}divide(t){return t=new V(t),new V(this/t,this.unit||t.unit)}init(t,e){return e=Array.isArray(t)?t[1]:e,t=Array.isArray(t)?t[0]:t,this.value=0,this.unit=e||"",typeof t=="number"?this.value=isNaN(t)?0:isFinite(t)?t:t<0?-34e37:34e37:typeof t=="string"?(e=t.match(js),e&&(this.value=parseFloat(e[1]),e[5]==="%"?this.value/=100:e[5]==="s"&&(this.value*=1e3),this.unit=e[5])):t instanceof V&&(this.value=t.valueOf(),this.unit=t.unit),this}minus(t){return t=new V(t),new V(this-t,this.unit||t.unit)}plus(t){return t=new V(t),new V(this+t,this.unit||t.unit)}times(t){return t=new V(t),new V(this*t,this.unit||t.unit)}toArray(){return[this.value,this.unit]}toJSON(){return this.toString()}toString(){return(this.unit==="%"?~~(this.value*1e8)/1e6:this.unit==="s"?this.value/1e3:this.value)+this.unit}valueOf(){return this.value}}const Qs=[];function Va(s){Qs.push(s)}function Xa(s,t,e){if(s==null){s={},t=this.node.attributes;for(const i of t)s[i.nodeName]=ls.test(i.nodeValue)?parseFloat(i.nodeValue):i.nodeValue;return s}else{if(s instanceof Array)return s.reduce((i,n)=>(i[n]=this.attr(n),i),{});if(typeof s=="object"&&s.constructor===Object)for(t in s)this.attr(t,s[t]);else if(t===null)this.node.removeAttribute(s);else{if(t==null)return t=this.node.getAttribute(s),t==null?Wa[s]:ls.test(t)?parseFloat(t):t;t=Qs.reduce((i,n)=>n(s,i,this),t),typeof t=="number"?t=new V(t):lt.isColor(t)?t=new lt(t):t.constructor===Array&&(t=new je(t)),s==="leading"?this.leading&&this.leading(t):typeof e=="string"?this.node.setAttributeNS(e,s,t.toString()):this.node.setAttribute(s,t.toString()),this.rebuild&&(s==="font-size"||s==="x")&&this.rebuild()}}return this}class we extends pi{constructor(t,e){super(),this.node=t,this.type=t.nodeName,e&&t!==e&&this.attr(e)}add(t,e){return t=Xt(t),t.removeNamespace&&this.node instanceof nt.window.SVGElement&&t.removeNamespace(),e==null?this.node.appendChild(t.node):t.node!==this.node.childNodes[e]&&this.node.insertBefore(t.node,this.node.childNodes[e]),this}addTo(t,e){return Xt(t).put(this,e)}children(){return new Ae(Dn(this.node.children,function(t){return ie(t)}))}clear(){for(;this.node.hasChildNodes();)this.node.removeChild(this.node.lastChild);return this}clone(t=!0,e=!0){this.writeDataToDom();let i=this.node.cloneNode(t);return e&&(i=Xs(i)),new this.constructor(i)}each(t,e){const i=this.children();let n,r;for(n=0,r=i.length;n<r;n++)t.apply(i[n],[n,i]),e&&i[n].each(t,e);return this}element(t,e){return this.put(new we(oi(t),e))}first(){return ie(this.node.firstChild)}get(t){return ie(this.node.childNodes[t])}getEventHolder(){return this.node}getEventTarget(){return this.node}has(t){return this.index(t)>=0}html(t,e){return this.xml(t,e,Xo)}id(t){return typeof t>"u"&&!this.node.id&&(this.node.id=Vs(this.type)),this.attr("id",t)}index(t){return[].slice.call(this.node.childNodes).indexOf(t.node)}last(){return ie(this.node.lastChild)}matches(t){const e=this.node,i=e.matches||e.matchesSelector||e.msMatchesSelector||e.mozMatchesSelector||e.webkitMatchesSelector||e.oMatchesSelector||null;return i&&i.call(e,t)}parent(t){let e=this;if(!e.node.parentNode)return null;if(e=ie(e.node.parentNode),!t)return e;do if(typeof t=="string"?e.matches(t):e instanceof t)return e;while(e=ie(e.node.parentNode));return e}put(t,e){return t=Xt(t),this.add(t,e),t}putIn(t,e){return Xt(t).add(this,e)}remove(){return this.parent()&&this.parent().removeElement(this),this}removeElement(t){return this.node.removeChild(t.node),this}replace(t){return t=Xt(t),this.node.parentNode&&this.node.parentNode.replaceChild(t.node,this.node),t}round(t=2,e=null){const i=10**t,n=this.attr(e);for(const r in n)typeof n[r]=="number"&&(n[r]=Math.round(n[r]*i)/i);return this.attr(n),this}svg(t,e){return this.xml(t,e,An)}toString(){return this.id()}words(t){return this.node.textContent=t,this}wrap(t){const e=this.parent();if(!e)return this.addTo(t);const i=e.index(this);return e.put(t,i).put(this)}writeDataToDom(){return this.each(function(){this.writeDataToDom()}),this}xml(t,e,i){if(typeof t=="boolean"&&(i=e,e=t,t=null),t==null||typeof t=="function"){e=e??!0,this.writeDataToDom();let a=this;if(t!=null){if(a=ie(a.node.cloneNode(!0)),e){const l=t(a);if(a=l||a,l===!1)return""}a.each(function(){const l=t(this),h=l||this;l===!1?this.remove():l&&this!==h&&this.replace(h)},!0)}return e?a.node.outerHTML:a.node.innerHTML}e=e??!1;const n=oi("wrapper",i),r=nt.document.createDocumentFragment();n.innerHTML=t;for(let a=n.children.length;a--;)r.appendChild(n.firstElementChild);const o=this.parent();return e?this.replace(r)&&o:this.add(r)}}J(we,{attr:Xa,find:$a,findOne:Ha});et(we,"Dom");class de extends we{constructor(t,e){super(t,e),this.dom={},this.node.instance=this,t.hasAttribute("svgjs:data")&&this.setData(JSON.parse(t.getAttribute("svgjs:data"))||{})}center(t,e){return this.cx(t).cy(e)}cx(t){return t==null?this.x()+this.width()/2:this.x(t-this.width()/2)}cy(t){return t==null?this.y()+this.height()/2:this.y(t-this.height()/2)}defs(){const t=this.root();return t&&t.defs()}dmove(t,e){return this.dx(t).dy(e)}dx(t=0){return this.x(new V(t).plus(this.x()))}dy(t=0){return this.y(new V(t).plus(this.y()))}getEventHolder(){return this}height(t){return this.attr("height",t)}move(t,e){return this.x(t).y(e)}parents(t=this.root()){const e=typeof t=="string";e||(t=Xt(t));const i=new Ae;let n=this;for(;(n=n.parent())&&n.node!==nt.document&&n.nodeName!=="#document-fragment"&&(i.push(n),!(!e&&n.node===t.node||e&&n.matches(t)));)if(n.node===this.root().node)return null;return i}reference(t){if(t=this.attr(t),!t)return null;const e=(t+"").match(ca);return e?Xt(e[1]):null}root(){const t=this.parent(qo(In));return t&&t.root()}setData(t){return this.dom=t,this}size(t,e){const i=qe(this,t,e);return this.width(new V(i.width)).height(new V(i.height))}width(t){return this.attr("width",t)}writeDataToDom(){return this.node.removeAttribute("svgjs:data"),Object.keys(this.dom).length&&this.node.setAttribute("svgjs:data",JSON.stringify(this.dom)),super.writeDataToDom()}x(t){return this.attr("x",t)}y(t){return this.attr("y",t)}}J(de,{bbox:za,rbox:Fa,inside:Pa,point:Ra,ctm:Ia,screenCTM:Oa});et(de,"Element");const Qe={stroke:["color","width","opacity","linecap","linejoin","miterlimit","dasharray","dashoffset"],fill:["color","opacity","rule"],prefix:function(s,t){return t==="color"?s:s+"-"+t}};["fill","stroke"].forEach(function(s){const t={};let e;t[s]=function(i){if(typeof i>"u")return this.attr(s);if(typeof i=="string"||i instanceof lt||lt.isRgb(i)||i instanceof de)this.attr(s,i);else for(e=Qe[s].length-1;e>=0;e--)i[Qe[s][e]]!=null&&this.attr(Qe.prefix(s,Qe[s][e]),i[Qe[s][e]]);return this},K(["Element","Runner"],t)});K(["Element","Runner"],{matrix:function(s,t,e,i,n,r){return s==null?new G(this):this.attr("transform",new G(s,t,e,i,n,r))},rotate:function(s,t,e){return this.transform({rotate:s,ox:t,oy:e},!0)},skew:function(s,t,e,i){return arguments.length===1||arguments.length===3?this.transform({skew:s,ox:t,oy:e},!0):this.transform({skew:[s,t],ox:e,oy:i},!0)},shear:function(s,t,e){return this.transform({shear:s,ox:t,oy:e},!0)},scale:function(s,t,e,i){return arguments.length===1||arguments.length===3?this.transform({scale:s,ox:t,oy:e},!0):this.transform({scale:[s,t],ox:e,oy:i},!0)},translate:function(s,t){return this.transform({translate:[s,t]},!0)},relative:function(s,t){return this.transform({relative:[s,t]},!0)},flip:function(s="both",t="center"){return"xybothtrue".indexOf(s)===-1&&(t=s,s="both"),this.transform({flip:s,origin:t},!0)},opacity:function(s){return this.attr("opacity",s)}});K("radius",{radius:function(s,t=s){return(this._element||this).type==="radialGradient"?this.attr("r",new V(s)):this.rx(s).ry(t)}});K("Path",{length:function(){return this.node.getTotalLength()},pointAt:function(s){return new _t(this.node.getPointAtLength(s))}});K(["Element","Runner"],{font:function(s,t){if(typeof s=="object"){for(t in s)this.font(t,s[t]);return this}return s==="leading"?this.leading(t):s==="anchor"?this.attr("text-anchor",t):s==="size"||s==="family"||s==="weight"||s==="stretch"||s==="variant"||s==="style"?this.attr("font-"+s,t):this.attr(s,t)}});const ja=["click","dblclick","mousedown","mouseup","mouseover","mouseout","mousemove","mouseenter","mouseleave","touchstart","touchmove","touchleave","touchend","touchcancel"].reduce(function(s,t){const e=function(i){return i===null?this.off(t):this.on(t,i),this};return s[t]=e,s},{});K("Element",ja);function qa(){return this.attr("transform",null)}function Ka(){return(this.attr("transform")||"").split(ua).slice(0,-1).map(function(t){const e=t.trim().split("(");return[e[0],e[1].split(me).map(function(i){return parseFloat(i)})]}).reverse().reduce(function(t,e){return e[0]==="matrix"?t.lmultiply(G.fromArray(e[1])):t[e[0]].apply(t,e[1])},new G)}function Za(s,t){if(this===s)return this;const e=this.screenCTM(),i=s.screenCTM().inverse();return this.addTo(s,t).untransform().transform(i.multiply(e)),this}function Ja(s){return this.toParent(this.root(),s)}function Qa(s,t){if(s==null||typeof s=="string"){const n=new G(this).decompose();return s==null?n:n[s]}G.isMatrixLike(s)||(s={...s,origin:an(s,this)});const e=t===!0?this:t||!1,i=new G(e).transform(s);return this.attr("transform",i)}K("Element",{untransform:qa,matrixify:Ka,toParent:Za,toRoot:Ja,transform:Qa});class qt extends de{flatten(t=this,e){return this.each(function(){if(this instanceof qt)return this.flatten().ungroup()}),this}ungroup(t=this.parent(),e=t.index(this)){return e=e===-1?t.children().length:e,this.each(function(i,n){return n[n.length-i-1].toParent(t,e)}),this.remove()}}et(qt,"Container");class zn extends qt{constructor(t,e=t){super(pt("defs",t),e)}flatten(){return this}ungroup(){return this}}et(zn,"Defs");let Qt=class extends de{};et(Qt,"Shape");function Fn(s){return this.attr("rx",s)}function Pn(s){return this.attr("ry",s)}function tr(s){return s==null?this.cx()-this.rx():this.cx(s+this.rx())}function er(s){return s==null?this.cy()-this.ry():this.cy(s+this.ry())}function ir(s){return this.attr("cx",s)}function nr(s){return this.attr("cy",s)}function sr(s){return s==null?this.rx()*2:this.rx(new V(s).divide(2))}function rr(s){return s==null?this.ry()*2:this.ry(new V(s).divide(2))}var tl={__proto__:null,rx:Fn,ry:Pn,x:tr,y:er,cx:ir,cy:nr,width:sr,height:rr};class $i extends Qt{constructor(t,e=t){super(pt("ellipse",t),e)}size(t,e){const i=qe(this,t,e);return this.rx(new V(i.width).divide(2)).ry(new V(i.height).divide(2))}}J($i,tl);K("Container",{ellipse:ft(function(s=0,t=s){return this.put(new $i).size(s,t).move(0,0)})});et($i,"Ellipse");class or extends we{constructor(t=nt.document.createDocumentFragment()){super(t)}xml(t,e,i){if(typeof t=="boolean"&&(i=e,e=t,t=null),t==null||typeof t=="function"){const n=new we(oi("wrapper",i));return n.add(this.node.cloneNode(!0)),n.xml(!1,i)}return super.xml(t,!1,i)}}et(or,"Fragment");function ar(s,t){return(this._element||this).type==="radialGradient"?this.attr({fx:new V(s),fy:new V(t)}):this.attr({x1:new V(s),y1:new V(t)})}function lr(s,t){return(this._element||this).type==="radialGradient"?this.attr({cx:new V(s),cy:new V(t)}):this.attr({x2:new V(s),y2:new V(t)})}var el={__proto__:null,from:ar,to:lr};class mi extends qt{constructor(t,e){super(pt(t+"Gradient",typeof t=="string"?null:t),e)}attr(t,e,i){return t==="transform"&&(t="gradientTransform"),super.attr(t,e,i)}bbox(){return new Ut}targets(){return Ke("svg [fill*="+this.id()+"]")}toString(){return this.url()}update(t){return this.clear(),typeof t=="function"&&t.call(this,this),this}url(){return"url(#"+this.id()+")"}}J(mi,el);K({Container:{gradient(...s){return this.defs().gradient(...s)}},Defs:{gradient:ft(function(s,t){return this.put(new mi(s)).update(t)})}});et(mi,"Gradient");class ai extends qt{constructor(t,e=t){super(pt("pattern",t),e)}attr(t,e,i){return t==="transform"&&(t="patternTransform"),super.attr(t,e,i)}bbox(){return new Ut}targets(){return Ke("svg [fill*="+this.id()+"]")}toString(){return this.url()}update(t){return this.clear(),typeof t=="function"&&t.call(this,this),this}url(){return"url(#"+this.id()+")"}}K({Container:{pattern(...s){return this.defs().pattern(...s)}},Defs:{pattern:ft(function(s,t,e){return this.put(new ai).update(e).attr({x:0,y:0,width:s,height:t,patternUnits:"userSpaceOnUse"})})}});et(ai,"Pattern");let Ie=class extends Qt{constructor(t,e=t){super(pt("image",t),e)}load(t,e){if(!t)return this;const i=new nt.window.Image;return ln(i,"load",function(n){const r=this.parent(ai);this.width()===0&&this.height()===0&&this.size(i.width,i.height),r instanceof ai&&r.width()===0&&r.height()===0&&r.size(this.width(),this.height()),typeof e=="function"&&e.call(this,n)},this),ln(i,"load error",function(){He(i)}),this.attr("href",i.src=t,fi)}};Va(function(s,t,e){return(s==="fill"||s==="stroke")&&pa.test(t)&&(t=e.root().defs().image(t)),t instanceof Ie&&(t=e.root().defs().pattern(0,0,i=>{i.add(t)})),t});K({Container:{image:ft(function(s,t){return this.put(new Ie).size(0,0).load(s,t)})}});et(Ie,"Image");class _e extends je{bbox(){let t=-1/0,e=-1/0,i=1/0,n=1/0;return this.forEach(function(r){t=Math.max(r[0],t),e=Math.max(r[1],e),i=Math.min(r[0],i),n=Math.min(r[1],n)}),new Ut(i,n,t-i,e-n)}move(t,e){const i=this.bbox();if(t-=i.x,e-=i.y,!isNaN(t)&&!isNaN(e))for(let n=this.length-1;n>=0;n--)this[n]=[this[n][0]+t,this[n][1]+e];return this}parse(t=[0,0]){const e=[];t instanceof Array?t=Array.prototype.concat.apply([],t):t=t.trim().split(me).map(parseFloat),t.length%2!==0&&t.pop();for(let i=0,n=t.length;i<n;i=i+2)e.push([t[i],t[i+1]]);return e}size(t,e){let i;const n=this.bbox();for(i=this.length-1;i>=0;i--)n.width&&(this[i][0]=(this[i][0]-n.x)*t/n.width+n.x),n.height&&(this[i][1]=(this[i][1]-n.y)*e/n.height+n.y);return this}toLine(){return{x1:this[0][0],y1:this[0][1],x2:this[1][0],y2:this[1][1]}}toString(){const t=[];for(let e=0,i=this.length;e<i;e++)t.push(this[e].join(","));return t.join(" ")}transform(t){return this.clone().transformO(t)}transformO(t){G.isMatrixLike(t)||(t=new G(t));for(let e=this.length;e--;){const[i,n]=this[e];this[e][0]=t.a*i+t.c*n+t.e,this[e][1]=t.b*i+t.d*n+t.f}return this}}const il=_e;function nl(s){return s==null?this.bbox().x:this.move(s,this.bbox().y)}function sl(s){return s==null?this.bbox().y:this.move(this.bbox().x,s)}function rl(s){const t=this.bbox();return s==null?t.width:this.size(s,t.height)}function ol(s){const t=this.bbox();return s==null?t.height:this.size(t.width,s)}var Bn={__proto__:null,MorphArray:il,x:nl,y:sl,width:rl,height:ol};class li extends Qt{constructor(t,e=t){super(pt("line",t),e)}array(){return new _e([[this.attr("x1"),this.attr("y1")],[this.attr("x2"),this.attr("y2")]])}move(t,e){return this.attr(this.array().move(t,e).toLine())}plot(t,e,i,n){return t==null?this.array():(typeof e<"u"?t={x1:t,y1:e,x2:i,y2:n}:t=new _e(t).toLine(),this.attr(t))}size(t,e){const i=qe(this,t,e);return this.attr(this.array().size(i.width,i.height).toLine())}}J(li,Bn);K({Container:{line:ft(function(...s){return li.prototype.plot.apply(this.put(new li),s[0]!=null?s:[0,0,0,0])})}});et(li,"Line");class ki extends qt{constructor(t,e=t){super(pt("marker",t),e)}height(t){return this.attr("markerHeight",t)}orient(t){return this.attr("orient",t)}ref(t,e){return this.attr("refX",t).attr("refY",e)}toString(){return"url(#"+this.id()+")"}update(t){return this.clear(),typeof t=="function"&&t.call(this,this),this}width(t){return this.attr("markerWidth",t)}}K({Container:{marker(...s){return this.defs().marker(...s)}},Defs:{marker:ft(function(s,t,e){return this.put(new ki).size(s,t).ref(s/2,t/2).viewbox(0,0,s,t).attr("orient","auto").update(e)})},marker:{marker(s,t,e,i){let n=["marker"];return s!=="all"&&n.push(s),n=n.join("-"),s=arguments[1]instanceof ki?arguments[1]:this.defs().marker(t,e,i),this.attr(n,s)}}});et(ki,"Marker");function Ue(s,t){return function(e){return e==null?this[s]:(this[s]=e,t&&t.call(this),this)}}const al={"-":function(s){return s},"<>":function(s){return-Math.cos(s*Math.PI)/2+.5},">":function(s){return Math.sin(s*Math.PI/2)},"<":function(s){return-Math.cos(s*Math.PI/2)+1},bezier:function(s,t,e,i){return function(n){return n<0?s>0?t/s*n:e>0?i/e*n:0:n>1?e<1?(1-i)/(1-e)*n+(i-e)/(1-e):s<1?(1-t)/(1-s)*n+(t-s)/(1-s):1:3*n*(1-n)**2*t+3*n**2*(1-n)*i+n**3}},steps:function(s,t="end"){t=t.split("-").reverse()[0];let e=s;return t==="none"?--e:t==="both"&&++e,(i,n=!1)=>{let r=Math.floor(i*s);const o=i*r%1===0;return(t==="start"||t==="both")&&++r,n&&o&&--r,i>=0&&r<0&&(r=0),i<=1&&r>e&&(r=e),r/e}}};class $n{done(){return!1}}class hn extends $n{constructor(t=ti.ease){super(),this.ease=al[t]||t}step(t,e,i){return typeof t!="number"?i<1?t:e:t+(e-t)*this.ease(i)}}class zi extends $n{constructor(t){super(),this.stepper=t}done(t){return t.done}step(t,e,i,n){return this.stepper(t,e,i,n)}}function ds(){const s=(this._duration||500)/1e3,t=this._overshoot||0,e=1e-10,i=Math.PI,n=Math.log(t/100+e),r=-n/Math.sqrt(i*i+n*n),o=3.9/(r*s);this.d=2*r*o,this.k=o*o}class ll extends zi{constructor(t=500,e=0){super(),this.duration(t).overshoot(e)}step(t,e,i,n){if(typeof t=="string")return t;if(n.done=i===1/0,i===1/0)return e;if(i===0)return t;i>100&&(i=16),i/=1e3;const r=n.velocity||0,o=-this.d*r-this.k*(t-e),a=t+r*i+o*i*i/2;return n.velocity=r+o*i,n.done=Math.abs(e-a)+Math.abs(r)<.002,n.done?e:a}}J(ll,{duration:Ue("_duration",ds),overshoot:Ue("_overshoot",ds)});class hl extends zi{constructor(t=.1,e=.01,i=0,n=1e3){super(),this.p(t).i(e).d(i).windup(n)}step(t,e,i,n){if(typeof t=="string")return t;if(n.done=i===1/0,i===1/0)return e;if(i===0)return t;const r=e-t;let o=(n.integral||0)+r*i;const a=(r-(n.error||0))/i,l=this._windup;return l!==!1&&(o=Math.max(-l,Math.min(o,l))),n.error=r,n.integral=o,n.done=Math.abs(r)<.001,n.done?e:t+(this.P*r+this.I*o+this.D*a)}}J(hl,{windup:Ue("_windup"),p:Ue("P"),i:Ue("I"),d:Ue("D")});const dl={M:2,L:2,H:1,V:1,C:6,S:4,Q:4,T:2,A:7,Z:0},dn={M:function(s,t,e){return t.x=e.x=s[0],t.y=e.y=s[1],["M",t.x,t.y]},L:function(s,t){return t.x=s[0],t.y=s[1],["L",s[0],s[1]]},H:function(s,t){return t.x=s[0],["H",s[0]]},V:function(s,t){return t.y=s[0],["V",s[0]]},C:function(s,t){return t.x=s[4],t.y=s[5],["C",s[0],s[1],s[2],s[3],s[4],s[5]]},S:function(s,t){return t.x=s[2],t.y=s[3],["S",s[0],s[1],s[2],s[3]]},Q:function(s,t){return t.x=s[2],t.y=s[3],["Q",s[0],s[1],s[2],s[3]]},T:function(s,t){return t.x=s[0],t.y=s[1],["T",s[0],s[1]]},Z:function(s,t,e){return t.x=e.x,t.y=e.y,["Z"]},A:function(s,t){return t.x=s[5],t.y=s[6],["A",s[0],s[1],s[2],s[3],s[4],s[5],s[6]]}},qi="mlhvqtcsaz".split("");for(let s=0,t=qi.length;s<t;++s)dn[qi[s]]=function(e){return function(i,n,r){if(e==="H")i[0]=i[0]+n.x;else if(e==="V")i[0]=i[0]+n.y;else if(e==="A")i[5]=i[5]+n.x,i[6]=i[6]+n.y;else for(let o=0,a=i.length;o<a;++o)i[o]=i[o]+(o%2?n.y:n.x);return dn[e](i,n,r)}}(qi[s].toUpperCase());function cl(s){const t=s.segment[0];return dn[t](s.segment.slice(1),s.p,s.p0)}function cn(s){return s.segment.length&&s.segment.length-1===dl[s.segment[0].toUpperCase()]}function ul(s,t){s.inNumber&&Te(s,!1);const e=On.test(t);if(e)s.segment=[t];else{const i=s.lastCommand,n=i.toLowerCase(),r=i===n;s.segment=[n==="m"?r?"l":"L":i]}return s.inSegment=!0,s.lastCommand=s.segment[0],e}function Te(s,t){if(!s.inNumber)throw new Error("Parser Error");s.number&&s.segment.push(parseFloat(s.number)),s.inNumber=t,s.number="",s.pointSeen=!1,s.hasExponent=!1,cn(s)&&un(s)}function un(s){s.inSegment=!1,s.absolute&&(s.segment=cl(s)),s.segments.push(s.segment)}function fl(s){if(!s.segment.length)return!1;const t=s.segment[0].toUpperCase()==="A",e=s.segment.length;return t&&(e===4||e===5)}function pl(s){return s.lastToken.toUpperCase()==="E"}function ml(s,t=!0){let e=0,i="";const n={segment:[],inNumber:!1,number:"",lastToken:"",inSegment:!1,segments:[],pointSeen:!1,hasExponent:!1,absolute:t,p0:new _t,p:new _t};for(;n.lastToken=i,i=s.charAt(e++);)if(!(!n.inSegment&&ul(n,i))){if(i==="."){if(n.pointSeen||n.hasExponent){Te(n,!1),--e;continue}n.inNumber=!0,n.pointSeen=!0,n.number+=i;continue}if(!isNaN(parseInt(i))){if(n.number==="0"||fl(n)){n.inNumber=!0,n.number=i,Te(n,!0);continue}n.inNumber=!0,n.number+=i;continue}if(i===" "||i===","){n.inNumber&&Te(n,!1);continue}if(i==="-"){if(n.inNumber&&!pl(n)){Te(n,!1),--e;continue}n.number+=i,n.inNumber=!0;continue}if(i.toUpperCase()==="E"){n.number+=i,n.hasExponent=!0;continue}if(On.test(i)){if(n.inNumber)Te(n,!1);else if(cn(n))un(n);else throw new Error("parser Error");--e}}return n.inNumber&&Te(n,!1),n.inSegment&&cn(n)&&un(n),n.segments}function gl(s){let t="";for(let e=0,i=s.length;e<i;e++)t+=s[e][0],s[e][1]!=null&&(t+=s[e][1],s[e][2]!=null&&(t+=" ",t+=s[e][2],s[e][3]!=null&&(t+=" ",t+=s[e][3],t+=" ",t+=s[e][4],s[e][5]!=null&&(t+=" ",t+=s[e][5],t+=" ",t+=s[e][6],s[e][7]!=null&&(t+=" ",t+=s[e][7])))));return t+" "}class Re extends je{bbox(){return ye().path.setAttribute("d",this.toString()),new Ut(ye.nodes.path.getBBox())}move(t,e){const i=this.bbox();if(t-=i.x,e-=i.y,!isNaN(t)&&!isNaN(e))for(let n,r=this.length-1;r>=0;r--)n=this[r][0],n==="M"||n==="L"||n==="T"?(this[r][1]+=t,this[r][2]+=e):n==="H"?this[r][1]+=t:n==="V"?this[r][1]+=e:n==="C"||n==="S"||n==="Q"?(this[r][1]+=t,this[r][2]+=e,this[r][3]+=t,this[r][4]+=e,n==="C"&&(this[r][5]+=t,this[r][6]+=e)):n==="A"&&(this[r][6]+=t,this[r][7]+=e);return this}parse(t="M0 0"){return Array.isArray(t)&&(t=Array.prototype.concat.apply([],t).toString()),ml(t)}size(t,e){const i=this.bbox();let n,r;for(i.width=i.width===0?1:i.width,i.height=i.height===0?1:i.height,n=this.length-1;n>=0;n--)r=this[n][0],r==="M"||r==="L"||r==="T"?(this[n][1]=(this[n][1]-i.x)*t/i.width+i.x,this[n][2]=(this[n][2]-i.y)*e/i.height+i.y):r==="H"?this[n][1]=(this[n][1]-i.x)*t/i.width+i.x:r==="V"?this[n][1]=(this[n][1]-i.y)*e/i.height+i.y:r==="C"||r==="S"||r==="Q"?(this[n][1]=(this[n][1]-i.x)*t/i.width+i.x,this[n][2]=(this[n][2]-i.y)*e/i.height+i.y,this[n][3]=(this[n][3]-i.x)*t/i.width+i.x,this[n][4]=(this[n][4]-i.y)*e/i.height+i.y,r==="C"&&(this[n][5]=(this[n][5]-i.x)*t/i.width+i.x,this[n][6]=(this[n][6]-i.y)*e/i.height+i.y)):r==="A"&&(this[n][1]=this[n][1]*t/i.width,this[n][2]=this[n][2]*e/i.height,this[n][6]=(this[n][6]-i.x)*t/i.width+i.x,this[n][7]=(this[n][7]-i.y)*e/i.height+i.y);return this}toString(){return gl(this)}}const hr=s=>{const t=typeof s;return t==="number"?V:t==="string"?lt.isColor(s)?lt:me.test(s)?On.test(s)?Re:je:js.test(s)?V:fn:Hn.indexOf(s.constructor)>-1?s.constructor:Array.isArray(s)?je:t==="object"?hi:fn};class Se{constructor(t){this._stepper=t||new hn("-"),this._from=null,this._to=null,this._type=null,this._context=null,this._morphObj=null}at(t){return this._morphObj.morph(this._from,this._to,t,this._stepper,this._context)}done(){return this._context.map(this._stepper.done).reduce(function(e,i){return e&&i},!0)}from(t){return t==null?this._from:(this._from=this._set(t),this)}stepper(t){return t==null?this._stepper:(this._stepper=t,this)}to(t){return t==null?this._to:(this._to=this._set(t),this)}type(t){return t==null?this._type:(this._type=t,this)}_set(t){this._type||this.type(hr(t));let e=new this._type(t);return this._type===lt&&(e=this._to?e[this._to[4]]():this._from?e[this._from[4]]():e),this._type===hi&&(e=this._to?e.align(this._to):this._from?e.align(this._from):e),e=e.toConsumable(),this._morphObj=this._morphObj||new this._type,this._context=this._context||Array.apply(null,Array(e.length)).map(Object).map(function(i){return i.done=!0,i}),e}}class fn{constructor(...t){this.init(...t)}init(t){return t=Array.isArray(t)?t[0]:t,this.value=t,this}toArray(){return[this.value]}valueOf(){return this.value}}class gi{constructor(...t){this.init(...t)}init(t){return Array.isArray(t)&&(t={scaleX:t[0],scaleY:t[1],shear:t[2],rotate:t[3],translateX:t[4],translateY:t[5],originX:t[6],originY:t[7]}),Object.assign(this,gi.defaults,t),this}toArray(){const t=this;return[t.scaleX,t.scaleY,t.shear,t.rotate,t.translateX,t.translateY,t.originX,t.originY]}}gi.defaults={scaleX:1,scaleY:1,shear:0,rotate:0,translateX:0,translateY:0,originX:0,originY:0};const vl=(s,t)=>s[0]<t[0]?-1:s[0]>t[0]?1:0;class hi{constructor(...t){this.init(...t)}align(t){const e=this.values;for(let i=0,n=e.length;i<n;++i){if(e[i+1]===t[i+1]){if(e[i+1]===lt&&t[i+7]!==e[i+7]){const a=t[i+7],l=new lt(this.values.splice(i+3,5))[a]().toArray();this.values.splice(i+3,0,...l)}i+=e[i+2]+2;continue}if(!t[i+1])return this;const r=new t[i+1]().toArray(),o=e[i+2]+3;e.splice(i,o,t[i],t[i+1],t[i+2],...r),i+=e[i+2]+2}return this}init(t){if(this.values=[],Array.isArray(t)){this.values=t.slice();return}t=t||{};const e=[];for(const i in t){const n=hr(t[i]),r=new n(t[i]).toArray();e.push([i,n,r.length,...r])}return e.sort(vl),this.values=e.reduce((i,n)=>i.concat(n),[]),this}toArray(){return this.values}valueOf(){const t={},e=this.values;for(;e.length;){const i=e.shift(),n=e.shift(),r=e.shift(),o=e.splice(0,r);t[i]=new n(o)}return t}}const Hn=[fn,gi,hi];function yl(s=[]){Hn.push(...[].concat(s))}function xl(){J(Hn,{to(s){return new Se().type(this.constructor).from(this.toArray()).to(s)},fromArray(s){return this.init(s),this},toConsumable(){return this.toArray()},morph(s,t,e,i,n){const r=function(o,a){return i.step(o,t[a],e,n[a],n)};return this.fromArray(s.map(r))}})}class Oe extends Qt{constructor(t,e=t){super(pt("path",t),e)}array(){return this._array||(this._array=new Re(this.attr("d")))}clear(){return delete this._array,this}height(t){return t==null?this.bbox().height:this.size(this.bbox().width,t)}move(t,e){return this.attr("d",this.array().move(t,e))}plot(t){return t==null?this.array():this.clear().attr("d",typeof t=="string"?t:this._array=new Re(t))}size(t,e){const i=qe(this,t,e);return this.attr("d",this.array().size(i.width,i.height))}width(t){return t==null?this.bbox().width:this.size(t,this.bbox().height)}x(t){return t==null?this.bbox().x:this.move(t,this.bbox().y)}y(t){return t==null?this.bbox().y:this.move(this.bbox().x,t)}}Oe.prototype.MorphArray=Re;K({Container:{path:ft(function(s){return this.put(new Oe).plot(s||new Re)})}});et(Oe,"Path");function wl(){return this._array||(this._array=new _e(this.attr("points")))}function _l(){return delete this._array,this}function Ml(s,t){return this.attr("points",this.array().move(s,t))}function El(s){return s==null?this.array():this.clear().attr("points",typeof s=="string"?s:this._array=new _e(s))}function Cl(s,t){const e=qe(this,s,t);return this.attr("points",this.array().size(e.width,e.height))}var dr={__proto__:null,array:wl,clear:_l,move:Ml,plot:El,size:Cl};class ke extends Qt{constructor(t,e=t){super(pt("polygon",t),e)}}K({Container:{polygon:ft(function(s){return this.put(new ke).plot(s||new _e)})}});J(ke,Bn);J(ke,dr);et(ke,"Polygon");class vi extends Qt{constructor(t,e=t){super(pt("polyline",t),e)}}K({Container:{polyline:ft(function(s){return this.put(new vi).plot(s||new _e)})}});J(vi,Bn);J(vi,dr);et(vi,"Polyline");class Jt extends Qt{constructor(t,e=t){super(pt("rect",t),e)}}J(Jt,{rx:Fn,ry:Pn});K({Container:{rect:ft(function(s,t){return this.put(new Jt).size(s,t)})}});et(Jt,"Rect");class Ki{constructor(){this._first=null,this._last=null}first(){return this._first&&this._first.value}last(){return this._last&&this._last.value}push(t){const e=typeof t.next<"u"?t:{value:t,next:null,prev:null};return this._last?(e.prev=this._last,this._last.next=e,this._last=e):(this._last=e,this._first=e),e}remove(t){t.prev&&(t.prev.next=t.next),t.next&&(t.next.prev=t.prev),t===this._last&&(this._last=t.prev),t===this._first&&(this._first=t.next),t.prev=null,t.next=null}shift(){const t=this._first;return t?(this._first=t.next,this._first&&(this._first.prev=null),this._last=this._first?this._last:null,t.value):null}}const ot={nextDraw:null,frames:new Ki,timeouts:new Ki,immediates:new Ki,timer:()=>nt.window.performance||nt.window.Date,transforms:[],frame(s){const t=ot.frames.push({run:s});return ot.nextDraw===null&&(ot.nextDraw=nt.window.requestAnimationFrame(ot._draw)),t},timeout(s,t){t=t||0;const e=ot.timer().now()+t,i=ot.timeouts.push({run:s,time:e});return ot.nextDraw===null&&(ot.nextDraw=nt.window.requestAnimationFrame(ot._draw)),i},immediate(s){const t=ot.immediates.push(s);return ot.nextDraw===null&&(ot.nextDraw=nt.window.requestAnimationFrame(ot._draw)),t},cancelFrame(s){s!=null&&ot.frames.remove(s)},clearTimeout(s){s!=null&&ot.timeouts.remove(s)},cancelImmediate(s){s!=null&&ot.immediates.remove(s)},_draw(s){let t=null;const e=ot.timeouts.last();for(;(t=ot.timeouts.shift())&&(s>=t.time?t.run():ot.timeouts.push(t),t!==e););let i=null;const n=ot.frames.last();for(;i!==n&&(i=ot.frames.shift());)i.run(s);let r=null;for(;r=ot.immediates.shift();)r();ot.nextDraw=ot.timeouts.first()||ot.frames.first()?nt.window.requestAnimationFrame(ot._draw):null}},Nl=function(s){const t=s.start,e=s.runner.duration(),i=t+e;return{start:t,duration:e,end:i,runner:s.runner}},Tl=function(){const s=nt.window;return(s.performance||s.Date).now()};let cr=class extends pi{constructor(t=Tl){super(),this._timeSource=t,this._startTime=0,this._speed=1,this._persist=0,this._nextFrame=null,this._paused=!0,this._runners=[],this._runnerIds=[],this._lastRunnerId=-1,this._time=0,this._lastSourceTime=0,this._lastStepTime=0,this._step=this._stepFn.bind(this,!1),this._stepImmediate=this._stepFn.bind(this,!0)}active(){return!!this._nextFrame}finish(){return this.time(this.getEndTimeOfTimeline()+1),this.pause()}getEndTime(){const t=this.getLastRunnerInfo(),e=t?t.runner.duration():0;return(t?t.start:this._time)+e}getEndTimeOfTimeline(){const t=this._runners.map(e=>e.start+e.runner.duration());return Math.max(0,...t)}getLastRunnerInfo(){return this.getRunnerInfoById(this._lastRunnerId)}getRunnerInfoById(t){return this._runners[this._runnerIds.indexOf(t)]||null}pause(){return this._paused=!0,this._continue()}persist(t){return t==null?this._persist:(this._persist=t,this)}play(){return this._paused=!1,this.updateTime()._continue()}reverse(t){const e=this.speed();if(t==null)return this.speed(-e);const i=Math.abs(e);return this.speed(t?-i:i)}schedule(t,e,i){if(t==null)return this._runners.map(Nl);let n=0;const r=this.getEndTime();if(e=e||0,i==null||i==="last"||i==="after")n=r;else if(i==="absolute"||i==="start")n=e,e=0;else if(i==="now")n=this._time;else if(i==="relative"){const l=this.getRunnerInfoById(t.id);l&&(n=l.start+e,e=0)}else if(i==="with-last"){const l=this.getLastRunnerInfo();n=l?l.start:this._time}else throw new Error('Invalid value for the "when" parameter');t.unschedule(),t.timeline(this);const o=t.persist(),a={persist:o===null?this._persist:o,start:n+e,runner:t};return this._lastRunnerId=t.id,this._runners.push(a),this._runners.sort((l,h)=>l.start-h.start),this._runnerIds=this._runners.map(l=>l.runner.id),this.updateTime()._continue(),this}seek(t){return this.time(this._time+t)}source(t){return t==null?this._timeSource:(this._timeSource=t,this)}speed(t){return t==null?this._speed:(this._speed=t,this)}stop(){return this.time(0),this.pause()}time(t){return t==null?this._time:(this._time=t,this._continue(!0))}unschedule(t){const e=this._runnerIds.indexOf(t.id);return e<0?this:(this._runners.splice(e,1),this._runnerIds.splice(e,1),t.timeline(null),this)}updateTime(){return this.active()||(this._lastSourceTime=this._timeSource()),this}_continue(t=!1){return ot.cancelFrame(this._nextFrame),this._nextFrame=null,t?this._stepImmediate():this._paused?this:(this._nextFrame=ot.frame(this._step),this)}_stepFn(t=!1){const e=this._timeSource();let i=e-this._lastSourceTime;t&&(i=0);const n=this._speed*i+(this._time-this._lastStepTime);this._lastSourceTime=e,t||(this._time+=n,this._time=this._time<0?0:this._time),this._lastStepTime=this._time,this.fire("time",this._time);for(let o=this._runners.length;o--;){const a=this._runners[o],l=a.runner;this._time-a.start<=0&&l.reset()}let r=!1;for(let o=0,a=this._runners.length;o<a;o++){const l=this._runners[o],h=l.runner;let d=n;const c=this._time-l.start;if(c<=0){r=!0;continue}else c<d&&(d=c);if(!h.active())continue;h.step(d).done?l.persist!==!0&&h.duration()-h.time()+this._time+l.persist<this._time&&(h.unschedule(),--o,--a):r=!0}return r&&!(this._speed<0&&this._time===0)||this._runnerIds.length&&this._speed<0&&this._time>0?this._continue():(this.pause(),this.fire("finished")),this}};K({Element:{timeline:function(s){return s==null?(this._timeline=this._timeline||new cr,this._timeline):(this._timeline=s,this)}}});class Zt extends pi{constructor(t){super(),this.id=Zt.id++,t=t??ti.duration,t=typeof t=="function"?new zi(t):t,this._element=null,this._timeline=null,this.done=!1,this._queue=[],this._duration=typeof t=="number"&&t,this._isDeclarative=t instanceof zi,this._stepper=this._isDeclarative?t:new hn,this._history={},this.enabled=!0,this._time=0,this._lastTime=0,this._reseted=!0,this.transforms=new G,this.transformId=1,this._haveReversed=!1,this._reverse=!1,this._loopsDone=0,this._swing=!1,this._wait=0,this._times=1,this._frameId=null,this._persist=this._isDeclarative?!0:null}static sanitise(t,e,i){let n=1,r=!1,o=0;return t=t||ti.duration,e=e||ti.delay,i=i||"last",typeof t=="object"&&!(t instanceof $n)&&(e=t.delay||e,i=t.when||i,r=t.swing||r,n=t.times||n,o=t.wait||o,t=t.duration||ti.duration),{duration:t,delay:e,swing:r,times:n,wait:o,when:i}}active(t){return t==null?this.enabled:(this.enabled=t,this)}addTransform(t,e){return this.transforms.lmultiplyO(t),this}after(t){return this.on("finished",t)}animate(t,e,i){const n=Zt.sanitise(t,e,i),r=new Zt(n.duration);return this._timeline&&r.timeline(this._timeline),this._element&&r.element(this._element),r.loop(n).schedule(n.delay,n.when)}clearTransform(){return this.transforms=new G,this}clearTransformsFromQueue(){(!this.done||!this._timeline||!this._timeline._runnerIds.includes(this.id))&&(this._queue=this._queue.filter(t=>!t.isTransform))}delay(t){return this.animate(0,t)}duration(){return this._times*(this._wait+this._duration)-this._wait}during(t){return this.queue(null,t)}ease(t){return this._stepper=new hn(t),this}element(t){return t==null?this._element:(this._element=t,t._prepareRunner(),this)}finish(){return this.step(1/0)}loop(t,e,i){return typeof t=="object"&&(e=t.swing,i=t.wait,t=t.times),this._times=t||1/0,this._swing=e||!1,this._wait=i||0,this._times===!0&&(this._times=1/0),this}loops(t){const e=this._duration+this._wait;if(t==null){const o=Math.floor(this._time/e),l=(this._time-o*e)/this._duration;return Math.min(o+l,this._times)}const i=Math.floor(t),n=t%1,r=e*i+this._duration*n;return this.time(r)}persist(t){return t==null?this._persist:(this._persist=t,this)}position(t){const e=this._time,i=this._duration,n=this._wait,r=this._times,o=this._swing,a=this._reverse;let l;if(t==null){const f=function(u){const m=o*Math.floor(u%(2*(n+i))/(n+i)),p=m&&!a||!m&&a,x=Math.pow(-1,p)*(u%(n+i))/i+p;return Math.max(Math.min(x,1),0)},g=r*(n+i)-n;return l=e<=0?Math.round(f(1e-5)):e<g?f(e):Math.round(f(g-1e-5)),l}const h=Math.floor(this.loops()),d=o&&h%2===0;return l=h+(d&&!a||a&&d?t:1-t),this.loops(l)}progress(t){return t==null?Math.min(1,this._time/this.duration()):this.time(t*this.duration())}queue(t,e,i,n){return this._queue.push({initialiser:t||hs,runner:e||hs,retarget:i,isTransform:n,initialised:!1,finished:!1}),this.timeline()&&this.timeline()._continue(),this}reset(){return this._reseted?this:(this.time(0),this._reseted=!0,this)}reverse(t){return this._reverse=t??!this._reverse,this}schedule(t,e,i){if(t instanceof cr||(i=e,e=t,t=this.timeline()),!t)throw Error("Runner cannot be scheduled without timeline");return t.schedule(this,e,i),this}step(t){if(!this.enabled)return this;t=t??16,this._time+=t;const e=this.position(),i=this._lastPosition!==e&&this._time>=0;this._lastPosition=e;const n=this.duration(),r=this._lastTime<=0&&this._time>0,o=this._lastTime<n&&this._time>=n;this._lastTime=this._time,r&&this.fire("start",this);const a=this._isDeclarative;this.done=!a&&!o&&this._time>=n,this._reseted=!1;let l=!1;return(i||a)&&(this._initialise(i),this.transforms=new G,l=this._run(a?t:e),this.fire("step",this)),this.done=this.done||l&&a,o&&this.fire("finished",this),this}time(t){if(t==null)return this._time;const e=t-this._time;return this.step(e),this}timeline(t){return typeof t>"u"?this._timeline:(this._timeline=t,this)}unschedule(){const t=this.timeline();return t&&t.unschedule(this),this}_initialise(t){if(!(!t&&!this._isDeclarative))for(let e=0,i=this._queue.length;e<i;++e){const n=this._queue[e],r=this._isDeclarative||!n.initialised&&t;t=!n.finished,r&&t&&(n.initialiser.call(this),n.initialised=!0)}}_rememberMorpher(t,e){if(this._history[t]={morpher:e,caller:this._queue[this._queue.length-1]},this._isDeclarative){const i=this.timeline();i&&i.play()}}_run(t){let e=!0;for(let i=0,n=this._queue.length;i<n;++i){const r=this._queue[i],o=r.runner.call(this,t);r.finished=r.finished||o===!0,e=e&&r.finished}return e}_tryRetarget(t,e,i){if(this._history[t]){if(!this._history[t].caller.initialised){const r=this._queue.indexOf(this._history[t].caller);return this._queue.splice(r,1),!1}this._history[t].caller.retarget?this._history[t].caller.retarget.call(this,e,i):this._history[t].morpher.to(e),this._history[t].caller.finished=!1;const n=this.timeline();return n&&n.play(),!0}return!1}}Zt.id=0;class Fi{constructor(t=new G,e=-1,i=!0){this.transforms=t,this.id=e,this.done=i}clearTransformsFromQueue(){}}J([Zt,Fi],{mergeWith(s){return new Fi(s.transforms.lmultiply(this.transforms),s.id)}});const ur=(s,t)=>s.lmultiplyO(t),fr=s=>s.transforms;function Sl(){const t=this._transformationRunners.runners.map(fr).reduce(ur,new G);this.transform(t),this._transformationRunners.merge(),this._transformationRunners.length()===1&&(this._frameId=null)}class Ll{constructor(){this.runners=[],this.ids=[]}add(t){if(this.runners.includes(t))return;const e=t.id+1;return this.runners.push(t),this.ids.push(e),this}clearBefore(t){const e=this.ids.indexOf(t+1)||1;return this.ids.splice(0,e,0),this.runners.splice(0,e,new Fi).forEach(i=>i.clearTransformsFromQueue()),this}edit(t,e){const i=this.ids.indexOf(t+1);return this.ids.splice(i,1,t+1),this.runners.splice(i,1,e),this}getByID(t){return this.runners[this.ids.indexOf(t+1)]}length(){return this.ids.length}merge(){let t=null;for(let e=0;e<this.runners.length;++e){const i=this.runners[e];if(t&&i.done&&t.done&&(!i._timeline||!i._timeline._runnerIds.includes(i.id))&&(!t._timeline||!t._timeline._runnerIds.includes(t.id))){this.remove(i.id);const r=i.mergeWith(t);this.edit(t.id,r),t=r,--e}else t=i}return this}remove(t){const e=this.ids.indexOf(t+1);return this.ids.splice(e,1),this.runners.splice(e,1),this}}K({Element:{animate(s,t,e){const i=Zt.sanitise(s,t,e),n=this.timeline();return new Zt(i.duration).loop(i).element(this).timeline(n.play()).schedule(i.delay,i.when)},delay(s,t){return this.animate(0,s,t)},_clearTransformRunnersBefore(s){this._transformationRunners.clearBefore(s.id)},_currentTransform(s){return this._transformationRunners.runners.filter(t=>t.id<=s.id).map(fr).reduce(ur,new G)},_addRunner(s){this._transformationRunners.add(s),ot.cancelImmediate(this._frameId),this._frameId=ot.immediate(Sl.bind(this))},_prepareRunner(){this._frameId==null&&(this._transformationRunners=new Ll().add(new Fi(new G(this))))}}});const bl=(s,t)=>s.filter(e=>!t.includes(e));J(Zt,{attr(s,t){return this.styleAttr("attr",s,t)},css(s,t){return this.styleAttr("css",s,t)},styleAttr(s,t,e){if(typeof t=="string")return this.styleAttr(s,{[t]:e});let i=t;if(this._tryRetarget(s,i))return this;let n=new Se(this._stepper).to(i),r=Object.keys(i);return this.queue(function(){n=n.from(this.element()[s](r))},function(o){return this.element()[s](n.at(o).valueOf()),n.done()},function(o){const a=Object.keys(o),l=bl(a,r);if(l.length){const d=this.element()[s](l),c=new hi(n.from()).valueOf();Object.assign(c,d),n.from(c)}const h=new hi(n.to()).valueOf();Object.assign(h,o),n.to(h),r=a,i=o}),this._rememberMorpher(s,n),this},zoom(s,t){if(this._tryRetarget("zoom",s,t))return this;let e=new Se(this._stepper).to(new V(s));return this.queue(function(){e=e.from(this.element().zoom())},function(i){return this.element().zoom(e.at(i),t),e.done()},function(i,n){t=n,e.to(i)}),this._rememberMorpher("zoom",e),this},transform(s,t,e){if(t=s.relative||t,this._isDeclarative&&!t&&this._tryRetarget("transform",s))return this;const i=G.isMatrixLike(s);e=s.affine!=null?s.affine:e??!i;const n=new Se(this._stepper).type(e?gi:G);let r,o,a,l,h;function d(){o=o||this.element(),r=r||an(s,o),h=new G(t?void 0:o),o._addRunner(this),t||o._clearTransformRunnersBefore(this)}function c(g){t||this.clearTransform();const{x:u,y:m}=new _t(r).transform(o._currentTransform(this));let p=new G({...s,origin:[u,m]}),x=this._isDeclarative&&a?a:h;if(e){p=p.decompose(u,m),x=x.decompose(u,m);const _=p.rotate,T=x.rotate,C=[_-360,_,_+360],L=C.map(X=>Math.abs(X-T)),F=Math.min(...L),W=L.indexOf(F);p.rotate=C[W]}t&&(i||(p.rotate=s.rotate||0),this._isDeclarative&&l&&(x.rotate=l)),n.from(x),n.to(p);const E=n.at(g);return l=E.rotate,a=new G(E),this.addTransform(a),o._addRunner(this),n.done()}function f(g){(g.origin||"center").toString()!==(s.origin||"center").toString()&&(r=an(g,o)),s={...g,origin:r}}return this.queue(d,c,f,!0),this._isDeclarative&&this._rememberMorpher("transform",n),this},x(s,t){return this._queueNumber("x",s)},y(s){return this._queueNumber("y",s)},dx(s=0){return this._queueNumberDelta("x",s)},dy(s=0){return this._queueNumberDelta("y",s)},dmove(s,t){return this.dx(s).dy(t)},_queueNumberDelta(s,t){if(t=new V(t),this._tryRetarget(s,t))return this;const e=new Se(this._stepper).to(t);let i=null;return this.queue(function(){i=this.element()[s](),e.from(i),e.to(i+t)},function(n){return this.element()[s](e.at(n)),e.done()},function(n){e.to(i+new V(n))}),this._rememberMorpher(s,e),this},_queueObject(s,t){if(this._tryRetarget(s,t))return this;const e=new Se(this._stepper).to(t);return this.queue(function(){e.from(this.element()[s]())},function(i){return this.element()[s](e.at(i)),e.done()}),this._rememberMorpher(s,e),this},_queueNumber(s,t){return this._queueObject(s,new V(t))},cx(s){return this._queueNumber("cx",s)},cy(s){return this._queueNumber("cy",s)},move(s,t){return this.x(s).y(t)},center(s,t){return this.cx(s).cy(t)},size(s,t){let e;return(!s||!t)&&(e=this._element.bbox()),s||(s=e.width/e.height*t),t||(t=e.height/e.width*s),this.width(s).height(t)},width(s){return this._queueNumber("width",s)},height(s){return this._queueNumber("height",s)},plot(s,t,e,i){if(arguments.length===4)return this.plot([s,t,e,i]);if(this._tryRetarget("plot",s))return this;const n=new Se(this._stepper).type(this._element.MorphArray).to(s);return this.queue(function(){n.from(this._element.array())},function(r){return this._element.plot(n.at(r)),n.done()}),this._rememberMorpher("plot",n),this},leading(s){return this._queueNumber("leading",s)},viewbox(s,t,e,i){return this._queueObject("viewbox",new Ut(s,t,e,i))},update(s){return typeof s!="object"?this.update({offset:arguments[0],color:arguments[1],opacity:arguments[2]}):(s.opacity!=null&&this.attr("stop-opacity",s.opacity),s.color!=null&&this.attr("stop-color",s.color),s.offset!=null&&this.attr("offset",s.offset),this)}});J(Zt,{rx:Fn,ry:Pn,from:ar,to:lr});et(Zt,"Runner");class Un extends qt{constructor(t,e=t){super(pt("svg",t),e),this.namespace()}defs(){return this.isRoot()?ie(this.node.querySelector("defs"))||this.put(new zn):this.root().defs()}isRoot(){return!this.node.parentNode||!(this.node.parentNode instanceof nt.window.SVGElement)&&this.node.parentNode.nodeName!=="#document-fragment"}namespace(){return this.isRoot()?this.attr({xmlns:An,version:"1.1"}).attr("xmlns:xlink",fi,Mi).attr("xmlns:svgjs",jo,Mi):this.root().namespace()}removeNamespace(){return this.attr({xmlns:null,version:null}).attr("xmlns:xlink",null,Mi).attr("xmlns:svgjs",null,Mi)}root(){return this.isRoot()?this:super.root()}}K({Container:{nested:ft(function(){return this.put(new Un)})}});et(Un,"Svg",!0);let Gn=class extends qt{constructor(t,e=t){super(pt("symbol",t),e)}};K({Container:{symbol:ft(function(){return this.put(new Gn)})}});et(Gn,"Symbol");function Dl(s){return this._build===!1&&this.clear(),this.node.appendChild(nt.document.createTextNode(s)),this}function Al(){return this.node.getComputedTextLength()}function Rl(s,t=this.bbox()){return s==null?t.x:this.attr("x",this.attr("x")+s-t.x)}function Il(s,t=this.bbox()){return s==null?t.y:this.attr("y",this.attr("y")+s-t.y)}function Ol(s,t,e=this.bbox()){return this.x(s,e).y(t,e)}function kl(s,t=this.bbox()){return s==null?t.cx:this.attr("x",this.attr("x")+s-t.cx)}function zl(s,t=this.bbox()){return s==null?t.cy:this.attr("y",this.attr("y")+s-t.cy)}function Fl(s,t,e=this.bbox()){return this.cx(s,e).cy(t,e)}function Pl(s){return this.attr("x",s)}function Bl(s){return this.attr("y",s)}function $l(s,t){return this.ax(s).ay(t)}function Hl(s){return this._build=!!s,this}var pr={__proto__:null,plain:Dl,length:Al,x:Rl,y:Il,move:Ol,cx:kl,cy:zl,center:Fl,ax:Pl,ay:Bl,amove:$l,build:Hl};class Ot extends Qt{constructor(t,e=t){super(pt("text",t),e),this.dom.leading=new V(1.3),this._rebuild=!0,this._build=!1}leading(t){return t==null?this.dom.leading:(this.dom.leading=new V(t),this.rebuild())}rebuild(t){if(typeof t=="boolean"&&(this._rebuild=t),this._rebuild){const e=this;let i=0;const n=this.dom.leading;this.each(function(r){const o=nt.window.getComputedStyle(this.node).getPropertyValue("font-size"),a=n*new V(o);this.dom.newLined&&(this.attr("x",e.attr("x")),this.text()===`
`?i+=a:(this.attr("dy",r?a+i:0),i=0))}),this.fire("rebuild")}return this}setData(t){return this.dom=t,this.dom.leading=new V(t.leading||1.3),this}text(t){if(t===void 0){const e=this.node.childNodes;let i=0;t="";for(let n=0,r=e.length;n<r;++n){if(e[n].nodeName==="textPath"){n===0&&(i=1);continue}n!==i&&e[n].nodeType!==3&&ie(e[n]).dom.newLined===!0&&(t+=`
`),t+=e[n].textContent}return t}if(this.clear().build(!0),typeof t=="function")t.call(this,this);else{t=(t+"").split(`
`);for(let e=0,i=t.length;e<i;e++)this.newLine(t[e])}return this.build(!1).rebuild()}}J(Ot,pr);K({Container:{text:ft(function(s=""){return this.put(new Ot).text(s)}),plain:ft(function(s=""){return this.put(new Ot).plain(s)})}});et(Ot,"Text");class Hi extends Qt{constructor(t,e=t){super(pt("tspan",t),e),this._build=!1}dx(t){return this.attr("dx",t)}dy(t){return this.attr("dy",t)}newLine(){this.dom.newLined=!0;const t=this.parent();if(!(t instanceof Ot))return this;const e=t.index(this),i=nt.window.getComputedStyle(this.node).getPropertyValue("font-size"),n=t.dom.leading*new V(i);return this.dy(e?n:0).attr("x",t.x())}text(t){return t==null?this.node.textContent+(this.dom.newLined?`
`:""):(typeof t=="function"?(this.clear().build(!0),t.call(this,this),this.build(!1)):this.plain(t),this)}}J(Hi,pr);K({Tspan:{tspan:ft(function(s=""){const t=new Hi;return this._build||this.clear(),this.put(t).text(s)})},Text:{newLine:function(s=""){return this.tspan(s).newLine()}}});et(Hi,"Tspan");class Ze extends Qt{constructor(t,e=t){super(pt("circle",t),e)}radius(t){return this.attr("r",t)}rx(t){return this.attr("r",t)}ry(t){return this.rx(t)}size(t){return this.radius(new V(t).divide(2))}}J(Ze,{x:tr,y:er,cx:ir,cy:nr,width:sr,height:rr});K({Container:{circle:ft(function(s=0){return this.put(new Ze).size(s).move(0,0)})}});et(Ze,"Circle");class pn extends qt{constructor(t,e=t){super(pt("clipPath",t),e)}remove(){return this.targets().forEach(function(t){t.unclip()}),super.remove()}targets(){return Ke("svg [clip-path*="+this.id()+"]")}}K({Container:{clip:ft(function(){return this.defs().put(new pn)})},Element:{clipper(){return this.reference("clip-path")},clipWith(s){const t=s instanceof pn?s:this.parent().clip().add(s);return this.attr("clip-path","url(#"+t.id()+")")},unclip(){return this.attr("clip-path",null)}}});et(pn,"ClipPath");class Yn extends de{constructor(t,e=t){super(pt("foreignObject",t),e)}}K({Container:{foreignObject:ft(function(s,t){return this.put(new Yn).size(s,t)})}});et(Yn,"ForeignObject");function Ul(s,t){return this.children().forEach((e,i)=>{let n;try{n=e.bbox()}catch{return}const r=new G(e),o=r.translate(s,t).transform(r.inverse()),a=new _t(n.x,n.y).transform(o);e.move(a.x,a.y)}),this}function Gl(s){return this.dmove(s,0)}function Yl(s){return this.dmove(0,s)}function Wl(s,t=this.bbox()){return s==null?t.height:this.size(t.width,s,t)}function Vl(s=0,t=0,e=this.bbox()){const i=s-e.x,n=t-e.y;return this.dmove(i,n)}function Xl(s,t,e=this.bbox()){const i=qe(this,s,t,e),n=i.width/e.width,r=i.height/e.height;return this.children().forEach((o,a)=>{const l=new _t(e).transform(new G(o).inverse());o.scale(n,r,l.x,l.y)}),this}function jl(s,t=this.bbox()){return s==null?t.width:this.size(s,t.height,t)}function ql(s,t=this.bbox()){return s==null?t.x:this.move(s,t.y,t)}function Kl(s,t=this.bbox()){return s==null?t.y:this.move(t.x,s,t)}var mr={__proto__:null,dmove:Ul,dx:Gl,dy:Yl,height:Wl,move:Vl,size:Xl,width:jl,x:ql,y:Kl};class Bt extends qt{constructor(t,e=t){super(pt("g",t),e)}}J(Bt,mr);K({Container:{group:ft(function(){return this.put(new Bt)})}});et(Bt,"G");class di extends qt{constructor(t,e=t){super(pt("a",t),e)}target(t){return this.attr("target",t)}to(t){return this.attr("href",t,fi)}}J(di,mr);K({Container:{link:ft(function(s){return this.put(new di).to(s)})},Element:{unlink(){const s=this.linker();if(!s)return this;const t=s.parent();if(!t)return this.remove();const e=t.index(s);return t.add(this,e),s.remove(),this},linkTo(s){let t=this.linker();return t||(t=new di,this.wrap(t)),typeof s=="function"?s.call(t,t):t.to(s),this},linker(){const s=this.parent();return s&&s.node.nodeName.toLowerCase()==="a"?s:null}}});et(di,"A");class mn extends qt{constructor(t,e=t){super(pt("mask",t),e)}remove(){return this.targets().forEach(function(t){t.unmask()}),super.remove()}targets(){return Ke("svg [mask*="+this.id()+"]")}}K({Container:{mask:ft(function(){return this.defs().put(new mn)})},Element:{masker(){return this.reference("mask")},maskWith(s){const t=s instanceof mn?s:this.parent().mask().add(s);return this.attr("mask","url(#"+t.id()+")")},unmask(){return this.attr("mask",null)}}});et(mn,"Mask");class gr extends de{constructor(t,e=t){super(pt("stop",t),e)}update(t){return(typeof t=="number"||t instanceof V)&&(t={offset:arguments[0],color:arguments[1],opacity:arguments[2]}),t.opacity!=null&&this.attr("stop-opacity",t.opacity),t.color!=null&&this.attr("stop-color",t.color),t.offset!=null&&this.attr("offset",new V(t.offset)),this}}K({Gradient:{stop:function(s,t,e){return this.put(new gr).update(s,t,e)}}});et(gr,"Stop");function Zl(s,t){if(!s)return"";if(!t)return s;let e=s+"{";for(const i in t)e+=Vo(i)+":"+t[i]+";";return e+="}",e}let gn=class extends de{constructor(t,e=t){super(pt("style",t),e)}addText(t=""){return this.node.textContent+=t,this}font(t,e,i={}){return this.rule("@font-face",{fontFamily:t,src:e,...i})}rule(t,e){return this.addText(Zl(t,e))}};K("Dom",{style(s,t){return this.put(new gn).rule(s,t)},fontface(s,t,e){return this.put(new gn).font(s,t,e)}});et(gn,"Style");class Wn extends Ot{constructor(t,e=t){super(pt("textPath",t),e)}array(){const t=this.track();return t?t.array():null}plot(t){const e=this.track();let i=null;return e&&(i=e.plot(t)),t==null?i:this}track(){return this.reference("href")}}K({Container:{textPath:ft(function(s,t){return s instanceof Ot||(s=this.text(s)),s.path(t)})},Text:{path:ft(function(s,t=!0){const e=new Wn;s instanceof Oe||(s=this.defs().path(s)),e.attr("href","#"+s,fi);let i;if(t)for(;i=this.node.firstChild;)e.node.appendChild(i);return this.put(e)}),textPath(){return this.findOne("textPath")}},Path:{text:ft(function(s){return s instanceof Ot||(s=new Ot().addTo(this.parent()).text(s)),s.path(this)}),targets(){return Ke("svg textPath").filter(s=>(s.attr("href")||"").includes(this.id()))}}});Wn.prototype.MorphArray=Re;et(Wn,"TextPath");class vr extends Qt{constructor(t,e=t){super(pt("use",t),e)}use(t,e){return this.attr("href",(e||"")+"#"+t,fi)}}K({Container:{use:ft(function(s,t){return this.put(new vr).use(s,t)})}});et(vr,"Use");const Tt=Xt;J([Un,Gn,Ie,ai,ki],jt("viewbox"));J([li,vi,ke,Oe],jt("marker"));J(Ot,jt("Text"));J(Oe,jt("Path"));J(zn,jt("Defs"));J([Ot,Hi],jt("Tspan"));J([Jt,$i,mi,Zt],jt("radius"));J(pi,jt("EventTarget"));J(we,jt("Dom"));J(de,jt("Element"));J(Qt,jt("Shape"));J([qt,or],jt("Container"));J(mi,jt("Gradient"));J(Zt,jt("Runner"));Ae.extend(Yo());yl([V,lt,Ut,G,je,_e,Re,_t]);xl();const yr={paddingX:15,paddingY:5,imgMaxWidth:200,imgMaxHeight:100,iconSize:20,lineWidth:1,lineColor:"#549688",lineDasharray:"none",lineFlow:!1,lineFlowDuration:1,lineFlowForward:!0,lineStyle:"straight",rootLineKeepSameInCurve:!0,rootLineStartPositionKeepSameInCurve:!1,lineRadius:5,showLineMarker:!1,generalizationLineWidth:1,generalizationLineColor:"#549688",generalizationLineMargin:0,generalizationNodeMargin:20,associativeLineWidth:2,associativeLineColor:"rgb(51, 51, 51)",associativeLineActiveWidth:8,associativeLineActiveColor:"rgba(2, 167, 240, 1)",associativeLineDasharray:"6,4",associativeLineTextColor:"rgb(51, 51, 51)",associativeLineTextFontSize:14,associativeLineTextLineHeight:1.2,associativeLineTextFontFamily:"微软雅黑, Microsoft YaHei",backgroundColor:"#fafafa",backgroundImage:"none",backgroundRepeat:"no-repeat",backgroundPosition:"center center",backgroundSize:"cover",nodeUseLineStyle:!1,root:{shape:"rectangle",fillColor:"#549688",fontFamily:"微软雅黑, Microsoft YaHei",color:"#fff",fontSize:16,fontWeight:"bold",fontStyle:"normal",borderColor:"transparent",borderWidth:0,borderDasharray:"none",borderRadius:5,textDecoration:"none",gradientStyle:!1,startColor:"#549688",endColor:"#fff",startDir:[0,0],endDir:[1,0],lineMarkerDir:"end",hoverRectColor:"",hoverRectRadius:5,textAlign:"left",imgPlacement:"top",tagPlacement:"right"},second:{shape:"rectangle",marginX:100,marginY:40,fillColor:"#fff",fontFamily:"微软雅黑, Microsoft YaHei",color:"#565656",fontSize:16,fontWeight:"normal",fontStyle:"normal",borderColor:"#549688",borderWidth:1,borderDasharray:"none",borderRadius:5,textDecoration:"none",gradientStyle:!1,startColor:"#549688",endColor:"#fff",startDir:[0,0],endDir:[1,0],lineMarkerDir:"end",hoverRectColor:"",hoverRectRadius:5,textAlign:"left",imgPlacement:"top",tagPlacement:"right"},node:{shape:"rectangle",marginX:50,marginY:0,fillColor:"transparent",fontFamily:"微软雅黑, Microsoft YaHei",color:"#6a6d6c",fontSize:14,fontWeight:"normal",fontStyle:"normal",borderColor:"transparent",borderWidth:0,borderRadius:5,borderDasharray:"none",textDecoration:"none",gradientStyle:!1,startColor:"#549688",endColor:"#fff",startDir:[0,0],endDir:[1,0],lineMarkerDir:"end",hoverRectColor:"",hoverRectRadius:5,textAlign:"left",imgPlacement:"top",tagPlacement:"right"},generalization:{shape:"rectangle",marginX:100,marginY:40,fillColor:"#fff",fontFamily:"微软雅黑, Microsoft YaHei",color:"#565656",fontSize:16,fontWeight:"normal",fontStyle:"normal",borderColor:"#549688",borderWidth:1,borderDasharray:"none",borderRadius:5,textDecoration:"none",gradientStyle:!1,startColor:"#549688",endColor:"#fff",startDir:[0,0],endDir:[1,0],hoverRectColor:"",hoverRectRadius:5,textAlign:"left",imgPlacement:"top",tagPlacement:"right"}},Jl=["lineWidth","lineColor","lineDasharray","lineStyle","generalizationLineWidth","generalizationLineColor","associativeLineWidth","associativeLineColor","associativeLineActiveWidth","associativeLineActiveColor","associativeLineTextColor","associativeLineTextFontSize","associativeLineTextLineHeight","associativeLineTextFontFamily","backgroundColor","backgroundImage","backgroundRepeat","backgroundPosition","backgroundSize","rootLineKeepSameInCurve","rootLineStartPositionKeepSameInCurve","showLineMarker","lineRadius","hoverRectColor","hoverRectRadius","lineFlow","lineFlowDuration","lineFlowForward","textAlign"],Ql=s=>{let t=Object.keys(s);for(let e=0;e<t.length;e++)if(!Jl.find(i=>i===t[e]))return!1;return!0},vn=["lineColor","lineDasharray","lineWidth","lineMarkerDir","lineFlow","lineFlowDuration","lineFlowForward"],at=(s,t,e,i,n,r=0,o=0,a=[])=>{let l=!1;if(e&&(l=e(s,t,n,r,o,a)),!l&&s.children&&s.children.length>0){let h=r+1;s.children.forEach((d,c)=>{at(d,s,e,i,!1,h,c,[...a,s])})}i&&i(s,t,n,r,o,a)},xr=(s,t)=>{let e=[s],i=!1;for(t(s,null)==="stop"&&(i=!0);e.length&&!i;){let n=e.shift();n.children&&n.children.length&&n.children.forEach(r=>{i||(e.push(r),t(r,n)==="stop"&&(i=!0))})}},th=(s,t,e,i)=>{let n=s/t,r=[];if(e&&i)if(s<=e&&t<=i)r=[s,t];else{let o=e/i;n>o?r=[e,e/n]:r=[n*i,i]}else e?s<=e?r=[s,t]:r=[e,e/n]:i&&(t<=i?r=[s,t]:r=[n*i,i]);return r},eh=s=>{s=s.replace(/<br>/gim,`
`);let t=document.createElement("div");return t.innerHTML=s,s=t.textContent,s},le=s=>{try{return JSON.parse(JSON.stringify(s))}catch{return null}},wr=(s,t,e=!1)=>(s.data=le(t.data),e&&(s.data.isActive=!1,Pi(s.data).forEach(n=>{n.isActive=!1})),s.children=[],t.children&&t.children.length>0&&t.children.forEach((i,n)=>{s.children[n]=wr({},i,e)}),Object.keys(t).forEach(i=>{!["data","children"].includes(i)&&!/^_/.test(i)&&(s[i]=t[i])}),s),ci=(s,t,e=!1,i=!0)=>{const n=t.nodeData?t.nodeData:t;return s.data=le(n.data),i?delete s.data.uid:s.data.uid||(s.data.uid=tt()),e&&(s.data.isActive=!1),s.children=[],t.children&&t.children.length>0?t.children.forEach((r,o)=>{s.children[o]=ci({},r,e,i)}):t.nodeData&&t.nodeData.children&&t.nodeData.children.length>0&&t.nodeData.children.forEach((r,o)=>{s.children[o]=ci({},r,e,i)}),Object.keys(n).forEach(r=>{!["data","children"].includes(r)&&!/^_/.test(r)&&(s[r]=n[r])}),s},Vn=(s,t=300,e)=>{let i=null;return(...n)=>{i||(i=setTimeout(()=>{s.call(e,...n),i=null},t))}},ih=(s,t=300,e)=>{let i=null;return(...n)=>{i&&clearTimeout(i),i=setTimeout(()=>{i=null,s.apply(e,n)},t)}},ze=(s,t=()=>{})=>{let e=0,i=s.length;if(i<=0)return t();let n=()=>{if(e>=i){t();return}s[e](),setTimeout(()=>{e++,n()},0)};n()},Ge=s=>s*(Math.PI/180),nh=s=>s.replace(/([a-z])([A-Z])/g,(...t)=>t[1]+"-"+t[2].toLowerCase()),sh=function(s,t){let e=!1,i=null,n=()=>{e=!1,t?s.call(t):s()};if(typeof MutationObserver<"u"){let r=1,o=new MutationObserver(n),a=document.createTextNode(r);o.observe(a,{characterData:!0}),i=function(){r=(r+1)%2,a.data=r}}else i=setTimeout;return function(){e||(e=!0,i(n,0))}},rh=(s,t,e=0,i=0)=>{let n=s.elRect,{scaleX:r,scaleY:o,translateX:a,translateY:l}=s.draw.transform(),{left:h,top:d,width:c,height:f}=t,g=(h+c)*r+a,u=(d+f)*o+l;h=h*r+a,d=d*o+l;let m=0,p=0;return h<0+e&&(m=-h+e),g>n.width-e&&(m=-(g-n.width)-e),d<0+i&&(p=-d+i),u>n.height-i&&(p=-(u-n.height)-i),{isOuter:m!==0||p!==0,offsetLeft:m,offsetTop:p}};let Ei=null;const _r=s=>(Ei||(Ei=document.createElement("div")),Ei.innerHTML=s,Ei.textContent),oh=s=>new Promise(t=>{let e=new Image;e.src=s,e.onload=()=>{t({width:e.width,height:e.height})},e.onerror=()=>{t({width:0,height:0})}}),tt=()=>Go(),ah=s=>new Promise((t,e)=>{let i=new FileReader;i.readAsDataURL(s),i.onload=async n=>{let r=n.target.result,o=await oh(r);t({url:r,size:o})},i.onerror=n=>{e(n)}}),Le=s=>Object.prototype.toString.call(s).slice(8,-1),Me=s=>s==null||s==="";let Ci=null;const lh=s=>{Ci||(Ci=document.createElement("div")),Ci.innerHTML=s;for(let t=Ci.childNodes,e=t.length;e--;)if(t[e].nodeType==1)return!0;return!1},Mr=s=>(s=String(s).replace(/\s+/g,""),["#fff","#ffffff","#FFF","#FFFFFF","rgb(255,255,255)"].includes(s)||/rgba\(255,255,255,[^)]+\)/.test(s)),hh=s=>(s=String(s).replace(/\s+/g,""),["","transparent"].includes(s)||/rgba\(\d+,\d+,\d+,0\)/.test(s)),dh=s=>{let{lineColor:t,root:e,second:i,node:n}=s,r=[t,e.fillColor,e.color,i.fillColor,i.color,n.fillColor,n.color,e.borderColor,i.borderColor,n.borderColor];for(let o=0;o<r.length;o++){let a=r[o];if(!hh(a)&&!Mr(a))return a}};let ve=null;const ch=s=>{ve||(ve=document.createElement("div")),ve.innerHTML=s;const t=ve.querySelectorAll(".ql-formula");Array.from(t).forEach(n=>{const r=document.createTextNode("$smmformula$");n.parentNode.replaceChild(r,n)});const e=ve.childNodes;let i=[];for(let n=0;n<e.length;n++){const r=e[n];r.nodeType===1?i.push(r.textContent):r.nodeType===3&&i.push(r.nodeValue)}if(s=i.map(n=>`<p><span>${Ui(n)}</span></p>`).join(""),t.length>0){s=s.replace(/\$smmformula\$/g,'<span class="smmformula"></span>'),ve.innerHTML=s;const n=ve.querySelectorAll(".smmformula");Array.from(n).forEach((r,o)=>{r.parentNode.replaceChild(t[o],r)}),s=ve.innerHTML}return s},uh=(s,t)=>{const e={};return Object.keys(t).forEach(i=>{const n=s[i],r=t[i];if(Le(n)!==Le(r)){e[i]=r;return}if(Le(n)==="Object"){if(JSON.stringify(n)!==JSON.stringify(r)){e[i]=r;return}}else if(n!==r){e[i]=r;return}}),e},yn=s=>/^_/.test(s)?!1:!Ai.includes(s),fh=s=>{const t=[...vn],e=Object.keys(s);for(let i=0;i<e.length;i++)if(!t.includes(e[i]))return!1;return!0},ph=s=>s.reduce((t,e)=>{const i=t.find(n=>n.type===e.type);return i?e.list.forEach(n=>{const r=i.list.find(o=>o.name===n.name);r?r.icon=n.icon:i.list.push(n)}):t.push({...e}),t},[]),cs=s=>{let t=[];return s.forEach(e=>{s.find(i=>i.uid!==e.uid&&i.isAncestor(e))||t.push(e)}),t},mh=s=>{const t={},e={};s.forEach(n=>{const r=n.parent;if(r){const o=r.uid;e[o]=r;const a=n.getIndexInBrothers(),l={node:n,index:a};t[o]?t[o].find(h=>h.index===l.index)||t[o].push(l):t[o]=[l]}});const i=[];return Object.keys(t).forEach(n=>{if(t[n].length>1){const r=t[n].map(o=>o.index).sort((o,a)=>o-a);i.push({node:e[n],range:[r[0],r[r.length-1]]})}else i.push({node:t[n][0].node})}),i},gh=(s,t,e,i,n,r,o,a)=>t>n&&r>s&&i>o&&a>e,vh=s=>{let t=window.getSelection(),e=document.createRange();e.selectNodeContents(s),e.collapse(),t.removeAllRanges(),t.addRange(e)},yh=s=>{let t=window.getSelection(),e=document.createRange();e.selectNodeContents(s),t.removeAllRanges(),t.addRange(e)},Ni=(s,t={})=>{t={...t},t&&t.richText&&t.resetRichText&&delete t.resetRichText;const i=n=>{n.forEach(r=>{r.data={...r.data,...t},r.children&&r.children.length>0&&i(r.children)})};return i(s),s},ei=(s,t=!1,e=null,i=!1)=>{const n=r=>{r.forEach(o=>{o.data||(o.data={}),(t||Me(o.data.uid))&&(o.data.uid=tt()),i&&Pi(o.data).forEach(l=>{(t||Me(l.uid))&&(l.uid=tt())}),e&&e(o),o.children&&o.children.length>0&&n(o.children)})};return n(s),s},ee=s=>s?Array.isArray(s)?s:[s]:[],ii=s=>s.parent?s.parent.nodeData.children.findIndex(t=>t.data.uid===s.uid):0,Pt=(s,t)=>t.findIndex(e=>e.uid===s.uid),Er=s=>{let t=0;for(let n=0;n<s.length;n++)t=s.charCodeAt(n)+((t<<5)-t);return"hsla("+new bn(t).genrand_int32()%360+", 50%, 50%, 1)"},Ui=s=>([["&","&amp;"],["<","&lt;"],[">","&gt;"]].forEach(t=>{s=s.replace(new RegExp(t[0],"g"),t[1])}),s),xn=(s,t)=>{const e=Le(s);if(e!==Le(t))return!1;if(e==="Object"){const i=Object.keys(s),n=Object.keys(t);if(i.length!==n.length)return!1;for(let r=0;r<i.length;r++){const o=i[r];if(!n.includes(o)||!xn(s[o],t[o]))return!1}return!0}else if(e==="Array"){if(s.length!==t.length)return!1;for(let i=0;i<s.length;i++){const n=s[i],r=t[i],o=Le(n),a=Le(r);if(o!==a||!xn(n,r))return!1}return!0}else return s===t},Cr=()=>navigator.clipboard&&typeof navigator.clipboard.read=="function",us=s=>{navigator.clipboard&&navigator.clipboard.writeText&&navigator.clipboard.writeText(JSON.stringify(s))},xh=async()=>{let s=null,t=null;if(Cr()){const e=await navigator.clipboard.read();if(e&&e.length>0)for(const i of e)for(const n of i.types)/^image\//.test(n)?t=await i.getType(n):n==="text/plain"&&(s=await(await i.getType(n)).text())}return{text:s,img:t}},Zi=s=>{if(!s||!s.parent)return;const t=ii(s);t!==-1&&s.parent.nodeData.children.splice(t,1)},wh=(s,t)=>{if(s.length!==t.length)return!1;for(let e=0;e<s.length;e++)if(!t.find(i=>i.uid===s[e].uid))return!1;return!0},fs=s=>({simpleMindMap:!0,data:s}),wn=s=>{let t=null;if(typeof s=="string")try{const i=JSON.parse(s);typeof i=="object"&&i.simpleMindMap&&(t=i.data)}catch{}else typeof s=="object"&&s.simpleMindMap&&(t=s.data);const e=!!t;return{isSmm:e,data:e?t:String(s)}},ps=(s,t)=>{s.preventDefault();const e=window.getSelection();if(!e.rangeCount)return;e.deleteFromDocument(),t=t||s.clipboardData.getData("text"),t=Ui(t),t=_r(t);const i=t.split(/\n/g),n=document.createDocumentFragment();i.forEach((r,o)=>{const a=document.createTextNode(r);if(n.appendChild(a),o<i.length-1){const l=document.createElement("br");n.appendChild(l)}}),e.getRangeAt(0).insertNode(n),e.collapseToEnd()},ms=s=>{const t={},e=(i,n)=>{const r=i.data.uid;n&&n.children.push(r),t[r]={isRoot:!n,data:{...i.data},children:[]},i.children&&i.children.length>0&&i.children.forEach(o=>{e(o,t[r])})};return e(s,null),t},_h=({addContentToHeader:s,addContentToFooter:t})=>{const e=[];let i=null,n=0,r=null,o=0;const a=(l,h)=>{if(typeof l=="function"){const d=l();if(!d)return;const{el:c,cssText:f,height:g}=d;if(c instanceof HTMLElement){We(c);const u=Ye({el:c,height:g});h(u,g)}f&&e.push(f)}};return a(s,(l,h)=>{i=l,n=h}),a(t,(l,h)=>{r=l,o=h}),{cssTextList:e,header:i,headerHeight:n,footer:r,footerHeight:o}},Mh=(s,t=0,e=0,i=0,n=0,r=!1,o=!1)=>{let a=1/0,l=-1/0,h=1/0,d=-1/0;const c=(f,g)=>{if(!(g&&r)&&f.group)try{const{x:u,y:m,width:p,height:x}=f.group.findOne(".smm-node-shape").rbox();u<a&&(a=u),u+p>l&&(l=u+p),m<h&&(h=m),m+x>d&&(d=m+x)}catch{}!o&&f._generalizationList.length>0&&f._generalizationList.forEach(u=>{c(u.generalizationNode)}),f.children&&f.children.forEach(u=>{c(u)})};return c(s,!0),a=a-t+i,h=h-e+n,l=l-t+i,d=d-e+n,{left:a,top:h,width:l-a,height:d-h}},Eh=()=>{if(document.documentElement.requestFullScreen)return"fullscreenchange";if(document.documentElement.webkitRequestFullScreen)return"webkitfullscreenchange";if(document.documentElement.mozRequestFullScreen)return"mozfullscreenchange";if(document.documentElement.msRequestFullscreen)return"msfullscreenchange"};Eh();const Ye=({el:s,width:t,height:e})=>{const i=new Yn;return t!==void 0&&i.width(t),e!==void 0&&i.height(e),i.add(s),i},Pi=s=>{const t=s.generalization;return t?Array.isArray(t)?t:[t]:[]},We=s=>{s.setAttribute("xmlns","http://www.w3.org/1999/xhtml")},gs=s=>(s=[...s],s.sort((t,e)=>t.sortIndex-e.sortIndex),s),Nr=(s,t)=>Oi(s,t,{arrayMerge:(e,i)=>i}),Ch=s=>{const t={};return So.forEach(e=>{let i=s.style.merge(e);e==="fontSize"&&(i=i+"px"),t[e]=i}),t},vs=["backgroundColor","backgroundImage","backgroundRepeat","backgroundPosition","backgroundSize"],Tr=["gradientStyle","startColor","endColor","startDir","endDir","fillColor","borderColor","borderWidth","borderDasharray"];class ae{static setBackgroundStyle(t,e){if(!t)return;if(!ae.cacheStyle){ae.cacheStyle={};let l=window.getComputedStyle(t);vs.forEach(h=>{ae.cacheStyle[h]=l[h]})}let{backgroundColor:i,backgroundImage:n,backgroundRepeat:r,backgroundPosition:o,backgroundSize:a}=e;t.style.backgroundColor=i,n&&n!=="none"?(t.style.backgroundImage=`url(${n})`,t.style.backgroundRepeat=r,t.style.backgroundPosition=o,t.style.backgroundSize=a):t.style.backgroundImage="none"}static removeBackgroundStyle(t){ae.cacheStyle&&(vs.forEach(e=>{t.style[e]=ae.cacheStyle[e]}),ae.cacheStyle=null)}constructor(t){this.ctx=t,this._markerPath=null,this._marker=null,this._gradient=null}merge(t,e){let i=this.ctx.mindMap.themeConfig,n=null,r=!1;e?(r=!0,n=i):this.ctx.isGeneralization?n=i.generalization:this.ctx.layerIndex===0?n=i.root:this.ctx.layerIndex===1?n=i.second:n=i.node;let o="";return this.getSelfStyle(t)!==void 0?o=this.getSelfStyle(t):n[t]!==void 0?o=n[t]:o=i[t],r||this.addToEffectiveStyles({[t]:o}),o}getStyle(t,e){return this.merge(t,e)}getSelfStyle(t){return this.ctx.getData(t)}addToEffectiveStyles(t){this.ctx.mindMap.painter&&(this.ctx.effectiveStyles={...this.ctx.effectiveStyles,...t})}rect(t){this.shape(t),t.radius(this.merge("borderRadius"))}shape(t){const e={};Tr.forEach(i=>{e[i]=this.merge(i)}),e.gradientStyle?(this._gradient||(this._gradient=this.ctx.nodeDraw.gradient("linear")),this._gradient.update(i=>{i.stop(0,e.startColor),i.stop(1,e.endColor)}),this._gradient.from(...e.startDir).to(...e.endDir),t.fill(this._gradient)):t.fill({color:e.fillColor}),t.stroke({color:e.borderColor,width:e.borderWidth,dasharray:e.borderDasharray})}text(t){const e={color:this.merge("color"),fontFamily:this.merge("fontFamily"),fontSize:this.merge("fontSize"),fontWeight:this.merge("fontWeight"),fontStyle:this.merge("fontStyle"),textDecoration:this.merge("textDecoration")};t.fill({color:e.color}).css({"font-family":e.fontFamily,"font-size":e.fontSize+"px","font-weight":e.fontWeight,"font-style":e.fontStyle,"text-decoration":e.textDecoration})}domText(t,e=1){const i={color:this.merge("color"),fontFamily:this.merge("fontFamily"),fontSize:this.merge("fontSize"),fontWeight:this.merge("fontWeight"),fontStyle:this.merge("fontStyle"),textDecoration:this.merge("textDecoration"),textAlign:this.merge("textAlign")};t.style.color=i.color,t.style.textDecoration=i.textDecoration,t.style.fontFamily=i.fontFamily,t.style.fontSize=i.fontSize*e+"px",t.style.fontWeight=i.fontWeight||"normal",t.style.fontStyle=i.fontStyle,t.style.textAlign=i.textAlign}tagText(t,e){t.fill({color:"#fff"}).css({"font-size":e.fontSize+"px"})}tagRect(t,e){t.fill({color:e.fill}),e.radius&&t.radius(e.radius)}iconNode(t,e){t.attr({fill:e||this.merge("color")})}line(t,{width:e,color:i,dasharray:n}={},r,o){const{customHandleLine:a}=this.ctx.mindMap.opt;if(typeof a=="function"&&a(this.ctx,t,{width:e,color:i,dasharray:n}),t.stroke({color:i,dasharray:n,width:e}).fill({color:"none"}),r){const l=this.merge("showLineMarker",!0),h=o.style;if(l){h._marker=h._marker||h.createMarker(),h._markerPath.stroke({color:i}).fill({color:i}),t.attr("marker-start",""),t.attr("marker-end","");const d=h.merge("lineMarkerDir");t.marker(d,h._marker)}else h._marker&&(t.attr("marker-start",""),t.attr("marker-end",""),h._marker.remove(),h._marker=null)}}createMarker(){return this.ctx.lineDraw.marker(20,20,t=>{t.ref(8,5),t.size(20,20),t.attr("markerUnits","userSpaceOnUse"),t.attr("orient","auto-start-reverse"),this._markerPath=t.path("M0,0 L2,5 L0,10 L10,5 Z")})}generalizationLine(t){t.stroke({width:this.merge("generalizationLineWidth",!0),color:this.merge("generalizationLineColor",!0)}).fill({color:"none"})}iconBtn(t,e,i){let{color:n,fill:r,fontSize:o,fontColor:a}=this.ctx.mindMap.opt.expandBtnStyle||{color:"#808080",fill:"#fff",fontSize:12,fontColor:"#333333"};t.fill({color:n}),e.fill({color:n}),i.fill({color:r}),this.ctx.mindMap.opt.isShowExpandNum&&t.attr({"font-size":o+"px","font-color":a})}hasCustomStyle(){let t=!1;return Object.keys(this.ctx.getData()).forEach(e=>{yn(e)&&(t=!0)}),t}getCustomStyle(){const t={};return Object.keys(this.ctx.getData()).forEach(e=>{yn(e)&&(t[e]=this.ctx.getData(e))}),t}hoverNode(t){const e=this.merge("hoverRectColor")||this.ctx.mindMap.opt.hoverRectColor,i=this.merge("hoverRectRadius");t.radius(i).fill("none").stroke({color:e})}onRemove(){this._marker&&(this._marker.remove(),this._marker=null),this._markerPath&&(this._markerPath.remove(),this._markerPath=null),this._gradient&&(this._gradient.remove(),this._gradient=null)}}ae.cacheStyle=null;class Nh{constructor(t){this.node=t,this.mindMap=t.mindMap}getShapePadding(t,e,i,n){const r=this.node.getShape(),o=15,a=5,l=t+i*2,h=e+n*2,d=Math.abs(l-h);switch(r){case M.SHAPE.ROUNDED_RECTANGLE:return{paddingX:e>t?(e-t)/2:0,paddingY:0};case M.SHAPE.DIAMOND:return{paddingX:t/2,paddingY:e/2};case M.SHAPE.PARALLELOGRAM:return{paddingX:i<=0?o:0,paddingY:0};case M.SHAPE.OUTER_TRIANGULAR_RECTANGLE:return{paddingX:i<=0?o:0,paddingY:0};case M.SHAPE.INNER_TRIANGULAR_RECTANGLE:return{paddingX:i<=0?o:0,paddingY:0};case M.SHAPE.ELLIPSE:return{paddingX:i<=0?o:0,paddingY:n<=0?a:0};case M.SHAPE.CIRCLE:return{paddingX:h>l?d/2:0,paddingY:h<l?d/2:0}}const c=this.getShapeFromExtendList(r);return c?c.getPadding({node:this.node,width:t,height:e,paddingX:i,paddingY:n})||{paddingX:0,paddingY:0}:{paddingX:0,paddingY:0}}getShapeFromExtendList(t){return this.mindMap.extendShapeList.find(e=>e.name===t)}createShape(){const t=this.node.getShape();let e=null;if(t===M.SHAPE.RECTANGLE?e=this.createRect():t===M.SHAPE.DIAMOND?e=this.createDiamond():t===M.SHAPE.PARALLELOGRAM?e=this.createParallelogram():t===M.SHAPE.ROUNDED_RECTANGLE?e=this.createRoundedRectangle():t===M.SHAPE.OCTAGONAL_RECTANGLE?e=this.createOctagonalRectangle():t===M.SHAPE.OUTER_TRIANGULAR_RECTANGLE?e=this.createOuterTriangularRectangle():t===M.SHAPE.INNER_TRIANGULAR_RECTANGLE?e=this.createInnerTriangularRectangle():t===M.SHAPE.ELLIPSE?e=this.createEllipse():t===M.SHAPE.CIRCLE&&(e=this.createCircle()),!e){const i=this.getShapeFromExtendList(t);i&&(e=i.createShape(this.node))}return e||this.createRect()}getNodeSize(){const t=this.node.getBorderWidth();let{width:e,height:i}=this.node;return e-=t,i-=t,{width:e,height:i}}createPath(t){const{customCreateNodePath:e}=this.mindMap.opt;return e?Tt(e(t)):new Oe().plot(t)}createPolygon(t){const{customCreateNodePolygon:e}=this.mindMap.opt;return e?Tt(e(t)):new ke().plot(t)}createRect(){let{width:t,height:e}=this.getNodeSize(),i=this.node.style.merge("borderRadius");const n=`
      M${i},0
      L${t-i},0
      C${t-i},0 ${t},0 ${t},${i}
      L${t},${e-i}
      C${t},${e-i} ${t},${e} ${t-i},${e}
      L${i},${e}
      C${i},${e} 0,${e} 0,${e-i}
      L0,${i}
      C0,${i} 0,0 ${i},0
      Z
    `;return this.createPath(n)}createDiamond(){let{width:t,height:e}=this.getNodeSize(),i=t/2,n=e/2;const g=[[i,0],[t,n],[i,e],[0,n]];return this.createPolygon(g)}createParallelogram(){let{paddingX:t}=this.node.getPaddingVale();t=t||this.node.shapePadding.paddingX;let{width:e,height:i}=this.getNodeSize();const n=[[t,0],[e,0],[e-t,i],[0,i]];return this.createPolygon(n)}createRoundedRectangle(){let{width:t,height:e}=this.getNodeSize(),i=e/2;const n=`
      M${i},0
      L${t-i},0
      A${e/2},${e/2} 0 0,1 ${t-i},${e} 
      L${i},${e}
      A${e/2},${e/2} 0 0,1 ${i},0
    `;return this.createPath(n)}createOctagonalRectangle(){let t=5,{width:e,height:i}=this.getNodeSize();const n=[[0,t],[t,0],[e-t,0],[e,t],[e,i-t],[e-t,i],[t,i],[0,i-t]];return this.createPolygon(n)}createOuterTriangularRectangle(){let{paddingX:t}=this.node.getPaddingVale();t=t||this.node.shapePadding.paddingX;let{width:e,height:i}=this.getNodeSize();const n=[[t,0],[e-t,0],[e,i/2],[e-t,i],[t,i],[0,i/2]];return this.createPolygon(n)}createInnerTriangularRectangle(){let{paddingX:t}=this.node.getPaddingVale();t=t||this.node.shapePadding.paddingX;let{width:e,height:i}=this.getNodeSize();const n=[[0,0],[e,0],[e-t/2,i/2],[e,i],[0,i],[t/2,i/2]];return this.createPolygon(n)}createEllipse(){let{width:t,height:e}=this.getNodeSize(),i=t/2,n=e/2;const r=`
      M${i},0
      A${i},${n} 0 0,1 ${i},${e} 
      M${i},${e} 
      A${i},${n} 0 0,1 ${i},0 
    `;return this.createPath(r)}createCircle(){let{width:t,height:e}=this.getNodeSize(),i=t/2,n=e/2;const r=`
      M${i},0
      A${i},${n} 0 0,1 ${i},${e} 
      M${i},${e} 
      A${i},${n} 0 0,1 ${i},0 
    `;return this.createPath(r)}}const Th=[M.SHAPE.RECTANGLE,M.SHAPE.DIAMOND,M.SHAPE.PARALLELOGRAM,M.SHAPE.ROUNDED_RECTANGLE,M.SHAPE.OCTAGONAL_RECTANGLE,M.SHAPE.OUTER_TRIANGULAR_RECTANGLE,M.SHAPE.INNER_TRIANGULAR_RECTANGLE,M.SHAPE.ELLIPSE,M.SHAPE.CIRCLE];function Sh(){const s=this.getData("generalization");return Array.isArray(s)?s:s?[s]:[]}function Lh(){return this.formatGetGeneralization().length>0}function bh(){return!!this.formatGetGeneralization().find(t=>!t.range||t.range.length<=0)}function Dh(s){return this._generalizationList.findIndex(t=>t.generalizationNode.uid===s.uid)}function Ah(){if(this.isGeneralization||!this.checkHasGeneralization())return;let s=0,t=0;this.formatGetGeneralization().forEach((i,n)=>{let r=this._generalizationList[n];r||(r=this._generalizationList[n]={}),r.node=this,r.range=i.range,r.generalizationLine||(r.generalizationLine=this.lineDraw.path()),r.generalizationNode||(r.generalizationNode=new Yi({data:{inserting:i.inserting,data:i},uid:tt(),renderer:this.renderer,mindMap:this.mindMap,isGeneralization:!0})),delete i.inserting,r.generalizationNode.generalizationBelongNode=this,r.generalizationNode.width>s&&(s=r.generalizationNode.width),r.generalizationNode.height>t&&(t=r.generalizationNode.height),i.isActive&&this.renderer.addNodeToActiveList(r.generalizationNode)}),this._generalizationNodeWidth=s,this._generalizationNodeHeight=t}function Rh(){this.isGeneralization||(this.removeGeneralization(),this.createGeneralizationNode())}function Ih(s){if(this.isGeneralization)return;this.updateGeneralizationData();const t=this.formatGetGeneralization();if(t.length<=0||this.getData("expand")===!1){this.removeGeneralization();return}t.length!==this._generalizationList.length&&this.removeGeneralization(),this.createGeneralizationNode(),this.renderer.layout.renderGeneralization(this._generalizationList),this._generalizationList.forEach(e=>{this.style.generalizationLine(e.generalizationLine),e.generalizationNode.render(()=>{},s)})}function Oh(){const s=this.getChildrenLength(),t=this.formatGetGeneralization(),e=[];t.forEach(i=>{if(!i.range){e.push(i);return}i.range.length>0&&i.range[0]<=s-1&&i.range[1]<=s-1&&e.push(i)}),e.length!==t.length&&this.setData({generalization:e})}function kh(){this.isGeneralization||(this._generalizationList.forEach(s=>{s.generalizationNode.style.onRemove(),s.generalizationLine&&(s.generalizationLine.remove(),s.generalizationLine=null),s.generalizationNode&&(this.renderer.removeNodeFromActiveList(s.generalizationNode),s.generalizationNode.remove(),s.generalizationNode=null)}),this._generalizationList=[],this.generalizationBelongNode&&this.nodeDraw.find(".generalization_"+this.generalizationBelongNode.uid).remove())}function zh(){this.isGeneralization||this._generalizationList.forEach(s=>{s.generalizationLine&&s.generalizationLine.hide(),s.generalizationNode&&s.generalizationNode.hide()})}function Fh(){this.isGeneralization||this._generalizationList.forEach(s=>{s.generalizationLine&&s.generalizationLine.show(),s.generalizationNode&&s.generalizationNode.show()})}function Ph(s){this._generalizationList.forEach(t=>{t.generalizationLine.opacity(s),t.generalizationNode.group.opacity(s)})}function Bh(){const s=this.generalizationBelongNode,t=s.formatGetGeneralization(),e=s.getGeneralizationNodeIndex(this),i=t[e],r=this.getStyle("hoverRectColor")||this.mindMap.opt.hoverRectColor,o=r?{stroke:r}:null;Array.isArray(i.range)&&i.range.length>0?this.mindMap.renderer.highlightNode(s,i.range,o):this.mindMap.renderer.highlightNode(s,null,o)}function $h(){this.mindMap.renderer.closeHighlightNode()}const ys={formatGetGeneralization:Sh,checkHasGeneralization:Lh,checkHasSelfGeneralization:bh,getGeneralizationNodeIndex:Dh,createGeneralizationNode:Ah,updateGeneralization:Rh,updateGeneralizationData:Oh,renderGeneralization:Ih,removeGeneralization:kh,hideGeneralization:zh,showGeneralization:Fh,setGeneralizationOpacity:Ph,handleGeneralizationMouseenter:Bh,handleGeneralizationMouseleave:$h},Hh='<svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="200" height="200"><path d="M475.136 327.168v147.968h-147.968v74.24h147.968v147.968h74.24v-147.968h147.968v-74.24h-147.968v-147.968h-74.24z m36.864-222.208c225.28 0 407.04 181.76 407.04 407.04s-181.76 407.04-407.04 407.04-407.04-181.76-407.04-407.04 181.76-407.04 407.04-407.04z m0-74.24c-265.216 0-480.768 215.552-480.768 480.768s215.552 480.768 480.768 480.768 480.768-215.552 480.768-480.768-215.552-480.768-480.768-480.768z"></path></svg>',Uh='<svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="200" height="200"><path d="M512 105.472c225.28 0 407.04 181.76 407.04 407.04s-181.76 407.04-407.04 407.04-407.04-181.76-407.04-407.04 181.76-407.04 407.04-407.04z m0-74.24c-265.216 0-480.768 215.552-480.768 480.768s215.552 480.768 480.768 480.768 480.768-215.552 480.768-480.768-215.552-480.768-480.768-480.768z"></path><path d="M252.928 474.624h518.144v74.24h-518.144z"></path></svg>',Gh='<svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="48" height="48"><path d="M514.048 62.464q93.184 0 175.616 35.328t143.872 96.768 96.768 143.872 35.328 175.616q0 94.208-35.328 176.128t-96.768 143.36-143.872 96.768-175.616 35.328q-94.208 0-176.64-35.328t-143.872-96.768-96.768-143.36-35.328-176.128q0-93.184 35.328-175.616t96.768-143.872 143.872-96.768 176.64-35.328zM772.096 576.512q26.624 0 45.056-18.944t18.432-45.568-18.432-45.056-45.056-18.432l-192.512 0 0-192.512q0-26.624-18.944-45.568t-45.568-18.944-45.056 18.944-18.432 45.568l0 192.512-192.512 0q-26.624 0-45.056 18.432t-18.432 45.056 18.432 45.568 45.056 18.944l192.512 0 0 191.488q0 26.624 18.432 45.568t45.056 18.944 45.568-18.944 18.944-45.568l0-191.488 192.512 0z"></path></svg>',_n={open:Hh,close:Uh,quickCreateChild:Gh};function Yh(){if(this._openExpandNode)return;const{expandBtnSize:s,expandBtnIcon:t,isShowExpandNum:e}=this.mindMap.opt;let{close:i,open:n}=t||{};e?(this._openExpandNode=new Ot,this._openExpandNode.addClass("smm-expand-btn-text"),this._openExpandNode.attr({"text-anchor":"middle","dominant-baseline":"middle",x:s/2,y:2})):(this._openExpandNode=Tt(n||_n.open).size(s,s),this._openExpandNode.x(0).y(-s/2)),this._closeExpandNode=Tt(i||_n.close).size(s,s),this._closeExpandNode.x(0).y(-s/2),this._fillExpandNode=new Ze().size(s),this._fillExpandNode.x(0).y(-s/2),this.style.iconBtn(this._openExpandNode,this._closeExpandNode,this._fillExpandNode)}function Wh(s=[]){return s.reduce((t,e)=>t+this.sumNode(e.children||[]),s.length)}function Vh(){let{expand:s}=this.getData();if(s===this._lastExpandBtnType)return;this._expandBtn&&this._expandBtn.clear(),this.createExpandNodeContent();let t;if(s===!1?(t=this._openExpandNode,this._lastExpandBtnType=!1):(t=this._closeExpandNode,this._lastExpandBtnType=!0),this._expandBtn){let{isShowExpandNum:e,expandBtnStyle:i,expandBtnNumHandler:n}=this.mindMap.opt;if(e)if(s)this._fillExpandNode.stroke("none");else{this._fillExpandNode.stroke({color:i.strokeColor});let r=this.sumNode(this.nodeData.children||[]);if(typeof n=="function"){const o=n(r,this);Me(o)||(r=o)}t.text(String(r))}this._expandBtn.add(this._fillExpandNode).add(t)}}function Xh(){this._expandBtn&&this.renderer.layout.renderExpandBtn(this,this._expandBtn)}function jh(){this.getChildrenLength()<=0||this.isRoot||(this._expandBtn?this.group.add(this._expandBtn):(this._expandBtn=new Bt,this._expandBtn.on("mouseover",s=>{s.stopPropagation(),this._expandBtn.css({cursor:"pointer"})}),this._expandBtn.on("mouseout",s=>{s.stopPropagation(),this._expandBtn.css({cursor:"auto"})}),this._expandBtn.on("click",s=>{s.stopPropagation(),this.mindMap.execCommand("SET_NODE_EXPAND",this,!this.getData("expand")),this.mindMap.emit("expand_btn_click",this)}),this._expandBtn.on("dblclick",s=>{s.stopPropagation()}),this._expandBtn.addClass("smm-expand-btn"),this.group.add(this._expandBtn)),this._showExpandBtn=!0,this.updateExpandBtnNode(),this.updateExpandBtnPos())}function qh(){this._expandBtn&&this._showExpandBtn&&(this._expandBtn.remove(),this._showExpandBtn=!1)}function Kh(){const{alwaysShowExpandBtn:s,notShowExpandBtn:t}=this.mindMap.opt;s||t||setTimeout(()=>{this.renderExpandBtn()},0)}function Zh(){const{alwaysShowExpandBtn:s,notShowExpandBtn:t}=this.mindMap.opt;if(s||this._isMouseenter||t)return;let{isActive:e,expand:i}=this.getData();!e&&i&&setTimeout(()=>{this.removeExpandBtn()},0)}const xs={createExpandNodeContent:Yh,updateExpandBtnNode:Vh,updateExpandBtnPos:Xh,renderExpandBtn:jh,removeExpandBtn:qh,showExpandBtn:Kh,hideExpandBtn:Zh,sumNode:Wh};function Jh(s={}){this.mindMap.execCommand("SET_NODE_DATA",this,s)}function Qh(s,t,e){this.mindMap.execCommand("SET_NODE_TEXT",this,s,t,e)}function td(s){this.mindMap.execCommand("SET_NODE_IMAGE",this,s)}function ed(s){this.mindMap.execCommand("SET_NODE_ICON",this,s)}function id(s,t){this.mindMap.execCommand("SET_NODE_HYPERLINK",this,s,t)}function nd(s){this.mindMap.execCommand("SET_NODE_NOTE",this,s)}function sd(s,t){this.mindMap.execCommand("SET_NODE_ATTACHMENT",this,s,t)}function rd(s){this.mindMap.execCommand("SET_NODE_TAG",this,s)}function od(s){this.mindMap.execCommand("SET_NODE_SHAPE",this,s)}function ad(s,t){this.mindMap.execCommand("SET_NODE_STYLE",this,s,t)}function ld(s){this.mindMap.execCommand("SET_NODE_STYLES",this,s)}const ws={setData:Jh,setText:Qh,setImage:td,setIcon:ed,setHyperlink:id,setNote:nd,setAttachment:sd,setTag:rd,setShape:od,setStyle:ad,setStyles:ld},hd='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024" ><path d="M435.484444 251.733333v68.892445L295.822222 320.682667a168.504889 168.504889 0 0 0-2.844444 336.952889h142.506666v68.892444H295.822222a237.397333 237.397333 0 0 1 0-474.794667h139.662222z m248.945778 0a237.397333 237.397333 0 0 1 0 474.851556H544.654222v-69.006222l139.776 0.056889a168.504889 168.504889 0 0 0 2.844445-336.952889H544.597333V251.676444h139.776z m-25.827555 203.946667a34.474667 34.474667 0 0 1 0 68.892444H321.649778a34.474667 34.474667 0 0 1 0-68.892444h336.952889z" ></path></svg>',dd='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024" ><path d="M152.768 985.984 152.768 49.856l434.56 0 66.816 0 234.048 267.392 0 66.816 0 601.92L152.768 985.984 152.768 985.984zM654.144 193.088l0 124.16 108.736 0L654.144 193.088 654.144 193.088zM821.312 384.064l-167.168 0L587.328 384.064 587.328 317.312 587.328 116.736 219.584 116.736 219.584 919.04l601.728 0L821.312 384.064 821.312 384.064zM386.688 517.888 319.808 517.888 319.808 450.944l66.816 0L386.624 517.888 386.688 517.888zM386.688 651.584 319.808 651.584 319.808 584.704l66.816 0L386.624 651.584 386.688 651.584zM386.688 785.344 319.808 785.344l0-66.88 66.816 0L386.624 785.344 386.688 785.344zM721.024 517.888 453.632 517.888 453.632 450.944l267.392 0L721.024 517.888 721.024 517.888zM654.144 651.584 453.632 651.584 453.632 584.704l200.512 0L654.144 651.584 654.144 651.584zM620.672 785.344l-167.04 0 0-66.88 167.04 0L620.672 785.344 620.672 785.344z" ></path></svg>',cd='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024" width="128" height="128"><path d="M516.373333 375.978667l136.576-136.576a147.797333 147.797333 0 0 1 208.853334-0.021334 147.690667 147.690667 0 0 1-0.042667 208.832l-204.8 204.778667v0.021333l-153.621333 153.6c-85.973333 85.973333-225.28 85.973333-311.253334 0.021334-85.994667-85.973333-85.973333-225.216 0.149334-311.36L431.146667 256.362667a21.333333 21.333333 0 0 0-30.165334-30.165334L162.069333 465.066667c-102.805333 102.826667-102.826667 269.056-0.149333 371.733333 102.613333 102.613333 268.970667 102.613333 371.584 0l153.6-153.642667h0.021333l0.021334-0.021333 204.778666-204.778667c74.325333-74.325333 74.346667-194.858667 0.021334-269.184-74.24-74.24-194.88-74.24-269.162667 0.042667l-136.576 136.554667-187.626667 187.626666a117.845333 117.845333 0 0 0-0.106666 166.826667 118.037333 118.037333 0 0 0 166.826666-0.106667l255.850667-255.829333a21.333333 21.333333 0 0 0-30.165333-30.165333L435.136 669.973333a75.370667 75.370667 0 0 1-106.496 0.106667 75.178667 75.178667 0 0 1 0.128-106.496l187.605333-187.605333z" ></path></svg>',Sr=[{name:"优先级图标",type:"priority",list:[{name:"1",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512.042667 1024C229.248 1024 0 794.794667 0 511.957333 0 229.205333 229.248 0 512.042667 0 794.752 0 1024 229.205333 1024 511.957333 1024 794.794667 794.752 1024 512.042667 1024z" fill="#E93B30"></path><path d="M580.309333 256h-75.52c-10.666667 29.824-30.165333 55.765333-58.709333 78.165333-28.416 22.314667-54.869333 37.418667-79.146667 45.397334v84.608a320 320 0 0 0 120.234667-70.698667v352.085333H580.266667V256z" fill="#FFFFFF"></path></svg>'},{name:"2",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M511.957333 1024C229.248 1024 0 794.752 0 512S229.248 0 511.957333 0C794.752 0 1024 229.248 1024 512s-229.248 512-512.042667 512z" fill="#FA8D2E"></path><path d="M667.946667 658.602667h-185.301334c4.864-8.533333 11.178667-17.066667 19.072-25.984 7.808-8.874667 26.453333-26.837333 55.936-53.888 29.525333-27.008 49.877333-47.786667 61.226667-62.165334 16.981333-21.717333 29.44-42.453333 37.290667-62.293333 7.808-19.84 11.776-40.746667 11.776-62.677333 0-38.570667-13.738667-70.741333-41.088-96.725334C599.466667 268.928 561.706667 256 513.834667 256c-43.690667 0-80.128 11.136-109.354667 33.578667-29.098667 22.4-46.506667 59.306667-52.010667 110.805333l93.184 9.301333c1.792-27.349333 8.405333-46.890667 19.754667-58.624 11.434667-11.776 26.837333-17.664 46.165333-17.664 19.541333 0 34.858667 5.589333 45.909334 16.768 11.136 11.264 16.682667 27.221333 16.682666 48.042667 0 18.858667-6.4 37.930667-19.242666 57.258667-9.472 14.037333-35.157333 40.533333-77.098667 79.872-52.096 48.554667-87.04 87.509333-104.704 116.821333A226.688 226.688 0 0 0 341.333333 745.429333h326.613334v-86.826666z" fill="#FFFFFF"></path></svg>'},{name:"3",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 0C229.248 0 0 229.248 0 512s229.248 512 512 512 512-229.248 512-512S794.752 0 512 0z" fill="#2E66FA"></path><path d="M627.754667 731.733333c-29.354667 25.088-66.901333 37.632-112.725334 37.632-44.928 0-81.792-11.52-110.592-34.773333-33.066667-26.538667-49.877333-64.469333-50.304-114.133333h92.16c0.426667 21.76 7.552 38.314667 21.333334 49.664 12.288 10.88 28.117333 16.341333 47.402666 16.341333 20.309333 0 36.778667-6.101333 49.322667-18.432 12.544-12.330667 18.773333-29.568 18.773333-51.797333 0-21.290667-6.229333-38.186667-18.773333-50.773334-12.544-12.501333-29.866667-18.773333-52.138667-18.773333h-13.525333v-80.042667H512c42.112 0 63.274667-21.034667 63.274667-63.146666 0-20.309333-5.888-36.096-17.706667-47.445334a60.757333 60.757333 0 0 0-43.818667-17.066666c-17.493333 0-32 5.504-43.434666 16.298666-11.562667 10.88-17.792 25.728-18.773334 44.714667H359.68c0.981333-43.946667 16.042667-78.976 45.397333-104.96 29.354667-25.941333 65.706667-39.04 109.226667-39.04 44.928 0 81.792 13.525333 110.592 40.490667 28.8 26.922667 43.306667 61.610667 43.306667 104.149333 0 48.213333-19.413333 82.688-58.154667 103.552 43.52 23.125333 65.28 61.44 65.28 114.858667 0 48.128-15.957333 85.76-47.573333 112.682666z" fill="#FFFFFF"></path></svg>'},{name:"4",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512.042667 1024C229.248 1024 0 794.794667 0 512.042667 0 229.205333 229.248 0 512.042667 0 794.752 0 1024 229.205333 1024 512.042667 1024 794.794667 794.752 1024 512.042667 1024z" fill="#6D768D"></path><path d="M600.96 256v309.802667h60.117333v81.536h-60.16v98.218666h-90.154666v-98.218666H311.466667v-81.237334L522.666667 256h78.293333zM510.72 399.104l-112.042667 166.698667h112.042667V399.104z" fill="#FFFFFF"></path></svg>'},{name:"5",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512.042667 1024C229.248 1024 0 794.794667 0 512.042667 0 229.205333 229.248 0 512.042667 0 794.752 0 1024 229.205333 1024 512.042667 1024 794.794667 794.752 1024 512.042667 1024z" fill="#6D768D"></path><path d="M470.912 343.552h175.786667V256H400.256l-47.786667 253.952 75.434667 10.837333c21.205333-23.552 45.269333-35.413333 72.021333-35.413333 21.546667 0 38.997333 7.509333 52.437334 22.4 13.312 15.018667 20.053333 37.418667 20.053333 67.328 0 31.872-6.741333 55.765333-20.181333 71.552-13.397333 15.872-29.866667 23.765333-49.237334 23.765333-17.066667 0-32.085333-6.186667-45.013333-18.432-13.013333-12.373333-20.821333-29.013333-23.466667-50.133333L341.333333 611.498667c5.546667 40.874667 22.485333 73.429333 50.730667 97.621333 28.330667 24.32 64.938667 36.437333 109.866667 36.437333 56.149333 0 100.053333-21.546667 131.754666-64.554666a176.64 176.64 0 0 0 34.816-107.52c0-48.042667-14.378667-87.210667-43.221333-117.333334-28.8-30.208-63.957333-45.312-105.514667-45.312-21.674667 0-42.922667 5.248-63.829333 15.616l14.976-82.901333z" fill="#FFFFFF"></path></svg>'},{name:"6",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 1024C229.248 1024 0 794.794667 0 512.042667 0 229.205333 229.248 0 512 0c282.88 0 512 229.205333 512 512.042667C1024 794.794667 794.88 1024 512 1024z" fill="#6D768D"></path><path d="M519.210667 256c36.992 0 67.626667 10.368 91.776 31.189333 24.192 20.821333 39.68 51.029333 46.293333 90.709334l-90.197333 9.984c-2.176-18.56-7.978667-32.298667-17.28-41.173334-9.258667-8.874667-21.418667-13.226667-36.224-13.226666-19.754667 0-36.437333 8.789333-50.048 26.453333-13.696 17.664-22.314667 54.613333-25.856 110.549333 23.296-27.52 52.138667-41.258667 86.656-41.258666 38.997333 0 72.362667 14.805333 100.181333 44.544 27.733333 29.696 41.685333 68.010667 41.685333 114.858666 0 49.877333-14.634667 89.856-43.818666 119.936-29.226667 30.208-66.730667 45.226667-112.554667 45.226667-49.066667 0-89.429333-19.072-121.130667-57.344C357.12 658.218667 341.333333 595.541333 341.333333 508.416c0-89.344 16.469333-153.813333 49.493334-193.194667C423.722667 275.754667 466.56 256 519.168 256z m-9.472 241.834667c-17.962667 0-33.066667 6.997333-45.525334 21.12-12.330667 14.037333-18.56 34.858667-18.56 62.293333 0 30.421333 6.912 53.76 20.906667 70.4 13.952 16.469333 29.866667 24.746667 47.786667 24.746667 17.28 0 31.701333-6.826667 43.178666-20.309334 11.52-13.525333 17.237333-35.669333 17.237334-66.56 0-31.658667-6.186667-54.869333-18.517334-69.546666a58.197333 58.197333 0 0 0-46.506666-22.144z" fill="#FFFFFF"></path></svg>'},{name:"7",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512.042667 1024C229.248 1024 0 794.752 0 512S229.248 0 512.042667 0C794.752 0 1024 229.248 1024 512s-229.248 512-511.957333 512z" fill="#6D768D"></path><path d="M673.024 273.066667H354.133333v86.869333h212.224a691.2 691.2 0 0 0-104.746666 187.989333c-26.026667 70.101333-39.978667 138.88-41.429334 206.293334h89.6c-0.298667-42.922667 6.698667-91.776 21.034667-146.474667a654.72 654.72 0 0 1 62.08-154.965333c27.136-48.554667 53.888-85.76 80.128-111.701334V273.066667z" fill="#FFFFFF"></path></svg>'},{name:"8",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 1024C229.248 1024 0 794.752 0 512S229.248 0 512 0s512 229.248 512 512-229.248 512-512 512z" fill="#6D768D"></path><path d="M512.426667 256c46.208 0 82.048 11.861333 107.605333 35.541333 25.6 23.68 38.314667 53.674667 38.314667 89.898667 0 22.613333-5.802667 42.666667-17.578667 60.330667a111.445333 111.445333 0 0 1-49.450667 40.277333c26.965333 10.837333 47.36 26.752 61.312 47.658667 13.994667 20.906667 21.034667 45.013333 21.034667 72.362666 0 45.098667-14.336 81.834667-42.965333 109.952-28.586667 28.245333-66.602667 42.368-114.090667 42.368-44.245333 0-81.066667-11.648-110.464-34.986666-34.645333-27.52-52.010667-65.28-52.010667-113.365334 0-26.368 6.528-50.645333 19.626667-72.746666 13.056-22.144 33.578667-39.210667 61.696-51.242667-24.064-10.154667-41.557333-24.192-52.48-41.941333a109.824 109.824 0 0 1-16.512-58.666667c0-36.224 12.757333-66.218667 37.973333-89.898667 25.386667-23.68 61.354667-35.541333 108.032-35.541333z m1.28 265.429333c-22.784 0-39.722667 7.978667-50.901334 23.893334-11.136 15.786667-16.64 33.066667-16.64 51.498666 0 25.984 6.485333 46.208 19.712 60.714667 13.098667 14.506667 29.525333 21.802667 49.152 21.802667 19.242667 0 35.157333-6.997333 47.786667-20.992 12.629333-13.909333 18.858667-34.048 18.858667-60.416 0-23.082667-6.314667-41.557333-19.2-55.466667a63.274667 63.274667 0 0 0-48.725334-21.034667z m-0.341334-191.488c-17.792 0-32 5.333333-42.581333 16-10.538667 10.666667-15.872 24.746667-15.872 42.325334 0 18.645333 5.248 33.152 15.701333 43.648 10.453333 10.453333 24.362667 15.658667 41.770667 15.658666 17.664 0 31.658667-5.290667 42.24-15.872 10.538667-10.581333 15.872-25.173333 15.872-43.818666 0-17.493333-5.248-31.573333-15.701333-42.154667s-24.277333-15.786667-41.429334-15.786667z" fill="#FFFFFF"></path></svg>'},{name:"9",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 1024C229.248 1024 0 794.794667 0 512.042667 0 229.333333 229.248 0 512 0c282.88 0 512 229.333333 512 512.042667C1024 794.794667 794.88 1024 512 1024z" fill="#6D768D"></path><path d="M497.28 256c49.365333 0 89.856 19.157333 121.429333 57.429333 31.701333 38.229333 47.488 101.205333 47.488 188.842667 0 89.173333-16.384 153.386667-49.365333 192.853333-32.853333 39.594667-75.605333 59.264-128.426667 59.264-37.888 0-68.608-10.154667-91.989333-30.506666s-38.4-50.816-45.013333-91.306667l90.112-9.984c2.261333 18.474667 8.021333 32.085333 17.28 41.088 9.173333 8.874667 21.418667 13.312 36.608 13.312 19.2 0 35.541333-8.874667 48.981333-26.752 13.44-17.749333 22.016-54.613333 25.770667-110.549333-23.466667 27.264-52.821333 40.874667-88.064 40.874666-38.314667 0-71.253333-14.72-99.114667-44.330666C355.242667 506.709333 341.333333 468.224 341.333333 420.864c0-49.493333 14.592-89.258667 43.946667-119.466667C414.549333 271.104 451.925333 256 497.237333 256z m-4.352 77.482667c-17.237333 0-31.658667 6.826667-43.008 20.437333-11.477333 13.653333-17.194667 35.84-17.194667 66.816 0 31.402667 6.229333 54.485333 18.645334 69.205333 12.458667 14.72 27.946667 22.101333 46.592 22.101334 18.005333 0 33.066667-7.082667 45.44-21.205334 12.330667-14.208 18.432-35.029333 18.432-62.506666 0-29.994667-6.912-53.376-20.821334-69.973334-13.824-16.597333-29.866667-24.874667-48.085333-24.874666z" fill="#FFFFFF"></path></svg>'},{name:"10",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512.042667 1024C229.248 1024 0 794.794667 0 511.957333 0 229.205333 229.248 0 512.042667 0 794.752 0 1024 229.205333 1024 511.957333 1024 794.794667 794.752 1024 512.042667 1024z" fill="#6D768D"></path><path d="M619.946667 273.066667c46.976 0 83.754667 16.042667 110.250666 48.042666 31.573333 37.973333 47.36 100.864 47.36 188.672 0 87.722667-15.829333 150.698667-47.658666 189.056-26.325333 31.616-62.976 47.36-109.952 47.36-47.274667 0-85.418667-17.237333-114.346667-51.968-28.885333-34.602667-43.392-96.426667-43.392-185.386666 0-87.168 15.872-150.016 47.701333-188.416 26.282667-31.488 62.933333-47.36 110.037334-47.36z m-207.488 12.8v452.266666H325.504V411.690667A299.904 299.904 0 0 1 213.333333 476.373333V398.933333c22.656-7.296 47.36-21.12 73.856-41.514666 26.624-20.522667 44.842667-44.288 54.784-71.552h70.485334z m207.488 60.842666c-11.306667 0-21.461333 3.413333-30.336 10.24-8.874667 6.826667-15.786667 19.157333-20.693334 36.864-6.4 22.997333-9.642667 61.653333-9.642666 115.968 0 54.442667 2.944 91.733333 8.661333 112.128 5.802667 20.352 13.098667 33.877333 21.845333 40.618667 8.789333 6.741333 18.858667 10.154667 30.165334 10.154667 11.349333 0 21.376-3.498667 30.250666-10.325334 8.874667-6.826667 15.786667-19.157333 20.693334-36.778666 6.4-22.826667 9.642667-61.354667 9.642666-115.797334 0-54.314667-2.858667-91.648-8.661333-112.042666-5.802667-20.352-13.013333-33.962667-21.76-40.789334a47.616 47.616 0 0 0-30.165333-10.24z" fill="#FFFFFF"></path></svg>'}]},{name:"进度图标",type:"progress",list:[{name:"1",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 0C229.248 0 0 229.248 0 512s229.248 512 512 512 512-229.248 512-512S794.752 0 512 0z" fill="#12BB37"></path><path d="M512 928c-229.76 0-416-186.24-416-416S282.24 96 512 96V512l294.144-294.144A414.72 414.72 0 0 1 928 512c0 229.76-186.24 416-416 416z" fill="#FFFFFF"></path></svg>'},{name:"2",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 0C229.248 0 0 229.248 0 512s229.248 512 512 512 512-229.248 512-512S794.752 0 512 0z" fill="#12BB37"></path><path d="M512 928c-229.76 0-416-186.24-416-416S282.24 96 512 96V512h416c0 229.76-186.24 416-416 416z" fill="#FFFFFF"></path></svg>'},{name:"3",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 0C229.248 0 0 229.248 0 512s229.248 512 512 512 512-229.248 512-512S794.752 0 512 0z" fill="#12BB37"></path><path d="M512 928c-229.76 0-416-186.24-416-416S282.24 96 512 96V512l294.144 294.144A414.72 414.72 0 0 1 512 928z" fill="#FFFFFF"></path></svg>'},{name:"4",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 0C229.248 0 0 229.248 0 512s229.248 512 512 512 512-229.248 512-512S794.752 0 512 0z" fill="#12BB37"></path><path d="M512 928c-229.76 0-416-186.24-416-416S282.24 96 512 96v832z" fill="#FFFFFF"></path></svg>'},{name:"5",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 0C229.248 0 0 229.248 0 512s229.248 512 512 512 512-229.248 512-512S794.752 0 512 0z" fill="#12BB37"></path><path d="M512 512l-294.144 294.144A414.72 414.72 0 0 1 96 512c0-229.76 186.24-416 416-416V512z" fill="#FFFFFF"></path></svg>'},{name:"6",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 0C229.248 0 0 229.248 0 512s229.248 512 512 512 512-229.248 512-512S794.752 0 512 0z" fill="#12BB37"></path><path d="M512 512H96c0-229.76 186.24-416 416-416V512z" fill="#FFFFFF"></path></svg>'},{name:"7",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 0C229.248 0 0 229.248 0 512s229.248 512 512 512 512-229.248 512-512S794.752 0 512 0z" fill="#12BB37"></path><path d="M512 512L217.856 217.856A414.72 414.72 0 0 1 512 96V512z" fill="#FFFFFF"></path></svg>'},{name:"8",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M0 512c0 282.752 229.248 512 512 512s512-229.248 512-512S794.752 0 512 0 0 229.248 0 512z" fill="#12BB37"></path><path d="M716.629333 341.333333h-51.328a35.072 35.072 0 0 0-28.330666 14.293334l-171.989334 233.984-77.909333-106.026667a35.2 35.2 0 0 0-28.330667-14.293333H307.413333c-7.082667 0-11.264 7.936-7.082666 13.653333l136.32 185.472a35.2 35.2 0 0 0 56.533333 0l230.4-313.429333a8.533333 8.533333 0 0 0-6.954667-13.653334z" fill="#FFFFFF"></path></svg>'}]},{name:"表情图标",type:"expression",list:[{name:"1",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1026 1024"><path d="M1.097856 1.097642h1021.804717v1021.804716H1.097856z" fill="#F09495" ></path><path d="M1024.000214 1024H0.000214V0h1024v1024z m-1021.804716-2.195284h1019.609433V2.195284H2.195498v1019.609432z" fill="#FFFFFF" ></path><path d="M234.695985 335.179887m-27.341259 0a27.341259 27.341259 0 1 0 54.682518 0 27.341259 27.341259 0 1 0-54.682518 0Z" fill="#040000" ></path><path d="M234.695985 363.519002c-15.666342 0-28.339115-12.772559-28.339115-28.339115 0-15.666342 12.772559-28.339115 28.339115-28.339115s28.339115 12.772559 28.339115 28.339115c0.099786 15.666342-12.672773 28.339115-28.339115 28.339115z m0-54.582732c-14.468914 0-26.243617 11.774703-26.243617 26.243617s11.774703 26.243617 26.243617 26.243617 26.243617-11.774703 26.243617-26.243617-11.774703-26.243617-26.243617-26.243617z" fill="#FFFFFF" ></path><path d="M776.232528 335.179887m-27.341259 0a27.341259 27.341259 0 1 0 54.682518 0 27.341259 27.341259 0 1 0-54.682518 0Z" fill="#040000" ></path><path d="M776.232528 363.519002c-15.666342 0-28.339115-12.772559-28.339115-28.339115 0-15.666342 12.772559-28.339115 28.339115-28.339115 15.666342 0 28.339115 12.772559 28.339115 28.339115 0 15.666342-12.772559 28.339115-28.339115 28.339115z m0-54.582732c-14.468914 0-26.243617 11.774703-26.243617 26.243617s11.774703 26.243617 26.243617 26.243617 26.243617-11.774703 26.243617-26.243617c-0.099786-14.468914-11.874488-26.243617-26.243617-26.243617z" fill="#FFFFFF" ></path><path d="M512.000214 671.656987c-52.58702 0-105.872539-17.961411-105.872539-52.387449S459.413194 566.882089 512.000214 566.882089s105.872539 17.961411 105.87254 52.387449S564.587234 671.656987 512.000214 671.656987z m0-74.240499c-21.952836 0-43.207172 3.592282-58.2748 9.77899-13.870201 5.68778-17.06334 11.275775-17.06334 12.07406s3.19314 6.386279 17.06334 12.07406c15.067628 6.186708 36.321965 9.77899 58.2748 9.77899s43.207172-3.592282 58.274801-9.77899c13.870201-5.68778 17.06334-11.275775 17.06334-12.07406s-3.19314-6.386279-17.06334-12.07406c-15.067628-6.286494-36.321965-9.77899-58.274801-9.77899z" fill="#040000" ></path></svg>'},{name:"2",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M0 0h1024v1024H0z" fill="#E6A6C9" ></path><path d="M315.1 368.1c-23.9 0-43.3-19.4-43.3-43.3s19.4-43.3 43.3-43.3 43.3 19.4 43.3 43.3-19.4 43.3-43.3 43.3z m0-74.7c-17.3 0-31.3 14.1-31.3 31.3 0 17.3 14.1 31.3 31.3 31.3 17.3 0 31.3-14.1 31.3-31.3 0-17.2-14-31.3-31.3-31.3zM738.7 368.1c-23.9 0-43.3-19.4-43.3-43.3s19.4-43.3 43.3-43.3 43.3 19.4 43.3 43.3-19.4 43.3-43.3 43.3z m0-74.7c-17.3 0-31.3 14.1-31.3 31.3 0 17.3 14.1 31.3 31.3 31.3 17.3 0 31.3-14.1 31.3-31.3 0-17.2-14-31.3-31.3-31.3zM293.5 698.8l-14.5-1.3c0.1-0.6 1.5-14.6 15.1-27.9 17.2-16.7 45-24.8 82.7-24 4.9-0.1 10.9-10.5 16.1-19.6 8.4-14.7 19-33.1 37.9-34.3 19.4-1.2 42.2 16.4 71.5 55.4 9.9 5.2 16.5 11.2 21.8 16.1 8.4 7.7 13.1 11.9 25.1 10.8 14.9-1.4 38.9-11.1 77.5-31.4 26.8-28.4 56.4-41.4 83.5-36.6 27.9 4.9 50.6 27.6 67.5 67.5l-13.4 5.7c-14.7-34.5-34.3-54.9-56.7-58.8-22.3-3.9-47.6 7.8-71.2 33.1l-0.8 0.9-1.1 0.6c-85.6 45.1-99.4 38-120.2 19.1-5.5-5-11.2-10.2-20.1-14.7l-1.5-0.8-1-1.4c-32.2-43.2-50.4-51.6-60-51-11.1 0.7-18.8 14-26.2 27-7.6 13.2-15.4 26.9-28.8 26.9h-0.2c-78.4-1.6-83 38.3-83 38.7z" fill="#040000" ></path></svg>'},{name:"3",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1026 1024" ><path d="M1.1 1.097642h1021.804716v1021.804716H1.1z" fill="#F7E983" ></path><path d="M1024.002358 1024H0.002358V0h1024v1024z m-1021.804716-2.195284h1019.609433V2.195284H2.197642v1019.609432z" fill="#FFFFFF" ></path><path d="M329.174412 344.491728a38.118106 10.277919 57.6 1 0 17.355867-11.014369 38.118106 10.277919 57.6 1 0-17.355867 11.014369Z" fill="#040000" ></path><path d="M644.769475 355.956059a11.175989 36.321965 30 1 0 36.321965-62.911488 11.175989 36.321965 30 1 0-36.321965 62.911488Z" fill="#040000" ></path><path d="M569.678445 671.158059c-26.343403 0-51.190021-5.288638-70.049503-14.967843-20.755408-10.577275-32.230754-25.445332-32.230755-41.710388 0-16.265056 11.475346-31.133112 32.230755-41.710387 18.859482-9.579419 43.805886-14.967843 70.049503-14.967843s51.190021 5.288638 70.049503 14.967843c20.755408 10.577275 32.230754 25.445332 32.230754 41.710387 0 16.265056-11.475346 31.133112-32.230754 41.710388-18.859482 9.679205-43.805886 14.967843-70.049503 14.967843z m0-95.095693c-49.693237 0-84.318846 20.356266-84.318846 38.517248s34.625609 38.517248 84.318846 38.517248 84.318846-20.356266 84.318846-38.517248-34.725395-38.517248-84.318846-38.517248z" fill="#040000" ></path></svg>'},{name:"4",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1026 1024" ><path d="M1.1 1.097642h1021.804716v1021.804716H1.1z" fill="#A6D9E2" ></path><path d="M1024.002358 1024H0.002358V0h1024v1024z m-1021.804716-2.195284h1019.609433V2.195284H2.197642v1019.609432z" fill="#FFFFFF" ></path><path d="M376.194134 348.950302m-23.44962 0a23.44962 23.44962 0 1 0 46.89924 0 23.44962 23.44962 0 1 0-46.89924 0Z" fill="#040000" ></path><path d="M629.150672 348.950302m-24.647047 0a24.647047 24.647047 0 1 0 49.294095 0 24.647047 24.647047 0 1 0-49.294095 0Z" fill="#040000" ></path><path d="M397.847613 603.503411c13.471058 8.282206 28.738258 14.468914 43.7061 19.458195 29.835899 9.978562 62.266225 14.169558 93.299551 7.483921 21.054765-4.490353 40.213604-14.369129 56.778016-28.039758 6.785422-5.587995-2.893783-15.167414-9.579419-9.579419-46.999026 38.916391-112.258819 31.033327-163.847983 6.086922-4.590138-2.195284-9.080491-4.490353-13.371272-7.184564-7.583707-4.590138-14.468914 7.184564-6.984993 11.774703z" fill="#040000" ></path><path d="M627.753674 534.052621c-31.033327 24.048334-58.474371 68.253362-37.419607 106.970182 10.577275 19.35841 29.835899 32.629897 48.795167 42.708244 7.982849 4.190996 15.067628-7.883064 7.084779-12.07406-25.245761-13.271487-53.485091-35.324108-49.094524-66.557006 2.793997-20.156695 15.766127-37.319821 29.736114-51.190022 3.392711-3.392711 6.984993-6.785422 10.776847-9.77899 2.993569-2.295069 2.394855-7.483921 0-9.878776-2.893783-3.19314-6.885208-2.49464-9.878776-0.199572z" fill="#040000" ></path></svg>'},{name:"5",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1026 1024" ><path d="M1.1 1.097642h1021.804716v1021.804716H1.1z" fill="#AD6F59" ></path><path d="M1024.002358 1024H0.002358V0h1024v1024z m-1021.804716-2.195284h1019.609433V2.195284H2.197642v1019.609432z" fill="#FFFFFF" ></path><path d="M411.829832 330.730879a38.118106 10.277919 57.6 1 0 17.355867-11.014368 38.118106 10.277919 57.6 1 0-17.355867 11.014368Z" fill="#040000" ></path><path d="M480.669675 609.989476c11.774703-25.844475 27.740401-51.788735 44.60417-73.342429 13.770415-17.462483 29.237186-33.92711 47.897096-44.803742 17.262912-10.078347 35.324108-13.67063 54.283376-6.58585 11.974274 4.390567 23.948548 14.468914 33.128825 24.547261 14.369129 15.865913 25.145975 34.625609 34.725394 53.684662 4.290782 8.581563 17.262912 0.997856 12.972131-7.583707-15.167414-30.334828-35.224323-63.763009-66.157864-80.327421-21.054765-11.37556-44.504385-11.475346-66.157864-1.895927-21.054765 9.280062-38.617034 25.644904-53.485091 42.907815-14.468914 16.863769-27.041902 35.324108-38.217891 54.582733-5.887351 10.178133-11.674917 20.555837-16.464627 31.232898-1.696355 3.692068-0.997856 7.982849 2.694212 10.277918 3.19314 1.895927 8.581563 0.898071 10.178133-2.694211z" fill="#040000" ></path><path d="M663.863649 338.091735a14.468914 33.727538 30 1 0 33.727538-58.417811 14.468914 33.727538 30 1 0-33.727538 58.417811Z" fill="#040000" ></path></svg>'},{name:"6",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024" ><path d="M762.9 77.4H261.1L10.2 512l250.9 434.6h501.8L1013.8 512z" fill="#83CEE3" ></path><path d="M369 375.8m-34.6 0a34.6 34.6 0 1 0 69.2 0 34.6 34.6 0 1 0-69.2 0Z" fill="#040000" ></path><path d="M369 411.7c-19.8 0-36-16.1-36-36s16.1-36 36-36 36 16.1 36 36-16.1 36-36 36z m0-69.1c-18.3 0-33.2 14.9-33.2 33.2S350.7 409 369 409s33.2-14.9 33.2-33.2-14.9-33.2-33.2-33.2z" fill="#FFFFFF" ></path><path d="M672.2 333.6c-15.1 7.6-30.2 15.6-44.3 25-5.9 3.9-17 10.4-14.6 19.1 1.8 6.5 12 11.2 17.3 14.3 15.7 9.3 32.1 17.6 48.3 25.9 8.6 4.4 16.2-8.5 7.6-13-14.1-7.3-28.3-14.5-42.1-22.3-3.9-2.2-7.9-4.5-11.7-6.9-1.2-0.8-2.4-1.5-3.5-2.4-0.6-0.4-1.1-0.8-1.6-1.2 2.2 1.7-0.3-0.3-0.3-0.3-0.9 0.1-1.5-3.2-0.2 0.5 0.9 2.4 1.1 3.8 0.3 5.8 0.6-1.5-0.9 0.8-0.1 0 0.5-0.5 1-1.1 1.6-1.6 0.5-0.5 1-0.9 1.6-1.3 0.6-0.5 0 0 1.2-0.9 1.7-1.3 3.5-2.5 5.3-3.6 8.4-5.5 17.2-10.4 26-15.2 5.6-3 11.2-6 16.8-8.9 8.6-4.4 1-17.3-7.6-13zM578.2 720.9c-12.5-96.7-33.3-154.7-55.6-155.6-8.8 3.9-22.3 17.5-37.7 60.1-10.8 29.8-18.4 62.2-23 81.6-1.2 5.1-2.1 9.1-2.9 11.8l-9.3-2.4c0.7-2.6 1.6-6.6 2.8-11.6 14.9-63 36-136.8 67.5-148.8l0.8-0.3h0.8c18.2-0.4 33.2 19.5 45.8 60.8 10.2 33.3 16.7 74.6 20.5 103.3l-9.7 1.1z" fill="#040000" ></path></svg>'},{name:"7",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024" ><path d="M762.9 77.4H261.1L10.2 512l250.9 434.6h501.8L1013.8 512z" fill="#8CC66D" ></path><path d="M375.778679 404.47473a14.5 33.8 30 1 0 33.8-58.543317 14.5 33.8 30 1 0-33.8 58.543317Z" fill="#040000" ></path><path d="M627.220263 374.211388a43.1 11.6 57.6 1 0 19.588408-12.431182 43.1 11.6 57.6 1 0-19.588408 12.431182Z" fill="#040000" ></path><path d="M451.1 548.5c17.6-9.3 63.9-30 105.3-16.2 17 20.3 32.7 98.8 28.8 138.1-27.5 10.2-82.5 10.2-106.1 5.8-8.3-10.5-32.7-81.8-35.3-114.6-0.4-5.5 2.5-10.6 7.3-13.1z" fill="#040000" ></path></svg>'},{name:"8",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024" ><path d="M762.9 77.4H261.1L10.2 512l250.9 434.6h501.8L1013.8 512z" fill="#5A74B8" ></path><path d="M357.7 400m-34.6 0a34.6 34.6 0 1 0 69.2 0 34.6 34.6 0 1 0-69.2 0Z" fill="#040000" ></path><path d="M357.7 436c-19.8 0-36-16.1-36-36s16.1-36 36-36 36 16.1 36 36-16.2 36-36 36z m0-69.2c-18.3 0-33.2 14.9-33.2 33.2s14.9 33.2 33.2 33.2 33.2-14.9 33.2-33.2-14.9-33.2-33.2-33.2z" fill="#FFFFFF" ></path><path d="M676 400m-34.6 0a34.6 34.6 0 1 0 69.2 0 34.6 34.6 0 1 0-69.2 0Z" fill="#040000" ></path><path d="M676 436c-19.8 0-36-16.1-36-36s16.1-36 36-36 36 16.1 36 36-16.2 36-36 36z m0-69.2c-18.3 0-33.2 14.9-33.2 33.2s14.9 33.2 33.2 33.2c18.3 0 33.2-14.9 33.2-33.2s-14.9-33.2-33.2-33.2z" fill="#FFFFFF" ></path><path d="M347.6 684.1c0.3-0.9 0.6-1.7 0.9-2.6 0.2-0.5 1.4-3.2 0.3-0.8 0.6-1.4 1.3-2.9 2-4.3 3.2-6.3 6-10.7 10.9-15.3 4.3-4 10.8-7.5 17.1-6.1 3.9 0.9 7.9 4.9 11.1 7.2 3.1 2.2 6.3 4.5 9.7 6.2 7.5 3.8 15.3 4.4 23.4 1.9 4.7-1.5 9.2-3.6 13.6-5.9 5-2.6 10.7-5 14.2-9.5 4.5-5.7 6.1-8.5 11.4-14.1 1-1 2-2 3.1-3 0.2-0.2 2.2-1.7 0.6-0.5 0.6-0.4 1.2-0.9 1.8-1.3 1-0.6 2.1-1.3 3.2-1.7-2 0.8 0.2 0 0.6-0.1 2.3-0.7-0.3-0.2 1.2-0.3 2.8-0.1 3.6 0 5.5 1 3.8 1.9 6.6 4.7 9.5 7.8 4.5 5 7.5 11.1 11.7 16.2 1.8 2.2 3.7 4.3 5.4 6.5 8.1 10.3 17.7 22.2 32.2 22 8.8-0.1 16.6-5.2 22.6-11.2 4.2-4.1 7.7-8.9 11-13.7 2.9-4.2 4.6-9.9 6.2-13.5 3.2-7.1 7.2-13.1 13-18.1 4.8-4.2 11.1-6.5 16.7-5.3 10.5 2.4 17.2 12.1 23.1 20.2 4.7 6.5 9.8 13 16 18.2 7.8 6.4 17.1 11.4 27.5 11.1 14.1-0.4 25.5-9.5 34.2-19.9 3-3.6 3.6-8.8 0-12.4-3.1-3.1-9.4-3.7-12.4 0-6.3 7.6-14.7 15.9-24.9 14.7-2.2-0.3-5.3-1.5-7.9-3.1-3.5-2.1-6.1-4.4-9.1-7.5-4.9-5.1-6.8-8.1-10.9-13.8-7.3-10.1-16.1-19.6-28.2-23.7-18.5-6.3-35.7 5.6-46 20.1-2.4 3.3-4.4 6.9-6.1 10.6-1.8 3.9-2.7 8.5-5.2 11.9-3.1 4.4-6.2 8.8-10.2 12.5-3 2.8-5.7 4.4-8.6 5.1-0.4 0.1-1.7 0.1 0.1 0h-2.2c2.1 0.1 0 0-0.5-0.1-0.7-0.2-1.4-0.4-2-0.6 1.8 0.7-1.8-1.1-2.4-1.5l-1.2-0.9c1.5 1.2-0.9-0.9-1.2-1.1-4.7-4.3-8.4-9.5-12.3-14.4-10.9-13.6-20.9-34-41-34.9-14.2-0.6-24.5 10.6-32.4 20.8-1.2 1.6-2.5 3.2-3.7 4.8-1.5 1.9 1.1-1.4-0.4 0.5-0.4 0.5-0.8 1.2-1.3 1.6-1.7 1.4-4.6 2.6-6.6 3.6-2.9 1.6-5.9 3.2-9 4.5-1.6 0.7-3.4 1.2-5.1 1.7-2.2 0.6-0.7 0.5-2.8 0.4-2.8 0-3.9-0.4-6.6-1.9-3.9-2.2-7.5-4.9-11.1-7.5-5.6-4-10-6.9-17-7.5-10.5-0.9-20.3 3.2-28.2 9.9-9.4 8.1-16.4 20.2-20.1 32-3.6 11.2 13.3 15.8 16.8 5.1z" fill="#040000" ></path></svg>'},{name:"9",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024" ><path d="M762.9 77.4H261.1L10.2 512l250.9 434.6h501.8L1013.8 512z" fill="#F0884F" ></path><path d="M287.2 382c6.4 2.3 11.6-3.7 15.4-7.9 5.1-5.5 10.2-11 16-15.9 0.8-0.7 1.7-1.4 2.5-2.1 1.2-0.9-1.7 1.3 0.2-0.2l1.2-0.9c2.1-1.5 4.3-2.9 6.5-4.3 2-1.2 4-2.2 6.1-3.2 0.6-0.3 1.2-0.6 1.9-0.9-0.3 0.2-1.5 0.6 0.2-0.1 1.3-0.5 2.6-1 4-1.5 11.2-3.7 21.8-4 33.4-1.1 19.5 4.9 36.4 17 51.2 30.2 8.6 7.7 21.4-5 12.7-12.7-25.2-22.6-57.1-42.1-92.2-36.2-20.4 3.4-37.7 16.1-51.6 30.9-2.3 2.4-4.5 5-6.8 7.4-0.7 0.7-1.9 1.5-2.4 2.4-0.5 0.8 2.3-1.5 0.8-0.7 1.3-0.7 3.9-1.4 5.8-0.7-11.1-3.7-15.8 13.7-4.9 17.5zM598 382c6.4 2.3 11.6-3.7 15.4-7.9 5.1-5.5 10.2-11 16-15.9 0.8-0.7 1.7-1.4 2.5-2.1 1.2-0.9-1.7 1.3 0.2-0.2l1.2-0.9c2.1-1.5 4.3-2.9 6.5-4.3 2-1.2 4-2.2 6.1-3.2 0.6-0.3 1.2-0.6 1.9-0.9-0.3 0.2-1.5 0.6 0.2-0.1 1.3-0.5 2.6-1 4-1.5 11.2-3.7 21.8-4 33.4-1.1 19.5 4.9 36.4 17 51.2 30.2 8.6 7.7 21.4-5 12.7-12.7-25.2-22.6-57.1-42.1-92.2-36.2-20.4 3.4-37.7 16.1-51.6 30.9-2.3 2.4-4.5 5-6.8 7.4-0.7 0.7-1.9 1.5-2.4 2.4-0.5 0.8 2.3-1.5 0.8-0.7 1.3-0.7 3.9-1.4 5.8-0.7-11.1-3.7-15.8 13.7-4.9 17.5zM505.9 527.1c3.4 0.7 6.8 1.7 10.2 2.8 6.7 2.2 10.4 3.5 16.6 7.7 1.6 1.1-0.5-0.5 0.6 0.5 0.6 0.5 1.1 1.1 1.7 1.6 1.5 1.4-0.1-0.4 0.5 0.6 0.4 0.6 0.7 1.2 1 1.8-1-2 0.1 0 0 0.5 0.1-2-0.1 0-0.1 0-0.1 0.8 0 0.7 0.1-0.5-0.1 0.4-0.1 0.7-0.3 1.1-0.6 1 0.7-0.9-0.4 1-1.6 2.5-4.6 5.4-8.1 7.8-6.8 4.6-14.4 8.2-22 11.4-7 3-7.4 11.9 0 14.8 7.4 2.8 15 5.3 22.4 8.1 3.1 1.1 4.2 1.5 6.9 2.9 1.1 0.6 2.1 1.2 3.2 1.8 1.2 0.8-0.7-0.5 0.1 0 0.4 0.3 0.8 0.7 1.1 1.1 0.6 0.8-1.1-1.2-0.2-0.2 0.8 0.9-0.3-1.4-0.1-0.2 0.1 0.9 0.2-1.9 0-0.9-0.1 0.5-0.8 1.8 0 0.2-0.2 0.5-0.5 1-0.8 1.4-0.3 0.3-0.9 1.3-0.3 0.5-0.5 0.7-1.1 1.3-1.7 1.9-6.9 7.3-15.9 12.8-24.4 18.1-8.3 5.3-0.6 18.5 7.7 13.2 9.9-6.3 20.9-12.8 28.6-21.8 4.8-5.5 8.1-12.9 4.2-19.9-3.4-6-10.5-8.9-16.6-11.4-8.6-3.5-17.5-6.2-26.2-9.5v14.8c14.4-6.1 47.2-18.8 41.2-40.3-3.5-12.9-19.4-18.9-30.8-22.6-3.4-1.1-6.9-2.1-10.5-2.9-9.1-2.2-13.3 12.5-3.6 14.6z" fill="#040000" ></path></svg>'},{name:"10",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024" ><path d="M762.9 77.4H261.1L10.2 512l250.9 434.6h501.8L1013.8 512z" fill="#F6F180" ></path><path d="M342.9 400.6m-29.5 0a29.5 29.5 0 1 0 59 0 29.5 29.5 0 1 0-59 0Z" fill="#040000" ></path><path d="M342.9 431.3c-16.9 0-30.7-13.8-30.7-30.7s13.8-30.7 30.7-30.7 30.7 13.8 30.7 30.7-13.7 30.7-30.7 30.7z m0-59c-15.6 0-28.3 12.7-28.3 28.3s12.7 28.3 28.3 28.3 28.3-12.7 28.3-28.3-12.6-28.3-28.3-28.3z" fill="#FFFFFF" ></path><path d="M702 400.6m-29.5 0a29.5 29.5 0 1 0 59 0 29.5 29.5 0 1 0-59 0Z" fill="#040000" ></path><path d="M702 431.3c-16.9 0-30.7-13.8-30.7-30.7s13.8-30.7 30.7-30.7 30.7 13.8 30.7 30.7-13.8 30.7-30.7 30.7z m0-59c-15.6 0-28.3 12.7-28.3 28.3s12.7 28.3 28.3 28.3 28.3-12.7 28.3-28.3-12.7-28.3-28.3-28.3z" fill="#FFFFFF" ></path><path d="M358.7 519.9c20 22 45.5 40.4 71.3 54.8 51.2 28.5 111.7 39.9 168 19.5 44.3-16.1 80.7-47.8 110.2-83.9 3-3.7 3.6-8.9 0-12.5-3.1-3.1-9.5-3.7-12.5 0-25.5 31.4-56.2 59.7-93.7 76-27.1 11.7-56.6 15.7-85.8 12.2-24.7-2.9-49.5-11.8-71.5-23.4-18.7-9.8-36.6-22.2-51.1-34.3-7.8-6.5-15.5-13.3-22.4-20.9-7.7-8.5-20.1 4.1-12.5 12.5z" ></path></svg>'},{name:"11",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024" ><path d="M48.2 844.9c-68.5-210.6 186-782.1 409.1-795.4 6.3-0.4 12.5 0.2 18.6 1.6C665.1 94.6 985.4 515 987.1 821.3c0.1 20-12.9 37.9-22.4 43.1-162.7 89.8-605.8 179.7-884.4 30.9-15-7.9-24.2-26.1-32.1-50.4z" fill="#F0884F" ></path><path d="M401 352.1m-52.4 0a52.4 52.4 0 1 0 104.8 0 52.4 52.4 0 1 0-104.8 0Z" fill="#FFFFFF" ></path><path d="M408.7 329m-29.3 0a29.3 29.3 0 1 0 58.6 0 29.3 29.3 0 1 0-58.6 0Z" fill="#040000" ></path><path d="M527.5 352.1m-52.4 0a52.4 52.4 0 1 0 104.8 0 52.4 52.4 0 1 0-104.8 0Z" fill="#FFFFFF" ></path><path d="M527.5 329m-29.3 0a29.3 29.3 0 1 0 58.6 0 29.3 29.3 0 1 0-58.6 0Z" fill="#040000" ></path><path d="M450.7 517c1.1-8.2 3.2-16.4 6.1-24.1 0.1-0.3 1-2.5 0.5-1.4s0.3-0.7 0.5-1c0.7-1.4 1.4-2.8 2.2-4.1 0.4-0.8 2.8-3.9 1.3-2.1 0.8-1 1.7-1.9 2.6-2.8 1-1-1.5 1 0.1 0 0.5-0.3 1-0.6 1.5-0.8-1.3 0.7-1.2 0.3 0 0.1 1.9-0.3-1.8 0.3 0.1 0 1.2-0.2 1.5 0.3 0-0.1 0.6 0.2 1.3 0.3 1.9 0.5 0.3 0.1-1.3-0.7 0.2 0.1 0.8 0.5 1.6 0.9 2.4 1.4 1.4 1 0-0.1 1.4 1.1 0.9 0.8 1.8 1.7 2.6 2.6 1.8 1.9 3.5 3.9 5 6.1 5.1 7.1 9.3 14.8 13.2 22.6 3.5 6.9 13.7 4.7 15.8-2.1 2.6-8.7 4.8-17.4 7.4-26.1 0.9-3.2 1.9-6.4 3.2-9.4-0.7 1.6 0.8-1.6 1.2-2.2l0.9-1.5c0.7-1.2-1.4 0.7 0.1-0.1 1.7-0.9-1.2 0.3-0.3 0.1 0.8-0.2 1-1.2 0.3-0.3-0.6 0.8 0.6 0-0.5 0.2-2 0.3 2.4 0.5-1.1 0 0.5 0.1 1.2 0.2 1.6 0.4-1.1-0.8-0.8-0.4 0.2 0.2 0.7 0.4 3.4 2.3 2.7 1.8 8.9 7.1 15.9 16.9 22.5 26 2.8 3.8 7.5 5.6 11.8 3.1 3.7-2.2 5.9-8 3.1-11.8-8.2-11.1-16.6-23-27.7-31.4-6.3-4.7-14.5-7.6-21.7-3-6.7 4.2-9.6 12.5-11.9 19.6-3.2 9.9-5.5 20-8.6 29.9 5.3-0.7 10.5-1.4 15.8-2.1-7.8-15.5-24.8-50.1-48-41.7-14.1 5.1-19.7 23-22.9 36.2-0.9 3.8-1.8 7.7-2.3 11.6-0.6 4.6 1.1 9.3 6 10.6 4.2 1 10.2-1.5 10.8-6.1z" fill="#040000" ></path></svg>'},{name:"12",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024" ><path d="M485.538528 993.072489a362.00362 481.804818 3.149 1 0 52.933731-962.15464 362.00362 481.804818 3.149 1 0-52.933731 962.15464Z" fill="#AADCF0" ></path><path d="M688.2 334.1c-15.1 7.6-30.2 15.6-44.3 25-5.9 3.9-17 10.4-14.6 19.1 1.8 6.5 12 11.2 17.3 14.3 15.7 9.3 32.1 17.6 48.3 25.9 8.6 4.4 16.2-8.5 7.6-13-14.1-7.3-28.3-14.5-42.1-22.3-3.9-2.2-7.9-4.5-11.7-6.9-1.2-0.8-2.4-1.5-3.5-2.4-0.6-0.4-1.1-0.8-1.6-1.2 2.2 1.7-0.3-0.3-0.3-0.3-0.9 0.1-1.5-3.2-0.2 0.5 0.9 2.4 1.1 3.8 0.3 5.8 0.6-1.5-0.9 0.8-0.1 0 0.5-0.5 1-1.1 1.6-1.6 0.5-0.5 1-0.9 1.6-1.3 0.6-0.5 0 0 1.2-0.9 1.7-1.3 3.5-2.5 5.3-3.6 8.4-5.5 17.2-10.4 26-15.2 5.6-3 11.2-6 16.8-8.9 8.6-4.4 1-17.4-7.6-13zM375.8 347c13.4 6.8 26.7 14 39.5 21.9 1.8 1.2 3.7 2.3 5.5 3.5 0.9 0.6 1.7 1.2 2.6 1.8 0.9 0.6 1.9 1.4 1.6 1.1 1.1 0.9 2.1 1.9 3.1 2.8 1.2 1 0-0.3 0.1 0 0-0.2-0.8-2.4-0.3-4.1 1.5-5.5 2.3-2.7 0.8-2-0.4 0.2-0.9 0.8-1.3 1.1 1.7-1.4-1.6 1.1-2.3 1.6-3.4 2.3-6.9 4.4-10.4 6.4-14.9 8.6-30.3 16.4-45.6 24.3-8.6 4.4-1 17.4 7.6 13 15-7.7 30.1-15.4 44.8-23.8 6.2-3.6 13.8-7.3 18.7-12.7 7.6-8.3-3.8-16.6-9.9-20.9-8.7-6.1-18-11.3-27.3-16.4-6.5-3.6-13-7.1-19.6-10.4-8.6-4.5-16.3 8.5-7.6 12.8zM412.8 570.9c13.5 7.7 28.5 13.3 43.3 17.9 29.8 9.2 61.7 13.1 92.6 7.3 20.6-3.9 40-12.5 56.6-25.2 2.8-2.2 4.3-5.6 2.3-9-1.6-2.8-6.2-4.5-9-2.3-48.3 36.9-113.3 30-165.6 6.7-4.6-2.1-9.2-4.2-13.7-6.7-7.3-4.2-13.9 7.2-6.5 11.3z" fill="#040000" ></path><path d="M644.6 505.2c-30.1 21.5-60.6 62.5-39.1 99.8 10.7 18.6 30.3 30.9 49.1 40.1 7.8 3.8 14.6-7.9 6.8-11.7-23.6-11.5-53.7-31.4-49.4-60.9 2.8-18.9 15.8-34.6 29.5-47.2 2.5-2.3 5.1-4.6 7.8-6.7 0.5-0.4 0.9-0.7 1.4-1.1-0.4 0.3-1.2 0.9-0.1 0.1l0.9-0.6c6.9-5.1 0.2-16.8-6.9-11.8z" fill="#040000" ></path></svg>'},{name:"13",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024" ><path d="M235.1 76.9c75.6-26.5 297.3-90.1 514.2-16.6 16.3 5.5 29.8 17.4 37.1 33 57.5 122.4 127.1 602.1 62.1 785.6a62.58 62.58 0 0 1-32.5 35.8c-109.5 51.8-428.1 136.7-609.3 37.2-14.4-7.9-25-21.3-29.7-37.1-41.9-140.6-37-627.7 19.1-798 6.1-18.7 20.5-33.4 39-39.9z" fill="#F9DABD" ></path><path d="M392.2 360.2m-35.2 0a35.2 35.2 0 1 0 70.4 0 35.2 35.2 0 1 0-70.4 0Z" fill="#040000" ></path><path d="M618.6 360.2m-35.2 0a35.2 35.2 0 1 0 70.4 0 35.2 35.2 0 1 0-70.4 0Z" fill="#040000" ></path><path d="M512 562.6c-36 0-65.3-29.3-65.3-65.3S476 432 512 432s65.3 29.3 65.3 65.3-29.3 65.3-65.3 65.3z m0-122.9c-31.7 0-57.6 25.8-57.6 57.6s25.8 57.6 57.6 57.6c31.7 0 57.6-25.8 57.6-57.6s-25.9-57.6-57.6-57.6z" fill="#040000" ></path></svg>'},{name:"14",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024" ><path d="M178.1 971.5c38.1 15.9 98.7 26.6 171.3-12.3 3.7-2 8.4-1.6 11.6 1.1 43.3 35.9 123.3 80.8 236 10.9 3.8-2.4 8.7-2.4 12.6-0.2 41.8 23.9 191.6 58.2 246.6 14.2 4.4-3.5 9.1-6.6 14.5-8.5C1065 909.5 678.2-652 194.3 351c-37.5 77.8-38.4 94.1-71.9 211.3-27.6 96.3-29.1 231.3 1.4 348.1 7.2 27.3 27.3 49.9 54.3 61.1z" fill="#ABAAAA" ></path><path d="M468.9 349H418c-6.1 0-11.1-5-11.1-11.1V336c0-6.1 5-11.1 11.1-11.1h50.9c6.1 0 11.1 5 11.1 11.1v1.9c0 6.1-5 11.1-11.1 11.1zM643 471.9H390c-6.6 0-12-5.4-12-12s5.4-12 12-12h253c6.6 0 12 5.4 12 12s-5.4 12-12 12zM609 349h-61.2c-6 0-11-4.9-11-11v-2.1c0-6 4.9-11 11-11H609c6 0 11 4.9 11 11v2.1c0 6.1-4.9 11-11 11z" fill="#040000" ></path></svg>'},{name:"15",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024" ><path d="M673.1 318.7c3.7-17.5 5.6-35.7 5.6-54.4 0-137.9-105.5-249.7-235.6-249.7S207.4 126.4 207.4 264.3c0 55.4 17.1 106.7 45.9 148.1-55.2 63.3-88.6 145.9-88.6 236.3 0 199.2 162.1 360.6 362.1 360.6 200 0 362.1-161.5 362.1-360.6 0.1-147.3-88.7-274-215.8-330z" fill="#4F8A54" ></path><path d="M392 246.2m-47.1 0a47.1 47.1 0 1 0 94.2 0 47.1 47.1 0 1 0-94.2 0Z" fill="#FFFFFF" ></path><path d="M386 252.8m-26.4 0a26.4 26.4 0 1 0 52.8 0 26.4 26.4 0 1 0-52.8 0Z" fill="#040000" ></path><path d="M505.6 246.2m-47.1 0a47.1 47.1 0 1 0 94.2 0 47.1 47.1 0 1 0-94.2 0Z" fill="#FFFFFF" ></path><path d="M501.4 252.8m-26.4 0a26.4 26.4 0 1 0 52.8 0 26.4 26.4 0 1 0-52.8 0Z" fill="#040000" ></path><path d="M474.3 364.8h-50.9c-6.1 0-11.1-5-11.1-11.1v-1.9c0-6.1 5-11.1 11.1-11.1h50.9c6.1 0 11.1 5 11.1 11.1v1.9c0 6.2-5 11.1-11.1 11.1z" fill="#040000" ></path></svg>'},{name:"16",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024" ><path d="M246.4 227.6c-166.9 101.1-461.9 344 87 564.1 1.5 0.6 2.9 1.1 4.4 1.6 80.7 27.7 392.8 165.4 641-198.1 40-58.6 38.5-136.2-3.7-193.3C892 289.5 727 201.1 429.1 182.7c-64.1-4-127.8 11.6-182.7 44.9z" fill="#CF92BE" ></path><path d="M617.1 393.4c-17.4 8.8-34.9 18.1-51.2 28.9-6.9 4.6-20.3 12.3-17.4 22.6 1.2 4.3 5.6 7 9 9.5 3.7 2.7 7.6 5 11.5 7.3 18.2 10.8 37.1 20.3 55.9 30 10 5.1 18.9-10 8.8-15.1-16.4-8.4-32.9-16.9-49-26-4.5-2.6-9.1-5.2-13.5-8l-4.5-3c-0.7-0.5-1.3-1-2-1.5 1.6 1.2 0.7 0.4-0.2-0.2-1.3-0.9-0.3-0.9-0.5-0.3 0.2 0.2 0.4 0.5 0.6 0.7 1 1.9 1.3 3.7 0.8 5.7 0.1-0.6 0.7-1.4-0.6 1.3 0.7-1.5-0.1 0-0.2 0.1 0.6-0.6 1.2-1.3 1.9-1.9l1.8-1.5c1.8-1.6-0.6 0.3 1.2-0.9 2-1.5 4.1-2.9 6.2-4.3 10-6.5 20.4-12.4 30.9-18 6.5-3.5 13.1-7 19.7-10.4 9.6-5 0.8-20.1-9.2-15zM323.1 408.5c15.9 8.1 31.7 16.5 46.8 26 2.2 1.4 4.3 2.8 6.5 4.2 1 0.7 1.9 1.3 2.8 2 0.5 0.3 1 0.7 1.4 1.1-1.1-0.9-0.3-0.3 0.3 0.3 1.1 1 2.2 2.2 3.3 3.1 1.4 1.1-1-1.7-0.1-0.1-0.6-1.1-0.9-4.1 0.3-6.7 2.2-4.8 0.7 0.1 0-0.5 0 0-1.1 0.9-1.3 1 2.3-1.9 0 0-0.5 0.4-0.8 0.5-1.5 1.1-2.3 1.6-4 2.7-8.1 5.1-12.3 7.5-17.3 10-35.1 19.1-52.8 28.2-10 5.1-1.2 20.2 8.8 15.1 17.5-9 35-17.9 52-27.7 7.3-4.2 15.9-8.6 21.8-14.7 9.3-9.7-4.3-19.7-11.5-24.7-10.1-7.1-20.9-13.1-31.7-19-7.6-4.2-15.2-8.2-22.9-12.1-9.7-5.2-18.6 9.9-8.6 15zM513 592.1c-12.2 0-24.6-1.4-36.3-4.3-8-2-13.9-8.2-15.4-16.2s1.7-15.8 8.4-20.5c23.2-16.3 60.5-31.9 106.2-13 6.4 2.6 11 8.3 12.3 15.1 1.3 6.7-0.8 13.6-5.7 18.3-13.5 13.1-40.9 20.6-69.5 20.6z m-37.4-32.5c-3.4 2.4-4.9 6.2-4.2 10.2 0.8 4.1 3.6 7.1 7.7 8.1 39.1 9.7 81.2 0.7 96.1-13.7 2.4-2.3 3.4-5.6 2.7-8.9-0.7-3.4-2.9-6.2-6.1-7.5-41.2-17.2-75.1-3.1-96.2 11.8z" fill="#040000" ></path></svg>'},{name:"17",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024" ><path d="M1008.6 465.7c0-124.9-95.5-226.2-213.4-226.2-12 0-23.8 1.1-35.2 3.1v-3.1c0-124.9-95.5-226.2-213.4-226.2S333.4 114.6 333.4 239.5c0 2.4 0 4.8 0.1 7.2-17.1-4.7-35-7.2-53.4-7.2-117.8 0-213.4 101.3-213.4 226.2 0 92.1 51.9 171.3 126.3 206.6-13.7 29.9-21.4 63.4-21.4 98.8 0 124.9 95.5 226.2 213.4 226.2 68.8 0 130-34.5 169-88.1 39 53.6 100.2 88.1 169 88.1 117.8 0 213.4-101.3 213.4-226.2 0-41.2-10.4-79.9-28.6-113.1 60.5-39.9 100.8-111.1 100.8-192.3z" fill="#8CC66D" ></path><path d="M437.8 400.7m-24.7 0a24.7 24.7 0 1 0 49.4 0 24.7 24.7 0 1 0-49.4 0Z" fill="#040000" ></path><path d="M649.7 400.7m-24.7 0a24.7 24.7 0 1 0 49.4 0 24.7 24.7 0 1 0-49.4 0Z" fill="#040000" ></path><path d="M527.3 625.9c6.3-14.2 13.1-28.3 17.9-43 6.2-19 8.3-38.6 10.5-58.3l2.1-19.2c0.7-6.2-9-6.1-9.7 0-1.7 16.3-2.8 32.8-5.7 48.9-4.2 23.7-13.8 45-23.5 66.7-2.5 5.6 5.9 10.5 8.4 4.9z" fill="#252525" ></path><path d="M447.7 522.3c20.3-0.1 40.6-0.2 61-0.4l96.6-0.6c7.5 0 14.9-0.1 22.4-0.1 16.6-0.1 16.7-25.9 0-25.8-20.3 0.1-40.6 0.2-61 0.4l-96.6 0.6c-7.5 0-14.9 0.1-22.4 0.1-16.6 0.1-16.7 25.9 0 25.8z" fill="#040000" ></path><path d="M495.4 508.2c-10.3 3.8-9.2 20.9-9.2 29.5 0.1 16 2.1 32.3 6.1 47.8 3.5 13.7 8.7 29.9 20.6 38.7 12.9 9.5 27.6 2.1 37.6-7.9 10.2-10.3 17.8-23 24.7-35.6 11.6-21.3 20.9-43.8 29.7-66.4 3-7.8-9.5-11.1-12.5-3.4-7.4 19.1-15.3 38.1-24.7 56.4-5.9 11.5-12.2 23-20.3 33.1-2.8 3.5-5.8 6.9-9.2 9.8-1.9 1.7-1.4 1.3-3.3 2.5-1.3 0.8-2.6 1.6-3.9 2.2-0.7 0.3 1-0.2-0.8 0.3-0.6 0.2-1.2 0.3-1.8 0.5-1.1 0.3-1.2 0.2-0.5 0.1-0.6 0-1.3 0-1.9 0.1-2.2 0.1 0.6 0.5-1.8-0.2l-1.8-0.6c1.5 0.5 0.2 0.1-0.5-0.3-0.8-0.5-2.9-2.1-1.7-1.1-1-0.9-2-1.7-2.8-2.7-0.4-0.5-0.9-1-1.3-1.5 0.4 0.5 0.1 0.2-0.5-0.7-0.8-1.3-1.7-2.5-2.4-3.9-0.7-1.3-1.4-2.5-2-3.8-0.4-0.8-0.8-1.6-1.1-2.4-0.1-0.2-0.5-1.1 0 0l-0.6-1.5a86.8 86.8 0 0 1-3.3-9.8c-4.4-14.9-6.2-27.9-6.8-42.8-0.3-6.6-0.3-13.1 0.4-19.7 0.2-1.5-0.3 1.5 0.1-0.5l0.3-1.8c0.2-0.9 0.5-1.8 0.7-2.8 0.4-1.9-0.7 1.1 0.3-0.7 0.5-1-1.3 1.2-0.3 0.5-0.3 0.3-1.1 0.8-2 1.1 7.7-2.9 4.3-15.4-3.5-12.5z" fill="#040000" ></path></svg>'},{name:"18",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024" ><path d="M75.4 739.8c-78.7-134.4-194-455.7 401.4-579.6 9.8-2 19.2-6.2 29.2-7.5C656.8 133 947.3 205 1000.1 578.4c42.6 223.8 29.7 392.1-822 233.6-43.1-8-80.6-34.4-102.7-72.2z" fill="#F09495" ></path><path d="M704.6 875.4c-129 0-301.8-20.5-526.6-62.3-43.5-8.1-81.2-34.6-103.5-72.7-19.3-32.9-44.8-84.3-57.1-142.5-13.9-65.1-8.8-125.3 15.1-179.2 54.3-122.3 203.7-209.6 444-259.6 4.1-0.9 8.3-2.1 12.3-3.4 5.5-1.7 11.1-3.4 16.9-4.2 29-3.8 75.7-5.9 133.8 5.7 54.5 10.9 105.3 31 150.8 59.9C843.7 251 888.2 296 922.7 351c39.7 63.1 66.1 139.6 78.5 227.3 8.1 42.4 15.2 87.3 12.5 127.9-2.8 42.6-16.4 75.5-41.5 100.7-42.5 42.7-120.3 65-237.8 68.1-9.6 0.2-19.6 0.4-29.8 0.4zM76.3 739.3c22 37.6 59.2 63.7 102.1 71.7 242.5 45.1 424.4 65.3 556.1 61.9 116.9-3.1 194.1-25.2 236.3-67.5 55.4-55.6 44.4-142.5 28.3-226.7C976 415.8 903.4 291.5 789.2 219c-124-78.7-248.1-69.9-283.2-65.3-5.6 0.7-11.2 2.4-16.6 4.1-4.1 1.2-8.3 2.5-12.5 3.4C237.3 211.1 88.5 298 34.5 419.6c-54.6 122.8 2.8 253 41.8 319.7z" fill="#FFFFFF" ></path><path d="M424.1 442.5m-24.7 0a24.7 24.7 0 1 0 49.4 0 24.7 24.7 0 1 0-49.4 0Z" fill="#040000" ></path><path d="M635.9 442.5m-24.7 0a24.7 24.7 0 1 0 49.4 0 24.7 24.7 0 1 0-49.4 0Z" fill="#040000" ></path><path d="M426.2 543.3c17.1 7.9 36.6 26 25.5 46.1-6.9 12.5-19.8 21.2-31.7 28.4-4.5 2.7-0.4 9.8 4.1 7.1 17.4-10.5 41.6-27.6 39-51.1-1.6-14-12.4-24.8-23.5-32.3-3-2-6.1-3.9-9.3-5.4-4.8-2.1-8.9 5-4.1 7.2zM629.5 535.4c-21.8 11.7-40.6 37-25.7 61.3 8.2 13.4 22.2 22.7 35.7 30.3 4.7 2.7 8.9-4.6 4.2-7.2-15.5-8.7-39.9-23.9-36.9-45.2 1.6-11.4 10.7-20.7 19.6-27.2 2.4-1.7 4.8-3.4 7.4-4.8 4.7-2.5 0.4-9.8-4.3-7.2z" fill="#040000" ></path><path d="M457.2 584.6c25.6 25.6 66.7 41 101.8 28.3 18.2-6.6 33.2-19.1 45.5-33.8 4.2-5.1-3-12.4-7.3-7.3-18.5 22-43.3 38.1-73 35-18.6-1.9-36.2-10.8-50.9-22-2.9-2.2-6.1-4.8-8.8-7.5-4.7-4.7-12 2.6-7.3 7.3z" fill="#040000" ></path></svg>'},{name:"19",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024" ><path d="M915.9 510.5c8.4-19 13.1-39.8 13.1-61.7 0-90-78.9-162.9-176.2-162.9-3.2 0-6.3 0.1-9.5 0.2v-0.2c0-94.8-116.2-171.6-259.6-171.6S224 191.2 224 286v2c-96.2 0-174.1 72-174.1 160.9 0 38 14.3 73 38.2 100.5-41.8 29.4-68.8 75.9-68.8 128.2 0 88.9 78 160.9 174.1 160.9 17.1 0 33.6-2.3 49.3-6.5 28.9 46.1 88.7 77.7 157.6 77.7 49.4 0 94-16.2 126-42.3 32 26.1 76.6 42.3 126 42.3 77.3 0 143-39.7 166.7-95 3.1 0.2 6.3 0.2 9.5 0.2 97.3 0 176.2-72.9 176.2-162.9 0-60.6-35.7-113.4-88.8-141.5z" fill="#5A74B8" ></path><path d="M357.6 449.5a46.6 73.2 0 1 0 93.2 0 46.6 73.2 0 1 0-93.2 0Z" fill="#FEFEFD" ></path><path d="M357.5 449.5a25.1 39.4 0 1 0 50.2 0 25.1 39.4 0 1 0-50.2 0Z" fill="#040000" ></path><path d="M531.3 449.5a46.6 73.2 0 1 0 93.2 0 46.6 73.2 0 1 0-93.2 0Z" fill="#FEFEFD" ></path><path d="M531.2 449.5a25.1 39.4 0 1 0 50.2 0 25.1 39.4 0 1 0-50.2 0Z" fill="#040000" ></path><path d="M426.7 574.6c20.9 29.9 59.7 52.2 96.2 38.6 19.2-7.2 34.7-21.2 47.6-36.9 2.8-3.5 3.4-8.3 0-11.7-2.9-2.9-8.9-3.5-11.7 0-16.5 20.2-40.9 40.9-68.1 35.5-17.3-3.4-31-13.2-42.9-25.9-2-2.2-3.9-4.4-5.8-6.7-1.6-1.9 1.1 1.5-0.4-0.6-0.2-0.2-0.3-0.5-0.5-0.7-6.2-8.7-20.6-0.4-14.4 8.4z" fill="#040000" ></path></svg>'},{name:"20",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024" ><path d="M792.8 301.4c-8.2 0-16.2 0.4-24.2 1.3-12.3-81.8-129.2-145.9-271.8-145.9-137.1 0-250.5 59.3-269.9 136.6C105.3 295.5 7.4 391.2 7.4 508.9c0 119.1 100.2 215.6 223.7 215.6 5.3 0 10.6-0.2 15.8-0.5 14.4 80.5 130.4 143.2 271.3 143.2 135.9 0 248.6-58.3 269.4-134.6 1.7 0 3.4 0.1 5.1 0.1 123.6 0 223.7-96.5 223.7-215.6s-100-215.7-223.6-215.7z" fill="#F6CD50" ></path><path d="M435.9 431.5m-52.2 0a52.2 52.2 0 1 0 104.4 0 52.2 52.2 0 1 0-104.4 0Z" fill="#FAFAFA" ></path><path d="M588.1 431.5m-52.2 0a52.2 52.2 0 1 0 104.4 0 52.2 52.2 0 1 0-104.4 0Z" fill="#FAFAFA" ></path><path d="M435.9 431.5m-27.8 0a27.8 27.8 0 1 0 55.6 0 27.8 27.8 0 1 0-55.6 0Z" fill="#040000" ></path><path d="M601.9 407.4c-5.7 2.9-11.3 5.9-16.9 9-6.8 3.8-15.3 7.8-20.5 13.8-5.6 6.5 1.6 11.1 6.7 14.4 11.2 7.1 23.3 13 35.1 19 5.7 2.9 10.8-5.7 5.1-8.6-10.9-5.6-21.9-11.1-32.4-17.4-2.4-1.4-4.6-3.1-7-4.6 1 0.6-0.4-0.4-0.4-0.4-1.9-0.3-0.5 4.2 0.5 4.1-0.1 0-0.6 0.3 0.3-0.3 0.5-0.3 1-0.9 1.5-1.3 9.7-7.9 21.9-13.5 33.1-19.2 5.7-2.7 0.6-11.4-5.1-8.5zM406.6 547.6c11.5 14.4 27 26.7 42.7 36.3 32.2 19.8 71.2 27.2 107.6 15.4 29.5-9.6 54.6-29.1 75.5-51.6 10.8-11.6-6.6-29.1-17.5-17.5-9.4 10.1-19.5 19.7-30.8 27.7-4.6 3.2-9.3 6.2-14.2 8.9-5 2.8-9.9 5.1-14.1 6.7-4.6 1.7-9.3 3.2-14.1 4.4-2.2 0.5-4.4 1-6.6 1.4-1 0.2-2 0.3-2.9 0.5 2.6-0.4-2.1 0.2-2.5 0.3-4.1 0.4-8.3 0.5-12.5 0.4-2.2-0.1-4.4-0.2-6.6-0.4-1.1-0.1-2.2-0.2-3.2-0.3-1.5-0.2-1.4-0.2 0.1 0l-2.1-0.3c-7.8-1.3-15.4-3.4-22.8-6.2-0.9-0.4-1.8-0.7-2.8-1.1-3.1-1.2 2.3 1.1-0.7-0.3-1.5-0.7-2.9-1.3-4.4-2-3.7-1.8-7.2-3.7-10.8-5.8-5.7-3.4-11.1-7.1-16.4-11.1 3 2.3-1.1-0.9-1.8-1.5-1.1-0.9-2.1-1.7-3.1-2.6-2.1-1.8-4.2-3.7-6.3-5.6-4.4-4.1-8.7-8.4-12.4-13.1-4.2-5.2-13.1-4.3-17.5 0-5 5.1-4 12.2 0.2 17.4z" fill="#040000" ></path></svg>'}]},{name:"标记图标",type:"sign",list:[{name:"1",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#6D768D"></path><path d="M809.728 429.696a18.901333 18.901333 0 0 0-15.274667-12.885333l-183.466666-26.624-81.92-166.272a18.901333 18.901333 0 0 0-34.005334 0l-81.92 166.272-183.594666 26.624a19.029333 19.029333 0 0 0-10.496 32.298666l132.693333 129.536-31.274667 182.741334a18.816 18.816 0 0 0 27.477334 19.84l164.138666-86.186667 164.096 86.058667a18.773333 18.773333 0 1 0 27.434667-19.84l-31.36-182.741334 132.693333-129.408a18.901333 18.901333 0 0 0 4.778667-19.413333z" fill="#FFFFFF"></path></svg>'},{name:"2",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#6D768D"></path><path d="M644.565333 306.901333c32.128 0 65.834667-5.76 101.077334-17.237333a17.066667 17.066667 0 0 1 22.357333 16.213333v328.32c-1.109333 0.768 10.325333 27.093333-99.370667 19.84-109.653333-7.210667-181.76-45.098667-246.869333-45.098666-65.152 0-49.322667 2.688-74.154667 8.405333v168.064a24.746667 24.746667 0 0 1-24.490666 25.258667 22.528 22.528 0 0 1-17.28-7.253334 24.149333 24.149333 0 0 1-7.168-18.005333V281.258667C299.776 280.490667 328.106667 256 421.76 256s164.437333 50.901333 222.805333 50.901333z" fill="#FFFFFF"></path></svg>'},{name:"3",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#6D768D"></path><path d="M524.074667 225.408l274.517333 274.517333a17.066667 17.066667 0 0 1 0 24.149334l-274.517333 274.517333a17.066667 17.066667 0 0 1-24.149334 0l-274.517333-274.517333a17.066667 17.066667 0 0 1 0-24.149334l274.517333-274.517333a17.066667 17.066667 0 0 1 24.149334 0z" fill="#FFFFFF"></path></svg>'},{name:"4",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#6D768D"></path><path d="M317.866667 300.8h388.266666c9.386667 0 17.066667 7.68 17.066667 17.066667v388.266666a17.066667 17.066667 0 0 1-17.066667 17.066667h-388.266666a17.066667 17.066667 0 0 1-17.066667-17.066667v-388.266666c0-9.386667 7.68-17.066667 17.066667-17.066667z" fill="#FFFFFF"></path></svg>'},{name:"5",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#6D768D"></path><path d="M498.346667 279.082667L248.789333 701.44a15.829333 15.829333 0 0 0 13.653334 23.893333h499.114666a15.829333 15.829333 0 0 0 13.653334-23.893333l-249.6-422.357333a15.829333 15.829333 0 0 0-27.264 0z" fill="#FFFFFF"></path></svg>'},{name:"6",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#6D768D"></path><path d="M497.749333 798.549333l-31.445333-28.501333C313.941333 631.722667 213.333333 540.501333 213.333333 428.8a160.981333 160.981333 0 0 1 162.730667-162.730667c51.498667 0 100.906667 23.978667 133.12 61.696a177.536 177.536 0 0 1 133.162667-61.696 160.981333 160.981333 0 0 1 162.730666 162.730667c0 111.701333-100.608 202.965333-252.970666 341.333333l-31.445334 28.458667a17.066667 17.066667 0 0 1-22.912 0z" fill="#FFFFFF"></path><path d="M634.538667 487.808L555.050667 426.24 507.306667 256a201.002667 201.002667 0 0 0-23.594667 20.394667l-0.256-0.256L525.653333 426.666667l-133.290666 59.946666a14.08 14.08 0 0 0-8.021334 15.957334l28.757334 126.378666a14.208 14.208 0 0 0 27.733333-6.229333l-26.24-115.114667 126.037333-56.704 76.416 59.136a14.250667 14.250667 0 0 0 19.968-2.474666 14.08 14.08 0 0 0-2.474666-19.797334z" fill="#6D768D"></path></svg>'},{name:"7",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#6D768D"></path><path d="M497.749333 798.549333l-31.445333-28.501333C313.941333 631.722667 213.333333 540.501333 213.333333 428.8a160.981333 160.981333 0 0 1 162.730667-162.730667c51.498667 0 100.906667 23.978667 133.12 61.696a177.536 177.536 0 0 1 133.162667-61.696 160.981333 160.981333 0 0 1 162.730666 162.730667c0 111.701333-100.608 202.965333-252.970666 341.333333l-31.445334 28.458667a17.066667 17.066667 0 0 1-22.912 0z" fill="#FFFFFF"></path></svg>'},{name:"8",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#6D768D"></path><path d="M374.656 273.194667c5.973333 4.48 12.117333 9.6 18.346667 15.36 6.272 5.717333 11.904 12.373333 16.896 19.84 2.517333 4.010667 5.504 8.490667 9.002666 13.482666a529.493333 529.493333 0 0 1 20.266667 32.213334h155.221333a169.813333 169.813333 0 0 0 9.770667-15.744c2.474667-4.48 5.248-8.96 8.234667-13.482667a460.842667 460.842667 0 0 1 23.253333-31.829333c4.992-6.229333 12.245333-12.373333 21.76-18.346667a34.261333 34.261333 0 0 0 10.112-9.728 31.274667 31.274667 0 0 0 5.248-11.989333 18.56 18.56 0 0 0-1.536-11.605334 17.664 17.664 0 0 0-10.112-8.618666c-4.48-1.493333-8.362667-2.005333-11.605333-1.493334a46.933333 46.933333 0 0 0-9.770667 2.602667c-3.242667 1.28-6.613333 2.645333-10.112 4.138667a32.426667 32.426667 0 0 1-12.757333 2.261333 26.026667 26.026667 0 0 1-12.373334-2.645333 45.653333 45.653333 0 0 1-8.96-6.357334l-8.661333-7.850666a30.336 30.336 0 0 0-11.989333-6.4c-9.984-3.968-18.005333-4.693333-24.021334-2.218667-5.973333 2.474667-11.946667 6.485333-17.962666 11.946667a88.618667 88.618667 0 0 1-11.989334 10.496 7.338667 7.338667 0 0 1-3.754666 1.493333 46.165333 46.165333 0 0 1-8.277334-5.205333 71.808 71.808 0 0 1-7.125333-4.906667 37.973333 37.973333 0 0 1-6.4-6.357333c-3.968-3.968-9.941333-6.613333-17.92-7.850667a31.061333 31.061333 0 0 0-21.76 4.138667c-8.533333 5.461333-14.506667 10.069333-18.048 13.824a29.354667 29.354667 0 0 1-15.744 7.893333 23.978667 23.978667 0 0 1-13.098667-0.768 987.733333 987.733333 0 0 0-14.634666-4.48 80.725333 80.725333 0 0 0-14.250667-2.986667 16.768 16.768 0 0 0-11.989333 2.986667c-6.997333 5.461333-9.258667 12.074667-6.741334 19.84a34.56 34.56 0 0 0 13.482667 18.346667z" fill="#FFFFFF"></path><path d="M780.757333 545.152a219.306667 219.306667 0 0 0-19.882666-65.536 224.981333 224.981333 0 0 0-33.365334-49.792 430.336 430.336 0 0 0-37.12-37.12c-14.506667-11.946667-27.264-23.296-38.272-34.048a544.512 544.512 0 0 1-27.733333-28.842667 305.28 305.28 0 0 1-22.485333-26.197333h-168.746667c-6.485333 8.490667-13.994667 17.493333-22.485333 26.965333a360.96 360.96 0 0 1-26.24 28.074667c-10.538667 10.24-22.272 21.12-35.285334 32.597333a305.493333 305.493333 0 0 0-41.6 44.16 250.026667 250.026667 0 0 0-49.493333 117.589334 216.106667 216.106667 0 0 0 1.877333 70.4 220.586667 220.586667 0 0 0 75.349334 126.549333c21.248 18.005333 47.146667 32.597333 77.653333 43.818667 30.464 11.264 65.493333 16.853333 104.96 16.853333 38.528 0 72.874667-4.864 103.125333-14.592a265.045333 265.045333 0 0 0 78.378667-39.338667c21.973333-16.469333 39.594667-35.797333 52.864-58.026666 13.226667-22.186667 22.101333-45.824 26.624-70.784 4.992-30.421333 5.632-58.026667 1.877333-82.773334z" fill="#FFFFFF"></path><path d="M593.322667 647.509333a20.48 20.48 0 0 1-11.861334 3.2h-50.133333v14.165334c0 4.266667-1.792 8.362667-5.376 12.373333a15.914667 15.914667 0 0 1-13.952 5.333333 24.917333 24.917333 0 0 1-14.336-3.882666c-3.84-2.602667-5.973333-7.210667-6.4-13.824v-14.165334h-48.725333a17.792 17.792 0 0 1-11.818667-3.882666 10.24 10.24 0 0 1-3.968-9.6c0-4.266667 1.578667-7.68 4.693333-10.24a16.768 16.768 0 0 1 11.093334-3.925334h48.682666v-24.789333h-48.682666a15.573333 15.573333 0 0 1-11.52-4.266667 13.525333 13.525333 0 0 1-4.266667-9.941333 15.36 15.36 0 0 1 4.693333-10.624 14.72 14.72 0 0 1 11.093334-4.949333h48.682666l0.725334-14.890667a1053.568 1053.568 0 0 1-40.832-42.538667l-10.752-9.898666a41.216 41.216 0 0 1-6.442667-11.690667c-1.92-4.992-0.938667-10.069333 2.858667-15.274667a13.653333 13.653333 0 0 1 15.786666-3.84c6.186667 2.090667 11.221333 4.821333 15.018667 8.106667 1.92 2.389333 5.248 5.888 10.026667 10.666667l15.061333 14.848 19.328 19.157333 22.186667-20.565333a987.605333 987.605333 0 0 1 29.397333-25.514667 21.162667 21.162667 0 0 1 14.293333-5.674667c5.290667 0 9.557333 2.133333 12.928 6.4 6.186667 7.082667 3.84 15.36-7.168 24.789334a179.072 179.072 0 0 0-12.885333 12.373333c-5.76 5.973333-11.52 11.733333-17.194667 17.408-6.698667 7.082667-14.08 14.378667-22.186666 21.973333v13.44h46.506666c6.698667 0 11.605333 1.536 14.72 4.608a14.165333 14.165333 0 0 1 4.650667 10.282667c0 4.266667-1.450667 7.936-4.309333 11.008-2.858667 3.029333-7.637333 4.352-14.336 3.84l-46.506667 0.768-0.768 24.064h45.866667c13.354667 0 20.053333 4.992 20.053333 14.933333 0.469333 4.693333-0.853333 8.106667-3.925333 10.24z" fill="#6D768D"></path></svg>'},{name:"9",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#6D768D"></path><path d="M512 213.333333l234.666667 341.333334h-128v213.333333h-213.333334v-213.333333h-128L512 213.333333z" fill="#FFFFFF"></path></svg>'},{name:"10",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#6D768D"></path><path d="M533.333333 810.666667L298.666667 469.333333h128V256h213.333333v213.333333h128l-234.666667 341.333334z" fill="#FFFFFF"></path></svg>'},{name:"11",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#6D768D"></path><path d="M213.333333 533.333333L554.666667 298.666667v128h213.333333v213.333333h-213.333333v128l-341.333334-234.666667z" fill="#FFFFFF"></path></svg>'},{name:"12",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#6D768D"></path><path d="M810.666667 533.333333L469.333333 768v-128H256v-213.333333h213.333333V298.666667l341.333334 234.666666z" fill="#FFFFFF"></path></svg>'},{name:"13",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M0 512c0 282.752 229.248 512 512 512s512-229.248 512-512S794.752 0 512 0 0 229.248 0 512z" fill="#6D768D"></path><path d="M571.349333 508.586667l162.389334-162.346667a44.330667 44.330667 0 1 0-62.72-62.72l-162.389334 162.389333-162.517333-162.389333a44.330667 44.330667 0 1 0-62.72 62.72l162.389333 162.389333-162.389333 162.474667a44.330667 44.330667 0 1 0 62.72 62.72l162.389333-162.346667 162.389334 162.389334a44.330667 44.330667 0 1 0 62.72-62.72l-162.261334-162.56z" fill="#FFFFFF"></path></svg>'},{name:"14",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 0C233.386667 0 0 225.877333 0 512s225.877333 512 512 512 512-225.877333 512-512S790.613333 0 512 0z" fill="#6D768D"></path><path d="M726.144 311.210667l-277.333333 305.066666-124.8-124.8c-13.866667-13.866667-41.6-13.866667-55.466667 0-13.866667 13.866667-13.866667 41.6 0 55.466667l159.445333 152.533333c13.866667 13.866667 41.6 13.866667 55.466667 0l305.066667-332.8c13.866667-13.866667 13.866667-41.6 0-55.466666-20.778667-13.866667-48.512-13.866667-62.378667 0z" fill="#FFFFFF"></path></svg>'},{name:"15",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#6D768D"></path><path d="M541.952 755.626667a40.618667 40.618667 0 0 1-29.824 12.373333 41.344 41.344 0 0 1-30.122667-12.373333 40.106667 40.106667 0 0 1-12.672-30.122667c0-11.605333 4.096-21.845333 12.672-30.122667a40.405333 40.405333 0 0 1 30.122667-12.714666c11.605333 0 21.546667 4.138667 29.824 12.714666a40.32 40.32 0 0 1 12.714667 30.122667c0 11.861333-4.096 21.76-12.714667 30.122667zM450.986667 241.28A77.866667 77.866667 0 0 1 512.256 213.333333c24.874667 0 45.354667 8.917333 61.354667 27.946667 15.488 18.432 23.722667 41.685333 23.722666 69.674667 0 23.765333-33.152 200.533333-44.672 329.045333h-80.128C463.146667 511.402667 426.666667 334.677333 426.666667 310.954667c0-27.392 8.277333-50.645333 24.32-69.674667z" fill="#FFFFFF"></path></svg>'},{name:"16",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 0C229.248 0 0 229.248 0 512s229.248 512 512 512 512-229.248 512-512S794.794667 0 512 0z" fill="#6D768D"></path><path d="M490.666667 682.666667a64 64 0 1 1 0 128 64 64 0 0 1 0-128z m13.994666-490.752c61.397333 0 112.341333 14.634667 153.002667 43.946666 40.533333 29.269333 60.885333 72.618667 60.885333 130.133334 0 35.242667-12.373333 64.938667-29.952 89.045333-10.282667 14.677333-33.664 33.408-62.890666 56.192l-32.426667 22.357333c-15.701333 12.202667-29.696 26.453333-34.858667 42.666667-1.706667 5.546667-3.072 14.677333-3.968 24.533333-0.426667 4.949333-4.864 15.018667-15.232 15.018667h-83.328c-13.568 0-15.957333-10.581333-15.744-15.786667 1.493333-34.005333 4.608-64.213333 18.474667-80.469333 28.074667-32.896 91.904-73.813333 91.904-73.813333a104.106667 104.106667 0 0 0 23.552-24.021334c10.837333-14.933333 19.797333-31.317333 19.797333-49.237333 0-20.565333-6.016-39.338667-18.090666-56.32-12.032-16.938667-34.090667-25.386667-66.005334-25.386667-31.445333 0-53.76 10.410667-66.901333 31.274667-9.685333 15.445333-15.786667 29.610667-18.346667 45.013333-0.853333 5.461333-4.394667 16.981333-16.042666 16.981334H327.210667c-17.322667 0-21.12-11.221333-20.650667-16.64 6.272-68.138667 32.896-114.688 80-144.597334 32-20.565333 71.381333-30.890667 118.101333-30.890666z" fill="#FFFFFF"></path></svg>'},{name:"17",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#6D768D"></path><path d="M336.256 410.026667H253.312a40.021333 40.021333 0 0 0-39.850667 43.264l23.296 278.101333c1.706667 20.693333 19.072 36.608 39.850667 36.608h59.648c11.050667 0 20.010667-8.96 20.010667-19.968v-318.037333a19.968 19.968 0 0 0-20.010667-19.968z m434.432 0h-178.944C653.312 182.314667 548.949333 170.666667 548.949333 170.666667c-44.288 0-35.114667 34.986667-38.442666 40.832 0 84.48-68.010667 155.093333-101.034667 184.362666a39.552 39.552 0 0 0-13.226667 29.653334v322.56c0 11.008 8.96 19.925333 20.010667 19.925333h233.728c30.378667 0 58.154667-17.152 71.68-44.373333 18.176-36.736 40.448-90.112 54.656-133.973334 13.781333-42.410667 26.24-94.976 33.578667-131.968a39.850667 39.850667 0 0 0-39.253334-47.658666z" fill="#FFFFFF"></path></svg>'},{name:"18",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#6D768D"></path><path d="M796.16 413.909333c-31.146667-0.298667-115.626667-0.085333-146.858667-0.085333h-158.464c8.533333-7.68 15.914667-14.506667 23.594667-20.906667 29.781333-24.874667 25.813333-71.082667-14.208-88.874666-22.954667-10.24-44.970667-5.632-64 11.52-34.944 31.274667-69.632 62.677333-104.277333 93.994666a15.488 15.488 0 0 1-11.178667 4.437334c-11.221333-0.085333-26.88-0.128-46.933333-0.170667a17.066667 17.066667 0 0 0-17.109334 17.066667L256 719.701333a17.066667 17.066667 0 0 0 17.066667 17.152l49.578666-0.085333c3.968 0 7.466667 0.768 10.88 2.602667 15.829333 8.832 31.701333 17.493333 47.616 26.24a18.133333 18.133333 0 0 0 9.301334 2.346666h168.405333c6.186667 0 11.946667-0.981333 17.834667-2.56 29.44-7.253333 40.021333-30.293333 38.528-52.565333-0.768-9.728-4.266667-18.346667-9.984-26.24 19.626667-5.76 35.114667-16.213333 42.112-36.096 7.125333-20.394667 1.621333-38.4-12.672-53.333333 28.16-19.754667 34.858667-44.672 18.645333-75.648h140.458667c6.570667 0 13.013333-0.597333 19.370666-2.645334 31.957333-9.813333 48.810667-42.88 35.626667-71.552-10.154667-22.186667-28.629333-33.152-52.608-33.450666z" fill="#FFFFFF"></path></svg>'},{name:"19",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#6D768D"></path><path d="M270.506667 413.909333c31.146667-0.298667 115.626667-0.085333 146.858666-0.085333h158.464c-8.533333-7.68-15.914667-14.506667-23.594666-20.906667-29.781333-24.874667-25.813333-71.082667 14.208-88.874666 22.954667-10.24 44.970667-5.632 64 11.52 34.944 31.274667 69.632 62.677333 104.277333 93.994666 3.413333 2.986667 6.528 4.437333 11.178667 4.437334 11.221333-0.085333 26.88-0.128 46.933333-0.170667a17.066667 17.066667 0 0 1 17.109333 17.066667l0.682667 288.853333a17.066667 17.066667 0 0 1-17.066667 17.152l-49.578666-0.085333a22.101333 22.101333 0 0 0-10.88 2.602666c-15.829333 8.832-31.701333 17.493333-47.616 26.24a18.133333 18.133333 0 0 1-9.301334 2.346667h-168.405333a68.693333 68.693333 0 0 1-17.834667-2.56c-29.44-7.253333-40.021333-30.293333-38.528-52.565333 0.768-9.728 4.266667-18.346667 9.984-26.24-19.626667-5.76-35.114667-16.213333-42.112-36.096-7.125333-20.394667-1.621333-38.4 12.672-53.333334-28.16-19.754667-34.858667-44.672-18.645333-75.648H272.853333c-6.570667 0-13.013333-0.597333-19.370666-2.645333-31.957333-9.813333-48.810667-42.88-35.626667-71.552 10.154667-22.186667 28.629333-33.152 52.608-33.450667z" fill="#FFFFFF"></path></svg>'},{name:"20",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#6D768D"></path><path d="M667.733333 480.128H400v-111.36a97.706667 97.706667 0 0 1 97.621333-97.621333 97.706667 97.706667 0 0 1 97.578667 97.621333 28.885333 28.885333 0 0 0 57.813333 0A155.605333 155.605333 0 0 0 497.621333 213.333333a155.605333 155.605333 0 0 0-155.392 155.434667v111.36h-14.677333A28.885333 28.885333 0 0 0 298.666667 509.013333v292.010667a28.885333 28.885333 0 0 0 28.885333 28.885333h340.138667a28.885333 28.885333 0 0 0 28.928-28.885333V509.013333a28.885333 28.885333 0 0 0-28.928-28.885333z" fill="#FFFFFF"></path></svg>'},{name:"21",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#6D768D"></path><path d="M400.042667 437.461333v-111.36a97.706667 97.706667 0 0 1 97.621333-97.621333 97.706667 97.706667 0 0 1 97.578667 97.621333 28.885333 28.885333 0 0 0 57.813333 0A155.605333 155.605333 0 0 0 497.621333 170.666667a155.605333 155.605333 0 0 0-155.392 155.434666v111.36h-14.677333A28.885333 28.885333 0 0 0 298.666667 466.346667v292.010666a28.885333 28.885333 0 0 0 28.885333 28.885334h340.138667a28.885333 28.885333 0 0 0 28.928-28.885334V466.346667a28.885333 28.885333 0 0 0-28.928-28.885334H400.042667z" fill="#FFFFFF"></path><path d="M595.242667 437.461333v-111.36a97.706667 97.706667 0 0 0-97.621334-97.621333 97.706667 97.706667 0 0 0-97.578666 97.621333 28.885333 28.885333 0 0 1-57.813334 0A155.605333 155.605333 0 0 1 497.621333 170.666667a155.605333 155.605333 0 0 1 155.434667 155.434666v111.36h14.634667c16 0 28.928 12.928 28.928 28.885334v292.010666a28.885333 28.885333 0 0 1-28.928 28.885334H327.552A28.885333 28.885333 0 0 1 298.666667 758.357333V466.346667c0-15.957333 12.928-28.885333 28.885333-28.885334h267.690667z" fill="#FFFFFF"></path></svg>'},{name:"22",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M511.999787 512.000213m-511.999787 0a511.999787 511.999787 0 1 0 1023.999573 0 511.999787 511.999787 0 1 0-1023.999573 0Z" fill="#6D768D"></path><path d="M381.354508 364.586941c0 54.015977 29.013321 103.935957 75.946635 130.986613a152.53327 152.53327 0 0 0 151.935936 0 151.12527 151.12527 0 0 0 75.946636-130.986613A151.594604 151.594604 0 0 0 533.333111 213.333671a151.594604 151.594604 0 0 0-151.89327 151.25327zM660.479725 498.901552a185.258589 185.258589 0 0 1-127.146614 50.346646c-49.066646 0-93.866628-19.199992-127.06128-50.346646C317.141201 544.853533 255.999893 637.440161 255.999893 744.106783c0 13.183995 10.709329 23.850657 23.978657 23.850657h506.709122a23.893323 23.893323 0 0 0 23.978657-23.893323c0-106.538622-61.098641-199.25325-150.186604-245.205232z" fill="#FFFFFF"></path></svg>'},{name:"23",icon:'<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#6D768D"></path><path d="M445.610667 401.578667a129.322667 129.322667 0 1 0 258.645333 0 129.322667 129.322667 0 0 0-258.645333 0z m237.568 114.901333a157.354667 157.354667 0 0 1-216.362667 0 236.373333 236.373333 0 0 0-127.957333 209.706667c0 11.264 9.130667 20.394667 20.394666 20.394666h431.402667a20.394667 20.394667 0 0 0 20.394667-20.394666 236.373333 236.373333 0 0 0-127.872-209.706667zM409.813333 401.578667c0-40.362667 14.592-77.397333 38.698667-106.112a112.725333 112.725333 0 0 0-29.013333-3.925334 112.64 112.64 0 0 0-112.426667 112.469334 112.64 112.64 0 0 0 144.853333 107.648 164.693333 164.693333 0 0 1-42.112-110.08z m-18.602666 136.704a136.533333 136.533333 0 0 1-65.706667-34.474667 205.44 205.44 0 0 0-111.232 182.4c0 9.813333 7.936 17.706667 17.706667 17.706667H303.36a273.621333 273.621333 0 0 1 87.893333-165.632z" fill="#FFFFFF"></path></svg>'}]}],ud=(s,t=[])=>{let e=s.split("_"),n=ph([...Sr,...t]).find(r=>r.type===e[0]);if(n){let r=n.list.find(o=>o.name===e[1]);return r?r.icon:""}else return""},Gi={hyperlink:hd,note:dd,attachment:cd,nodeIconList:Sr,getNodeIconListIcon:ud},fd=(s,t)=>{const e=new Bt,i=new Ot().text(s);return t.text(i),e.add(i),e.bbox()},_s={radius:3,fontSize:12,fill:"",height:20,paddingX:8};function pd(){let s=this.getData("image");if(!s)return;s=(this.mindMap.renderer.renderTree.data.imgMap||{})[s]||s;const t=this.getImgShowSize(),e=new Ie().load(s).size(...t),{defaultNodeImage:i}=this.mindMap.opt;if(i){const n=new Image;n.onerror=()=>{e.load(i)},n.src=s}return this.getData("imageTitle")&&e.attr("title",this.getData("imageTitle")),e.on("click",n=>{this.mindMap.emit("node_img_click",this,e,n)}),e.on("dblclick",n=>{this.mindMap.emit("node_img_dblclick",this,n,e)}),e.on("mouseenter",n=>{this.mindMap.emit("node_img_mouseenter",this,e,n)}),e.on("mouseleave",n=>{this.mindMap.emit("node_img_mouseleave",this,e,n)}),e.on("mousemove",n=>{this.mindMap.emit("node_img_mousemove",this,e,n)}),{node:e,width:t[0],height:t[1]}}function md(){const{custom:s,width:t,height:e}=this.getData("imageSize");return s?[t,e]:th(t,e,this.mindMap.themeConfig.imgMaxWidth,this.mindMap.themeConfig.imgMaxHeight)}function gd(){let s=this.getData();if(!s.icon||s.icon.length<=0)return[];let t=this.mindMap.themeConfig.iconSize;return s.icon.map(e=>{let i=Gi.getNodeIconListIcon(e,this.mindMap.opt.iconList||[]),n=null;return/^<svg/.test(i)?n=Tt(i):n=new Ie().load(i),n.size(t,t),n.on("click",r=>{this.mindMap.emit("node_icon_click",this,e,r,n)}),n.on("mouseenter",r=>{this.mindMap.emit("node_icon_mouseenter",this,e,r,n)}),n.on("mouseleave",r=>{this.mindMap.emit("node_icon_mouseleave",this,e,r,n)}),{node:n,width:t,height:t}})}function vd(s){const t=this.hasCustomWidth();let e=typeof s=="string"?s:this.getData("text"),{textAutoWrapWidth:i,emptyTextMeasureHeightText:n}=this.mindMap.opt;i=t?this.customTextWidth:i;const r=new Bt;let o=!1;this.getData("resetRichText")&&(delete this.nodeData.data.resetRichText,o=!0),o&&!Me(e)&&(lh(e)?e=ch(e):e=`<p>${e}</p>`,this.setData({text:e}));const a=[],l=Ch(this);Object.keys(l).forEach(p=>{a.push([p,l[p]])}),this.mindMap.commonCaches.measureRichtextNodeTextSizeEl||(this.mindMap.commonCaches.measureRichtextNodeTextSizeEl=document.createElement("div"),this.mindMap.commonCaches.measureRichtextNodeTextSizeEl.style.position="fixed",this.mindMap.commonCaches.measureRichtextNodeTextSizeEl.style.left="-999999px",this.mindMap.el.appendChild(this.mindMap.commonCaches.measureRichtextNodeTextSizeEl));const h=this.mindMap.commonCaches.measureRichtextNodeTextSizeEl;a.forEach(([p,x])=>{h.style[p]=x}),h.style.lineHeight=1.2;const d=`<div>${e}</div>`;h.innerHTML=d;const c=h.children[0];c.classList.add("smm-richtext-node-wrap"),We(c),c.style.maxWidth=i+"px",t?c.style.width=this.customTextWidth+"px":c.style.width="";let{width:f,height:g}=c.getBoundingClientRect();if(g<=0){h.innerHTML=`<p>${n}</p>`;let p=h.children[0];p.classList.add("smm-richtext-node-wrap"),g=p.getBoundingClientRect().height,h.innerHTML=d}f=Math.min(Math.ceil(f)+1,i),g=Math.ceil(g),r.attr("data-width",f),r.attr("data-height",g);const u=Ye({el:h.children[0],width:f,height:g}),m={"line-height":1.2};return a.forEach(([p,x])=>{m[nh(p)]=x}),u.css(m),r.add(u),{node:r,nodeContent:u,width:f,height:g}}function yd(s){if(this.getData("needUpdate")&&delete this.nodeData.data.needUpdate,this.getData("richText"))return this.createRichTextNode(s);const t=typeof s=="string"?s:this.getData("text");this.getData("resetRichText")&&delete this.nodeData.data.resetRichText;const e=new Bt,i=this.getStyle("fontSize",!1),n=this.getStyle("textAlign",!1);let r=[];Me(t)||(r=String(t).split(/\n/gim));const{textAutoWrapWidth:o,emptyTextMeasureHeightText:a}=this.mindMap.opt;let l=r.length>1;r.forEach((c,f)=>{let g=c.split(""),u=[],m=[];for(;g.length;){let p=g.shift(),x=[...m,p].join("");fd(x,this.style).width<=o?m.push(p):(u.push(m.join("")),m=[p])}m.length>0&&u.push(m.join("")),u.length>1&&(l=!0),r[f]=u.join(`
`)}),r=r.join(`
`).replace(/\n$/g,"").split(/\n/gim),r.forEach((c,f)=>{c===""&&(c="\uFEFF");const g=new Ot().text(c);g.addClass("smm-text-node-wrap"),g.attr("text-anchor",{left:"start",center:"middle",right:"end"}[n]||"start"),this.style.text(g),g.y(i*Ri*f+(Ri-1)*i/2),e.add(g)});let{width:h,height:d}=e.bbox();if(d<=0){const c=new Ot().text(a);this.style.text(c),d=c.bbox().height}return h=Math.min(Math.ceil(h),o),d=Math.ceil(d),e.attr("data-width",h),e.attr("data-height",d),e.attr("data-ismultiLine",l||r.length>1),{node:e,width:h,height:d}}function xd(){const{hyperlink:s,hyperlinkTitle:t}=this.getData();if(!s)return;const{customHyperlinkJump:e,hyperlinkIcon:i}=this.mindMap.opt,{icon:n,style:r}=i,o=this.getNodeIconSize("hyperlinkIcon"),a=new Tt().size(o,o),l=new di().to(s).target("_blank");l.node.addEventListener("click",d=>{typeof e=="function"&&(d.preventDefault(),e(s,this))}),t&&a.add(Tt(`<title>${t}</title>`)),l.rect(o,o).fill({color:"transparent"});const h=Tt(n||Gi.hyperlink).size(o,o);return this.style.iconNode(h,r.color),l.add(h),a.add(l),{node:a,width:o,height:o}}function wd(){const s=this.getData("tag");if(!s||s.length<=0)return[];let{maxTag:t,tagsColorMap:e}=this.mindMap.opt;e=e||{};const i=[];return s.slice(0,t).forEach((n,r)=>{let o="",a={..._s};typeof n=="string"?o=n:(o=n.text,a={..._s,...n.style});const l=typeof a.width<"u",h=new Bt;h.on("click",()=>{this.mindMap.emit("node_tag_click",this,n,r,h)});const d=new Ot().text(o);this.style.tagText(d,a);const{width:c,height:f}=d.bbox(),g=l?a.width:c+a.paddingX*2,u=l?Math.max(g,c):g,m=Math.max(a.height,f);l?d.x((u-c)/2):d.x(l?0:a.paddingX),d.cy(-m/2);const p=new Jt().size(g,a.height).cy(-m/2);l&&p.x((u-g)/2),this.style.tagRect(p,{...a,fill:a.fill||e[d.node.textContent]||Er(d.node.textContent)}),h.add(p).add(d),i.push({node:h,width:u,height:m})}),i}function _d(){if(!this.getData("note"))return null;const{icon:s,style:t}=this.mindMap.opt.noteIcon,e=this.getNodeIconSize("noteIcon"),i=new Tt().attr("cursor","pointer").addClass("smm-node-note").size(e,e);i.add(new Jt().size(e,e).fill({color:"transparent"}));const n=Tt(s||Gi.note).size(e,e);return this.style.iconNode(n,t.color),i.add(n),this.mindMap.opt.customNoteContentShow||(this.noteEl||(this.noteEl=document.createElement("div"),this.noteEl.style.cssText=`
          position: fixed;
          padding: 10px;
          border-radius: 5px;
          box-shadow: 0 2px 5px rgb(0 0 0 / 10%);
          display: none;
          background-color: #fff;
          z-index: ${this.mindMap.opt.nodeNoteTooltipZIndex}
      `,(this.mindMap.opt.customInnerElsAppendTo||document.body).appendChild(this.noteEl)),this.noteEl.innerText=this.getData("note")),i.on("mouseover",()=>{const{left:r,top:o}=this.getNoteContentPosition();this.mindMap.opt.customNoteContentShow?this.mindMap.opt.customNoteContentShow.show(this.getData("note"),r,o,this):(this.noteEl.style.left=r+"px",this.noteEl.style.top=o+"px",this.noteEl.style.display="block")}),i.on("mouseout",()=>{this.mindMap.opt.customNoteContentShow?this.mindMap.opt.customNoteContentShow.hide():this.noteEl.style.display="none"}),i.on("click",r=>{this.mindMap.emit("node_note_click",this,r,i)}),i.on("dblclick",r=>{this.mindMap.emit("node_note_dblclick",this,r,i)}),{node:i,width:e,height:e}}function Md(){const{attachmentUrl:s,attachmentName:t}=this.getData();if(!s)return;const e=this.getNodeIconSize("attachmentIcon"),{icon:i,style:n}=this.mindMap.opt.attachmentIcon,r=new Tt().attr("cursor","pointer").size(e,e);t&&r.add(Tt(`<title>${t}</title>`)),r.add(new Jt().size(e,e).fill({color:"transparent"}));const o=Tt(i||Gi.attachment).size(e,e);return this.style.iconNode(o,n.color),r.add(o),r.on("click",a=>{this.mindMap.emit("node_attachmentClick",this,a,r)}),r.on("contextmenu",a=>{this.mindMap.emit("node_attachmentContextmenu",this,a,r)}),{node:r,width:e,height:e}}function Ed(s){const{style:t}=this.mindMap.opt[s];return Me(t.size)?this.mindMap.themeConfig.iconSize:t.size}function Cd(){const s=this.getNodeIconSize("noteIcon"),{scaleY:t}=this.mindMap.view.getTransformData().transform,e=s*t;let{left:i,top:n}=this._noteData.node.node.getBoundingClientRect();return n+=e,{left:i,top:n}}function Nd(s){this.mindMap.commonCaches.measureCustomNodeContentSizeEl||(this.mindMap.commonCaches.measureCustomNodeContentSizeEl=document.createElement("div"),this.mindMap.commonCaches.measureCustomNodeContentSizeEl.style.cssText=`
      position: fixed;
      left: -99999px;
      top: -99999px;
    `,this.mindMap.el.appendChild(this.mindMap.commonCaches.measureCustomNodeContentSizeEl)),this.mindMap.commonCaches.measureCustomNodeContentSizeEl.innerHTML="",this.mindMap.commonCaches.measureCustomNodeContentSizeEl.appendChild(s);let t=this.mindMap.commonCaches.measureCustomNodeContentSizeEl.getBoundingClientRect();return{width:t.width,height:t.height}}function Td(){return!!this._customNodeContent}const Ms={createImgNode:pd,getImgShowSize:md,createIconNode:gd,createRichTextNode:vd,createTextNode:yd,createHyperlinkNode:xd,createTagNode:wd,createNoteNode:_d,createAttachmentNode:Md,getNoteContentPosition:Cd,getNodeIconSize:Ed,measureCustomNodeContentSize:Nd,isUseCustomNodeContent:Td};function Sd(){if(this.getChildrenLength()<=0||this.isRoot)return;const{alwaysShowExpandBtn:s,notShowExpandBtn:t,expandBtnSize:e}=this.mindMap.opt;if(!s&&!t){let{width:i,height:n}=this;this._unVisibleRectRegionNode||(this._unVisibleRectRegionNode=new Jt,this._unVisibleRectRegionNode.fill({color:"transparent"})),this.group.add(this._unVisibleRectRegionNode),this.renderer.layout.renderExpandBtnRect(this._unVisibleRectRegionNode,e,i,n,this)}}function Ld(){this._unVisibleRectRegionNode&&(this._unVisibleRectRegionNode.remove(),this._unVisibleRectRegionNode=null)}function bd(){this.needRerenderExpandBtnPlaceholderRect&&(this.needRerenderExpandBtnPlaceholderRect=!1,this.renderExpandBtnPlaceholderRect()),this.getChildrenLength()>0?this._unVisibleRectRegionNode||this.renderExpandBtnPlaceholderRect():this._unVisibleRectRegionNode&&this.clearExpandBtnPlaceholderRect()}const Es={renderExpandBtnPlaceholderRect:Sd,clearExpandBtnPlaceholderRect:Ld,updateExpandBtnPlaceholderRect:bd};function Dd(){this.checkEnableDragModifyNodeWidth()&&(this._dragHandleNodes=null,this.dragHandleWidth=4,this.dragHandleMousedownX=0,this.isDragHandleMousedown=!1,this.dragHandleIndex=0,this.dragHandleMousedownCustomTextWidth=0,this.dragHandleMousedownBodyCursor="",this.dragHandleMousedownLeft=0,this.onDragMousemoveHandle=this.onDragMousemoveHandle.bind(this),window.addEventListener("mousemove",this.onDragMousemoveHandle),this.onDragMouseupHandle=this.onDragMouseupHandle.bind(this),window.addEventListener("mouseup",this.onDragMouseupHandle),this.mindMap.on("node_mouseup",this.onDragMouseupHandle))}function Ad(s){if(!this.isDragHandleMousedown)return;s.stopPropagation(),s.preventDefault();let{minNodeTextModifyWidth:t,maxNodeTextModifyWidth:e,isUseCustomNodeContent:i,customCreateNodeContent:n}=this.mindMap.opt;const r=i&&n&&this._customNodeContent;document.body.style.cursor="ew-resize",this.group.css({cursor:"ew-resize"});const{scaleX:o}=this.mindMap.draw.transform(),a=s.clientX-this.dragHandleMousedownX;let l=this.dragHandleMousedownCustomTextWidth+(this.dragHandleIndex===0?-a:a)/o;if(l=Math.max(l,t),e!==-1&&(l=Math.min(l,e)),!r&&this.getData("image")){const h=this.getImgShowSize();this._rectInfo.textContentWidth-this.customTextWidth+l<=h[0]&&(l=h[0]+this.customTextWidth-this._rectInfo.textContentWidth)}this.customTextWidth=l,this.dragHandleIndex===0&&(this.left=this.dragHandleMousedownLeft+a/o),this.reRender(r?[]:["text"],{ignoreUpdateCustomTextWidth:!0})}function Rd(){this.isDragHandleMousedown&&(document.body.style.cursor=this.dragHandleMousedownBodyCursor,this.group.css({cursor:"default"}),this.isDragHandleMousedown=!1,this.dragHandleMousedownX=0,this.dragHandleIndex=0,this.dragHandleMousedownCustomTextWidth=0,this.setData({customTextWidth:this.customTextWidth}),this.mindMap.render(),this.mindMap.emit("dragModifyNodeWidthEnd",this))}function Id(){const s=[new Jt,new Jt];return s.forEach((t,e)=>{t.size(this.dragHandleWidth,this.height).fill({color:"transparent"}).css({cursor:"ew-resize"}),t.on("mousedown",i=>{i.stopPropagation(),i.preventDefault(),this.dragHandleMousedownX=i.clientX,this.dragHandleIndex=e,this.dragHandleMousedownCustomTextWidth=this.customTextWidth===void 0?this._textData?this._textData.width:this.width:this.customTextWidth,this.dragHandleMousedownBodyCursor=document.body.style.cursor,this.dragHandleMousedownLeft=this.left,this.isDragHandleMousedown=!0})}),s}function Od(){this.checkEnableDragModifyNodeWidth()&&(this._dragHandleNodes||(this._dragHandleNodes=this.createDragHandleNode()),this.getData("isActive")?(this._dragHandleNodes.forEach(s=>{s.height(this.height),this.group.add(s)}),this._dragHandleNodes[1].x(this.width-this.dragHandleWidth)):this._dragHandleNodes.forEach(s=>{s.remove()}))}const Cs={initDragHandle:Dd,onDragMousemoveHandle:Ad,onDragMouseupHandle:Rd,createDragHandleNode:Id,updateDragHandle:Od};function kd(){this.mindMap.cooperate&&(this._userListGroup=new Bt,this.group.add(this._userListGroup))}function zd(s){const{avatarSize:t,fontSize:e}=this.mindMap.opt.cooperateStyle,i=new Bt,n=s.isMore?s.name:String(s.name)[0],r=new Ze().size(t,t);r.fill({color:s.color||Er(n)});const o=new Ot().text(n).fill({color:"#fff"}).css({"font-size":e+"px"}).dx(-e/2).dy((t-e)/2);return i.add(r).add(o),i}function Fd(s){const{avatarSize:t}=this.mindMap.opt.cooperateStyle;return new Ie().load(s.avatar).size(t,t)}function Pd(){if(!this._userListGroup)return;const{avatarSize:s}=this.mindMap.opt.cooperateStyle;this._userListGroup.clear();const t=this.userList.length,e=Math.floor(this.width/s),i=[];t>e?i.push(...this.userList.slice(0,e-1),{isMore:!0,name:"+"+(t-e+1)}):i.push(...this.userList),i.forEach((n,r)=>{let o=null;n.avatar?o=this.createImageAvatar(n):o=this.createTextAvatar(n),o.on("click",a=>{this.mindMap.emit("node_cooperate_avatar_click",n,this,o,a)}),o.on("mouseenter",a=>{this.mindMap.emit("node_cooperate_avatar_mouseenter",n,this,o,a)}),o.on("mouseleave",a=>{this.mindMap.emit("node_cooperate_avatar_mouseleave",n,this,o,a)}),o.x(r*s).cy(-s/2),this._userListGroup.add(o)})}function Bd(s){this.userList.find(t=>t.id==s.id)||(this.userList.push(s),this.updateUserListNode())}function $d(s){const t=this.userList.findIndex(e=>e.id==s.id);t!==-1&&(this.userList.splice(t,1),this.updateUserListNode())}function Hd(){this.userList=[],this.updateUserListNode()}const Ns={createUserListNode:kd,updateUserListNode:Pd,createTextAvatar:zd,createImageAvatar:Fd,addUser:Bd,removeUser:$d,emptyUser:Hd};function Ud(){this.isGeneralization||(this._quickCreateChildBtn=null,this._showQuickCreateChildBtn=!1)}function Gd(){if(!(this.isGeneralization||this.getChildrenLength()>0)){if(this._quickCreateChildBtn)this.group.add(this._quickCreateChildBtn);else{const{quickCreateChildBtnIcon:s,expandBtnStyle:t,expandBtnSize:e}=this.mindMap.opt,{icon:i,style:n}=s;let{color:r,fill:o}=t||{color:"#808080",fill:"#fff"};r=n.color||r;const a=Tt(i||_n.quickCreateChild).size(e,e);a.css({cursor:"pointer"}),a.x(0).y(-e/2),this.style.iconNode(a,r);const l=new Ze().size(e);l.x(0).y(-e/2),l.fill({color:o}).css({cursor:"pointer"}),this._quickCreateChildBtn=new Bt,this._quickCreateChildBtn.add(l).add(a),this._quickCreateChildBtn.on("click",h=>{h.stopPropagation(),this.mindMap.emit("quick_create_btn_click",this);const{customQuickCreateChildBtnClick:d}=this.mindMap.opt;if(typeof d=="function"){d(this);return}this.mindMap.execCommand("INSERT_CHILD_NODE",!0,[this])}),this._quickCreateChildBtn.on("dblclick",h=>{h.stopPropagation()}),this._quickCreateChildBtn.addClass("smm-quick-create-child-btn"),this.group.add(this._quickCreateChildBtn)}this._showQuickCreateChildBtn=!0,this.renderer.layout.renderExpandBtn(this,this._quickCreateChildBtn)}}function Yd(){this.isGeneralization||this._quickCreateChildBtn&&this._showQuickCreateChildBtn&&(this._quickCreateChildBtn.remove(),this._showQuickCreateChildBtn=!1)}function Wd(){if(this.isGeneralization)return;const{isActive:s}=this.getData();s||this.removeQuickCreateChildBtn()}const Ts={initQuickCreateChildBtn:Ud,showQuickCreateChildBtn:Gd,removeQuickCreateChildBtn:Yd,hideQuickCreateChildBtn:Wd};function Vd(s,t,e,i,n){const{imgTextMargin:r}=this.mindMap.opt;return s==="v"?i>0&&n>0?r:0:t>0&&e>0?r:0}function Xd(s){let t=0,e=this._tagData.reduce((i,n)=>(t=Math.max(t,n.height),i+=n.width),0);return e+=(this._tagData.length-1)*s,{width:e,height:t}}function jd(){if(this.isUseCustomNodeContent()){const T=this.measureCustomNodeContentSize(this._customNodeContent);return{width:this.hasCustomWidth()?this.customTextWidth:T.width,height:T.height}}const{TAG_PLACEMENT:s,IMG_PLACEMENT:t}=M,{textContentMargin:e}=this.mindMap.opt,n=(this.getStyle("tagPlacement")||s.RIGHT)===s.BOTTOM,r=this.getStyle("imgPlacement")||t.TOP;let o=0,a=0,l=0,h=0,d=0,c=0,f=0;if(this._imgData&&(o=this._imgData.width,a=this._imgData.height),this.mindMap.nodeInnerPrefixList.forEach(T=>{const C=this[`_${T.name}Data`];C&&(l+=C.width,h=Math.max(h,C.height),f++)}),this._prefixData&&(l+=this._prefixData.width,h=Math.max(h,this._prefixData.height),f++),this._iconData.length>0&&(l+=this._iconData.reduce((T,C)=>(h=Math.max(h,C.height),T+=C.width),0)+(this._iconData.length-1)*e,f++),this._textData&&(l+=this._textData.width,h=Math.max(h,this._textData.height),f++),this._hyperlinkData&&(l+=this._hyperlinkData.width,h=Math.max(h,this._hyperlinkData.height),f++),this._tagData.length>0){const{width:T,height:C}=this.getTagContentSize(e);n?(d=T,c=C):(l+=T,h=Math.max(h,C),f++)}this._noteData&&(l+=this._noteData.width,h=Math.max(h,this._noteData.height),f++),this._attachmentData&&(l+=this._attachmentData.width,h=Math.max(h,this._attachmentData.height),f++),this._postfixData&&(l+=this._postfixData.width,h=Math.max(h,this._postfixData.height),f++),this.mindMap.nodeInnerPostfixList.forEach(T=>{const C=this[`_${T.name}Data`];C&&(l+=C.width,h=Math.max(h,C.height),f++)}),l+=(f-1)*e,n&&l>0&&c>0&&(this._rectInfo.textContentWidthWithoutTag=l,l=Math.max(l,d),h=h+e+c),this._rectInfo.textContentWidth=l,this._rectInfo.textContentHeight=h;let g=0,u=0;[t.TOP,t.BOTTOM].includes(r)?(g=Math.max(o,l),u=a+h+this.getImgTextMarin("v",0,0,a,h)):(g=o+l+this.getImgTextMarin("h",o,l),u=Math.max(a,h));const{paddingX:m,paddingY:p}=this.getPaddingVale(),{paddingX:x,paddingY:E}=this.shapeInstance.getShapePadding(g,u,m,p);this.shapePadding.paddingX=x,this.shapePadding.paddingY=E;const _=this.getBorderWidth();return{width:g+m*2+x*2+_,height:u+p*2+E*2+_}}function qd(){if(!this.group)return;this.group.clear();const{hoverRectPadding:s,openRealtimeRenderOnNodeTextEdit:t,textContentMargin:e,addCustomContentToNode:i}=this.mindMap.opt,{width:n,height:r}=this;let{paddingX:o,paddingY:a}=this.getPaddingVale();const l=this.getBorderWidth()/2;o+=this.shapePadding.paddingX+l,a+=this.shapePadding.paddingY+l,this.shapeNode=this.shapeInstance.createShape(),this.shapeNode.addClass("smm-node-shape"),this.shapeNode.translate(l,l),this.style.shape(this.shapeNode),this.group.add(this.shapeNode),this.renderExpandBtnPlaceholderRect(),this.createUserListNode&&this.createUserListNode(),this.isGeneralization&&this.generalizationBelongNode&&this.group.addClass("generalization_"+this.generalizationBelongNode.uid);const h=()=>{this.hoverNode=new Jt().size(n+s*2,r+s*2).x(-s).y(-s),this.hoverNode.addClass("smm-hover-node"),this.style.hoverNode(this.hoverNode,n,r),this.group.add(this.hoverNode)};if(this.isUseCustomNodeContent()){const O=Ye({el:this._customNodeContent,width:n,height:r});this.group.add(O),h();return}const{IMG_PLACEMENT:d,TAG_PLACEMENT:c}=M,f=this.getStyle("imgPlacement")||d.TOP,u=(this.getStyle("tagPlacement")||c.RIGHT)===c.BOTTOM;let{textContentWidth:m,textContentHeight:p,textContentWidthWithoutTag:x}=this._rectInfo;const E=p;let _=0,T=0;const C=this._tagData&&this._tagData.length>0;if(C){const O=this.getTagContentSize(e);_=O.width,T=O.height,u&&(p-=T+e)}let L=0,F=0;if(this._imgData)switch(L=this._imgData.width,F=this._imgData.height,this.group.add(this._imgData.node),f){case d.TOP:this._imgData.node.cx(n/2).y(a);break;case d.BOTTOM:this._imgData.node.cx(n/2).y(r-a-F);break;case d.LEFT:this._imgData.node.x(o).cy(r/2);break;case d.RIGHT:this._imgData.node.x(n-o-L).cy(r/2);break}let W=new Bt,X=0;if(C&&u&&(X=x<m?(m-x)/2:0),this.mindMap.nodeInnerPrefixList.forEach(O=>{const H=this[`_${O.name}Data`];H&&(H.node.x(X).y((p-H.height)/2),W.add(H.node),X+=H.width+e)}),this._prefixData){const O=Ye({el:this._prefixData.el,width:this._prefixData.width,height:this._prefixData.height});O.x(X).y((p-this._prefixData.height)/2),W.add(O),X+=this._prefixData.width+e}let Mt=new Bt;if(this._iconData&&this._iconData.length>0){let O=0;this._iconData.forEach(H=>{H.node.x(X+O).y((p-H.height)/2),Mt.add(H.node),O+=H.width+e}),W.add(Mt),X+=O}if(this._textData){const O=this._textData.node.attr("data-offsetx")||0;this._textData.node.attr("data-offsetx",X),(this._textData.nodeContent||this._textData.node).x(-O).x(X).y((p-this._textData.height)/2),t&&this._textData.node.opacity(this.mindMap.renderer.textEdit.getCurrentEditNode()===this?0:1),W.add(this._textData.node),X+=this._textData.width+e}this._hyperlinkData&&(this._hyperlinkData.node.x(X).y((p-this._hyperlinkData.height)/2),W.add(this._hyperlinkData.node),X+=this._hyperlinkData.width+e);let ct=new Bt;if(C)if(u){let O=0;this._tagData.forEach(H=>{H.node.x(O).y((T-H.height)/2),ct.add(H.node),O+=H.width+e}),ct.x((m-_)/2).y(E-T),W.add(ct)}else{let O=0;this._tagData.forEach(H=>{H.node.x(X+O).y((p-H.height)/2),ct.add(H.node),O+=H.width+e}),W.add(ct),X+=O}if(this._noteData&&(this._noteData.node.x(X).y((p-this._noteData.height)/2),W.add(this._noteData.node),X+=this._noteData.width+e),this._attachmentData&&(this._attachmentData.node.x(X).y((p-this._attachmentData.height)/2),W.add(this._attachmentData.node),X+=this._attachmentData.width+e),this._postfixData){const O=Ye({el:this._postfixData.el,width:this._postfixData.width,height:this._postfixData.height});O.x(X).y((p-this._postfixData.height)/2),W.add(O),X+=this._postfixData.width+e}this.mindMap.nodeInnerPostfixList.forEach(O=>{const H=this[`_${O.name}Data`];H&&(H.node.x(X).y((p-H.height)/2),W.add(H.node),X+=H.width+e)}),this.group.add(W);const{width:Lt,height:bt}=W.bbox();let Dt=0,A=0;switch(f){case d.TOP:Dt=n/2-Lt/2,A=a+F+this.getImgTextMarin("v",0,0,F,E);break;case d.BOTTOM:Dt=n/2-Lt/2,A=a;break;case d.LEFT:Dt=L+o+this.getImgTextMarin("h",L,m),A=r/2-bt/2;break;case d.RIGHT:Dt=o,A=r/2-bt/2;break}if(W.translate(Dt,A),h(),this._customContentAddToNodeAdd&&this._customContentAddToNodeAdd.el){const O=Ye(this._customContentAddToNodeAdd);this.group.add(O),i&&typeof i.handle=="function"&&i.handle({content:this._customContentAddToNodeAdd,element:O,node:this})}this.mindMap.emit("node_layout_end",this)}const Ss={getImgTextMarin:Vd,getTagContentSize:Xd,getNodeRect:jd,layout:qd};class Yi{constructor(t={}){this.opt=t,this.nodeData=this.handleData(t.data||{}),this.nodeDataSnapshot="",this.uid=t.uid,this.mindMap=t.mindMap,this.renderer=t.renderer,this.draw=this.mindMap.draw,this.nodeDraw=this.mindMap.nodeDraw,this.lineDraw=this.mindMap.lineDraw,this.style=new ae(this),this.effectiveStyles={},this.shapeInstance=new Nh(this),this.shapePadding={paddingX:0,paddingY:0},this.isRoot=t.isRoot===void 0?!1:t.isRoot,this.isGeneralization=t.isGeneralization===void 0?!1:t.isGeneralization,this.generalizationBelongNode=null,this.layerIndex=t.layerIndex===void 0?0:t.layerIndex,this.width=t.width||0,this.height=t.height||0,this.customTextWidth=t.data.data.customTextWidth||void 0,this._left=t.left||0,this._top=t.top||0,this.customLeft=t.data.data.customLeft||void 0,this.customTop=t.data.data.customTop||void 0,this.isDrag=!1,this.parent=t.parent||null,this.children=t.children||[],this.userList=[],this.group=null,this.shapeNode=null,this.hoverNode=null,this._customNodeContent=null,this._imgData=null,this._iconData=null,this._textData=null,this._hyperlinkData=null,this._tagData=null,this._noteData=null,this.noteEl=null,this.noteContentIsShow=!1,this._attachmentData=null,this._prefixData=null,this._postfixData=null,this._expandBtn=null,this._lastExpandBtnType=null,this._showExpandBtn=!1,this._openExpandNode=null,this._closeExpandNode=null,this._fillExpandNode=null,this._userListGroup=null,this._lines=[],this._generalizationList=[],this._unVisibleRectRegionNode=null,this._isMouseenter=!1,this._customContentAddToNodeAdd=null,this._rectInfo={textContentWidth:0,textContentHeight:0,textContentWidthWithoutTag:0},this._generalizationNodeWidth=0,this._generalizationNodeHeight=0,this.expandBtnSize=this.mindMap.opt.expandBtnSize,this.isMultipleChoice=!1,this.needLayout=!1,this.isHide=!1;const e=Object.getPrototypeOf(this);e.bindEvent||(Object.keys(Ss).forEach(i=>{e[i]=Ss[i]}),Object.keys(ys).forEach(i=>{e[i]=ys[i]}),Object.keys(xs).forEach(i=>{e[i]=xs[i]}),Object.keys(Es).forEach(i=>{e[i]=Es[i]}),Object.keys(ws).forEach(i=>{e[i]=ws[i]}),Object.keys(Ms).forEach(i=>{e[i]=Ms[i]}),this.mindMap.cooperate&&Object.keys(Ns).forEach(i=>{e[i]=Ns[i]}),Object.keys(Cs).forEach(i=>{e[i]=Cs[i]}),this.mindMap.opt.isShowCreateChildBtnIcon&&(Object.keys(Ts).forEach(i=>{e[i]=Ts[i]}),this.initQuickCreateChildBtn()),e.bindEvent=!0),this.getSize(),this.updateGeneralization(),this.initDragHandle()}get left(){return this.customLeft||this._left}set left(t){this._left=t}get top(){return this.customTop||this._top}set top(t){this._top=t}reset(){this.children=[],this.parent=null,this.isRoot=!1,this.layerIndex=0,this.left=0,this.top=0}resetWhenDelete(){this._isMouseenter=!1}handleData(t){return t.data.expand=t.data.expand!==!1,t.data.isActive=t.data.isActive===!0,t.children=t.children||[],t}createNodeData(t){const{isUseCustomNodeContent:e,customCreateNodeContent:i,createNodePrefixContent:n,createNodePostfixContent:r,addCustomContentToNode:o}=this.mindMap.opt,a=["custom","image","icon","text","hyperlink","tag","note","attachment","prefix","postfix",...this.mindMap.nodeInnerPrefixList.map(h=>h.name),...this.mindMap.nodeInnerPostfixList.map(h=>h.name)],l={};if(Array.isArray(t)?a.forEach(h=>{t.includes(h)&&(l[h]=!0)}):a.forEach(h=>{l[h]=!0}),e&&i&&l.custom&&(this._customNodeContent=i(this)),this._customNodeContent){We(this._customNodeContent);return}l.image&&(this._imgData=this.createImgNode()),l.icon&&(this._iconData=this.createIconNode()),l.text&&(this._textData=this.createTextNode()),l.hyperlink&&(this._hyperlinkData=this.createHyperlinkNode()),l.tag&&(this._tagData=this.createTagNode()),l.note&&(this._noteData=this.createNoteNode()),l.attachment&&(this._attachmentData=this.createAttachmentNode()),this.mindMap.nodeInnerPrefixList.forEach(h=>{l[h.name]&&(this[`_${h.name}Data`]=h.createContent(this))}),l.prefix&&(this._prefixData=n?n(this):null,this._prefixData&&this._prefixData.el&&We(this._prefixData.el)),l.postfix&&(this._postfixData=r?r(this):null,this._postfixData&&this._postfixData.el&&We(this._postfixData.el)),this.mindMap.nodeInnerPostfixList.forEach(h=>{l[h.name]&&(this[`_${h.name}Data`]=h.createContent(this))}),o&&typeof o.create=="function"&&(this._customContentAddToNodeAdd=o.create(this),this._customContentAddToNodeAdd&&this._customContentAddToNodeAdd.el&&We(this._customContentAddToNodeAdd.el))}getSize(t,e={}){e.ignoreUpdateCustomTextWidth||!1||(this.customTextWidth=this.getData("customTextWidth")||void 0),this.customLeft=this.getData("customLeft")||void 0,this.customTop=this.getData("customTop")||void 0,this.createNodeData(t);const{width:n,height:r}=this.getNodeRect(),o=this.width!==n||this.height!==r;return this.width=n,this.height=r,o}bindGroupEvent(){this.group.on("click",t=>{if(this.mindMap.emit("node_click",this,t),this.isMultipleChoice){t.stopPropagation(),this.isMultipleChoice=!1;return}this.mindMap.opt.onlyOneEnableActiveNodeOnCooperate&&this.userList.length>0||this.active(t)}),this.group.on("mousedown",t=>{const{readonly:e,enableCtrlKeyNodeSelection:i,useLeftKeySelectionRightKeyDrag:n,mousedownEventPreventDefault:r}=this.mindMap.opt;if(r&&t.preventDefault(),e||(this.isRoot?t.which===3&&!n&&t.stopPropagation():t.which!==2&&t.stopPropagation()),!e&&(t.ctrlKey||t.metaKey)&&i){this.isMultipleChoice=!0;const o=this.getData("isActive");o||this.mindMap.emit("before_node_active",this,this.renderer.activeNodeList),this.mindMap.renderer[o?"removeNodeFromActiveList":"addNodeToActiveList"](this,!0),this.renderer.emitNodeActiveEvent(o?null:this)}this.mindMap.emit("node_mousedown",this,t)}),this.group.on("mouseup",t=>{!this.isRoot&&t.which!==2&&!this.mindMap.opt.readonly&&t.stopPropagation(),this.mindMap.emit("node_mouseup",this,t)}),this.group.on("mouseenter",t=>{this.isDrag||(this._isMouseenter=!0,this.showExpandBtn(),this.isGeneralization&&this.handleGeneralizationMouseenter(),this.mindMap.emit("node_mouseenter",this,t))}),this.group.on("mouseleave",t=>{this._isMouseenter&&(this._isMouseenter=!1,this.hideExpandBtn(),this.isGeneralization&&this.handleGeneralizationMouseleave(),this.mindMap.emit("node_mouseleave",this,t))}),this.group.on("dblclick",t=>{const{readonly:e,onlyOneEnableActiveNodeOnCooperate:i}=this.mindMap.opt;e||t.ctrlKey||t.metaKey||(t.stopPropagation(),!(i&&this.userList.length>0)&&this.mindMap.emit("node_dblclick",this,t))}),this.group.on("contextmenu",t=>{const{readonly:e,useLeftKeySelectionRightKeyDrag:i}=this.mindMap.opt;e||t.ctrlKey||(t.stopPropagation(),t.preventDefault(),!(this.mindMap.select&&!i&&this.mindMap.select.hasSelectRange())&&(this.getData("isActive")&&this.renderer.activeNodeList.length===1||(this.renderer.clearActiveNodeList(),this.active(t)),this.mindMap.emit("node_contextmenu",t,this)))})}active(t){this.mindMap.opt.readonly||(t&&t.stopPropagation(),!this.getData("isActive")&&(this.mindMap.emit("before_node_active",this,this.renderer.activeNodeList),this.renderer.clearActiveNodeList(),this.renderer.addNodeToActiveList(this,!0),this.renderer.emitNodeActiveEvent(this)))}deactivate(){this.mindMap.renderer.removeNodeFromActiveList(this),this.mindMap.renderer.emitNodeActiveEvent()}update(t){if(!this.group)return;this.updateNodeActiveClass();const{alwaysShowExpandBtn:e,notShowExpandBtn:i,isShowCreateChildBtnIcon:n,readonly:r}=this.mindMap.opt,o=this.getChildrenLength();if(!i)if(e)this._expandBtn&&o<=0?this.removeExpandBtn():this.renderExpandBtn();else{const{isActive:l,expand:h}=this.getData();o<=0?this.removeExpandBtn():h&&!l&&!this._isMouseenter?this.hideExpandBtn():this.showExpandBtn()}if(n)if(o>0)this.removeQuickCreateChildBtn();else{const{isActive:l}=this.getData();l?this.showQuickCreateChildBtn():this.hideQuickCreateChildBtn()}this.updateDragHandle(),this.renderGeneralization(t),this.updateUserListNode&&this.updateUserListNode();const a=this.group.transform();this.nodeDataSnapshot=r?"":JSON.stringify(this.getData()),(this.left!==a.translateX||this.top!==a.translateY)&&this.group.translate(this.left-a.translateX,this.top-a.translateY)}getNodePosInClient(t,e){const i=this.mindMap.draw.transform(),{scaleX:n,scaleY:r,translateX:o,translateY:a}=i,l=t*n+o,h=e*r+a;return{left:l,top:h}}checkIsInClient(t=0){const{left:e,top:i}=this.getNodePosInClient(this.left,this.top);return e+this.width>0-t&&i+this.height>0-t&&e<this.mindMap.width+t&&i<this.mindMap.height+t}reRender(t,e){const i=this.getSize(t,e);return this.layout(),this.update(),i}updateNodeActiveClass(){if(!this.group)return;const t=this.getData("isActive");this.group[t?"addClass":"removeClass"]("active")}updateNodeByActive(t){if(this.group){const{isShowCreateChildBtnIcon:e}=this.mindMap.opt;t?(this.showExpandBtn(),e&&this.showQuickCreateChildBtn()):(this.hideExpandBtn(),e&&this.hideQuickCreateChildBtn()),this.updateNodeActiveClass(),this.updateDragHandle()}}render(t=()=>{},e=!1,i=!1){this.renderLine();const{openPerformance:n,performanceConfig:r}=this.mindMap.opt;if(e||!n||this.checkIsInClient(r.padding)||this.isRoot?this.group?(this.nodeDraw.has(this.group)||this.nodeDraw.add(this.group),this.needLayout&&(this.needLayout=!1,this.layout()),this.updateExpandBtnPlaceholderRect(),this.update(e)):(this.group=new Bt,this.group.addClass("smm-node"),this.group.css({cursor:"default"}),this.bindGroupEvent(),this.nodeDraw.add(this.group),this.layout(),this.update(e)):n&&r.removeNodeWhenOutCanvas&&this.removeSelf(),this.children&&this.children.length&&this.getData("expand")!==!1){let o=0;this.children.forEach(a=>{const l=()=>{a.render(()=>{o++,o>=this.children.length&&t()},e,i)};i?setTimeout(l,0):l()})}else t();this.nodeData.inserting&&(delete this.nodeData.inserting,this.active(),this.mindMap.emit("node_dblclick",this,null,!0))}removeSelf(){this.group&&(this.group.remove(),this.removeGeneralization())}remove(){this.group&&(this.group.remove(),this.removeGeneralization(),this.removeLine(),this.children&&this.children.length&&this.children.forEach(t=>{t.remove()}))}destroy(){this.removeLine(),this.parent&&this.parent.removeLine(),this.group&&(this.emptyUser&&this.emptyUser(),this.resetWhenDelete(),this.group.remove(),this.removeGeneralization(),this.group=null,this.style.onRemove())}hide(){if(this.group&&this.group.hide(),this.hideGeneralization(),this.parent){const t=this.parent.children.indexOf(this);this.parent._lines[t]&&this.parent._lines[t].hide(),this._lines.forEach(e=>{e.hide()})}this.children&&this.children.length&&this.children.forEach(t=>{t.hide()})}show(){if(this.group){if(this.group.show(),this.showGeneralization(),this.parent){const t=this.parent.children.indexOf(this);this.parent._lines[t]&&this.parent._lines[t].show(),this._lines.forEach(e=>{e.show()})}this.children&&this.children.length&&this.children.forEach(t=>{t.show()})}}setOpacity(t){this.group&&this.group.opacity(t),this._lines.forEach(e=>{e.opacity(t)}),this.children.forEach(e=>{e.setOpacity(t)}),this.setGeneralizationOpacity(t)}hideChildren(){this._lines.forEach(t=>{t.hide()}),this.children&&this.children.length&&this.children.forEach(t=>{t.hide()})}showChildren(){this._lines.forEach(t=>{t.show()}),this.children&&this.children.length&&this.children.forEach(t=>{t.show()})}startDrag(){this.isDrag=!0,this.group&&this.group.addClass("smm-node-dragging")}endDrag(){this.isDrag=!1,this.group&&this.group.removeClass("smm-node-dragging")}renderLine(t=!1){if(this.getData("expand")===!1)return;let e=this.getChildrenLength();this.mindMap.renderer.layout.nodeIsRemoveAllLines&&this.mindMap.renderer.layout.nodeIsRemoveAllLines(this)&&(e=0),e>this._lines.length?new Array(e-this._lines.length).fill(0).forEach(()=>{this._lines.push(this.lineDraw.path())}):e<this._lines.length&&(this._lines.slice(e).forEach(i=>{i.remove()}),this._lines=this._lines.slice(0,e)),this.renderer.layout.renderLine(this,this._lines,(...i)=>{this.styleLine(...i)},this.style.getStyle("lineStyle",!0)),t&&this.children&&this.children.length>0&&this.children.forEach(i=>{i.renderLine(t)})}getShape(){return this.mindMap.themeConfig.nodeUseLineStyle?M.SHAPE.RECTANGLE:this.style.getStyle("shape",!1,!1)}hasCustomPosition(){return this.customLeft!==void 0&&this.customTop!==void 0}ancestorHasCustomPosition(){let t=this;for(;t;){if(t.hasCustomPosition())return!0;t=t.parent}return!1}ancestorHasGeneralization(){let t=this.parent;for(;t;){if(t.checkHasGeneralization())return!0;t=t.parent}return!1}addChildren(t){this.children.push(t)}styleLine(t,e,i){const{enableInheritAncestorLineStyle:n}=this.mindMap.opt,r=n?"getSelfInhertStyle":"getSelfStyle",o=e[r]("lineWidth")||e.getStyle("lineWidth",!0),a=e[r]("lineColor")||this.getRainbowLineColor(e)||e.getStyle("lineColor",!0),l=e[r]("lineDasharray")||e.getStyle("lineDasharray",!0);this.style.line(t,{width:o,color:a,dasharray:l},i,e)}getRainbowLineColor(t){return this.mindMap.rainbowLines?this.mindMap.rainbowLines.getNodeColor(t):""}removeLine(){this._lines.forEach(t=>{t.remove()}),this._lines=[]}isAncestor(t){if(this.uid===t.uid)return!1;let e=t.parent;for(;e;){if(this.uid===e.uid)return!0;e=e.parent}return!1}isParent(t){if(this.uid===t.uid)return!1;const e=t.parent;return!!(e&&this.uid===e.uid)}isBrother(t){return!this.parent||this.uid===t.uid?!1:this.parent.children.find(e=>e.uid===t.uid)}getIndexInBrothers(){return this.parent&&this.parent.children?this.parent.children.findIndex(t=>t.uid===this.uid):-1}getPaddingVale(){return{paddingX:this.getStyle("paddingX"),paddingY:this.getStyle("paddingY")}}getStyle(t,e){const i=this.style.merge(t,e);return i===void 0?"":i}getSelfStyle(t){return this.style.getSelfStyle(t)}getParentSelfStyle(t){return this.parent?this.parent.getSelfStyle(t)||this.parent.getParentSelfStyle(t):null}getSelfInhertStyle(t){return this.getSelfStyle(t)||this.getParentSelfStyle(t)}getBorderWidth(){return this.style.merge("borderWidth",!1)||0}getData(t){return t?this.nodeData.data[t]:this.nodeData.data}getPureData(t=!0,e=!1){return ci({},this,t,e)}getAncestorNodes(){const t=[];let e=this.parent;for(;e;)t.unshift(e),e=e.parent;return t}hasCustomStyle(){return this.style.hasCustomStyle()}getRect(){return this.group?this.group.rbox():null}getRectInSvg(){const{scaleX:t,scaleY:e,translateX:i,translateY:n}=this.mindMap.draw.transform();let{left:r,top:o,width:a,height:l}=this;const h=(r+a)*t+i,d=(o+l)*e+n;return r=r*t+i,o=o*e+n,{left:r,right:h,top:o,bottom:d,width:a*t,height:l*e}}highlight(){this.group&&this.group.addClass("smm-node-highlight")}closeHighlight(){this.group&&this.group.removeClass("smm-node-highlight")}fakeClone(){const t=new Yi({...this.opt,uid:tt()});return Object.keys(this).forEach(e=>{t[e]=this[e]}),t}createSvgTextNode(t=""){return new Ot().text(t)}getSvgObjects(){return{SVG:Tt,G:Bt,Rect:Jt}}checkEnableDragModifyNodeWidth(){const{enableDragModifyNodeWidth:t,isUseCustomNodeContent:e,customCreateNodeContent:i}=this.mindMap.opt;return t&&(this.mindMap.richText||e&&i)}hasCustomWidth(){return this.checkEnableDragModifyNodeWidth()&&this.customTextWidth!==void 0}getChildrenLength(){return this.nodeData.children?this.nodeData.children.length:0}}class Kd{constructor(t){this.max=t||1e3,this.size=0,this.pool=new Map}add(t,e){return!this.has(t)&&this.size>=this.max?!1:(this.delete(t),this.pool.set(t,e),this.size++,!0)}delete(t){this.pool.has(t)&&(this.pool.delete(t),this.size--)}has(t){return this.pool.has(t)}get(t){if(this.pool.has(t))return this.pool.get(t)}clear(){this.size=0,this.pool=new Map}}class Fe{constructor(t){this.renderer=t,this.mindMap=t.mindMap,this.draw=this.mindMap.draw,this.lineDraw=this.mindMap.lineDraw,this.root=null,this.lru=new Kd(this.mindMap.opt.maxNodeCacheCount),this.rootNodeCenterOffset=null}doLayout(){throw new Error("【computed】方法为必要方法，需要子类进行重写！")}renderLine(){throw new Error("【renderLine】方法为必要方法，需要子类进行重写！")}renderExpandBtn(){throw new Error("【renderExpandBtn】方法为必要方法，需要子类进行重写！")}renderGeneralization(){}cacheNode(t,e){this.renderer.nodeCache[t]=e,this.lru.add(t,e)}checkIsNeedResizeSources(){return this.renderer.checkHasRenderSource(M.CHANGE_THEME)}checkIsLayerTypeChange(t,e){if(t>=2&&e>=2)return!1;if(t>=2&&e<2||t<2&&e>=2)return!0}checkIsLayoutChangeRerenderExpandBtnPlaceholderRect(t){this.renderer.checkHasRenderSource(M.CHANGE_LAYOUT)&&(t.needRerenderExpandBtnPlaceholderRect=!0)}checkIsNodeDataChange(t,e){if(t)t=typeof t=="string"?JSON.parse(t):t,t.isActive=e.isActive,t.expand=e.expand,t=JSON.stringify(t);else return!1;return t!==JSON.stringify(e)}checkNodeFixChange(t,e,i){let n=!1;this.mindMap.nodeInnerPrefixList.forEach(o=>{if(o.updateNodeData){const a=o.updateNodeData(t,e);a&&(n=a)}});let r=!1;return this.mindMap.nodeInnerPostfixList.forEach(o=>{if(o.updateNodeData){const a=o.updateNodeData(t,i);a&&(r=a)}}),n||r}createNode(t,e,i,n,r,o){const a={};this.mindMap.nodeInnerPrefixList.forEach(c=>{if(c.createNodeData){const[f,g]=c.createNodeData({data:t,parent:e,ancestors:o,layerIndex:n,index:r});a[f]=g}});const l={};this.mindMap.nodeInnerPostfixList.forEach(c=>{if(c.createNodeData){const[f,g]=c.createNodeData({data:t,parent:e,ancestors:o,layerIndex:n,index:r});l[f]=g}});const h=t.data.uid;let d=null;if(t&&t._node&&!this.renderer.reRender){d=t._node;const c=this.checkIsLayerTypeChange(d.layerIndex,n);d.reset(),d.layerIndex=n,i?d.isRoot=!0:d.parent=e._node,this.cacheNode(t._node.uid,d),this.checkIsLayoutChangeRerenderExpandBtnPlaceholderRect(d);const f=this.checkNodeFixChange(d,a,l),g=this.checkIsNeedResizeSources(),u=this.checkIsNodeDataChange(t._node.nodeDataSnapshot,t.data);(g||u||c||d.getData("resetRichText")||d.getData("needUpdate")||f)&&(d.getSize(),d.needLayout=!0),this.checkGetGeneralizationChange(d,g)}else if((this.lru.has(h)||this.renderer.lastNodeCache[h])&&!this.renderer.reRender){d=this.lru.get(h)||this.renderer.lastNodeCache[h];const c=JSON.stringify(d.getData()),f=this.checkIsLayerTypeChange(d.layerIndex,n);d.reset(),d.nodeData=d.handleData(t||{}),d.layerIndex=n,i?d.isRoot=!0:d.parent=e._node,this.cacheNode(h,d),this.checkIsLayoutChangeRerenderExpandBtnPlaceholderRect(d),t._node=d;const g=this.checkIsNeedResizeSources(),u=this.checkIsNodeDataChange(c,t.data),m=this.checkNodeFixChange(d,a,l);(g||u||f||d.getData("resetRichText")||d.getData("needUpdate")||m)&&(d.getSize(),d.needLayout=!0),this.checkGetGeneralizationChange(d,g)}else{const c=h||tt();d=new Yi({data:t,uid:c,renderer:this.renderer,mindMap:this.mindMap,draw:this.draw,layerIndex:n,isRoot:i,parent:i?null:e._node,...a}),t.data.uid=c,this.cacheNode(c,d),t._node=d}return t.data.isActive&&this.renderer.addNodeToActiveList(d),this.mindMap.renderer.findActiveNodeIndex(d)!==-1&&d.setData({isActive:!0}),i?this.root=d:e._node.addChildren(d),d}checkGetGeneralizationChange(t,e){const i=t.getData("generalization");i&&t._generalizationList&&t._generalizationList.length>0&&t._generalizationList.forEach((n,r)=>{const o=n.generalizationNode,a=o.getData(),l=i[r];(e||l&&JSON.stringify(a)!==JSON.stringify(l))&&(l&&(o.nodeData.data=l),o.getSize(),o.needLayout=!0)})}formatPosition(t,e,i){return typeof t=="number"?t:es[t]!==void 0?e*es[t]:/^\d\d*%$/.test(t)?Number.parseFloat(t)/100*e:(e-i)/2}formatInitRootNodePosition(t){const{CENTER:e}=M.INIT_ROOT_NODE_POSITION;return(!t||!Array.isArray(t)||t.length<2)&&(t=[e,e]),t}setNodeCenter(t,e){let{initRootNodePosition:i}=this.mindMap.opt;i=this.formatInitRootNodePosition(e||i),t.left=this.formatPosition(i[0],this.mindMap.width,t.width),t.top=this.formatPosition(i[1],this.mindMap.height,t.height)}getRootCenterOffset(t,e){if(this.rootNodeCenterOffset)return this.rootNodeCenterOffset;let{initRootNodePosition:i}=this.mindMap.opt;const{CENTER:n}=M.INIT_ROOT_NODE_POSITION;if(i=this.formatInitRootNodePosition(i),i[0]===n&&i[1]===n)this.rootNodeCenterOffset={x:0,y:0};else{const r={width:t,height:e},o={width:t,height:e};this.setNodeCenter(r,[n,n]),this.setNodeCenter(o),this.rootNodeCenterOffset={x:o.left-r.left,y:o.top-r.top}}return this.rootNodeCenterOffset}updateChildren(t,e,i){t.forEach(n=>{n[e]+=i,n.children&&n.children.length&&!n.hasCustomPosition()&&this.updateChildren(n.children,e,i)})}updateChildrenPro(t,e){t.forEach(i=>{Object.keys(e).forEach(n=>{i[n]+=e[n]}),i.children&&i.children.length&&!i.hasCustomPosition()&&this.updateChildrenPro(i.children,e)})}getNodeAreaWidth(t,e=!1){let i=[],n=0,r=(o,a)=>{e&&o.checkHasGeneralization()&&(n+=o._generalizationNodeWidth),o.children.length?(a+=o.width/2,o.children.forEach(l=>{r(l,a)})):(a+=o.width,i.push(a))};return r(t,0),Math.max(...i)+n}quadraticCurvePath(t,e,i,n,r=!1){let o,a;return r?(o=t+(i-t)*.8,a=e+(n-e)*.2):(o=t+(i-t)*.2,a=e+(n-e)*.8),`M ${t},${e} Q ${o},${a} ${i},${n}`}cubicBezierPath(t,e,i,n,r=!1){let o,a,l,h;return r?(o=t,a=e+(n-e)/2,l=i,h=a):(o=t+(i-t)/2,a=e,l=o,h=n),`M ${t},${e} C ${o},${a} ${l},${h} ${i},${n}`}computeNewPoint(t,e,i=0){if(t[0]===e[0])return e[1]>t[1]?[e[0],e[1]-i]:[e[0],e[1]+i];if(t[1]===e[1])return e[0]>t[0]?[e[0]-i,e[1]]:[e[0]+i,e[1]]}createFoldLine(t){const{lineRadius:e}=this.mindMap.themeConfig,i=t.length;let n="",r="";if(i>=3&&e>0){const o=t[i-3],a=t[i-2],l=t[i-1];if(!(o[0].toFixed(0)===a[0].toFixed(0)&&a[0].toFixed(0)===l[0].toFixed(0)||o[1].toFixed(0)===a[1].toFixed(0)&&a[1].toFixed(0)===l[1].toFixed(0))){const d=this.computeNewPoint(o,a,e),c=this.computeNewPoint(l,a,e);r=`Q ${a[0]},${a[1]} ${c[0]},${c[1]}`,t.splice(i-2,1,d,r)}}return t.forEach((o,a)=>{if(typeof o=="string")n+=o;else{const[l,h]=o;a===0?n+=`M ${l},${h}`:n+=`L ${l},${h}`}}),n}getMarginX(t){const{themeConfig:e,opt:i}=this.mindMap,{second:n,node:r}=e,o=i.hoverRectPadding*2;return t===1?n.marginX+o:r.marginX+o}getMarginY(t){const{themeConfig:e,opt:i}=this.mindMap,{second:n,node:r}=e,o=i.hoverRectPadding*2;return t===1?n.marginY+o:r.marginY+o}getNodeWidthWithGeneralization(t){return Math.max(t.width,t.checkHasGeneralization()?t._generalizationNodeWidth:0)}getNodeHeightWithGeneralization(t){return Math.max(t.height,t.checkHasGeneralization()?t._generalizationNodeHeight:0)}getNodeBoundaries(t,e){let{generalizationLineMargin:i,generalizationNodeMargin:n}=this.mindMap.themeConfig,r=d=>{let c=1/0,f=-1/0,g=1/0,u=-1/0;d.children&&d.children.length>0&&d.children.forEach(p=>{let{left:x,right:E,top:_,bottom:T}=r(p),C=p.checkHasGeneralization()&&p.getData("expand")?p._generalizationNodeWidth+n:0,L=p.checkHasGeneralization()&&p.getData("expand")?p._generalizationNodeHeight+n:0;x-(e==="h"?C:0)<c&&(c=x-(e==="h"?C:0)),E+(e==="h"?C:0)>f&&(f=E+(e==="h"?C:0)),_<g&&(g=_),T+(e==="v"?L:0)>u&&(u=T+(e==="v"?L:0))});let m={left:d.left,right:d.left+d.width,top:d.top,bottom:d.top+d.height};return{left:m.left<c?m.left:c,right:m.right>f?m.right:f,top:m.top<g?m.top:g,bottom:m.bottom>u?m.bottom:u}},{left:o,right:a,top:l,bottom:h}=r(t);return{left:o,right:a,top:l,bottom:h,generalizationLineMargin:i,generalizationNodeMargin:n}}getChildrenBoundaries(t,e,i=0,n){let{generalizationLineMargin:r,generalizationNodeMargin:o}=this.mindMap.themeConfig;const a=t.children.slice(i,n+1);let l=1/0,h=-1/0,d=1/0,c=-1/0;return a.forEach(f=>{const g=this.getNodeBoundaries(f,e);l=g.left<l?g.left:l,h=g.right>h?g.right:h,d=g.top<d?g.top:d,c=g.bottom>c?g.bottom:c}),{left:l,right:h,top:d,bottom:c,generalizationLineMargin:r,generalizationNodeMargin:o}}getNodeGeneralizationRenderBoundaries(t,e){let i=null;return t.range?i=this.getChildrenBoundaries(t.node,e,t.range[0],t.range[1]):i=this.getNodeBoundaries(t.node,e),i}getNodeActChildrenLength(t){return t.nodeData.children&&t.nodeData.children.length}setLineStyle(t,e,i,n){e.plot(this.transformPath(i)),t&&t(e,n,!0)}transformPath(t){const{customTransformNodeLinePath:e}=this.mindMap.opt;return e?e(t):t}}class Ls extends Fe{constructor(t={},e){super(t),this.isUseLeft=e===M.LAYOUT.LOGICAL_STRUCTURE_LEFT}doLayout(t){ze([()=>{this.computedBaseValue()},()=>{this.computedTopValue()},()=>{this.adjustTopValue()},()=>{t(this.root)}])}computedBaseValue(){let t=0;at(this.renderer.renderTree,null,(e,i,n,r,o,a)=>{let l=this.createNode(e,i,n,r,o,a);if(l.sortIndex=t,t++,n?this.setNodeCenter(l):this.isUseLeft?l.left=i._node.left-l.width-this.getMarginX(r):l.left=i._node.left+i._node.width+this.getMarginX(r),!e.data.expand)return!0},(e,i,n,r)=>{let o=e.data.expand===!1?0:e._node.children.length;e._node.childrenAreaHeight=o?e._node.children.reduce((l,h)=>l+h.height,0)+(o+1)*this.getMarginY(r+1):0;let a=e._node.checkHasGeneralization()?e._node._generalizationNodeHeight+this.getMarginY(r+1):0;e._node.childrenAreaHeight2=Math.max(e._node.childrenAreaHeight,a)},!0,0)}computedTopValue(){at(this.root,null,(t,e,i,n)=>{if(t.getData("expand")&&t.children&&t.children.length){let r=this.getMarginY(n+1),a=t.top+t.height/2-t.childrenAreaHeight/2+r;t.children.forEach(l=>{l.top=a,a+=l.height+r})}},null,!0)}adjustTopValue(){at(this.root,null,(t,e,i,n)=>{if(!t.getData("expand"))return;let r=t.childrenAreaHeight2-this.getMarginY(n+1)*2-t.height;r>0&&this.updateBrothers(t,r/2)},null,!0)}updateBrothers(t,e){if(t.parent){let i=t.parent.children,n=Pt(t,i);i.forEach((r,o)=>{if(r.uid===t.uid||r.hasCustomPosition())return;let a=0;o<n?a=-e:o>n&&(a=e),r.top+=a,r.children&&r.children.length&&this.updateChildren(r.children,"top",a)}),this.updateBrothers(t.parent,e)}}renderLine(t,e,i,n){n==="curve"?this.renderLineCurve(t,e,i):n==="direct"?this.renderLineDirect(t,e,i):this.renderLineStraight(t,e,i)}renderLineStraight(t,e,i){if(t.children.length<=0)return[];let{left:n,top:r,width:o,height:a,expandBtnSize:l}=t;const{alwaysShowExpandBtn:h,notShowExpandBtn:d}=this.mindMap.opt;(!h||d)&&(l=0);let f=(this.getMarginX(t.layerIndex+1)-l)*.6;this.isUseLeft&&(f*=-1);let g=this.mindMap.themeConfig.nodeUseLineStyle;t.children.forEach((u,m)=>{let p;this.isUseLeft?p=t.layerIndex===0?n:n-l:p=t.layerIndex===0?n+o:n+o+l;let x=r+a/2,E=this.isUseLeft?u.left+u.width:u.left,_=u.top+u.height/2,T=g?u.width*(this.isUseLeft?-1:1):0;x=g&&!t.isRoot?x+a/2:x,_=g?_+u.height/2:_;let C=this.createFoldLine([[p,x],[p+f,x],[p+f,_],[E+T,_]]);this.setLineStyle(i,e[m],C,u)})}renderLineDirect(t,e,i){if(t.children.length<=0)return[];let{left:n,top:r,width:o,height:a,expandBtnSize:l}=t;const{alwaysShowExpandBtn:h,notShowExpandBtn:d}=this.mindMap.opt;(!h||d)&&(l=0);const{nodeUseLineStyle:c}=this.mindMap.themeConfig;t.children.forEach((f,g)=>{t.layerIndex===0&&(l=0);let u=this.isUseLeft?n-l:n+o+l,m=r+a/2,p=this.isUseLeft?f.left+f.width:f.left,x=f.top+f.height/2;m=c&&!t.isRoot?m+a/2:m,x=c?x+f.height/2:x;let E=c?` L ${this.isUseLeft?f.left:f.left+f.width},${x}`:"",_=`M ${u},${m} L ${p},${x}`+E;this.setLineStyle(i,e[g],_,f)})}renderLineCurve(t,e,i){if(t.children.length<=0)return[];let{left:n,top:r,width:o,height:a,expandBtnSize:l}=t;const{alwaysShowExpandBtn:h,notShowExpandBtn:d}=this.mindMap.opt;(!h||d)&&(l=0);const{nodeUseLineStyle:c,rootLineStartPositionKeepSameInCurve:f,rootLineKeepSameInCurve:g}=this.mindMap.themeConfig;t.children.forEach((u,m)=>{t.layerIndex===0&&(l=0);let p;this.isUseLeft?p=t.layerIndex===0&&!f?n+o/2:n-l:p=t.layerIndex===0&&!f?n+o/2:n+o+l;let x=r+a/2,E=this.isUseLeft?u.left+u.width:u.left,_=u.top+u.height/2,T="";x=c&&!t.isRoot?x+a/2:x,_=c?_+u.height/2:_;let C;this.isUseLeft?C=c?` L ${u.left},${_}`:"":C=c?` L ${u.left+u.width},${_}`:"",t.isRoot&&!g?T=this.quadraticCurvePath(p,x,E,_)+C:T=this.cubicBezierPath(p,x,E,_)+C,this.setLineStyle(i,e[m],T,u)})}renderExpandBtn(t,e){let{width:i,height:n,expandBtnSize:r,layerIndex:o}=t;o===0&&(r=0);let{translateX:a,translateY:l}=e.transform(),h=this.mindMap.themeConfig.nodeUseLineStyle?n/2:0,d=this.isUseLeft?0-r:i,c=n/2+h;d===a&&c===l||e.translate(d-a,c-l)}renderGeneralization(t){t.forEach(e=>{let{left:i,top:n,bottom:r,right:o,generalizationLineMargin:a,generalizationNodeMargin:l}=this.getNodeGeneralizationRenderBoundaries(e,"h"),h=this.isUseLeft?i-a:o+a,d=h,c=n,f=h,g=r,u=d+(this.isUseLeft?-20:20),m=c+(g-c)/2,p=`M ${d},${c} Q ${u},${m} ${f},${g}`;e.generalizationLine.plot(p),e.generalizationNode.left=h+(this.isUseLeft?-l:l)-(this.isUseLeft?e.generalizationNode.width:0),e.generalizationNode.top=n+(r-n-e.generalizationNode.height)/2})}renderExpandBtnRect(t,e,i,n){this.isUseLeft?t.size(e,n).x(-e).y(0):t.size(e,n).x(i).y(0)}}let Zd=class extends Fe{constructor(t={}){super(t)}doLayout(t){ze([()=>{this.computedBaseValue()},()=>{this.computedTopValue()},()=>{this.adjustTopValue()},()=>{t(this.root)}])}computedBaseValue(){at(this.renderer.renderTree,null,(t,e,i,n,r,o)=>{let a=this.createNode(t,e,i,n,r,o);if(i?this.setNodeCenter(a):(e._node.dir?a.dir=e._node.dir:a.dir=a.getData("dir")||(r%2===0?M.LAYOUT_GROW_DIR.RIGHT:M.LAYOUT_GROW_DIR.LEFT),a.left=a.dir===M.LAYOUT_GROW_DIR.RIGHT?e._node.left+e._node.width+this.getMarginX(n):e._node.left-this.getMarginX(n)-a.width),!t.data.expand)return!0},(t,e,i,n)=>{if(!t.data.expand){t._node.leftChildrenAreaHeight=0,t._node.rightChildrenAreaHeight=0;return}let r=0,o=0,a=0,l=0;t._node.children.forEach(d=>{d.dir===M.LAYOUT_GROW_DIR.LEFT?(r++,a+=d.height):(o++,l+=d.height)}),t._node.leftChildrenAreaHeight=a+(r+1)*this.getMarginY(n+1),t._node.rightChildrenAreaHeight=l+(o+1)*this.getMarginY(n+1);let h=t._node.checkHasGeneralization()?t._node._generalizationNodeHeight+this.getMarginY(n+1):0;t._node.leftChildrenAreaHeight2=Math.max(t._node.leftChildrenAreaHeight,h),t._node.rightChildrenAreaHeight2=Math.max(t._node.rightChildrenAreaHeight,h)},!0,0)}computedTopValue(){at(this.root,null,(t,e,i,n)=>{if(t.getData("expand")&&t.children&&t.children.length){let r=this.getMarginY(n+1),o=t.top+t.height/2+r,a=o-t.leftChildrenAreaHeight/2,l=o-t.rightChildrenAreaHeight/2;t.children.forEach(h=>{h.dir===M.LAYOUT_GROW_DIR.LEFT?(h.top=a,a+=h.height+r):(h.top=l,l+=h.height+r)})}},null,!0)}adjustTopValue(){at(this.root,null,(t,e,i,n)=>{if(!t.getData("expand"))return;let r=this.getMarginY(n+1)*2+t.height,o=t.leftChildrenAreaHeight2-r,a=t.rightChildrenAreaHeight2-r;(o>0||a>0)&&this.updateBrothers(t,o/2,a/2)},null,!0)}updateBrothers(t,e,i){if(t.parent){let n=t.parent.children.filter(o=>o.dir===t.dir),r=Pt(t,n);n.forEach((o,a)=>{if(o.hasCustomPosition())return;let l=0,h=o.dir===M.LAYOUT_GROW_DIR.LEFT?e:i;a<r?l=-h:a>r&&(l=h),o.top+=l,o.children&&o.children.length&&this.updateChildren(o.children,"top",l)}),this.updateBrothers(t.parent,e,i)}}renderLine(t,e,i,n){n==="curve"?this.renderLineCurve(t,e,i):n==="direct"?this.renderLineDirect(t,e,i):this.renderLineStraight(t,e,i)}renderLineStraight(t,e,i){if(t.children.length<=0)return[];let{left:n,top:r,width:o,height:a,expandBtnSize:l}=t;const{alwaysShowExpandBtn:h,notShowExpandBtn:d}=this.mindMap.opt;(!h||d)&&(l=0);let f=(this.getMarginX(t.layerIndex+1)-l)*.6,g=this.mindMap.themeConfig.nodeUseLineStyle;t.children.forEach((u,m)=>{let p=0,x=0,E=g?u.width:0;u.dir===M.LAYOUT_GROW_DIR.LEFT?(x=-f,p=t.layerIndex===0?n:n-l,E=-E):(x=f,p=t.layerIndex===0?n+o:n+o+l);let _=r+a/2,T=u.dir===M.LAYOUT_GROW_DIR.LEFT?u.left+u.width:u.left,C=u.top+u.height/2;_=g&&!t.isRoot?_+a/2:_,C=g?C+u.height/2:C;let L=this.createFoldLine([[p,_],[p+x,_],[p+x,C],[T+E,C]]);this.setLineStyle(i,e[m],L,u)})}renderLineDirect(t,e,i){if(t.children.length<=0)return[];let{left:n,top:r,width:o,height:a,expandBtnSize:l}=t;const{alwaysShowExpandBtn:h,notShowExpandBtn:d}=this.mindMap.opt;(!h||d)&&(l=0);const{nodeUseLineStyle:c}=this.mindMap.themeConfig;t.children.forEach((f,g)=>{t.layerIndex===0&&(l=0);let u=f.dir===M.LAYOUT_GROW_DIR.LEFT?n-l:n+o+l,m=r+a/2,p=f.dir===M.LAYOUT_GROW_DIR.LEFT?f.left+f.width:f.left,x=f.top+f.height/2;m=c&&!t.isRoot?m+a/2:m,x=c?x+f.height/2:x;let E="";c&&(f.dir===M.LAYOUT_GROW_DIR.LEFT?E=` L ${f.left},${x}`:E=` L ${f.left+f.width},${x}`);let _=`M ${u},${m} L ${p},${x}`+E;this.setLineStyle(i,e[g],_,f)})}renderLineCurve(t,e,i){if(t.children.length<=0)return[];let{left:n,top:r,width:o,height:a,expandBtnSize:l}=t;const{alwaysShowExpandBtn:h,notShowExpandBtn:d}=this.mindMap.opt;(!h||d)&&(l=0);const{nodeUseLineStyle:c,rootLineKeepSameInCurve:f,rootLineStartPositionKeepSameInCurve:g}=this.mindMap.themeConfig;t.children.forEach((u,m)=>{t.layerIndex===0&&(l=0);let p=t.layerIndex===0&&!g?n+o/2:u.dir===M.LAYOUT_GROW_DIR.LEFT?n-l:n+o+l,x=r+a/2,E=u.dir===M.LAYOUT_GROW_DIR.LEFT?u.left+u.width:u.left,_=u.top+u.height/2,T="";x=c&&!t.isRoot?x+a/2:x,_=c?_+u.height/2:_;let C="";c&&(u.dir===M.LAYOUT_GROW_DIR.LEFT?C=` L ${u.left},${_}`:C=` L ${u.left+u.width},${_}`),t.isRoot&&!f?T=this.quadraticCurvePath(p,x,E,_)+C:T=this.cubicBezierPath(p,x,E,_)+C,this.setLineStyle(i,e[m],T,u)})}renderExpandBtn(t,e){let{width:i,height:n,expandBtnSize:r}=t,{translateX:o,translateY:a}=e.transform(),l=this.mindMap.themeConfig.nodeUseLineStyle?n/2:0,h=t.dir===M.LAYOUT_GROW_DIR.LEFT?0-r:i,d=n/2+l;if(h===o&&d===a)return;let c=h-o,f=d-a;e.translate(c,f)}renderGeneralization(t){t.forEach(e=>{let i=e.node.dir===M.LAYOUT_GROW_DIR.LEFT,{top:n,bottom:r,left:o,right:a,generalizationLineMargin:l,generalizationNodeMargin:h}=this.getNodeGeneralizationRenderBoundaries(e,"h"),d=i?o-l:a+l,c=d,f=n,g=d,u=r,m=c+(i?-20:20),p=f+(u-f)/2,x=`M ${c},${f} Q ${m},${p} ${g},${u}`;e.generalizationLine.plot(x),e.generalizationNode.left=d+(i?-h:h)-(i?e.generalizationNode.width:0),e.generalizationNode.top=n+(r-n-e.generalizationNode.height)/2})}renderExpandBtnRect(t,e,i,n,r){r.dir===M.LAYOUT_GROW_DIR.LEFT?t.size(e,n).x(-e).y(0):t.size(e,n).x(i).y(0)}};class Jd extends Fe{constructor(t={}){super(t)}doLayout(t){ze([()=>{this.computedBaseValue()},()=>{this.computedLeftTopValue()},()=>{this.adjustLeftTopValue()},()=>{t(this.root)}])}computedBaseValue(){at(this.renderer.renderTree,null,(t,e,i,n,r,o)=>{let a=this.createNode(t,e,i,n,r,o);if(i?this.setNodeCenter(a):e._node.isRoot&&(a.top=e._node.top+e._node.height+this.getMarginX(n)),!t.data.expand)return!0},(t,e,i,n)=>{if(i){let r=t.data.expand===!1?0:t._node.children.length;t._node.childrenAreaWidth=r?t._node.children.reduce((o,a)=>o+a.width,0)+(r+1)*this.getMarginX(n+1):0}},!0,0)}computedLeftTopValue(){at(this.root,null,(t,e,i,n)=>{if(t.getData("expand")&&t.children&&t.children.length){let r=this.getMarginX(n+1),o=this.getMarginY(n+1);if(i){let l=t.left+t.width/2-t.childrenAreaWidth/2+r;t.children.forEach(h=>{h.left=l,l+=h.width+r})}else{let a=t.top+this.getNodeHeightWithGeneralization(t)+o+(this.getNodeActChildrenLength(t)>0?t.expandBtnSize:0);t.children.forEach(l=>{l.left=t.left+t.width*.5,l.top=a,a+=this.getNodeHeightWithGeneralization(l)+o+(this.getNodeActChildrenLength(l)>0?l.expandBtnSize:0)})}}},null,!0)}adjustLeftTopValue(){at(this.root,null,(t,e,i,n)=>{if(!t.getData("expand"))return;if(e&&e.isRoot){let a=this.getNodeAreaWidth(t,!0)-t.width;a>0&&this.updateBrothersLeft(t,a)}let r=t.children.length;if(e&&!e.isRoot&&r>0){let o=this.getMarginY(n+1),a=t.children.reduce((l,h)=>l+this.getNodeHeightWithGeneralization(h)+(this.getNodeActChildrenLength(h)>0?h.expandBtnSize:0),0)+r*o;this.updateBrothersTop(t,a)}},(t,e,i)=>{if(i){let{right:n,left:r}=this.getNodeBoundaries(t,"h"),o=n-r,a=t.left-r-(o-t.width)/2;this.updateChildren(t.children,"left",a)}},!0)}updateBrothersLeft(t,e){if(t.parent){let i=t.parent.children,n=Pt(t,i);i.forEach((r,o)=>{r.hasCustomPosition()||o<=n||(r.left+=e,r.children&&r.children.length&&this.updateChildren(r.children,"left",e))}),this.updateBrothersLeft(t.parent,e)}}updateBrothersTop(t,e){if(t.parent&&!t.parent.isRoot){let i=t.parent.children,n=Pt(t,i);i.forEach((r,o)=>{if(r.hasCustomPosition())return;let a=0;o>n&&(a=e),r.top+=a,r.children&&r.children.length&&this.updateChildren(r.children,"top",a)}),this.updateBrothersTop(t.parent,e)}}renderLine(t,e,i){if(t.children.length<=0)return[];let{left:n,top:r,width:o,height:a,expandBtnSize:l}=t;const{alwaysShowExpandBtn:h,notShowExpandBtn:d}=this.mindMap.opt;(!h||d)&&(l=0);let c=t.children.length,f=this.getMarginX(t.layerIndex+1);if(t.isRoot){let g=n+o/2,u=r+a,m=f*.7,p=1/0,x=-1/0;t.children.forEach((_,T)=>{let C=_.left+_.width/2,L=_.top;C<p&&(p=C),C>x&&(x=C);let F=this.mindMap.themeConfig.nodeUseLineStyle?` L ${_.left},${L} L ${_.left+_.width},${L}`:"",W=`M ${C},${u+m} L ${C},${u+m>L?L+_.height:L}`+F;this.setLineStyle(i,e[T],W,_)}),p=Math.min(p,g),x=Math.max(x,g);let E=this.lineDraw.path();if(t.style.line(E),E.plot(this.transformPath(`M ${g},${u} L ${g},${u+m}`)),t._lines.push(E),i&&i(E,t),c>0){let _=this.lineDraw.path();t.style.line(_),_.plot(this.transformPath(`M ${p},${u+m} L ${x},${u+m}`)),t._lines.push(_),i&&i(_,t)}}else{let g=r+a,u=-1/0,m=t.left+t.width*.3;if(t.children.forEach((p,x)=>{let E=p.top+p.height/2;E>u&&(u=E);let _="",T=p.left,C=p.left+p.width<m,L=!1;C?T=p.left+p.width:p.left<m&&p.left+p.width>m&&(L=!0,E=p.top,u=E),E>r&&E<g?_=`M ${C?t.left:t.left+t.width},${E} L ${T},${E}`:E<g?(L&&(E=p.top+p.height,T=m),_=`M ${m},${r} L ${m},${E} L ${T},${E}`):(L&&(T=m),_=`M ${m},${E} L ${T},${E}`);let F=this.mindMap.themeConfig.nodeUseLineStyle?` L ${T},${E-p.height/2} L ${T},${E+p.height/2}`:"";_+=F,this.setLineStyle(i,e[x],_,p)}),c>0){let p=this.lineDraw.path();l=c>0?l:0,t.style.line(p),u<g+l?p.hide():(p.plot(this.transformPath(`M ${m},${g+l} L ${m},${u}`)),p.show()),t._lines.push(p),i&&i(p,t)}}}renderExpandBtn(t,e){let{width:i,height:n,expandBtnSize:r,isRoot:o}=t;if(!o){let{translateX:a,translateY:l}=e.transform();e.translate(i*.3-r/2-a,n+r/2-l)}}renderGeneralization(t){t.forEach(e=>{let{top:i,bottom:n,right:r,generalizationLineMargin:o,generalizationNodeMargin:a}=this.getNodeGeneralizationRenderBoundaries(e,"h"),l=r+o,h=i,d=r+o,c=n,f=l+20,g=h+(c-h)/2,u=`M ${l},${h} Q ${f},${g} ${d},${c}`;e.generalizationLine.plot(this.transformPath(u)),e.generalizationNode.left=r+a,e.generalizationNode.top=i+(n-i-e.generalizationNode.height)/2})}renderExpandBtnRect(t,e,i,n,r){t.size(i,e).x(0).y(n)}}class Qd extends Fe{constructor(t={}){super(t)}doLayout(t){ze([()=>{this.computedBaseValue()},()=>{this.computedLeftValue()},()=>{this.adjustLeftValue()},()=>{t(this.root)}])}computedBaseValue(){at(this.renderer.renderTree,null,(t,e,i,n,r,o)=>{let a=this.createNode(t,e,i,n,r,o);if(i?this.setNodeCenter(a):a.top=e._node.top+e._node.height+this.getMarginX(n),!t.data.expand)return!0},(t,e,i,n)=>{let r=t.data.expand===!1?0:t._node.children.length;t._node.childrenAreaWidth=r?t._node.children.reduce((a,l)=>a+l.width,0)+(r+1)*this.getMarginY(n+1):0;let o=t._node.checkHasGeneralization()?t._node._generalizationNodeWidth+this.getMarginY(n+1):0;t._node.childrenAreaWidth2=Math.max(t._node.childrenAreaWidth,o)},!0,0)}computedLeftValue(){at(this.root,null,(t,e,i,n)=>{if(t.getData("expand")&&t.children&&t.children.length){let r=this.getMarginY(n+1),a=t.left+t.width/2-t.childrenAreaWidth/2+r;t.children.forEach(l=>{l.left=a,a+=l.width+r})}},null,!0)}adjustLeftValue(){at(this.root,null,(t,e,i,n)=>{if(!t.getData("expand"))return;let r=t.childrenAreaWidth2-this.getMarginY(n+1)*2-t.width;r>0&&this.updateBrothers(t,r/2)},null,!0)}updateBrothers(t,e){if(t.parent){let i=t.parent.children,n=Pt(t,i);i.forEach((r,o)=>{if(r.hasCustomPosition())return;let a=0;o<n?a=-e:o>n&&(a=e),r.left+=a,r.children&&r.children.length&&this.updateChildren(r.children,"left",a)}),this.updateBrothers(t.parent,e)}}renderLine(t,e,i,n){n==="curve"?this.renderLineCurve(t,e,i):n==="direct"?this.renderLineDirect(t,e,i):this.renderLineStraight(t,e,i)}renderLineCurve(t,e,i){if(t.children.length<=0)return[];let{left:n,top:r,width:o,height:a,expandBtnSize:l}=t;const{alwaysShowExpandBtn:h,notShowExpandBtn:d}=this.mindMap.opt;(!h||d)&&(l=0);const{nodeUseLineStyle:c,rootLineStartPositionKeepSameInCurve:f,rootLineKeepSameInCurve:g}=this.mindMap.themeConfig;t.children.forEach((u,m)=>{t.layerIndex===0&&(l=0);let p=n+o/2,x=t.layerIndex===0&&!f?r+a/2:r+a+l,E=u.left+u.width/2,_=u.top,T="",C=c?` L ${u.left},${_} L ${u.left+u.width},${_}`:"";t.isRoot&&!g?T=this.quadraticCurvePath(p,x,E,_,!0)+C:T=this.cubicBezierPath(p,x,E,_,!0)+C,this.setLineStyle(i,e[m],T,u)})}renderLineDirect(t,e,i){if(t.children.length<=0)return[];let{left:n,top:r,width:o,height:a}=t;const{nodeUseLineStyle:l}=this.mindMap.themeConfig;let h=n+o/2,d=r+a;t.children.forEach((c,f)=>{let g=c.left+c.width/2,u=c.top,m=l?` L ${c.left},${u} L ${c.left+c.width},${u}`:"",p=`M ${h},${d} L ${g},${u}`+m;this.setLineStyle(i,e[f],p,c)})}renderLineStraight(t,e,i){if(t.children.length<=0)return[];let{left:n,top:r,width:o,height:a,expandBtnSize:l,isRoot:h}=t;const{alwaysShowExpandBtn:d,notShowExpandBtn:c}=this.mindMap.opt;(!d||c)&&(l=0);let f=n+o/2,g=r+a,m=this.getMarginX(t.layerIndex+1)*.7,p=1/0,x=-1/0,E=t.children.length;t.children.forEach((T,C)=>{let L=T.left+T.width/2,F=g+m>T.top?T.top+T.height:T.top;L<p&&(p=L),L>x&&(x=L);let W=this.mindMap.themeConfig.nodeUseLineStyle?` L ${T.left},${F} L ${T.left+T.width},${F}`:"",X=`M ${L},${g+m} L ${L},${F}`+W;this.setLineStyle(i,e[C],X,T)}),p=Math.min(f,p),x=Math.max(f,x);let _=this.lineDraw.path();if(t.style.line(_),l=E>0&&!h?l:0,_.plot(this.transformPath(`M ${f},${g+l} L ${f},${g+m}`)),t._lines.push(_),i&&i(_,t),E>0){let T=this.lineDraw.path();t.style.line(T),T.plot(this.transformPath(`M ${p},${g+m} L ${x},${g+m}`)),t._lines.push(T),i&&i(T,t)}}renderExpandBtn(t,e){let{width:i,height:n,expandBtnSize:r}=t,{translateX:o,translateY:a}=e.transform();e.translate(i/2-r/2-o,n+r/2-a)}renderGeneralization(t){t.forEach(e=>{let{bottom:i,left:n,right:r,generalizationLineMargin:o,generalizationNodeMargin:a}=this.getNodeGeneralizationRenderBoundaries(e,"v"),l=n,h=i+o,d=r,c=i+o,f=l+(d-l)/2,g=h+20,u=`M ${l},${h} Q ${f},${g} ${d},${c}`;e.generalizationLine.plot(this.transformPath(u)),e.generalizationNode.top=i+a,e.generalizationNode.left=n+(r-n-e.generalizationNode.width)/2})}renderExpandBtnRect(t,e,i,n,r){t.size(i,e).x(0).y(n)}}class bs extends Fe{constructor(t={},e){super(t),this.layout=e}doLayout(t){ze([()=>{this.computedBaseValue()},()=>{this.computedLeftTopValue()},()=>{this.adjustLeftTopValue()},()=>{t(this.root)}])}computedBaseValue(){at(this.renderer.renderTree,null,(t,e,i,n,r,o)=>{let a=this.createNode(t,e,i,n,r,o);if(i?this.setNodeCenter(a):(this.layout===M.LAYOUT.TIMELINE2?e._node.dir?a.dir=e._node.dir:a.dir=r%2===0?M.LAYOUT_GROW_DIR.BOTTOM:M.LAYOUT_GROW_DIR.TOP:a.dir="",e._node.isRoot&&(a.top=e._node.top+(t._node.height>e._node.height?-(t._node.height-e._node.height)/2:(e._node.height-t._node.height)/2))),!t.data.expand)return!0},null,!0,0)}computedLeftTopValue(){at(this.root,null,(t,e,i,n,r)=>{if(t.getData("expand")&&t.children&&t.children.length){let o=this.getMarginX(n+1),a=this.getMarginY(n+1);if(i){let h=t.left+t.width+o;t.children.forEach(d=>{d.left=h,h+=d.width+o})}else{let l=t.top+t.height+a+(this.getNodeActChildrenLength(t)>0?t.expandBtnSize:0);t.children.forEach(h=>{h.left=t.left+t.width*.5,h.top=l,l+=h.height+a+(this.getNodeActChildrenLength(h)>0?h.expandBtnSize:0)})}}},null,!0)}adjustLeftTopValue(){at(this.root,null,(t,e,i,n)=>{if(!t.getData("expand"))return;t.isRoot&&this.updateBrothersLeft(t);let r=t.children.length;if(e&&!e.isRoot&&r>0){let o=this.getMarginY(n+1),a=t.children.reduce((l,h)=>l+h.height+(this.getNodeActChildrenLength(h)>0?h.expandBtnSize:0),0)+r*o;this.updateBrothersTop(t,a)}},(t,e,i,n)=>{e&&e.isRoot&&t.dir===M.LAYOUT_GROW_DIR.TOP&&t.children.forEach(r=>{let o=this.getNodeAreaHeight(r),a=r.top;r.top=t.top-(r.top-t.top)-o+t.height,this.updateChildren(r.children,"top",r.top-a)})},!0)}getNodeAreaHeight(t){let e=0,i=n=>{e+=n.height+(this.getNodeActChildrenLength(n)>0?n.expandBtnSize:0)+this.getMarginY(n.layerIndex),n.children.length&&n.children.forEach(r=>{i(r)})};return i(t),e}updateBrothersLeft(t){let e=t.children,i=0;e.forEach(n=>{n.left+=i,n.children&&n.children.length&&this.updateChildren(n.children,"left",i);let{left:r,right:o}=this.getNodeBoundaries(n,"h"),l=o-r-n.width;l>0&&(i+=l)})}updateBrothersTop(t,e){if(t.parent&&!t.parent.isRoot){let i=t.parent.children,n=Pt(t,i);i.forEach((r,o)=>{if(r.hasCustomPosition())return;let a=0;o>n&&(a=e),r.top+=a,r.children&&r.children.length&&this.updateChildren(r.children,"top",a)}),this.updateBrothersTop(t.parent,e)}}renderLine(t,e,i){if(t.children.length<=0)return[];let{left:n,top:r,width:o,height:a,expandBtnSize:l}=t;const{alwaysShowExpandBtn:h,notShowExpandBtn:d}=this.mindMap.opt;(!h||d)&&(l=0);let c=t.children.length;if(t.isRoot){let f=t;t.children.forEach((g,u)=>{let m=f.left+f.width,p=g.left,x=t.top+t.height/2,E=`M ${m},${x} L ${p},${x}`;this.setLineStyle(i,e[u],E,g),f=g})}else{let f=-1/0,g=1/0,u=t.left+t.width*.3;if(t.children.forEach((m,p)=>{let x=m.top+m.height/2;x>f&&(f=x),x<g&&(g=x);let E=`M ${u},${x} L ${m.left},${x}`;this.setLineStyle(i,e[p],E,m)}),c>0){let m=this.lineDraw.path();l=c>0?l:0,t.parent&&t.parent.isRoot&&t.dir===M.LAYOUT_GROW_DIR.TOP?m.plot(this.transformPath(`M ${u},${r} L ${u},${g}`)):m.plot(this.transformPath(`M ${u},${r+a+l} L ${u},${f}`)),t.style.line(m),t._lines.push(m),i&&i(m,t)}}}renderExpandBtn(t,e){let{width:i,height:n,expandBtnSize:r,isRoot:o}=t;if(!o){let{translateX:a,translateY:l}=e.transform();t.parent&&t.parent.isRoot&&t.dir===M.LAYOUT_GROW_DIR.TOP?e.translate(i*.3-r/2-a,-r/2-l):e.translate(i*.3-r/2-a,n+r/2-l)}}renderGeneralization(t){t.forEach(e=>{let{top:i,bottom:n,right:r,generalizationLineMargin:o,generalizationNodeMargin:a}=this.getNodeGeneralizationRenderBoundaries(e,"h"),l=r+o,h=i,d=r+o,c=n,f=l+20,g=h+(c-h)/2,u=`M ${l},${h} Q ${f},${g} ${d},${c}`;e.generalizationLine.plot(this.transformPath(u)),e.generalizationNode.left=r+a,e.generalizationNode.top=i+(n-i-e.generalizationNode.height)/2})}renderExpandBtnRect(t,e,i,n,r){if(this.layout===M.LAYOUT.TIMELINE)t.size(i,e).x(0).y(n);else{let o="";r.dir===M.LAYOUT_GROW_DIR.TOP?o=r.layerIndex===1?M.LAYOUT_GROW_DIR.TOP:M.LAYOUT_GROW_DIR.BOTTOM:o=M.LAYOUT_GROW_DIR.BOTTOM,o===M.LAYOUT_GROW_DIR.TOP?t.size(i,e).x(0).y(-e):t.size(i,e).x(0).y(n)}}}class Ji extends Fe{constructor(t={},e){super(t),this.layout=e}doLayout(t){ze([()=>{this.computedBaseValue()},()=>{this.computedTopValue()},()=>{this.adjustLeftTopValue()},()=>{t(this.root)}])}computedBaseValue(){at(this.renderer.renderTree,null,(t,e,i,n,r,o)=>{let a=this.createNode(t,e,i,n,r,o);if(i?this.setNodeCenter(a):(e._node.dir?a.dir=e._node.dir:this.layout===M.LAYOUT.VERTICAL_TIMELINE2?a.dir=M.LAYOUT_GROW_DIR.LEFT:this.layout===M.LAYOUT.VERTICAL_TIMELINE3?a.dir=M.LAYOUT_GROW_DIR.RIGHT:a.dir=r%2===0?M.LAYOUT_GROW_DIR.RIGHT:M.LAYOUT_GROW_DIR.LEFT,e._node.isRoot?a.left=e._node.left+(t._node.width>e._node.width?-(t._node.width-e._node.width)/2:(e._node.width-t._node.width)/2):a.left=a.dir===M.LAYOUT_GROW_DIR.RIGHT?e._node.left+e._node.width+this.getMarginX(n):e._node.left-this.getMarginX(n)-a.width),!t.data.expand)return!0},(t,e,i,n)=>{if(i)return;let r=t.data.expand===!1?0:t._node.children.length;t._node.childrenAreaHeight=r?t._node.children.reduce((o,a)=>o+a.height,0)+(r+1)*this.getMarginY(n+1):0},!0,0)}computedTopValue(){at(this.root,null,(t,e,i,n,r)=>{if(t.getData("expand")&&t.children&&t.children.length){let o=this.getMarginY(n+1);if(i){let l=t.top+t.height+o;t.children.forEach(h=>{h.top=l,l+=h.height+o})}else{let a=this.getMarginY(n+1),h=t.top+t.height/2+a-t.childrenAreaHeight/2;t.children.forEach(d=>{d.top=h,h+=d.height+a})}}},null,!0)}adjustLeftTopValue(){at(this.root,null,(t,e,i,n)=>{if(!t.getData("expand")||i)return;let r=this.getMarginY(n+1)*2+t.height,o=t.childrenAreaHeight-r;o>0&&this.updateBrothers(t,o/2)},null,!0)}updateBrothers(t,e){if(t.parent){let i=t.parent.children,n=Pt(t,i);i.forEach((r,o)=>{if(r.hasCustomPosition()||!t.parent.isRoot&&r.uid===t.uid)return;let a=0;t.parent.isRoot?o<n?a=0:o>n?a=e*2:a=e:o<n?a=-e:o>n&&(a=e),r.top+=a,r.children&&r.children.length&&this.updateChildren(r.children,"top",a)}),this.updateBrothers(t.parent,e)}}updateBrothersTop(t,e){if(t.parent&&!t.parent.isRoot){let i=t.parent.children,n=Pt(t,i);i.forEach((r,o)=>{if(r.hasCustomPosition())return;let a=0;o>n&&(a=e),r.top+=a,r.children&&r.children.length&&this.updateChildren(r.children,"top",a)}),this.updateBrothersTop(t.parent,e)}}renderLine(t,e,i,n){n==="curve"?this.renderLineCurve(t,e,i):n==="direct"?this.renderLineDirect(t,e,i):this.renderLineStraight(t,e,i)}renderLineStraight(t,e,i){if(t.children.length<=0)return[];let{expandBtnSize:n}=t;const{alwaysShowExpandBtn:r,notShowExpandBtn:o}=this.mindMap.opt;if((!r||o)&&(n=0),t.isRoot){let a=t;t.children.forEach((l,h)=>{let d=a.top+a.height,c=l.top,f=t.left+t.width/2,g=`M ${f},${d} L ${f},${c}`;this.setLineStyle(i,e[h],g,l),a=l})}else if(t.dir===M.LAYOUT_GROW_DIR.RIGHT){let a=t.left+t.width,l=t.top+t.height/2,d=(this.getMarginX(t.layerIndex+1)-n)*.6;t.children.forEach((c,f)=>{let g=c.left,u=c.top+c.height/2,m=this.createFoldLine([[a,l],[a+d,l],[a+d,u],[g,u]]);this.setLineStyle(i,e[f],m,c)})}else{let a=t.left,l=t.top+t.height/2,d=(this.getMarginX(t.layerIndex+1)-n)*.6;t.children.forEach((c,f)=>{let g=c.left+c.width,u=c.top+c.height/2,m=this.createFoldLine([[a,l],[a-d,l],[a-d,u],[g,u]]);this.setLineStyle(i,e[f],m,c)})}}renderLineDirect(t,e,i){if(t.children.length<=0)return[];let{left:n,top:r,width:o,height:a,expandBtnSize:l}=t;const{alwaysShowExpandBtn:h,notShowExpandBtn:d}=this.mindMap.opt;(!h||d)&&(l=0),t.children.forEach((c,f)=>{if(t.isRoot){let g=t;t.children.forEach((u,m)=>{let p=g.top+g.height,x=u.top,E=t.left+t.width/2,_=`M ${E},${p} L ${E},${x}`;this.setLineStyle(i,e[m],_,u),g=u})}else{let g=c.dir===M.LAYOUT_GROW_DIR.LEFT?n-l:n+o+l,u=r+a/2,m=c.dir===M.LAYOUT_GROW_DIR.LEFT?c.left+c.width:c.left,p=c.top+c.height/2,x=`M ${g},${u} L ${m},${p}`;this.setLineStyle(i,e[f],x,c)}})}renderLineCurve(t,e,i){if(t.children.length<=0)return[];let{left:n,top:r,width:o,height:a,expandBtnSize:l}=t;const{alwaysShowExpandBtn:h,notShowExpandBtn:d}=this.mindMap.opt;(!h||d)&&(l=0),t.children.forEach((c,f)=>{if(t.isRoot){let g=t;t.children.forEach((u,m)=>{let p=g.top+g.height,x=u.top,E=t.left+t.width/2,_=`M ${E},${p} L ${E},${x}`;this.setLineStyle(i,e[m],_,u),g=u})}else{let g=c.dir===M.LAYOUT_GROW_DIR.LEFT?n-l:n+o+l,u=r+a/2,m=c.dir===M.LAYOUT_GROW_DIR.LEFT?c.left+c.width:c.left,p=c.top+c.height/2,x=this.cubicBezierPath(g,u,m,p);this.setLineStyle(i,e[f],x,c)}})}renderExpandBtn(t,e){let{width:i,height:n,expandBtnSize:r,isRoot:o}=t;if(!o){let{translateX:a,translateY:l}=e.transform();t.dir===M.LAYOUT_GROW_DIR.RIGHT?e.translate(i-a,n/2-l):e.translate(-r-a,n/2-l)}}renderGeneralization(t){t.forEach(e=>{let i=e.node.dir===M.LAYOUT_GROW_DIR.LEFT,{top:n,bottom:r,left:o,right:a,generalizationLineMargin:l,generalizationNodeMargin:h}=this.getNodeGeneralizationRenderBoundaries(e,"h"),d=i?o-l:a+l,c=d,f=n,g=d,u=r,m=c+(i?-20:20),p=f+(u-f)/2,x=`M ${c},${f} Q ${m},${p} ${g},${u}`;e.generalizationLine.plot(this.transformPath(x)),e.generalizationNode.left=d+(i?-h:h)-(i?e.generalizationNode.width:0),e.generalizationNode.top=n+(r-n-e.generalizationNode.height)/2})}renderExpandBtnRect(t,e,i,n,r){r.dir===M.LAYOUT_GROW_DIR.LEFT?t.size(e,n).x(-e).y(0):t.size(e,n).x(i).y(0)}}const he={top:{renderExpandBtn({node:s,btn:t,expandBtnSize:e,translateX:i,translateY:n,width:r,height:o}){s.parent&&s.parent.isRoot?t.translate(r*.3-e/2-i,-e/2-n):t.translate(r*.3-e/2-i,o+e/2-n)},renderLine({node:s,line:t,top:e,x:i,lineLength:n,height:r,expandBtnSize:o,maxy:a,ctx:l}){s.parent&&s.parent.isRoot?t.plot(l.transformPath(`M ${i},${e} L ${i+n},${e-Math.tan(Ge(l.mindMap.opt.fishboneDeg))*n}`)):t.plot(l.transformPath(`M ${i},${e+r+o} L ${i},${a}`))},computedLeftTopValue({layerIndex:s,node:t,ctx:e}){if(s>=1&&t.children){let i=e.getMarginY(s+1),n=t.left+t.width*e.childIndent,r=t.top+t.height+(e.getNodeActChildrenLength(t)>0?t.expandBtnSize:0)+i;t.children.forEach(o=>{o.left=n,o.top+=r,r+=o.height+(e.getNodeActChildrenLength(o)>0?o.expandBtnSize:0)+i})}},adjustLeftTopValueBefore({node:s,parent:t,ctx:e,layerIndex:i}){let n=s.children.length,r=e.getMarginY(i+1);if(t&&!t.isRoot&&n>0){let o=s.children.reduce((a,l)=>a+l.height+(e.getNodeActChildrenLength(l)>0?l.expandBtnSize:0)+r,0);e.updateBrothersTop(s,o)}},adjustLeftTopValueAfter({parent:s,node:t,ctx:e}){if(s&&s.isRoot){let i=e.getMarginY(t.layerIndex+1),n=t.expandBtnSize+i;t.children.forEach(r=>{let o=e.getNodeAreaHeight(r),a=r.top,l=r.left;r.top=t.top-(r.top-t.top)-o+t.height,r.left=t.left+t.width*e.indent+(o+n)/Math.tan(Ge(e.mindMap.opt.fishboneDeg)),n+=o,e.updateChildrenPro(r.children,{top:r.top-a,left:r.left-l})})}}},bottom:{renderExpandBtn({node:s,btn:t,expandBtnSize:e,translateX:i,translateY:n,width:r,height:o}){s.parent&&s.parent.isRoot?t.translate(r*.3-e/2-i,o+e/2-n):t.translate(r*.3-e/2-i,-e/2-n)},renderLine({node:s,line:t,top:e,x:i,lineLength:n,height:r,miny:o,ctx:a}){s.parent&&s.parent.isRoot?t.plot(a.transformPath(`M ${i},${e+r} L ${i+n},${e+r+Math.tan(Ge(a.mindMap.opt.fishboneDeg))*n}`)):t.plot(a.transformPath(`M ${i},${e} L ${i},${o}`))},computedLeftTopValue({layerIndex:s,node:t,ctx:e}){let i=e.getMarginY(s+1);if(s===1&&t.children){let n=t.left+t.width*e.childIndent,r=t.top+t.height+(e.getNodeActChildrenLength(t)>0?t.expandBtnSize:0)+i;t.children.forEach(o=>{o.left=n,o.top=r+(e.getNodeActChildrenLength(o)>0?o.expandBtnSize:0),r+=o.height+(e.getNodeActChildrenLength(o)>0?o.expandBtnSize:0)+i})}if(s>1&&t.children){let n=t.left+t.width*e.childIndent,r=t.top-(e.getNodeActChildrenLength(t)>0?t.expandBtnSize:0)-i;t.children.forEach(o=>{o.left=n,o.top=r-o.height,r-=o.height+(e.getNodeActChildrenLength(o)>0?o.expandBtnSize:0)+i})}},adjustLeftTopValueBefore({node:s,ctx:t,layerIndex:e}){let i=t.getMarginY(e+1),n=s.children.length;if(e>2&&n>0){let r=s.children.reduce((o,a)=>o+a.height+(t.getNodeActChildrenLength(a)>0?a.expandBtnSize:0)+i,0);t.updateBrothersTop(s,-r)}},adjustLeftTopValueAfter({parent:s,node:t,ctx:e}){if(s&&s.isRoot){let i=e.getMarginY(t.layerIndex+1),n=0,r=t.expandBtnSize;t.children.forEach(o=>{let a=e.getNodeActChildrenLength(o)>0,l=e.getNodeAreaHeight(o),h=a?l-o.height-(a?o.expandBtnSize:0):0;h-=a?i:0;let d=n+h,c=o.left;o.top+=d,o.left=t.left+t.width*e.indent+(l+r)/Math.tan(Ge(e.mindMap.opt.fishboneDeg)),n+=h,r+=l,e.updateChildrenPro(o.children,{top:d,left:o.left-c})})}}}};class Ds extends Fe{constructor(t={},e){super(t),this.layout=e,this.indent=.3,this.childIndent=.5,this.fishTail=null,this.maxx=0,this.headRatio=1,this.tailRatio=.6,this.paddingXRatio=.3,this.fishHeadPathStr="M4,181 C4,181, 0,177, 4,173 Q 96.09523809523809,0, 288.2857142857143,0 L 288.2857142857143,354 Q 48.047619047619044,354, 8,218.18367346938777 C8,218.18367346938777, 6,214.18367346938777, 8,214.18367346938777 L 41.183673469387756,214.18367346938777 Z",this.fishTailPathStr="M 606.9342905223708 0 Q 713.1342905223709 -177 819.3342905223708 -177 L 766.2342905223709 0 L 819.3342905223708 177 Q 713.1342905223709 177 606.9342905223708 0 z",this.bindEvent(),this.extendShape(),this.beforeChange=this.beforeChange.bind(this)}nodeIsRemoveAllLines(t){return t.isRoot||t.layerIndex===1}isFishbone2(){return this.layout===M.LAYOUT.FISHBONE2}bindEvent(){this.isFishbone2()&&(this.onCheckUpdateFishTail=this.onCheckUpdateFishTail.bind(this),this.mindMap.on("afterExecCommand",this.onCheckUpdateFishTail))}unBindEvent(){this.mindMap.off("afterExecCommand",this.onCheckUpdateFishTail)}extendShape(){this.isFishbone2()&&this.mindMap.addShape({name:"fishHead",createShape:t=>{const e=Tt(`<path d="${this.fishHeadPathStr}"></path>`),{width:i,height:n}=t.shapeInstance.getNodeSize();return e.size(i,n),e},getPadding:({width:t,height:e,paddingX:i,paddingY:n})=>{t+=i*2,e+=n*2;let r=this.paddingXRatio*t,o=0;return t+=r*2,o=(t/this.headRatio-e)/2,{paddingX:r,paddingY:o}}})}doLayout(t){ze([()=>{this.computedBaseValue(),this.addFishTail()},()=>{this.computedLeftTopValue()},()=>{this.adjustLeftTopValue(),this.updateFishTailPosition()},()=>{t(this.root)}])}addFishTail(){if(!this.isFishbone2())return;const t=this.mindMap.lineDraw.findOne(".smm-layout-fishbone-tail");t?this.fishTail=t:(this.fishTail=Tt(`<path d="${this.fishTailPathStr}"></path>`),this.fishTail.addClass("smm-layout-fishbone-tail"));const e=this.root.height,i=e*this.tailRatio;this.fishTail.size(i,e),this.styleFishTail(),this.mindMap.lineDraw.add(this.fishTail)}onCheckUpdateFishTail(t,e,i){if(t==="SET_NODE_DATA"){let n=!1;Object.keys(i).forEach(r=>{Tr.includes(r)&&(n=!0)}),n&&this.styleFishTail()}}styleFishTail(){this.root.style.shape(this.fishTail)}removeFishTail(){const t=this.mindMap.lineDraw.findOne(".smm-layout-fishbone-tail");t&&t.remove()}updateFishTailPosition(){this.isFishbone2()&&this.fishTail.x(this.maxx).cy(this.root.top+this.root.height/2)}computedBaseValue(){at(this.renderer.renderTree,null,(t,e,i,n,r,o)=>{i&&this.isFishbone2()&&(t.data.shape="fishHead");let a=this.createNode(t,e,i,n,r,o);if(i)this.setNodeCenter(a);else if(e._node.dir?a.dir=e._node.dir:a.dir=r%2===0?M.LAYOUT_GROW_DIR.TOP:M.LAYOUT_GROW_DIR.BOTTOM,e._node.isRoot){let l=this.getMarginY(n);const h=this.isFishbone2()?e._node.height/4:0;this.checkIsTop(a)?a.top=e._node.top-a.height-l+h:a.top=e._node.top+e._node.height+l-h}if(!t.data.expand)return!0},null,!0,0)}computedLeftTopValue(){at(this.root,null,(t,e,i,n)=>{if(t.isRoot){let o=this.getMarginX(n+1);const a=this.isFishbone2()?2:1;let l=t.left+t.width+t.height/a+o,h=t.left+t.width+t.height/a+o;t.children.forEach(d=>{this.checkIsTop(d)?(d.left=l,l+=d.width+o):(d.left=h+20,h+=d.width+o)})}let r={layerIndex:n,node:t,ctx:this};this.checkIsTop(t)?he.top.computedLeftTopValue(r):he.bottom.computedLeftTopValue(r)},null,!0)}adjustLeftTopValue(){at(this.root,null,(t,e,i,n)=>{if(!t.getData("expand"))return;let r={node:t,parent:e,layerIndex:n,ctx:this};this.checkIsTop(t)?he.top.adjustLeftTopValueBefore(r):he.bottom.adjustLeftTopValueBefore(r)},(t,e)=>{let i={parent:e,node:t,ctx:this};if(this.checkIsTop(t)?he.top.adjustLeftTopValueAfter(i):he.bottom.adjustLeftTopValueAfter(i),t.isRoot){let n=0,r=0,o=-1/0;t.children.forEach(a=>{if(this.checkIsTop(a)){a.left+=n,this.updateChildren(a.children,"left",n);let{left:l,right:h}=this.getNodeBoundaries(a,"h");h>o&&(o=h),n+=h-l}else{a.left+=r,this.updateChildren(a.children,"left",r);let{left:l,right:h}=this.getNodeBoundaries(a,"h");h>o&&(o=h),r+=h-l}}),this.maxx=o}},!0)}getNodeAreaHeight(t){let e=0,i=n=>{let r=this.getMarginY(n.layerIndex);e+=n.height+(this.getNodeActChildrenLength(n)>0?n.expandBtnSize:0)+r,n.children.length&&n.children.forEach(o=>{i(o)})};return i(t),e}updateBrothersLeft(t){let e=t.children,i=0;e.forEach(n=>{n.left+=i,n.children&&n.children.length&&this.updateChildren(n.children,"left",i);let{left:r,right:o}=this.getNodeBoundaries(n,"h"),l=o-r-n.width;l>0&&(i+=l)})}updateBrothersTop(t,e){if(t.parent&&!t.parent.isRoot){let i=t.parent.children,n=Pt(t,i);i.forEach((r,o)=>{if(r.hasCustomPosition())return;let a=0;o>n&&(a=e),r.top+=a,r.children&&r.children.length&&this.updateChildren(r.children,"top",a)}),this.checkIsTop(t)?this.updateBrothersTop(t.parent,e):this.updateBrothersTop(t.parent,t.layerIndex===3?0:e)}}checkIsTop(t){return t.dir===M.LAYOUT_GROW_DIR.TOP}renderLine(t,e,i){if(t.layerIndex!==1&&t.children.length<=0)return[];let{top:n,height:r,expandBtnSize:o}=t;const{alwaysShowExpandBtn:a,notShowExpandBtn:l}=this.mindMap.opt;(!a||l)&&(o=0);let h=t.children.length;if(t.isRoot){let d=-1/0;t.children.forEach(m=>{m.left>d&&(d=m.left);let p=this.getMarginY(m.layerIndex),x=m.left,E=t.height/2+p-(this.isFishbone2()?t.height/4:0),_=E/Math.tan(Ge(this.mindMap.opt.fishboneDeg)),T=this.lineDraw.path();this.checkIsTop(m)?T.plot(this.transformPath(`M ${x-_},${m.top+m.height+E} L ${m.left},${m.top+m.height}`)):T.plot(this.transformPath(`M ${x-_},${m.top-E} L ${x},${m.top}`)),t.style.line(T),t._lines.push(T),i&&i(T,t)});let c=t.top+t.height/2,f=t.height/2+this.getMarginY(t.layerIndex+1),g=this.lineDraw.path();const u=this.isFishbone2()?this.maxx:d-f/Math.tan(Ge(this.mindMap.opt.fishboneDeg));g.plot(this.transformPath(`M ${t.left+t.width},${c} L ${u},${c}`)),t.style.line(g),t._lines.push(g),i&&i(g,t)}else{let d=-1/0,c=1/0,f=-1/0,g=t.left+t.width*this.indent;if(t.children.forEach((u,m)=>{u.left>f&&(f=u.left);let p=u.top+u.height/2;if(p>d&&(d=p),p<c&&(c=p),t.layerIndex>1){let x=`M ${g},${p} L ${u.left},${p}`;this.setLineStyle(i,e[m],x,u)}}),h>=0){let u=this.lineDraw.path();o=h>0?o:0;let m=f-t.left-t.width*this.indent;m=Math.max(m,0);let p={node:t,line:u,top:n,x:g,lineLength:m,height:r,expandBtnSize:o,maxy:d,miny:c,ctx:this};this.checkIsTop(t)?he.top.renderLine(p):he.bottom.renderLine(p),t.style.line(u),t._lines.push(u),i&&i(u,t)}}}renderExpandBtn(t,e){let{width:i,height:n,expandBtnSize:r,isRoot:o}=t;if(!o){let{translateX:a,translateY:l}=e.transform(),h={node:t,btn:e,expandBtnSize:r,translateX:a,translateY:l,width:i,height:n};this.checkIsTop(t)?he.top.renderExpandBtn(h):he.bottom.renderExpandBtn(h)}}renderGeneralization(t){t.forEach(e=>{let{top:i,bottom:n,right:r,generalizationLineMargin:o,generalizationNodeMargin:a}=this.getNodeGeneralizationRenderBoundaries(e,"h"),l=r+o,h=i,d=r+o,c=n,f=l+20,g=h+(c-h)/2,u=`M ${l},${h} Q ${f},${g} ${d},${c}`;e.generalizationLine.plot(this.transformPath(u)),e.generalizationNode.left=r+a,e.generalizationNode.top=i+(n-i-e.generalizationNode.height)/2})}renderExpandBtnRect(t,e,i,n,r){let o="";r.dir===M.LAYOUT_GROW_DIR.TOP?o=r.layerIndex===1?M.LAYOUT_GROW_DIR.TOP:M.LAYOUT_GROW_DIR.BOTTOM:o=r.layerIndex===1?M.LAYOUT_GROW_DIR.BOTTOM:M.LAYOUT_GROW_DIR.TOP,o===M.LAYOUT_GROW_DIR.TOP?t.size(i,e).x(0).y(-e):t.size(i,e).x(0).y(n)}beforeChange(){this.isFishbone2()&&(this.root.nodeData.data.shape=M.SHAPE.RECTANGLE,this.removeFishTail(),this.unBindEvent(),this.mindMap.removeShape("fishHead"))}}const As="smm-node-edit-wrap";class tc{constructor(t){this.renderer=t,this.mindMap=t.mindMap,this.currentNode=null,this.textEditNode=null,this.showTextEdit=!1,this.cacheEditingText="",this.hasBodyMousedown=!1,this.textNodePaddingX=5,this.textNodePaddingY=3,this.isNeedUpdateTextEditNode=!1,this.mindMap.addEditNodeClass(As),this.bindEvent()}bindEvent(){this.show=this.show.bind(this),this.onScale=this.onScale.bind(this),this.onKeydown=this.onKeydown.bind(this),this.mindMap.on("node_dblclick",(t,e,i)=>{this.show({node:t,e,isInserting:i})}),this.mindMap.on("draw_click",()=>{this.hideEditTextBox()}),this.mindMap.on("body_mousedown",()=>{this.hasBodyMousedown=!0}),this.mindMap.on("body_click",()=>{this.hasBodyMousedown&&(this.hasBodyMousedown=!1,this.mindMap.opt.isEndNodeTextEditOnClickOuter&&this.hideEditTextBox())}),this.mindMap.on("svg_mousedown",()=>{this.hideEditTextBox()}),this.mindMap.on("expand_btn_click",()=>{this.hideEditTextBox()}),this.mindMap.on("before_node_active",()=>{this.hideEditTextBox()}),this.mindMap.on("mousewheel",()=>{this.mindMap.opt.mousewheelAction===M.MOUSE_WHEEL_ACTION.MOVE&&this.hideEditTextBox()}),this.mindMap.keyCommand.addShortcut("F2",()=>{this.renderer.activeNodeList.length<=0||this.show({node:this.renderer.activeNodeList[0]})}),this.mindMap.on("scale",this.onScale),this.mindMap.opt.enableAutoEnterTextEditWhenKeydown&&window.addEventListener("keydown",this.onKeydown),this.mindMap.on("beforeDestroy",()=>{this.unBindEvent()}),this.mindMap.on("after_update_config",(t,e)=>{t.openRealtimeRenderOnNodeTextEdit!==e.openRealtimeRenderOnNodeTextEdit&&(this.mindMap.richText?this.mindMap.richText.onOpenRealtimeRenderOnNodeTextEditConfigUpdate(t.openRealtimeRenderOnNodeTextEdit):this.onOpenRealtimeRenderOnNodeTextEditConfigUpdate(t.openRealtimeRenderOnNodeTextEdit)),t.enableAutoEnterTextEditWhenKeydown!==e.enableAutoEnterTextEditWhenKeydown&&window[t.enableAutoEnterTextEditWhenKeydown?"addEventListener":"removeEventListener"]("keydown",this.onKeydown)}),this.mindMap.on("afterExecCommand",()=>{this.isShowTextEdit()&&(this.isNeedUpdateTextEditNode=!0)}),this.mindMap.on("node_tree_render_end",()=>{this.isShowTextEdit()&&this.isNeedUpdateTextEditNode&&(this.isNeedUpdateTextEditNode=!1,this.updateTextEditNode())})}unBindEvent(){window.removeEventListener("keydown",this.onKeydown)}onKeydown(t){if(t.target!==document.body)return;const e=this.mindMap.renderer.activeNodeList;if(e.length<=0||e.length>1)return;const i=e[0];i&&this.checkIsAutoEnterTextEditKey(t)&&(t.preventDefault(),this.show({node:i,e:t,isInserting:!1,isFromKeyDown:!0}))}checkIsAutoEnterTextEditKey(t){const e=t.keyCode;return(e===229||e>=65&&e<=90||e>=48&&e<=57)&&!this.mindMap.keyCommand.hasCombinationKey(t)}registerTmpShortcut(){this.mindMap.keyCommand.addShortcut("Enter",()=>{this.hideEditTextBox()}),this.mindMap.keyCommand.addShortcut("Tab",()=>{this.hideEditTextBox()})}isShowTextEdit(){return this.mindMap.richText?this.mindMap.richText.showTextEdit:this.showTextEdit}setIsShowTextEdit(t){this.showTextEdit=t,t?this.mindMap.keyCommand.stopCheckInSvg():this.mindMap.keyCommand.recoveryCheckInSvg()}async show({node:t,isInserting:e=!1,isFromKeyDown:i=!1,isFromScale:n=!1}){if(t.isUseCustomNodeContent())return;this.getCurrentEditNode()&&this.hideEditTextBox();const{beforeTextEdit:o,openRealtimeRenderOnNodeTextEdit:a}=this.mindMap.opt;if(typeof o=="function"){let g=!1;try{g=await o(t,e)}catch(u){g=!1,this.mindMap.opt.errorHandler(Ve.BEFORE_TEXT_EDIT_ERROR,u)}if(!g)return}const{offsetLeft:l,offsetTop:h}=rh(this.mindMap,t);this.mindMap.view.translateXY(l,h);const d=t._textData.node;a&&d.show();const c=d.node.getBoundingClientRect();a&&d.hide();const f={node:t,rect:c,isInserting:e,isFromKeyDown:i,isFromScale:n};if(this.mindMap.richText){this.mindMap.richText.showEditText(f);return}this.currentNode=t,this.showEditTextBox(f)}onOpenRealtimeRenderOnNodeTextEditConfigUpdate(t){this.textEditNode&&(this.textEditNode.style.background=t?"transparent":this.currentNode?this.getBackground(this.currentNode):"",this.textEditNode.style.boxShadow=t?"none":"0 0 20px rgba(0,0,0,.5)")}onScale(){const t=this.getCurrentEditNode();t&&(this.mindMap.richText?(this.mindMap.richText.cacheEditingText=this.mindMap.richText.getEditText(),this.mindMap.richText.showTextEdit=!1):(this.cacheEditingText=this.getEditText(),this.setIsShowTextEdit(!1)),this.show({node:t,isFromScale:!0}))}showEditTextBox({node:t,rect:e,isInserting:i,isFromKeyDown:n,isFromScale:r}){if(this.showTextEdit)return;const{nodeTextEditZIndex:o,textAutoWrapWidth:a,selectTextOnEnterEditText:l,openRealtimeRenderOnNodeTextEdit:h,autoEmptyTextWhenKeydownEnterEdit:d}=this.mindMap.opt;r||this.mindMap.emit("before_show_text_edit"),this.registerTmpShortcut(),this.textEditNode||(this.textEditNode=document.createElement("div"),this.textEditNode.classList.add(As),this.textEditNode.style.cssText=`
        position: fixed;
        box-sizing: border-box;
        ${h?"":"box-shadow: 0 0 20px rgba(0,0,0,.5);"}
        padding: ${this.textNodePaddingY}px ${this.textNodePaddingX}px;
        margin-left: -${this.textNodePaddingX}px;
        margin-top: -${this.textNodePaddingY}px;
        outline: none; 
        word-break: break-all;
        line-break: anywhere;
      `,this.textEditNode.setAttribute("contenteditable",!0),this.textEditNode.addEventListener("keyup",p=>{p.stopPropagation()}),this.textEditNode.addEventListener("click",p=>{p.stopPropagation()}),this.textEditNode.addEventListener("mousedown",p=>{p.stopPropagation()}),this.textEditNode.addEventListener("keydown",p=>{this.checkIsAutoEnterTextEditKey(p)&&p.stopPropagation()}),this.textEditNode.addEventListener("paste",p=>{const x=p.clipboardData.getData("text"),{isSmm:E,data:_}=wn(x);E&&_[0]&&_[0].data?ps(p,_r(_[0].data.text)):ps(p),this.emitTextChangeEvent()}),this.textEditNode.addEventListener("input",()=>{this.emitTextChangeEvent()}),(this.mindMap.opt.customInnerElsAppendTo||document.body).appendChild(this.textEditNode));const c=this.mindMap.view.scale,f=t.style.merge("fontSize"),g=(this.cacheEditingText||t.getData("text")).split(/\n/gim).map(m=>Ui(m)),u=t._textData.node.attr("data-ismultiLine")==="true";t.style.domText(this.textEditNode,c),h||(this.textEditNode.style.background=this.getBackground(t)),this.textEditNode.style.zIndex=o,n&&d?this.textEditNode.innerHTML="":this.textEditNode.innerHTML=g.join("<br>"),this.textEditNode.style.minWidth=e.width+this.textNodePaddingX*2+"px",this.textEditNode.style.minHeight=e.height+"px",this.textEditNode.style.left=Math.floor(e.left)+"px",this.textEditNode.style.top=Math.floor(e.top)+"px",this.textEditNode.style.display="block",this.textEditNode.style.maxWidth=a*c+"px",u?(this.textEditNode.style.lineHeight=Ri,this.textEditNode.style.transform=`translateY(${(Ri-1)*f/2*c}px)`):this.textEditNode.style.lineHeight="normal",this.setIsShowTextEdit(!0),i||l&&!n?yh(this.textEditNode):vh(this.textEditNode),this.cacheEditingText=""}emitTextChangeEvent(){this.mindMap.emit("node_text_edit_change",{node:this.currentNode,text:this.getEditText(),richText:!1})}updateTextEditNode(){if(this.mindMap.richText){this.mindMap.richText.updateTextEditNode();return}if(!this.showTextEdit||!this.currentNode)return;const t=this.currentNode._textData.node.node.getBoundingClientRect();this.textEditNode.style.minWidth=t.width+this.textNodePaddingX*2+"px",this.textEditNode.style.minHeight=t.height+this.textNodePaddingY*2+"px",this.textEditNode.style.left=Math.floor(t.left)+"px",this.textEditNode.style.top=Math.floor(t.top)+"px"}getBackground(t){if(t.style.merge("gradientStyle")){const i=t.style.merge("startColor"),n=t.style.merge("endColor");return`linear-gradient(to right, ${i}, ${n})`}else{const i=t.style.merge("fillColor"),n=t.style.merge("color");return i==="transparent"?Mr(n)?dh(this.mindMap.themeConfig):"#fff":i}}removeTextEditEl(){if(this.mindMap.richText){this.mindMap.richText.removeTextEditEl();return}if(!this.textEditNode)return;(this.mindMap.opt.customInnerElsAppendTo||document.body).removeChild(this.textEditNode)}getEditText(){return eh(this.textEditNode.innerHTML)}hideEditTextBox(){if(this.mindMap.richText)return this.mindMap.richText.hideEditText();if(!this.showTextEdit)return;const t=this.currentNode,e=this.getEditText();this.currentNode=null,this.textEditNode.style.display="none",this.textEditNode.innerHTML="",this.textEditNode.style.fontFamily="inherit",this.textEditNode.style.fontSize="inherit",this.textEditNode.style.fontWeight="normal",this.textEditNode.style.transform="translateY(0)",this.setIsShowTextEdit(!1),this.mindMap.execCommand("SET_NODE_TEXT",t,e),this.mindMap.render(),this.mindMap.emit("hide_text_edit",this.textEditNode,this.renderer.activeNodeList,t)}getCurrentEditNode(){return this.mindMap.richText?this.mindMap.richText.node:this.currentNode}}const Rs={[M.LAYOUT.LOGICAL_STRUCTURE]:Ls,[M.LAYOUT.LOGICAL_STRUCTURE_LEFT]:Ls,[M.LAYOUT.MIND_MAP]:Zd,[M.LAYOUT.CATALOG_ORGANIZATION]:Jd,[M.LAYOUT.ORGANIZATION_STRUCTURE]:Qd,[M.LAYOUT.TIMELINE]:bs,[M.LAYOUT.TIMELINE2]:bs,[M.LAYOUT.VERTICAL_TIMELINE]:Ji,[M.LAYOUT.VERTICAL_TIMELINE2]:Ji,[M.LAYOUT.VERTICAL_TIMELINE3]:Ji,[M.LAYOUT.FISHBONE]:Ds,[M.LAYOUT.FISHBONE2]:Ds};class ec{constructor(t={}){this.opt=t,this.mindMap=t.mindMap,this.themeConfig=this.mindMap.themeConfig,this.renderTree=this.mindMap.opt.data?Oi({},this.mindMap.opt.data):null,this.reRender=!1,this.isRendering=!1,this.hasWaitRendering=!1,this.nodeCache={},this.lastNodeCache={},this.renderSourceList=[],this.renderCallbackList=[],this.activeNodeList=[],this.emitNodeActiveEventTimer=null,this.renderTimer=null,this.root=null,this.textEdit=new tc(this),this.beingCopyData=null,this.highlightBoxNode=null,this.highlightBoxNodeStyle=null,this.lastActiveNodeList=[],this.setLayout(),this.bindEvent(),this.registerCommands(),this.registerShortcutKeys()}setLayout(){this.layout&&this.layout.beforeChange&&this.layout.beforeChange();const{layout:t}=this.mindMap.opt;let e=Rs[t]||this.mindMap[t];e||(e=Rs[M.LAYOUT.LOGICAL_STRUCTURE],this.mindMap.opt.layout=M.LAYOUT.LOGICAL_STRUCTURE),this.layout=new e(this,t)}setData(t){this.renderTree=t||null}bindEvent(){const{openPerformance:t,performanceConfig:e,openRealtimeRenderOnNodeTextEdit:i}=this.mindMap.opt;this.mindMap.on("draw_click",r=>{this.clearActiveNodeListOnDrawClick(r,"click")}),this.mindMap.on("contextmenu",r=>{this.clearActiveNodeListOnDrawClick(r,"contextmenu")}),this.mindMap.svg.on("dblclick",()=>{this.mindMap.opt.enableDblclickBackToRootNode&&this.setRootNodeCenter()});const n=Vn(()=>{this.renderTree&&this.root&&(this.mindMap.emit("node_tree_render_start"),this.root.render(()=>{this.mindMap.emit("node_tree_render_end")},!1,!0))},e.time);t&&this.mindMap.on("view_data_change",n),this.onNodeTextEditChange=ih(this.onNodeTextEditChange,100,this),i&&this.mindMap.on("node_text_edit_change",this.onNodeTextEditChange),this.mindMap.on("after_update_config",(r,o)=>{r.openPerformance!==o.openPerformance&&(this.mindMap[r.openPerformance?"on":"off"]("view_data_change",n),this.forceLoadNode()),r.openRealtimeRenderOnNodeTextEdit!==o.openRealtimeRenderOnNodeTextEdit&&this.mindMap[r.openRealtimeRenderOnNodeTextEdit?"on":"off"]("node_text_edit_change",this.onNodeTextEditChange)})}onNodeTextEditChange({node:t,text:e}){t._textData=t.createTextNode(e);const{width:i,height:n}=t.getNodeRect();t.width=i,t.height=n,t.layout(),this.mindMap.render(()=>{this.textEdit.updateTextEditNode()})}forceLoadNode(t){t=t||this.root,t&&(this.mindMap.emit("node_tree_render_start"),t.render(()=>{this.mindMap.emit("node_tree_render_end")},!0))}registerCommands(){this.selectAll=this.selectAll.bind(this),this.mindMap.command.add("SELECT_ALL",this.selectAll),this.back=this.back.bind(this),this.mindMap.command.add("BACK",this.back),this.forward=this.forward.bind(this),this.mindMap.command.add("FORWARD",this.forward),this.insertNode=this.insertNode.bind(this),this.mindMap.command.add("INSERT_NODE",this.insertNode),this.insertMultiNode=this.insertMultiNode.bind(this),this.mindMap.command.add("INSERT_MULTI_NODE",this.insertMultiNode),this.insertChildNode=this.insertChildNode.bind(this),this.mindMap.command.add("INSERT_CHILD_NODE",this.insertChildNode),this.insertMultiChildNode=this.insertMultiChildNode.bind(this),this.mindMap.command.add("INSERT_MULTI_CHILD_NODE",this.insertMultiChildNode),this.insertParentNode=this.insertParentNode.bind(this),this.mindMap.command.add("INSERT_PARENT_NODE",this.insertParentNode),this.upNode=this.upNode.bind(this),this.mindMap.command.add("UP_NODE",this.upNode),this.downNode=this.downNode.bind(this),this.mindMap.command.add("DOWN_NODE",this.downNode),this.moveUpOneLevel=this.moveUpOneLevel.bind(this),this.mindMap.command.add("MOVE_UP_ONE_LEVEL",this.moveUpOneLevel),this.insertAfter=this.insertAfter.bind(this),this.mindMap.command.add("INSERT_AFTER",this.insertAfter),this.insertBefore=this.insertBefore.bind(this),this.mindMap.command.add("INSERT_BEFORE",this.insertBefore),this.moveNodeTo=this.moveNodeTo.bind(this),this.mindMap.command.add("MOVE_NODE_TO",this.moveNodeTo),this.removeNode=this.removeNode.bind(this),this.mindMap.command.add("REMOVE_NODE",this.removeNode),this.removeCurrentNode=this.removeCurrentNode.bind(this),this.mindMap.command.add("REMOVE_CURRENT_NODE",this.removeCurrentNode),this.pasteNode=this.pasteNode.bind(this),this.mindMap.command.add("PASTE_NODE",this.pasteNode),this.cutNode=this.cutNode.bind(this),this.mindMap.command.add("CUT_NODE",this.cutNode),this.setNodeStyle=this.setNodeStyle.bind(this),this.mindMap.command.add("SET_NODE_STYLE",this.setNodeStyle),this.setNodeStyles=this.setNodeStyles.bind(this),this.mindMap.command.add("SET_NODE_STYLES",this.setNodeStyles),this.setNodeActive=this.setNodeActive.bind(this),this.mindMap.command.add("SET_NODE_ACTIVE",this.setNodeActive),this.clearActiveNode=this.clearActiveNode.bind(this),this.mindMap.command.add("CLEAR_ACTIVE_NODE",this.clearActiveNode),this.setNodeExpand=this.setNodeExpand.bind(this),this.mindMap.command.add("SET_NODE_EXPAND",this.setNodeExpand),this.expandAllNode=this.expandAllNode.bind(this),this.mindMap.command.add("EXPAND_ALL",this.expandAllNode),this.unexpandAllNode=this.unexpandAllNode.bind(this),this.mindMap.command.add("UNEXPAND_ALL",this.unexpandAllNode),this.expandToLevel=this.expandToLevel.bind(this),this.mindMap.command.add("UNEXPAND_TO_LEVEL",this.expandToLevel),this.setNodeData=this.setNodeData.bind(this),this.mindMap.command.add("SET_NODE_DATA",this.setNodeData),this.setNodeText=this.setNodeText.bind(this),this.mindMap.command.add("SET_NODE_TEXT",this.setNodeText),this.setNodeImage=this.setNodeImage.bind(this),this.mindMap.command.add("SET_NODE_IMAGE",this.setNodeImage),this.setNodeIcon=this.setNodeIcon.bind(this),this.mindMap.command.add("SET_NODE_ICON",this.setNodeIcon),this.setNodeHyperlink=this.setNodeHyperlink.bind(this),this.mindMap.command.add("SET_NODE_HYPERLINK",this.setNodeHyperlink),this.setNodeNote=this.setNodeNote.bind(this),this.mindMap.command.add("SET_NODE_NOTE",this.setNodeNote),this.setNodeAttachment=this.setNodeAttachment.bind(this),this.mindMap.command.add("SET_NODE_ATTACHMENT",this.setNodeAttachment),this.setNodeTag=this.setNodeTag.bind(this),this.mindMap.command.add("SET_NODE_TAG",this.setNodeTag),this.insertFormula=this.insertFormula.bind(this),this.mindMap.command.add("INSERT_FORMULA",this.insertFormula),this.addGeneralization=this.addGeneralization.bind(this),this.mindMap.command.add("ADD_GENERALIZATION",this.addGeneralization),this.removeGeneralization=this.removeGeneralization.bind(this),this.mindMap.command.add("REMOVE_GENERALIZATION",this.removeGeneralization),this.setNodeCustomPosition=this.setNodeCustomPosition.bind(this),this.mindMap.command.add("SET_NODE_CUSTOM_POSITION",this.setNodeCustomPosition),this.resetLayout=this.resetLayout.bind(this),this.mindMap.command.add("RESET_LAYOUT",this.resetLayout),this.setNodeShape=this.setNodeShape.bind(this),this.mindMap.command.add("SET_NODE_SHAPE",this.setNodeShape),this.goTargetNode=this.goTargetNode.bind(this),this.mindMap.command.add("GO_TARGET_NODE",this.goTargetNode),this.removeCustomStyles=this.removeCustomStyles.bind(this),this.mindMap.command.add("REMOVE_CUSTOM_STYLES",this.removeCustomStyles),this.removeAllNodeCustomStyles=this.removeAllNodeCustomStyles.bind(this),this.mindMap.command.add("REMOVE_ALL_NODE_CUSTOM_STYLES",this.removeAllNodeCustomStyles)}registerShortcutKeys(){this.mindMap.keyCommand.addShortcut("Tab",()=>{this.mindMap.execCommand("INSERT_CHILD_NODE")}),this.mindMap.keyCommand.addShortcut("Insert",()=>{this.mindMap.execCommand("INSERT_CHILD_NODE")}),this.mindMap.keyCommand.addShortcut("Enter",()=>{this.mindMap.execCommand("INSERT_NODE")}),this.mindMap.keyCommand.addShortcut("Shift+Tab",()=>{this.mindMap.execCommand("INSERT_PARENT_NODE")}),this.mindMap.keyCommand.addShortcut("Control+g",()=>{this.mindMap.execCommand("ADD_GENERALIZATION")}),this.toggleActiveExpand=this.toggleActiveExpand.bind(this),this.mindMap.keyCommand.addShortcut("/",this.toggleActiveExpand),this.mindMap.keyCommand.addShortcut("Del|Backspace",()=>{this.mindMap.execCommand("REMOVE_NODE")}),this.mindMap.keyCommand.addShortcut("Shift+Backspace",()=>{this.mindMap.execCommand("REMOVE_CURRENT_NODE")}),this.mindMap.on("before_show_text_edit",()=>{this.startTextEdit()}),this.mindMap.on("hide_text_edit",()=>{this.endTextEdit()}),this.mindMap.keyCommand.addShortcut("Control+a",()=>{this.mindMap.execCommand("SELECT_ALL")}),this.mindMap.keyCommand.addShortcut("Control+l",()=>{this.mindMap.execCommand("RESET_LAYOUT")}),this.mindMap.keyCommand.addShortcut("Control+Up",()=>{this.mindMap.execCommand("UP_NODE")}),this.mindMap.keyCommand.addShortcut("Control+Down",()=>{this.mindMap.execCommand("DOWN_NODE")}),this.mindMap.keyCommand.addShortcut("Control+c",()=>{this.copy()}),this.mindMap.keyCommand.addShortcut("Control+x",()=>{this.cut()}),this.mindMap.keyCommand.addShortcut("Control+v",()=>{this.paste()}),this.mindMap.keyCommand.addShortcut("Control+Enter",()=>{this.setRootNodeCenter()})}emitNodeActiveEvent(t=null,e=[...this.activeNodeList]){wh(this.lastActiveNodeList,e)||(this.lastActiveNodeList=[...e],clearTimeout(this.emitNodeActiveEventTimer),this.emitNodeActiveEventTimer=setTimeout(()=>{this.mindMap.emit("node_active",t,e)},0))}clearActiveNodeListOnDrawClick(t,e){if(this.activeNodeList.length<=0)return;let i=!0;const{useLeftKeySelectionRightKeyDrag:n}=this.mindMap.opt;if(e==="contextmenu"?!n:n){const r=this.mindMap.event.mousedownPos;i=Math.abs(t.clientX-r.x)<=5&&Math.abs(t.clientY-r.y)<=5}i&&this.mindMap.execCommand("CLEAR_ACTIVE_NODE")}startTextEdit(){this.mindMap.keyCommand.save()}endTextEdit(){this.mindMap.keyCommand.restore()}clearCache(){this.layout.lru.clear(),this.nodeCache={},this.lastNodeCache={}}addRenderParams(t,e){t&&this.renderCallbackList.findIndex(n=>n===t)===-1&&this.renderCallbackList.push(t),e&&this.renderSourceList.findIndex(n=>n===e)===-1&&this.renderSourceList.push(e)}checkHasRenderSource(t){t=Array.isArray(t)?t:[t];for(let e=0;e<this.renderSourceList.length;e++)if(t.includes(this.renderSourceList[e]))return!0;return!1}onRenderEnd(){this.renderCallbackList.forEach(t=>{t()}),this.isRendering=!1,this.reRender=!1,this.renderCallbackList=[],this.renderSourceList=[],this.mindMap.emit("node_tree_render_end")}render(t,e){this.addRenderParams(t,e),clearTimeout(this.renderTimer),this.renderTimer=setTimeout(()=>{this._render()},0)}_render(){if(this.checkHasRenderSource(M.CHANGE_THEME)&&this.resetUnExpandNodeStyle(),this.isRendering){this.hasWaitRendering=!0;return}if(this.isRendering=!0,this.lastNodeCache=this.nodeCache,this.nodeCache={},this.reRender&&this.clearActiveNodeList(),!this.renderTree){this.onRenderEnd();return}this.mindMap.emit("node_tree_render_start"),this.root=null,this.layout.doLayout(t=>{Object.keys(this.lastNodeCache).forEach(e=>{this.nodeCache[e]||(this.removeNodeFromActiveList(this.lastNodeCache[e]),this.emitNodeActiveEvent(),this.lastNodeCache[e].destroy())}),this.root=t,this.root.render(()=>{if(this.isRendering=!1,this.hasWaitRendering){this.hasWaitRendering=!1,this.render();return}this.onRenderEnd()})}),this.emitNodeActiveEvent()}resetUnExpandNodeStyle(){this.renderTree&&at(this.renderTree,null,t=>{if(!t.data.expand)return at(t,null,e=>{e.data.needUpdate=!0}),!0})}clearActiveNode(){this.activeNodeList.length<=0||(this.clearActiveNodeList(),this.emitNodeActiveEvent(null,[]))}clearActiveNodeList(){this.activeNodeList.forEach(t=>{this.mindMap.execCommand("SET_NODE_ACTIVE",t,!1)}),this.activeNodeList=[]}addNodeToActiveList(t,e=!1){if(this.mindMap.opt.onlyOneEnableActiveNodeOnCooperate&&t.userList.length>0)return;this.findActiveNodeIndex(t)===-1&&(e||this.mindMap.emit("before_node_active",t,this.activeNodeList),this.mindMap.execCommand("SET_NODE_ACTIVE",t,!0),this.activeNodeList.push(t))}removeNodeFromActiveList(t){let e=this.findActiveNodeIndex(t);e!==-1&&(this.mindMap.execCommand("SET_NODE_ACTIVE",t,!1),this.activeNodeList.splice(e,1))}activeMultiNode(t=[]){t.forEach(e=>{this.mindMap.emit("before_node_active",e,this.activeNodeList),this.addNodeToActiveList(e,!0),this.emitNodeActiveEvent(e)})}cancelActiveMultiNode(t=[]){t.forEach(e=>{this.removeNodeFromActiveList(e),this.emitNodeActiveEvent(null)})}findActiveNodeIndex(t){return Pt(t,this.activeNodeList)}selectAll(){this.mindMap.opt.readonly||(at(this.root,null,t=>{t.getData("isActive")||this.addNodeToActiveList(t),t._generalizationList&&t._generalizationList.length>0&&t._generalizationList.forEach(e=>{const i=e.generalizationNode;i.getData("isActive")||this.addNodeToActiveList(i)})},null,!0,0,0),this.emitNodeActiveEvent())}back(t){this.backForward("back",t)}forward(t){this.backForward("forward",t)}backForward(t,e){this.mindMap.execCommand("CLEAR_ACTIVE_NODE");const i=this.mindMap.command[t](e);i&&(this.renderTree=i,this.mindMap.render()),this.mindMap.emit("data_change",i)}getNewNodeBehavior(t=!1,e=!1){const{createNewNodeBehavior:i}=this.mindMap.opt;let n=!1,r=!1;switch(i){case M.CREATE_NEW_NODE_BEHAVIOR.DEFAULT:n=e||!t,r=e?!1:t;break;case M.CREATE_NEW_NODE_BEHAVIOR.NOT_ACTIVE:n=!1,r=!1;break;case M.CREATE_NEW_NODE_BEHAVIOR.ACTIVE_ONLY:n=!0,r=!1;break}return{focusNewNode:n,inserting:r}}insertNode(t=!0,e=[],i=null,n=[]){if(e=ee(e),this.activeNodeList.length<=0&&e.length<=0)return;this.textEdit.hideEditTextBox();const{defaultInsertSecondLevelNodeText:r,defaultInsertBelowSecondLevelNodeText:o}=this.mindMap.opt,a=e.length>0?e:this.activeNodeList,l=a.length>1,h=this.hasRichTextPlugin(),{focusNewNode:d,inserting:c}=this.getNewNodeBehavior(t,l),f={expand:!0,richText:h,isActive:d};h&&(f.resetRichText=!0),n=Ni(n,f);const g=i&&i.richText;let u=!1;a.forEach(m=>{if(m.isGeneralization||m.isRoot)return;n=le(n);const p=m.parent,E=m.layerIndex===1?r:o,_=ii(m);g&&f.resetRichText&&delete f.resetRichText;const T={inserting:c,data:{text:E,...f,uid:tt(),...i||{}},children:[...ei(n,u)]};u=!0,p.nodeData.children.splice(_+1,0,T)}),d&&this.clearActiveNodeList(),this.mindMap.render()}insertMultiNode(t,e){if(!e||e.length<=0||(t=ee(t),this.activeNodeList.length<=0&&t.length<=0))return;this.textEdit.hideEditTextBox();const i=t.length>0?t:this.activeNodeList,n=this.hasRichTextPlugin(),{focusNewNode:r}=this.getNewNodeBehavior(!1,!0),o={expand:!0,richText:n,isActive:r};n&&(o.resetRichText=!0),e=Ni(e,o);let a=!1;i.forEach(l=>{if(l.isGeneralization||l.isRoot)return;e=le(e);const h=l.parent,d=ii(l),c=ei(e,a);a=!0,h.nodeData.children.splice(d+1,0,...c)}),r&&this.clearActiveNodeList(),this.mindMap.render()}insertChildNode(t=!0,e=[],i=null,n=[]){if(e=ee(e),this.activeNodeList.length<=0&&e.length<=0)return;this.textEdit.hideEditTextBox();const{defaultInsertSecondLevelNodeText:r,defaultInsertBelowSecondLevelNodeText:o}=this.mindMap.opt,a=e.length>0?e:this.activeNodeList,l=a.length>1,h=this.hasRichTextPlugin(),{focusNewNode:d,inserting:c}=this.getNewNodeBehavior(t,l),f={expand:!0,richText:h,isActive:d};h&&(f.resetRichText=!0),n=Ni(n,f);const g=i&&i.richText;let u=!1;a.forEach(m=>{if(m.isGeneralization)return;n=le(n),m.nodeData.children||(m.nodeData.children=[]);const p=m.isRoot?r:o;g&&f.resetRichText&&delete f.resetRichText;const x={inserting:c,data:{text:p,uid:tt(),...f,...i||{}},children:[...ei(n,u)]};u=!0,m.nodeData.children.push(x),m.setData({expand:!0})}),d&&this.clearActiveNodeList(),this.mindMap.render()}insertMultiChildNode(t,e){if(!e||e.length<=0||(t=ee(t),this.activeNodeList.length<=0&&t.length<=0))return;this.textEdit.hideEditTextBox();const i=t.length>0?t:this.activeNodeList,n=this.hasRichTextPlugin(),{focusNewNode:r}=this.getNewNodeBehavior(!1,!0),o={expand:!0,richText:n,isActive:r};n&&(o.resetRichText=!0),e=Ni(e,o);let a=!1;i.forEach(l=>{l.isGeneralization||(e=le(e),l.nodeData.children||(l.nodeData.children=[]),e=ei(e,a),a=!0,l.nodeData.children.push(...e),l.setData({expand:!0}))}),r&&this.clearActiveNodeList(),this.mindMap.render()}insertParentNode(t=!0,e,i){if(e=ee(e),this.activeNodeList.length<=0&&e.length<=0)return;this.textEdit.hideEditTextBox();const{defaultInsertSecondLevelNodeText:n,defaultInsertBelowSecondLevelNodeText:r}=this.mindMap.opt,o=e.length>0?e:this.activeNodeList,a=o.length>1,l=this.hasRichTextPlugin(),{focusNewNode:h,inserting:d}=this.getNewNodeBehavior(t,a),c={expand:!0,richText:l,isActive:h};l&&(c.resetRichText=!0);const f=i&&i.richText;o.forEach(g=>{if(g.isGeneralization||g.isRoot)return;const u=g.layerIndex===1?n:r;f&&c.resetRichText&&delete c.resetRichText;const m={inserting:d,data:{text:u,uid:tt(),...c,...i||{}},children:[g.nodeData]},p=g.parent,x=ii(g);p.nodeData.children.splice(x,1,m)}),h&&this.clearActiveNodeList(),this.mindMap.render()}upNode(t){if(this.activeNodeList.length<=0&&!t)return;const i=(t?[t]:this.activeNodeList)[0];if(i.isRoot)return;let n=i.parent,r=n.children,o=Pt(i,r);if(o===-1||o===0)return;let a=o-1;r.splice(o,1),r.splice(a,0,i),n.nodeData.children.splice(o,1),n.nodeData.children.splice(a,0,i.nodeData),this.mindMap.render()}downNode(t){if(this.activeNodeList.length<=0&&!t)return;const i=(t?[t]:this.activeNodeList)[0];if(i.isRoot)return;let n=i.parent,r=n.children,o=Pt(i,r);if(o===-1||o===r.length-1)return;let a=o+1;r.splice(o,1),r.splice(a,0,i),n.nodeData.children.splice(o,1),n.nodeData.children.splice(a,0,i.nodeData),this.mindMap.render()}moveUpOneLevel(t){if(t=t||this.activeNodeList[0],!t||t.isRoot||t.layerIndex<=1)return;const e=t.parent,i=e.parent,n=Pt(t,e.children),r=Pt(e,i.children);e.nodeData.children.splice(n,1),i.nodeData.children.splice(r+1,0,t.nodeData),this.mindMap.render()}_handleRemoveCustomStyles(t){let e=!1;return Object.keys(t).forEach(i=>{yn(i)&&(e=!0,delete t[i])}),this.hasRichTextPlugin()&&(e=!0,t.resetRichText=!0),e}removeCustomStyles(t){if(t=t||this.activeNodeList[0],!t)return;this._handleRemoveCustomStyles(t.getData())&&this.reRenderNodeCheckChange(t)}removeAllNodeCustomStyles(t){t=ee(t);let e=!1;if(t.length>0)t.forEach(i=>{this._handleRemoveCustomStyles(i.getData())&&(e=!0)});else{if(!this.renderTree)return;at(this.renderTree,null,i=>{this._handleRemoveCustomStyles(i.data)&&(e=!0);const r=Pi(i.data);r.length>0&&r.forEach(o=>{this._handleRemoveCustomStyles(o)&&(e=!0)})})}e&&this.mindMap.reRender()}copy(){this.beingCopyData=this.copyNode(),this.beingCopyData&&(this.mindMap.opt.disabledClipboard||us(fs(this.beingCopyData)))}cut(){this.mindMap.execCommand("CUT_NODE",t=>{this.beingCopyData=t,this.mindMap.opt.disabledClipboard||us(fs(t))})}handlePaste(t){const{disabledClipboard:e}=this.mindMap.opt;if(e)return;const i=t.clipboardData||t.originalEvent.clipboardData,n=i.items;Array.from(n).forEach(r=>{r.type.indexOf("image")>-1&&r.getAsFile(),r.type.indexOf("text")>-1&&i.getData("text")}),this.paste()}async paste(){const{errorHandler:t,handleIsSplitByWrapOnPasteCreateNewNode:e,handleNodePasteImg:i,disabledClipboard:n,onlyPasteTextWhenHasImgAndText:r}=this.mindMap.opt;if(!n&&Cr())try{const o=await xh();let a=o.text||"",l=o.img||null;if(a){let h=null,d=!0;if(this.mindMap.opt.customHandleClipboardText)try{const c=await this.mindMap.opt.customHandleClipboardText(a);if(!Me(c)){d=!1;const f=wn(c);f.isSmm?h=f.data:a=f.data}}catch(c){t(Ve.CUSTOM_HANDLE_CLIPBOARD_TEXT_ERROR,c)}if(d){const c=wn(a);c.isSmm?h=c.data:a=c.data}if(h)this.mindMap.execCommand("INSERT_MULTI_CHILD_NODE",[],Array.isArray(h)?h:[h]);else{this.hasRichTextPlugin()&&(a=Ui(a));const c=a.split(new RegExp(`\r?
|(?<!
)\r`,"g")).filter(f=>!!f);c.length>1&&e?e().then(()=>{this.mindMap.execCommand("INSERT_MULTI_CHILD_NODE",[],c.map(f=>({data:{text:f},children:[]})))}).catch(()=>{this.mindMap.execCommand("INSERT_CHILD_NODE",!1,[],{text:a})}):this.mindMap.execCommand("INSERT_CHILD_NODE",!1,[],{text:a})}}if(l&&(!a||!r))try{let h=null;i&&typeof i=="function"?h=await i(l):h=await ah(l),this.activeNodeList.length>0&&this.activeNodeList.forEach(d=>{this.mindMap.execCommand("SET_NODE_IMAGE",d,{url:h.url,title:"",width:h.size.width,height:h.size.height})})}catch(h){t(Ve.LOAD_CLIPBOARD_IMAGE_ERROR,h)}}catch(o){t(Ve.READ_CLIPBOARD_ERROR,o)}else this.beingCopyData&&this.mindMap.execCommand("PASTE_NODE",this.beingCopyData)}insertBefore(t,e){this.insertTo(t,e,"before")}insertAfter(t,e){this.insertTo(t,e,"after")}insertTo(t,e,i="before"){let n=ee(t);n=n.filter(r=>!r.isRoot),i==="after"&&n.reverse(),n.forEach(r=>{let o=r.parent,a=o.children,l=Pt(r,a);if(l===-1)return;a.splice(l,1),o.nodeData.children.splice(l,1);let h=e.parent,d=h.children,c=Pt(e,d);c!==-1&&(i==="after"&&c++,d.splice(c,0,r),h.nodeData.children.splice(c,0,r.nodeData))}),this.mindMap.render()}removeNode(t=[]){if(t=ee(t),this.activeNodeList.length<=0&&t.length<=0)return;let e=null,i=t.length>0,n=i?t:this.activeNodeList,r=n.find(o=>o.isRoot);if(r)this.clearActiveNodeList(),r.children=[],r.nodeData.children=[];else{e=this.getNextActiveNode(n);for(let o=0;o<n.length;o++){const a=n[o],l=this.textEdit.getCurrentEditNode();l&&l.getData("uid")===a.getData("uid")&&this.textEdit.hideEditTextBox(),i&&n.splice(o,1),a.isGeneralization?(this.deleteNodeGeneralization(a),this.removeNodeFromActiveList(a),o--):(this.removeNodeFromActiveList(a),Zi(a),o--)}}this.activeNodeList=[],e&&this.addNodeToActiveList(e),this.emitNodeActiveEvent(),this.mindMap.render()}deleteNodeGeneralization(t){const e=t.generalizationBelongNode,i=e.getGeneralizationNodeIndex(t);let n=e.getData("generalization");Array.isArray(n)?n.splice(i,1):n=null,this.mindMap.execCommand("SET_NODE_DATA",e,{generalization:n}),this.closeHighlightNode()}removeCurrentNode(t=[]){if(t=ee(t),this.activeNodeList.length<=0&&t.length<=0)return;let i=t.length>0?t:this.activeNodeList;i=i.filter(r=>!r.isRoot);let n=this.getNextActiveNode(i);for(let r=0;r<i.length;r++){let o=i[r];if(o.isGeneralization)this.deleteNodeGeneralization(o);else{const a=o.parent,l=ii(o);a.nodeData.children.splice(l,1,...o.nodeData.children||[])}}this.activeNodeList=[],n&&this.addNodeToActiveList(n),this.emitNodeActiveEvent(),this.mindMap.render()}getNextActiveNode(t){if(t.length!==1||this.findActiveNodeIndex(t[0])===-1)return null;let e=null;if(this.activeNodeList.length===1&&!this.activeNodeList[0].isGeneralization&&this.mindMap.opt.deleteNodeActive){const i=this.activeNodeList[0],n=i.parent.children,r=Pt(i,n);r<n.length-1?e=n[r+1]:r>0?e=n[r-1]:e=i.parent}return e}copyNode(){if(this.activeNodeList.length<=0)return null;let t=cs(this.activeNodeList);return t=gs(t),t.map(e=>ci({},e,!0))}cutNode(t){if(this.activeNodeList.length<=0)return;let e=cs(this.activeNodeList).filter(n=>!n.isRoot);e=gs(e);const i=e.map(n=>ci({},n,!0));e.forEach(n=>{Zi(n)}),this.clearActiveNodeList(),this.mindMap.render(),t&&typeof t=="function"&&t(i)}moveNodeTo(t,e){let i=ee(t);i=i.filter(n=>!n.isRoot),i.forEach(n=>{this.removeNodeFromActiveList(n),Zi(n),e.setData({expand:!0}),e.nodeData.children.push(n.nodeData)}),this.emitNodeActiveEvent(),this.mindMap.render()}pasteNode(t){t=ee(t),this.mindMap.execCommand("INSERT_MULTI_CHILD_NODE",[],t)}setNodeStyle(t,e,i){const n={[e]:i};this.setNodeDataRender(t,n),vn.includes(e)&&(t.parent||t).renderLine(!0)}setNodeStyles(t,e){const i={...e};this.setNodeDataRender(t,i);let n=Object.keys(e),r=!1;n.forEach(o=>{vn.includes(o)&&(r=!0)}),r&&(t.parent||t).renderLine(!0)}setNodeActive(t,e){this.mindMap.execCommand("SET_NODE_DATA",t,{isActive:e}),t.updateNodeByActive(e)}setNodeExpand(t,e){this.mindMap.execCommand("SET_NODE_DATA",t,{expand:e}),this.mindMap.render()}expandAllNode(t=""){if(!this.renderTree)return;const e=(i,n)=>{!n&&i.data.uid===t&&(n=!0),n&&!i.data.expand&&(i.data.expand=!0),i.children&&i.children.length>0&&i.children.forEach(r=>{e(r,n)})};e(this.renderTree,!t),this.mindMap.render()}unexpandAllNode(t=!0,e=""){if(!this.renderTree)return;const i=(n,r,o)=>{!o&&n.data.uid===e&&(o=!0),o&&!r&&n.children&&n.children.length>0&&(n.data.expand=!1),n.children&&n.children.length>0&&n.children.forEach(a=>{i(a,!1,o)})};i(this.renderTree,!0,!e),this.mindMap.render(()=>{t&&this.setRootNodeCenter()})}expandToLevel(t){this.renderTree&&(at(this.renderTree,null,(e,i,n,r)=>{r<t?e.data.expand=!0:!n&&e.children&&e.children.length>0&&(e.data.expand=!1)},null,!0,0,0),this.mindMap.render())}toggleActiveExpand(){this.activeNodeList.forEach(t=>{t.nodeData.children.length<=0||t.isRoot||this.toggleNodeExpand(t)})}toggleNodeExpand(t){this.mindMap.execCommand("SET_NODE_EXPAND",t,!t.getData("expand"))}setNodeText(t,e,i,n){i=i===void 0?t.getData("richText"):i,this.setNodeDataRender(t,{text:e,richText:i,resetRichText:n})}setNodeImage(t,e){const{url:i,title:n,width:r,height:o,custom:a=!1}=e||{url:"",title:"",width:0,height:0,custom:!1};this.setNodeDataRender(t,{image:i,imageTitle:n||"",imageSize:{width:r,height:o,custom:a}})}setNodeIcon(t,e){this.setNodeDataRender(t,{icon:e})}setNodeHyperlink(t,e,i=""){this.setNodeDataRender(t,{hyperlink:e,hyperlinkTitle:i})}setNodeNote(t,e){this.setNodeDataRender(t,{note:e})}setNodeAttachment(t,e,i=""){this.setNodeDataRender(t,{attachmentUrl:e,attachmentName:i})}setNodeTag(t,e){this.setNodeDataRender(t,{tag:e})}insertFormula(t,e=[]){if(!this.hasRichTextPlugin()||!this.mindMap.formula)return;e=ee(e),(e.length>0?e:this.activeNodeList).forEach(n=>{this.mindMap.formula.insertFormulaToNode(n,t)})}addGeneralization(t,e=!0){if(this.activeNodeList.length<=0)return;const i=this.activeNodeList.filter(d=>!d.isRoot&&!d.isGeneralization&&!d.checkHasSelfGeneralization()),n=mh(i);if(n.length<=0)return;const r=this.hasRichTextPlugin(),{focusNewNode:o,inserting:a}=this.getNewNodeBehavior(e,n.length>1);let l=!1;const h=t&&t.richText;n.forEach(d=>{const c={inserting:a,...t||{text:this.mindMap.opt.defaultGeneralizationText},range:d.range||null,uid:tt(),richText:r,isActive:o};r&&!h&&(c.resetRichText=r);let f=d.node.getData("generalization");if(f=f?Array.isArray(f)?f:[f]:[],d.range){if(!!f.find(u=>u.range&&u.range[0]===d.range[0]&&u.range[1]===d.range[1]))return;f.push(c)}else f.push(c);l=!0,this.mindMap.execCommand("SET_NODE_DATA",d.node,{generalization:f}),d.node.setData({expand:!0})}),l&&(o&&this.clearActiveNodeList(),this.mindMap.render(()=>{this.mindMap.render()}))}removeGeneralization(){this.activeNodeList.length<=0||(this.activeNodeList.forEach(t=>{t.checkHasGeneralization()&&this.mindMap.execCommand("SET_NODE_DATA",t,{generalization:null})}),this.mindMap.render(),this.closeHighlightNode())}setNodeCustomPosition(t,e=void 0,i=void 0){[t].forEach(r=>{this.mindMap.execCommand("SET_NODE_DATA",r,{customLeft:e,customTop:i})})}resetLayout(){at(this.root,null,t=>{t.customLeft=void 0,t.customTop=void 0,this.mindMap.execCommand("SET_NODE_DATA",t,{customLeft:void 0,customTop:void 0}),this.mindMap.render()},null,!0,0,0)}setNodeShape(t,e){if(!e||!Th.includes(e))return;[t].forEach(n=>{this.setNodeStyle(n,"shape",e)})}goTargetNode(t,e=()=>{}){let i=typeof t=="string"?t:t.getData("uid");i&&this.expandToNodeUid(i,()=>{let n=this.findNodeByUid(i);n&&(n.active(),this.moveNodeToCenter(n),e(n))})}setNodeData(t,e){Object.keys(e).forEach(i=>{t.nodeData.data[i]=e[i]})}setNodeDataRender(t,e,i=!1){if(this.mindMap.execCommand("SET_NODE_DATA",t,e),fh(e)){this.mindMap.emit("node_tree_render_end");return}this.reRenderNodeCheckChange(t,i)}reRenderNodeCheckChange(t,e){t.reRender()?e||this.mindMap.render():this.mindMap.emit("node_tree_render_end")}moveNodeToCenter(t,e){let{resetScaleOnMoveNodeToCenter:i}=this.mindMap.opt;e!==void 0&&(i=e);let{transform:n,state:r}=this.mindMap.view.getTransformData(),{left:o,top:a,width:l,height:h}=t;i||(o*=n.scaleX,a*=n.scaleY,l*=n.scaleX,h*=n.scaleY);let d=this.mindMap.width/2,c=this.mindMap.height/2,f=o+l/2,g=a+h/2,u=d-r.x,m=c-r.y,p=u-f,x=m-g;this.mindMap.view.translateX(p),this.mindMap.view.translateY(x),i&&this.mindMap.view.setScale(1)}setRootNodeCenter(){this.moveNodeToCenter(this.root)}expandToNodeUid(t,e=()=>{}){if(!this.renderTree){e();return}let i=[],n=!1;const r={};xr(this.renderTree,(a,l)=>{if(a.data.uid===t)return i=l?[...r[l.data.uid],l]:[],"stop";if(Pi(a.data).forEach(d=>{d.uid===t&&(i=l?[...r[l.data.uid],l,a]:[],n=!0)}),n)return"stop";r[a.data.uid]=l?[...r[l.data.uid],l]:[]});let o=!1;if(i.forEach(a=>{a.data.expand||(o=!0,a.data.expand=!0)}),n){const a=i[i.length-1];a&&at(a,null,l=>{l.data.expand||(o=!0,l.data.expand=!0)})}o?this.mindMap.render(e):e()}findNodeByUid(t){if(!this.root)return;let e=null;return at(this.root,null,i=>{if(i.getData("uid")===t)return e=i,!0;let n=!1;if((i._generalizationList||[]).forEach(r=>{r.generalizationNode.getData("uid")===t&&(e=r.generalizationNode,n=!0)}),n)return!0}),e}highlightNode(t,e,i){if(this.isRendering)return;i={stroke:"rgb(94, 200, 248)",fill:"transparent",...i||{}},this.highlightBoxNode?this.highlightBoxNodeStyle&&(this.highlightBoxNodeStyle.stroke!==i.stroke||this.highlightBoxNodeStyle.fill!==i.fill)&&this.highlightBoxNode.stroke({color:i.stroke||"transparent"}).fill({color:i.fill||"transparent"}):this.highlightBoxNode=new ke().stroke({color:i.stroke||"transparent"}).fill({color:i.fill||"transparent"}),this.highlightBoxNodeStyle={...i};let n=1/0,r=1/0,o=-1/0,a=-1/0;e?t.children.slice(e[0],e[1]+1).forEach(h=>{h.left<n&&(n=h.left),h.top<r&&(r=h.top);const d=h.left+h.width,c=h.top+h.height;d>o&&(o=d),c>a&&(a=c)}):(n=t.left,r=t.top,o=t.left+t.width,a=t.top+t.height),this.highlightBoxNode.plot([[n,r],[o,r],[o,a],[n,a]]),this.mindMap.otherDraw.add(this.highlightBoxNode)}closeHighlightNode(){this.highlightBoxNode&&this.highlightBoxNode.remove()}hasRichTextPlugin(){return!!this.mindMap.richText}}const De={default:yr},Xn={Backspace:8,Tab:9,Enter:13,Shift:16,Control:17,Alt:18,CapsLock:20,Esc:27,Spacebar:32,PageUp:33,PageDown:34,End:35,Home:36,Insert:45,Left:37,Up:38,Right:39,Down:40,Del:46,NumLock:144,Cmd:91,CmdFF:224,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123,"`":192,"=":187,"-":189,"/":191,".":190};for(let s=0;s<=9;s++)Xn[s]=s+48;"abcdefghijklmnopqrstuvwxyz".split("").forEach((s,t)=>{Xn[s]=t+65});const Ne=Xn;class ic{constructor(t){this.opt=t,this.mindMap=t.mindMap,this.shortcutMap={},this.shortcutMapCache={},this.isPause=!1,this.isInSvg=!1,this.isStopCheckInSvg=!1,this.defaultEnableCheck=this.defaultEnableCheck.bind(this),this.bindEvent()}extendKeyMap(t,e){Ne[t]=e}removeKeyMap(t){typeof Ne[t]<"u"&&delete Ne[t]}pause(){this.isPause=!0}recovery(){this.isPause=!1}save(){Object.keys(this.shortcutMapCache).length>0||(this.shortcutMapCache=this.shortcutMap,this.shortcutMap={})}restore(){Object.keys(this.shortcutMapCache).length<=0||(this.shortcutMap=this.shortcutMapCache,this.shortcutMapCache={})}stopCheckInSvg(){const{enableShortcutOnlyWhenMouseInSvg:t}=this.mindMap.opt;t&&(this.isStopCheckInSvg=!0)}recoveryCheckInSvg(){const{enableShortcutOnlyWhenMouseInSvg:t}=this.mindMap.opt;t&&(this.isStopCheckInSvg=!0)}bindEvent(){this.onKeydown=this.onKeydown.bind(this),this.mindMap.on("svg_mouseenter",()=>{this.isInSvg=!0}),this.mindMap.on("svg_mouseleave",()=>{this.isInSvg=!1}),window.addEventListener("keydown",this.onKeydown),this.mindMap.on("beforeDestroy",()=>{this.unBindEvent()})}unBindEvent(){window.removeEventListener("keydown",this.onKeydown)}defaultEnableCheck(t){const e=t.target;if(e===document.body)return!0;for(let i=0;i<this.mindMap.editNodeClassList.length;i++){const n=this.mindMap.editNodeClassList[i];if(e.classList.contains(n))return!0}return!1}onKeydown(t){const{enableShortcutOnlyWhenMouseInSvg:e,beforeShortcutRun:i,customCheckEnableShortcut:n}=this.mindMap.opt;(typeof n=="function"?n:this.defaultEnableCheck)(t)&&(this.isPause||e&&!this.isStopCheckInSvg&&!this.isInSvg||Object.keys(this.shortcutMap).forEach(o=>{if(this.checkKey(t,o)){if(this.checkKey(t,"Control+v")||(t.stopPropagation(),t.preventDefault()),typeof i=="function"&&i(o,[...this.mindMap.renderer.activeNodeList]))return;this.shortcutMap[o].forEach(a=>{a()})}}))}checkKey(t,e){let i=this.getOriginEventCodeArr(t),n=this.getKeyCodeArr(e);if(i.length!==n.length)return!1;for(let r=0;r<i.length;r++){let o=n.findIndex(a=>a===i[r]);if(o===-1)return!1;n.splice(o,1)}return!0}getOriginEventCodeArr(t){let e=[];return(t.ctrlKey||t.metaKey)&&e.push(Ne.Control),t.altKey&&e.push(Ne.Alt),t.shiftKey&&e.push(Ne.Shift),e.includes(t.keyCode)||e.push(t.keyCode),e}hasCombinationKey(t){return t.ctrlKey||t.metaKey||t.altKey||t.shiftKey}getKeyCodeArr(t){let e=t.split(/\s*\+\s*/),i=[];return e.forEach(n=>{i.push(Ne[n])}),i}addShortcut(t,e){t.split(/\s*\|\s*/).forEach(i=>{this.shortcutMap[i]?this.shortcutMap[i].push(e):this.shortcutMap[i]=[e]})}removeShortcut(t,e){t.split(/\s*\|\s*/).forEach(i=>{if(this.shortcutMap[i])if(e){let n=this.shortcutMap[i].findIndex(r=>r===e);n!==-1&&this.shortcutMap[i].splice(n,1)}else this.shortcutMap[i]=[],delete this.shortcutMap[i]})}getShortcutFn(t){let e=[];return t.split(/\s*\|\s*/).forEach(i=>{e=this.shortcutMap[i]||[]}),e}}const nc="0.14.0-fix.1",sc={version:nc};class rc{constructor(t={}){this.opt=t,this.mindMap=t.mindMap,this.commands={},this.history=[],this.activeHistoryIndex=0,this.registerShortcutKeys(),this.originAddHistory=this.addHistory.bind(this),this.addHistory=Vn(this.addHistory,this.mindMap.opt.addHistoryTime,this),this.isPause=!1}pause(){this.isPause=!0}recovery(){this.isPause=!1}clearHistory(){this.history=[],this.activeHistoryIndex=0,this.mindMap.emit("back_forward",0,0)}registerShortcutKeys(){this.mindMap.keyCommand.addShortcut("Control+z",()=>{this.mindMap.execCommand("BACK")}),this.mindMap.keyCommand.addShortcut("Control+y",()=>{this.mindMap.execCommand("FORWARD")})}exec(t,...e){if(this.commands[t]){if(this.commands[t].forEach(i=>{i(...e)}),this.mindMap.emit("afterExecCommand",t,...e),["BACK","FORWARD","SET_NODE_ACTIVE","CLEAR_ACTIVE_NODE"].includes(t))return;this.addHistory()}}add(t,e){this.commands[t]?this.commands[t].push(e):this.commands[t]=[e]}remove(t,e){if(this.commands[t])if(!e)this.commands[t]=[],delete this.commands[t];else{let i=this.commands[t].find(n=>n===e);i!==-1&&this.commands[t].splice(i,1)}}addHistory(){if(this.mindMap.opt.readonly||this.isPause)return;this.mindMap.emit("beforeAddHistory");const t=this.history.length>0?this.history[this.activeHistoryIndex]:null,e=this.getCopyData(),i=JSON.stringify(e);t&&t===i||(this.emitDataUpdatesEvent(t,i),this.history=this.history.slice(0,this.activeHistoryIndex+1),this.history.push(i),this.history.length>this.mindMap.opt.maxHistoryCount&&this.history.shift(),this.activeHistoryIndex=this.history.length-1,this.mindMap.emit("data_change",e),this.mindMap.emit("back_forward",this.activeHistoryIndex,this.history.length))}back(t=1){if(!this.mindMap.opt.readonly&&this.activeHistoryIndex-t>=0){const e=this.history[this.activeHistoryIndex];this.activeHistoryIndex-=t,this.mindMap.emit("back_forward",this.activeHistoryIndex,this.history.length);const i=this.history[this.activeHistoryIndex],n=JSON.parse(i);return this.emitDataUpdatesEvent(e,i),n}}forward(t=1){if(this.mindMap.opt.readonly)return;let e=this.history.length;if(this.activeHistoryIndex+t<=e-1){const i=this.history[this.activeHistoryIndex];this.activeHistoryIndex+=t,this.mindMap.emit("back_forward",this.activeHistoryIndex,this.history.length);const n=this.history[this.activeHistoryIndex],r=JSON.parse(n);return this.emitDataUpdatesEvent(i,n),r}}getCopyData(){if(!this.mindMap.renderer.renderTree)return null;const t=wr({},this.mindMap.renderer.renderTree,!0);return t.smmVersion=sc.version,t}removeDataUid(t){t=le(t);let e=i=>{delete i.data.uid,i.children&&i.children.length>0&&i.children.forEach(n=>{e(n)})};return e(t),t}emitDataUpdatesEvent(t,e){try{const i="data_change_detail";if(this.mindMap.event.listenerCount(i)>0&&t&&e){const r=JSON.parse(t),o=JSON.parse(e),a=le(ms(r)),l=le(ms(o)),h=[],d=(c,f)=>(c.children&&c.children.length>0&&c.children.forEach((g,u)=>{c.children[u]=typeof g=="string"?f[g]:f[g.data.uid],d(c.children[u],f)}),c);Object.keys(l).forEach(c=>{a[c]?xn(a[c],l[c])||h.push({action:"update",oldData:d(a[c],a),data:d(l[c],l)}):h.push({action:"create",data:d(l[c],l)})}),Object.keys(a).forEach(c=>{l[c]||h.push({action:"delete",data:d(a[c],a)})}),this.mindMap.emit(i,h)}}catch(i){this.mindMap.opt.errorHandler(Ve.DATA_CHANGE_DETAIL_EVENT_ERROR,i)}}}class oc{constructor(){this.has={},this.queue=[],this.nextTick=sh(this.flush,this)}push(t,e){if(this.has[t]){this.replaceTask(t,e);return}this.has[t]=!0,this.queue.push({name:t,fn:e}),this.nextTick()}replaceTask(t,e){const i=this.queue.findIndex(n=>n.name===t);i!==-1&&(this.queue[i]={name:t,fn:e})}flush(){let t=this.queue.slice(0);this.queue=[],t.forEach(({name:e,fn:i})=>{this.has[e]=!1,i()})}}const Is={el:null,data:null,viewData:null,readonly:!1,layout:M.LAYOUT.LOGICAL_STRUCTURE,fishboneDeg:45,theme:"default",themeConfig:{},scaleRatio:.2,translateRatio:1,minZoomRatio:20,maxZoomRatio:400,customCheckIsTouchPad:null,mouseScaleCenterUseMousePosition:!0,maxTag:5,expandBtnSize:20,imgTextMargin:5,textContentMargin:2,customNoteContentShow:null,textAutoWrapWidth:500,customHandleMousewheel:null,mousewheelAction:M.MOUSE_WHEEL_ACTION.MOVE,mousewheelMoveStep:100,mousewheelZoomActionReverse:!0,defaultInsertSecondLevelNodeText:"二级节点",defaultInsertBelowSecondLevelNodeText:"分支主题",expandBtnStyle:{color:"#808080",fill:"#fff",fontSize:13,strokeColor:"#333333"},expandBtnIcon:{open:"",close:""},expandBtnNumHandler:null,isShowExpandNum:!0,enableShortcutOnlyWhenMouseInSvg:!0,customCheckEnableShortcut:null,initRootNodePosition:null,nodeTextEditZIndex:3e3,nodeNoteTooltipZIndex:3e3,isEndNodeTextEditOnClickOuter:!0,maxHistoryCount:500,alwaysShowExpandBtn:!1,notShowExpandBtn:!1,iconList:[],maxNodeCacheCount:1e3,fitPadding:50,enableCtrlKeyNodeSelection:!0,useLeftKeySelectionRightKeyDrag:!1,beforeTextEdit:null,isUseCustomNodeContent:!1,customCreateNodeContent:null,customInnerElsAppendTo:null,enableAutoEnterTextEditWhenKeydown:!1,autoEmptyTextWhenKeydownEnterEdit:!1,customHandleClipboardText:null,disableMouseWheelZoom:!1,errorHandler:(s,t)=>{console.error(s,t)},enableDblclickBackToRootNode:!1,hoverRectColor:"rgb(94, 200, 248)",hoverRectPadding:2,selectTextOnEnterEditText:!1,deleteNodeActive:!0,fit:!1,tagsColorMap:{},cooperateStyle:{avatarSize:22,fontSize:12},onlyOneEnableActiveNodeOnCooperate:!1,defaultGeneralizationText:"概要",handleIsSplitByWrapOnPasteCreateNewNode:null,addHistoryTime:100,isDisableDrag:!1,createNewNodeBehavior:M.CREATE_NEW_NODE_BEHAVIOR.DEFAULT,defaultNodeImage:"",isLimitMindMapInCanvas:!1,handleNodePasteImg:null,customCreateNodePath:null,customCreateNodePolygon:null,customTransformNodeLinePath:null,beforeShortcutRun:null,resetScaleOnMoveNodeToCenter:!1,createNodePrefixContent:null,createNodePostfixContent:null,disabledClipboard:!1,customHyperlinkJump:null,openPerformance:!1,performanceConfig:{time:250,padding:100,removeNodeWhenOutCanvas:!0},emptyTextMeasureHeightText:"abc123我和你",openRealtimeRenderOnNodeTextEdit:!1,mousedownEventPreventDefault:!1,onlyPasteTextWhenHasImgAndText:!0,enableDragModifyNodeWidth:!0,minNodeTextModifyWidth:20,maxNodeTextModifyWidth:-1,customHandleLine:null,addHistoryOnInit:!0,noteIcon:{icon:"",style:{}},hyperlinkIcon:{icon:"",style:{}},attachmentIcon:{icon:"",style:{}},isShowCreateChildBtnIcon:!0,quickCreateChildBtnIcon:{icon:"",style:{}},customQuickCreateChildBtnClick:null,addCustomContentToNode:null,enableInheritAncestorLineStyle:!0,selectTranslateStep:3,selectTranslateLimit:20,enableFreeDrag:!1,autoMoveWhenMouseInEdgeOnDrag:!0,dragMultiNodeRectConfig:{width:40,height:20,fill:"rgb(94, 200, 248)"},dragPlaceholderRectFill:"rgb(94, 200, 248)",dragPlaceholderLineConfig:{color:"rgb(94, 200, 248)",width:2},dragOpacityConfig:{cloneNodeOpacity:.5,beingDragNodeOpacity:.3},handleDragCloneNode:null,beforeDragEnd:null,beforeDragStart:null,watermarkConfig:{onlyExport:!1,text:"",lineSpacing:100,textSpacing:100,angle:30,textStyle:{color:"#999",opacity:.5,fontSize:14},belowNode:!1},exportPaddingX:10,exportPaddingY:10,resetCss:`
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
  `,minExportImgCanvasScale:2,addContentToHeader:null,addContentToFooter:null,handleBeingExportSvg:null,maxCanvasSize:16384,defaultAssociativeLineText:"关联",associativeLineIsAlwaysAboveNode:!0,associativeLineInitPointsPosition:{from:"",to:""},enableAdjustAssociativeLinePoints:!0,beforeAssociativeLineConnection:null,disableTouchZoom:!1,minTouchZoomScale:20,maxTouchZoomScale:-1,isLimitMindMapInCanvasWhenHasScrollbar:!0,isOnlySearchCurrentRenderNodes:!1,beforeCooperateUpdate:null,rainbowLinesConfig:{open:!1,colorsList:[]},demonstrateConfig:null,enableEditFormulaInRichTextEdit:!0,katexFontPath:"https://unpkg.com/katex@0.16.11/dist/",getKatexOutputType:null,transformRichTextOnEnterEdit:null,beforeHideRichTextEdit:null,outerFramePaddingX:10,outerFramePaddingY:10,defaultOuterFrameText:"外框",onlyPainterNodeCustomStyles:!1,beforeDeleteNodeImg:null,imgResizeBtnSize:25,minImgResizeWidth:50,minImgResizeHeight:50,maxImgResizeWidthInheritTheme:!1,maxImgResizeWidth:1/0,maxImgResizeHeight:1/0,customDeleteBtnInnerHTML:"",customResizeBtnInnerHTML:""};class ut{constructor(t={}){if(ut.instanceCount++,this.opt=this.handleOpt(Oi(Is,t)),this.opt.data=this.handleData(this.opt.data),this.el=this.opt.el,!this.el)throw new Error("缺少容器元素el");this.getElRectInfo(),this.initWidth=this.width,this.initHeight=this.height,this.cssEl=null,this.cssTextMap={},this.nodeInnerPrefixList=[],this.nodeInnerPostfixList=[],this.editNodeClassList=[],this.extendShapeList=[],this.initContainer(),this.initTheme(),this.initCache(),ut.pluginList.filter(e=>e.preload).forEach(e=>{this.initPlugin(e)}),this.event=new Ao({mindMap:this}),this.keyCommand=new ic({mindMap:this}),this.command=new rc({mindMap:this}),this.renderer=new ec({mindMap:this}),this.view=new Lo({mindMap:this}),this.batchExecution=new oc,ut.pluginList.filter(e=>!e.preload).forEach(e=>{this.initPlugin(e)}),this.addCss(),this.render(this.opt.fit?()=>this.view.fit():()=>{}),this.opt.addHistoryOnInit&&this.opt.data&&this.command.addHistory()}handleOpt(t){return is.includes(t.layout)||(t.layout=M.LAYOUT.LOGICAL_STRUCTURE),t.theme=t.theme&&De[t.theme]?t.theme:"default",t}handleData(t){return Me(t)||Object.keys(t).length<=0?null:(t=le(t||{}),t.data&&!t.data.expand&&(t.data.expand=!0),ei([t],!1,null,!0),t)}initContainer(){const{associativeLineIsAlwaysAboveNode:t}=this.opt;this.el.classList.add("smm-mind-map-container");const e=()=>{this.associativeLineDraw=this.draw.group(),this.associativeLineDraw.addClass("smm-associative-line-container")};this.svg=Tt().addTo(this.el).size(this.width,this.height),this.draw=this.svg.group(),this.draw.addClass("smm-container"),this.lineDraw=this.draw.group(),this.lineDraw.addClass("smm-line-container"),t||e(),this.nodeDraw=this.draw.group(),this.nodeDraw.addClass("smm-node-container"),t&&e(),this.otherDraw=this.draw.group(),this.otherDraw.addClass("smm-other-container")}clearDraw(){this.lineDraw.clear(),this.associativeLineDraw.clear(),this.nodeDraw.clear(),this.otherDraw.clear()}appendCss(t,e){this.cssTextMap[t]=e,this.removeCss(),this.addCss()}removeAppendCss(t){this.cssTextMap[t]&&(delete this.cssTextMap[t],this.removeCss(),this.addCss())}joinCss(){return To+Object.keys(this.cssTextMap).map(t=>this.cssTextMap[t]).join(`
`)}addCss(){this.cssEl=document.createElement("style"),this.cssEl.type="text/css",this.cssEl.innerHTML=this.joinCss(),document.head.appendChild(this.cssEl)}removeCss(){this.cssEl&&document.head.removeChild(this.cssEl)}checkEditNodeClassIndex(t){return this.editNodeClassList.findIndex(e=>e===t)}addEditNodeClass(t){this.checkEditNodeClassIndex(t)===-1&&this.editNodeClassList.push(t)}deleteEditNodeClass(t){const e=this.checkEditNodeClassIndex(t);e!==-1&&this.editNodeClassList.splice(e,1)}render(t,e=""){this.initTheme(),this.renderer.render(t,e)}reRender(t,e=""){this.renderer.reRender=!0,this.renderer.clearCache(),this.clearDraw(),this.render(t,e)}getElRectInfo(){if(this.elRect=this.el.getBoundingClientRect(),this.width=this.elRect.width,this.height=this.elRect.height,this.width<=0||this.height<=0)throw new Error("容器元素el的宽高不能为0")}resize(){const t=this.width,e=this.height;this.getElRectInfo(),this.svg.size(this.width,this.height),(t!==this.width||e!==this.height)&&(this.demonstrate?this.demonstrate.isInDemonstrate||this.render():this.render()),this.emit("resize")}on(t,e){this.event.on(t,e)}emit(t,...e){this.event.emit(t,...e)}off(t,e){this.event.off(t,e)}initCache(){this.commonCaches={measureCustomNodeContentSizeEl:null,measureRichtextNodeTextSizeEl:null}}initTheme(){this.themeConfig=Nr(De[this.opt.theme]||De.default,this.opt.themeConfig),ae.setBackgroundStyle(this.el,this.themeConfig)}setTheme(t,e=!1){this.execCommand("CLEAR_ACTIVE_NODE"),this.opt.theme=t,e||this.render(null,M.CHANGE_THEME),this.emit("view_theme_change",t)}getTheme(){return this.opt.theme}setThemeConfig(t,e=!1){const i=uh(this.themeConfig,t);if(this.opt.themeConfig=t,!e){const n=Ql(i);this.render(null,n?"":M.CHANGE_THEME)}}getCustomThemeConfig(){return this.opt.themeConfig}getThemeConfig(t){return t===void 0?this.themeConfig:this.themeConfig[t]}getConfig(t){return t===void 0?this.opt:this.opt[t]}updateConfig(t={}){this.emit("before_update_config",this.opt);const e={...this.opt};this.opt=this.handleOpt(Oi.all([Is,this.opt,t])),this.emit("after_update_config",this.opt,e)}getLayout(){return this.opt.layout}setLayout(t,e=!1){is.includes(t)||(t=M.LAYOUT.LOGICAL_STRUCTURE),this.opt.layout=t,this.view.reset(),this.renderer.setLayout(),e||this.render(null,M.CHANGE_LAYOUT),this.emit("layout_change",t)}execCommand(...t){this.command.exec(...t)}updateData(t){t=this.handleData(t),this.emit("before_update_data",t),this.renderer.setData(t),this.render(),this.command.addHistory(),this.emit("update_data",t)}setData(t){t=this.handleData(t),this.emit("before_set_data",t),this.opt.data=t,this.execCommand("CLEAR_ACTIVE_NODE"),this.command.clearHistory(),this.command.addHistory(),this.renderer.setData(t),this.reRender(),this.emit("set_data",t)}setFullData(t){t.root&&this.setData(t.root),t.layout&&this.setLayout(t.layout),t.theme&&(t.theme.template&&this.setTheme(t.theme.template),t.theme.config&&this.setThemeConfig(t.theme.config)),t.view&&this.view.setTransformData(t.view)}getData(t){let e=this.command.getCopyData(),i={};return t?i={layout:this.getLayout(),root:e,theme:{template:this.getTheme(),config:this.getCustomThemeConfig()},view:this.view.getTransformData()}:i=e,le(i)}async export(...t){try{if(!this.doExport)throw new Error("请注册Export插件！");return await this.doExport.export(...t)}catch(e){this.opt.errorHandler(Ve.EXPORT_ERROR,e)}}toPos(t,e){return{x:t-this.elRect.left,y:e-this.elRect.top}}setMode(t){if(![M.MODE.READONLY,M.MODE.EDIT].includes(t))return;const e=t===M.MODE.READONLY;e!==this.opt.readonly&&(e&&(this.renderer.textEdit.isShowTextEdit()&&(this.renderer.textEdit.hideEditTextBox(),this.command.originAddHistory()),this.execCommand("CLEAR_ACTIVE_NODE")),this.opt.readonly=e,!e&&this.command.history.length<=0&&this.command.originAddHistory(),this.emit("mode_change",t))}getSvgData({paddingX:t=0,paddingY:e=0,ignoreWatermark:i=!1,addContentToHeader:n,addContentToFooter:r,node:o}={}){const{watermarkConfig:a,openPerformance:l}=this.opt;l&&this.renderer.forceLoadNode(o);const{cssTextList:h,header:d,headerHeight:c,footer:f,footerHeight:g}=_h({addContentToHeader:n,addContentToFooter:r}),u=this.svg,m=this.draw,p=u.width(),x=u.height(),E=m.transform(),_=this.elRect;m.scale(1/E.scaleX,1/E.scaleY);const T=m.rbox();let C=null;o&&(C=Mh(o,T.x,T.y,t,e));const L=0;T.width+=t*2,T.height+=e*2+L+c+g,m.translate(t,e),u.size(T.width,T.height),m.translate(-T.x+_.left,-T.y+_.top);let F=u.clone();const W=this.watermark&&this.watermark.hasWatermark();if(!i&&W){this.watermark.isInExport=!0;const{onlyExport:ct}=a;T.width>p||T.height>x?(this.width=T.width,this.height=T.height,this.watermark.onResize(),F=u.clone(),this.width=p,this.height=x,this.watermark.onResize()):ct&&(this.watermark.onResize(),F=u.clone()),ct&&this.watermark.clear(),this.watermark.isInExport=!1}[this.joinCss(),...h].forEach(ct=>{F.add(Tt(`<style>${ct}</style>`))}),d&&c>0&&(F.findOne(".smm-container").translate(0,c),d.width(T.width),d.y(e),F.add(d,0)),f&&g>0&&(f.width(T.width),f.y(T.height-e-g),F.add(f));const X=u.find("defs"),Mt=F.find("defs");return X.forEach((ct,Lt)=>{const bt=Mt[Lt];if(!bt)return;const Dt=ct.children(),A=bt.children();for(let O=0;O<Dt.length;O++){const H=Dt[O],rt=A[O];H&&rt&&rt.attr("id",H.attr("id"))}}),u.size(p,x),m.transform(E),{svg:F,svgHTML:F.svg(),clipData:C,rect:{...T,ratio:T.width/T.height},origWidth:p,origHeight:x,scaleX:E.scaleX,scaleY:E.scaleY}}addShape(t){!t||this.extendShapeList.find(i=>i.name===t.name)||this.extendShapeList.push(t)}removeShape(t){const e=this.extendShapeList.findIndex(i=>i.name===t);e!==-1&&this.extendShapeList.splice(e,1)}getSvgObjects(){return{SVG:Tt,G:Bt,Rect:Jt}}addPlugin(t,e){ut.hasPlugin(t)===-1&&ut.usePlugin(t,e),this.initPlugin(t)}removePlugin(t){let e=ut.hasPlugin(t);e!==-1&&(ut.pluginList.splice(e,1),this[t.instanceName]&&(this[t.instanceName].beforePluginRemove&&this[t.instanceName].beforePluginRemove(),delete this[t.instanceName]))}initPlugin(t){this[t.instanceName]||(this[t.instanceName]=new t({mindMap:this,pluginOpt:t.pluginOpt}))}destroy(){this.emit("beforeDestroy"),this.renderer.textEdit.hideEditTextBox(),this.renderer.textEdit.removeTextEditEl(),[...ut.pluginList].forEach(t=>{this[t.instanceName]&&this[t.instanceName].beforePluginDestroy&&this[t.instanceName].beforePluginDestroy(),this[t.instanceName]=null}),this.event.unbind(),this.svg.remove(),ae.removeBackgroundStyle(this.el),this.el.classList.remove("smm-mind-map-container"),this.el.innerHTML="",this.el=null,this.removeCss(),ut.instanceCount--}}let Mn=[];ut.extendNodeDataNoStylePropList=(s=[])=>{Mn.push(...s),Ai.push(...s)};ut.resetNodeDataNoStylePropList=()=>{Mn.forEach(s=>{const t=Ai.findIndex(e=>e===s);t!==-1&&Ai.splice(t,1)}),Mn=[]};ut.pluginList=[];ut.usePlugin=(s,t={})=>(ut.hasPlugin(s)!==-1||(s.pluginOpt=t,ut.pluginList.push(s)),ut);ut.hasPlugin=s=>ut.pluginList.findIndex(t=>t===s);ut.instanceCount=0;ut.defineTheme=(s,t={})=>{if(De[s])return new Error("该主题名称已存在");De[s]=Nr(yr,t)};ut.removeTheme=s=>{De[s]&&(De[s]=null)};class ac{constructor(t){this.mindMap=t,this.autoMoveTimer=null}onMove(t,e,i=()=>{},n=()=>{}){i();let r=this.mindMap.opt.selectTranslateStep,o=this.mindMap.opt.selectTranslateLimit,a=0;t<=this.mindMap.elRect.left+o&&(n("left",r),this.mindMap.view.translateX(r),a++),t>=this.mindMap.elRect.right-o&&(n("right",r),this.mindMap.view.translateX(-r),a++),e<=this.mindMap.elRect.top+o&&(n("top",r),this.mindMap.view.translateY(r),a++),e>=this.mindMap.elRect.bottom-o&&(n("bottom",r),this.mindMap.view.translateY(-r),a++),a>0&&this.startAutoMove(t,e,i,n)}startAutoMove(t,e,i,n){this.autoMoveTimer=setTimeout(()=>{this.onMove(t,e,i,n)},20)}clearAutoMoveTimer(){clearTimeout(this.autoMoveTimer)}}class Lr{constructor({mindMap:t}){this.mindMap=t,this.rect=null,this.isMousedown=!1,this.mouseDownX=0,this.mouseDownY=0,this.mouseMoveX=0,this.mouseMoveY=0,this.isSelecting=!1,this.cacheActiveList=[],this.autoMove=new ac(t),this.bindEvent()}bindEvent(){this.onMousedown=this.onMousedown.bind(this),this.onMousemove=this.onMousemove.bind(this),this.onMouseup=this.onMouseup.bind(this),this.checkInNodes=Vn(this.checkInNodes,300,this),this.mindMap.on("mousedown",this.onMousedown),this.mindMap.on("mousemove",this.onMousemove),this.mindMap.on("mouseup",this.onMouseup),this.mindMap.on("node_mouseup",this.onMouseup)}unBindEvent(){this.mindMap.off("mousedown",this.onMousedown),this.mindMap.off("mousemove",this.onMousemove),this.mindMap.off("mouseup",this.onMouseup),this.mindMap.off("node_mouseup",this.onMouseup)}onMousedown(t){const{readonly:e,mousedownEventPreventDefault:i}=this.mindMap.opt;if(e)return;let{useLeftKeySelectionRightKeyDrag:n}=this.mindMap.opt;if(!(t.ctrlKey||t.metaKey)&&(n?t.which!==1:t.which!==3))return;i&&t.preventDefault(),this.isMousedown=!0,this.cacheActiveList=[...this.mindMap.renderer.activeNodeList];let{x:r,y:o}=this.mindMap.toPos(t.clientX,t.clientY);this.mouseDownX=r,this.mouseDownY=o,this.createRect(r,o)}onMousemove(t){if(this.mindMap.opt.readonly||!this.isMousedown)return;let{x:e,y:i}=this.mindMap.toPos(t.clientX,t.clientY);this.mouseMoveX=e,this.mouseMoveY=i,!(Math.abs(e-this.mouseDownX)<=10&&Math.abs(i-this.mouseDownY)<=10)&&(this.autoMove.clearAutoMoveTimer(),this.autoMove.onMove(t.clientX,t.clientY,()=>{this.isSelecting=!0,this.rect&&this.rect.plot([[this.mouseDownX,this.mouseDownY],[this.mouseMoveX,this.mouseDownY],[this.mouseMoveX,this.mouseMoveY],[this.mouseDownX,this.mouseMoveY]]),this.checkInNodes()},(n,r)=>{switch(n){case"left":this.mouseDownX+=r;break;case"top":this.mouseDownY+=r;break;case"right":this.mouseDownX-=r;break;case"bottom":this.mouseDownY-=r;break}}))}onMouseup(){this.mindMap.opt.readonly||this.isMousedown&&(this.checkTriggerNodeActiveEvent(),this.autoMove.clearAutoMoveTimer(),this.isMousedown=!1,this.cacheActiveList=[],this.rect&&this.rect.remove(),this.rect=null,setTimeout(()=>{this.isSelecting=!1},0))}checkTriggerNodeActiveEvent(){let t=this.cacheActiveList.length!==this.mindMap.renderer.activeNodeList.length,e=!1;if(!t)for(let i=0;i<this.cacheActiveList.length;i++){let n=this.cacheActiveList[i];if(!this.mindMap.renderer.activeNodeList.find(r=>r.getData("uid")===n.getData("uid"))){e=!0;break}}(t||e)&&this.mindMap.renderer.emitNodeActiveEvent()}createRect(t,e){this.rect&&this.rect.remove(),this.rect=this.mindMap.svg.polygon().stroke({color:"#0984e3"}).fill({color:"rgba(9,132,227,0.3)"}).plot([[t,e]])}checkInNodes(){let{scaleX:t,scaleY:e,translateX:i,translateY:n}=this.mindMap.draw.transform(),r=Math.min(this.mouseDownX,this.mouseMoveX),o=Math.min(this.mouseDownY,this.mouseMoveY),a=Math.max(this.mouseDownX,this.mouseMoveX),l=Math.max(this.mouseDownY,this.mouseMoveY);const h=d=>{let{left:c,top:f,width:g,height:u}=d,m=(c+g)*t+i,p=(f+u)*e+n;if(c=c*t+i,f=f*e+n,gh(r,a,o,l,c,m,f,p)){if(d.getData("isActive"))return;this.mindMap.renderer.addNodeToActiveList(d),this.mindMap.renderer.emitNodeActiveEvent()}else if(d.getData("isActive")){if(!d.getData("isActive"))return;this.mindMap.renderer.removeNodeFromActiveList(d),this.mindMap.renderer.emitNodeActiveEvent()}};xr(this.mindMap.renderer.root,d=>{h(d),d._generalizationList&&d._generalizationList.length>0&&d._generalizationList.forEach(c=>{h(c.generalizationNode)})})}hasSelectRange(){return this.isSelecting}beforePluginRemove(){this.unBindEvent()}beforePluginDestroy(){this.unBindEvent()}}Lr.instanceName="select";class br{constructor(){this.listeners=[]}parseMarkdownToMindmap(t,e="未命名文档",i={}){const{showContentAsNodes:n=!0}=i;if(!t)return this.createEmptyMindmap(e);const r=t.split(`
`);let o=null;const a=[];let l=[],h=!1;return r.forEach(d=>{const c=d.trim();if(!c){l.push("");return}const f=c.match(/^(#+)\s+(.+)$/);if(f){const g=f[1].length,u=Math.min(g,6),m=f[2],p=g>6?`${"  ".repeat(g-6)}${m}`:m;a.length>0&&(a[a.length-1].content=l.join(`
`).trim()),l=[];const x={id:tt(),title:p,level:u,rawLevel:g,children:[],content:"",expanded:!0,x:0,y:0};if(u===1&&!h)o=x,o.isRoot=!0,a.push(o),h=!0;else if(h){for(;a.length>0&&a[a.length-1].level>=u;)a.pop();a.length===0?o?(o.children.push(x),a.push(o,x)):(o=x,o.isRoot=!0,a.push(o)):(a[a.length-1].children.push(x),a.push(x))}else o=x,o.isRoot=!0,a.push(o),h=!0}else l.push(d)}),a.length>0&&(a[a.length-1].content=l.join(`
`).trim()),o||(o=this.createEmptyMindmap(e)),n&&this.convertContentToChildNodes(o),o}convertContentToChildNodes(t){if(t){if(t.content&&t.content.trim()){const e=this.parseContentToNodes(t.content,t.level,!1);if(e.length>0){const i=e.map(n=>({...n,level:t.level+1}));t.children=[...i,...t.children],t.content=""}}t.children&&t.children.length>0&&t.children.forEach(e=>this.convertContentToChildNodes(e))}}parseContentToNodes(t,e,i=!1){if(!t||!t.trim())return[];const n=[],r=t.split(`
`);let o=[];if(r.forEach(a=>{const l=a.trim();if(l){const h=l.match(/^(#+)\s+(.+)$/);if(h){if(o.length>0){const g=o.join(" ").trim();g&&n.push(this.createContentOrTitleNode(g,e,!0)),o=[]}const d=h[1].length,c=h[2];let f;i?f=e+1:(f=Math.min(d,6),f=Math.max(f,e+1)),n.push({id:tt(),title:c,level:f,rawLevel:d,children:[],content:"",expanded:!0,isTitle:!0})}else if(l.startsWith("- ")||l.startsWith("* ")||l.startsWith("+ ")){if(o.length>0){const c=o.join(" ").trim();c&&n.push(this.createContentOrTitleNode(c,e,i)),o=[]}const d=l.replace(/^[-*+]\s+/,"").trim();d&&n.push(this.createContentOrTitleNode(d,e,!0))}else o.push(l)}else if(o.length>0){const h=o.join(" ").trim();h&&n.push(this.createContentOrTitleNode(h,e,i)),o=[]}}),o.length>0){const a=o.join(" ").trim();a&&n.push(this.createContentOrTitleNode(a,e,i))}return n}createContentOrTitleNode(t,e,i=!1){return{id:tt(),title:t,level:i?e:e+1,children:[],content:"",expanded:!0,isContent:i,isTitle:!i}}separateContentAndTitleNodes(t){const e=[],i=[];return t.forEach(n=>{n.isContent?i.push(n):e.push(n)}),{titleNodes:e,contentNodes:i}}createEmptyMindmap(t){return{id:tt(),title:t||"新建思维导图",level:1,children:[],content:"",expanded:!0,isRoot:!0}}convertMindmapToMarkdown(t){return t?this.convertNodeToMarkdown(t,0):""}convertNodeToMarkdown(t,e=0,i=!1){let n="";const r=i||t.isContent||this.isContentNode(t,e);if(t.isRoot||e===0){const o=t.level||1,a="#".repeat(o);if(n+=`${a} ${t.title}

`,t.content&&(n+=`${t.content}

`),t.children){const{titleNodes:l,contentNodes:h}=this.separateContentNodes(t.children);h.length>0&&h.forEach(d=>{n+=`${d.title}

`}),l.forEach(d=>{n+=this.convertNodeToMarkdown(d,o,!1)})}}else if(r)n+=`${t.title}

`;else{const o="#".repeat(e+1);if(n+=`${o} ${t.title}

`,t.content&&(n+=`${t.content}

`),t.children){const{titleNodes:a,contentNodes:l}=this.separateContentNodes(t.children);l.length>0&&l.forEach(h=>{n+=`${h.title}

`}),a.forEach(h=>{n+=this.convertNodeToMarkdown(h,e+1,!1)})}}return n}isContentNode(t,e){if(!t.children||t.children.length===0){const i=t.title.trim();if(i.length>50||i.includes("。")||i.includes(".")||i.split(/[。.!！?？]/).length>2)return!0}return!1}separateContentNodes(t){const e=[],i=[];return t.forEach(n=>{this.isContentNode(n,0)?i.push(n):e.push(n)}),{titleNodes:e,contentNodes:i}}getNodePath(t,e){const i=[],n=(r,o,a)=>{if(r.id===o.id)return i.push(...a,r.title),!0;if(r.children){for(const l of r.children)if(n(l,o,[...a,r.title]))return!0}return!1};return n(e,t,[]),i.join(" > ")}getNodeContext(t,e){const i=this.getNodePath(t,e),n=this.getSiblings(t,e),r=this.getParentNode(t,e);return{path:i,currentTitle:t.title,currentContent:t.content||"",level:t.level,siblings:n.map(o=>o.title),parentTitle:r?r.title:"",parentContent:r&&r.content||"",existingChildren:t.children?t.children.map(o=>o.title):[]}}getSiblings(t,e){const i=this.getParentNode(t,e);return i?i.children.filter(n=>n.id!==t.id):[]}getParentNode(t,e){const i=n=>{if(n.children)for(const r of n.children){if(r.id===t.id)return n;const o=i(r);if(o)return o}return null};return i(e)}applyAIGeneratedContent({node:t,type:e,content:i,childrenCount:n,mindmapData:r}){if(!i||!t)return!1;try{switch(e){case"expand":return this.expandNodeContent(t,i);case"children":return this.generateChildNodes(t,i,n);case"related":return this.generateRelatedContent(t,i,r);default:return console.warn("未知的生成类型:",e),!1}}catch(o){return console.error("应用AI生成内容失败:",o),!1}}expandNodeContent(t,e){const i=t.content||"";return t.content=i+(i?`

`:"")+e.trim(),!0}generateChildNodes(t,e,i=5){try{const r=this.parseAIGeneratedNodes(e,t.level+1).slice(0,i);return t.children||(t.children=[]),t.children.push(...r),r.length>0}catch(n){return console.error("生成子节点失败:",n),!1}}parseAIGeneratedNodes(t,e){const i=[],n=t.split(`
`).filter(r=>r.trim());for(const r of n){const o=r.trim();if(!o)continue;let a="",l="";const h=o.match(/^(#+)\s+(.+)$/);if(h){const d=h[1].length;if(a=h[2],d>6){const c=d-6;a=`${"  ".repeat(c)}${a}`,e=Math.min(e+(d-1),6)}}else{const d=o.indexOf("：")!==-1?o.indexOf("："):o.indexOf(":");if(d!==-1){const c=o.substring(0,d).trim(),f=o.substring(d+1).trim();a=c.replace(/^[\d\.\-\*\+\s]+/,"").trim(),l=f}else a=o.replace(/^[\d\.\-\*\+\s]+/,"").trim()}if(a){const d={id:tt(),title:a,level:e,children:[],content:l,expanded:!0,x:0,y:0};i.push(d)}}return i}generateRelatedContent(t,e,i){const n=this.findParentNode(i,t.id);if(!n)return console.warn("无法找到父节点"),!1;const r={id:tt(),title:`${t.title} - 相关内容`,level:t.level,children:[],content:e.trim(),expanded:!0,x:0,y:0};return n.children||(n.children=[]),n.children.push(r),!0}findParentNode(t,e){if(!t.children)return null;for(const i of t.children){if(i.id===e)return t;const n=this.findParentNode(i,e);if(n)return n}return null}findNode(t,e){if(t.id===e)return t;if(t.children)for(const i of t.children){const n=this.findNode(i,e);if(n)return n}return null}cloneMindmapData(t){return JSON.parse(JSON.stringify(t))}validateMindmapData(t){return!t||typeof t!="object"||!t.id||!t.title||t.children&&!Array.isArray(t.children)?!1:t.children?t.children.every(e=>this.validateMindmapData(e)):!0}}const lc={class:"mindmap-toolbar"},hc={class:"toolbar-left"},dc={class:"toolbar-right"},cc={class:"toolbar-item"},uc={class:"toolbar-item"},fc=["value"],pc={__name:"MindmapCanvas",props:{mindmapData:{type:Object,default:null},fontSize:{type:Number,default:14},isDarkTheme:{type:Boolean,default:!1},availableModels:{type:Array,default:()=>[]},selectedModel:{type:String,default:""}},emits:["node-click","ai-generate","ai-generate-request","markdown-change","update:selectedModel"],setup(s,{expose:t,emit:e}){ut.usePlugin(Lr);const i=s,n=e,r=U(null),o=U(null),a=U(null),l=U("mindMap"),h=new br;U(!1);const d=U(null),c=U(!1),f=U(!1),g=U(null),u=U(!1),m=U(null),p=U({show:!1,x:0,y:0,type:"",node:null});gt(()=>{if(!p.value.node)return!1;const y=p.value.node,v=y.children&&y.children.length||y._children&&y._children.length||y.nodeData?.children&&y.nodeData.children.length||y.nodeData?.data?.children&&y.nodeData.data.children.length||0;return console.log("hasChildren检查:",{nodeText:y.nodeData?.data?.text,childrenCount:v,hasChildren:v>0}),v>0});const x=gt({get:()=>i.selectedModel,set:y=>n("update:selectedModel",y)});Nt(()=>i.mindmapData,y=>{if(f.value){f.value=!1;return}if(y&&a.value){console.log("外部数据变化，更新思维导图");const v=a.value.view.getTransformData(),D=a.value.view.scale;xe(()=>{b(y),setTimeout(()=>{if(a.value)try{a.value.view.setTransformData({...v,scale:D})}catch(I){console.warn("恢复视图状态失败:",I)}},100)})}},{deep:!0}),Nt(()=>i.isDarkTheme,y=>{a.value&&j(y)});let E=U(null);const _=y=>{y[0].isIntersecting&&!a.value&&o.value&&setTimeout(()=>{Et()&&Gt()},200)};Nt(()=>i.fontSize,y=>{a.value&&vt(y)});const T=()=>{a.value&&a.value.resize()},C=()=>{a.value&&Et()&&setTimeout(()=>{if(a.value&&Et())try{a.value.resize()}catch(y){console.error("思维导图resize失败:",y)}},100)},L=()=>{const y=document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement;u.value=!!y,a.value&&setTimeout(()=>{a.value.resize()},100)},F=(y,...v)=>{if(a.value)try{c.value=!0,a.value.execCommand(y,...v),setTimeout(()=>{c.value=!1},100)}catch(D){console.error("执行节点操作失败:",D),c.value=!1}},W=()=>{if(o.value){o.value.querySelectorAll('.smm-temp-node, .smm-node-animation, [class*="temp"], [class*="animation"], .smm-node[style*="position: absolute"]').forEach(I=>{I&&I.style&&(I.style.opacity="0",I.style.pointerEvents="none",setTimeout(()=>{if(I.parentNode)try{I.parentNode.removeChild(I)}catch{}},300))});const v=o.value.querySelectorAll(".smm-node"),D=new Map;v.forEach(I=>{const P=I.getBoundingClientRect(),k=`${Math.round(P.left)},${Math.round(P.top)}`;D.has(k)?P.left<50&&P.top<50&&(I.style.opacity="0",I.style.pointerEvents="none",setTimeout(()=>{if(I.parentNode)try{I.parentNode.removeChild(I)}catch{}},100)):D.set(k,I)})}};Sn(()=>{xe(()=>{setTimeout(()=>{o.value&&Et()?Gt():setTimeout(()=>{Gt()},200)},50)}),window.addEventListener("resize",T),o.value&&window.ResizeObserver&&(g.value=new ResizeObserver(C),g.value.observe(o.value)),document.addEventListener("fullscreenchange",L),document.addEventListener("webkitfullscreenchange",L),document.addEventListener("mozfullscreenchange",L),document.addEventListener("MSFullscreenChange",L),document.addEventListener("click",te),r.value&&window.IntersectionObserver&&(E.value=new IntersectionObserver(_,{threshold:.1}),E.value.observe(r.value))}),Ln(()=>{window.removeEventListener("resize",T),document.removeEventListener("click",te),document.removeEventListener("fullscreenchange",L),document.removeEventListener("webkitfullscreenchange",L),document.removeEventListener("mozfullscreenchange",L),document.removeEventListener("MSFullscreenChange",L),g.value&&(g.value.disconnect(),g.value=null),E.value&&(E.value.disconnect(),E.value=null),d.value&&(clearInterval(d.value),d.value=null),ge(),W(),se()});const X=()=>{a.value&&a.value.execCommand("EXPAND_ALL")},Mt=()=>{a.value&&a.value.execCommand("UNEXPAND_ALL")},ct=()=>{a.value&&a.value.view.reset()},Lt=()=>{a.value&&(a.value.view.reset(),setTimeout(()=>{a.value&&a.value.view.fit()},100))},bt=()=>{a.value&&a.value.view.enlarge()},Dt=()=>{a.value&&a.value.view.narrow()},A=async()=>{try{if(u.value)document.exitFullscreen?await document.exitFullscreen():document.webkitExitFullscreen?await document.webkitExitFullscreen():document.mozCancelFullScreen?await document.mozCancelFullScreen():document.msExitFullscreen&&await document.msExitFullscreen();else{const y=r.value;if(y.requestFullscreen)await y.requestFullscreen();else if(y.webkitRequestFullscreen)await y.webkitRequestFullscreen();else if(y.mozRequestFullScreen)await y.mozRequestFullScreen();else if(y.msRequestFullscreen)await y.msRequestFullscreen();else{$.warning("您的浏览器不支持全屏功能");return}}}catch(y){console.error("全屏切换失败:",y),$.error("全屏切换失败")}},O=y=>{const v=y.target.value;H(v)},H=y=>{l.value=y,a.value&&a.value.setLayout(y)},rt=y=>{if(!(!y||!a.value))try{const v=D=>{D.children&&D.children.length>0&&(D.showChildren(),D.children.forEach(I=>{v(I)}))};v(y),console.log("展开所有下级节点完成")}catch(v){console.error("展开节点失败:",v)}},kt=y=>{if(!(!y||!a.value))try{const v=D=>{D.children&&D.children.length>0&&(D.children.forEach(I=>{v(I)}),D.hideChildren())};v(y),console.log("收起所有下级节点完成")}catch(v){console.error("收起节点失败:",v)}},Et=()=>{if(!o.value)return!1;try{const y=o.value.getBoundingClientRect(),v=y.width>=100&&y.height>=100;return v||console.debug("容器尺寸检查:",{width:y.width,height:y.height}),v}catch(y){return console.warn("获取容器尺寸失败:",y),!1}},se=()=>{if(ge(),a.value)try{a.value.off(),o.value&&(o.value.innerHTML=""),a.value.destroy(),a.value=null,console.log("思维导图实例已清理")}catch(y){console.error("清理思维导图实例失败:",y),a.value=null,o.value&&(o.value.innerHTML="")}},Gt=()=>{if(!o.value){console.warn("思维导图容器不存在");return}if(se(),!Et()){setTimeout(()=>{Et()?Gt():setTimeout(()=>{Et()?Gt():console.warn("容器尺寸检查失败，但思维导图可能仍能正常显示")},200)},100);return}try{let y=null;i.mindmapData&&(y=q(i.mindmapData),console.log("用户数据转换结果:",y)),y||(y={data:{text:"根节点"},children:[]},console.log("使用官方标准默认数据:",y)),console.log("思维导图初始化，容器元素:",o.value);const v={exists:!!o.value,width:o.value.offsetWidth,height:o.value.offsetHeight,clientWidth:o.value.clientWidth,clientHeight:o.value.clientHeight,rect:o.value.getBoundingClientRect(),style:{display:getComputedStyle(o.value).display,visibility:getComputedStyle(o.value).visibility,position:getComputedStyle(o.value).position}};if(console.log("容器详细信息:",v),(v.width===0||v.height===0)&&(console.error("容器尺寸为0，强制设置尺寸"),o.value.style.width="100%",o.value.style.height="100%",o.value.style.minWidth="800px",o.value.style.minHeight="600px",setTimeout(()=>{console.log("强制设置后的容器尺寸:",{width:o.value.offsetWidth,height:o.value.offsetHeight})},100)),a.value=new ut({el:o.value,data:y,layout:l.value,theme:"default",hoverRectColor:"rgba(255, 165, 0, 0.9)",hoverRectPadding:3,enableCtrlKeyNodeSelection:!0,useLeftKeySelectionRightKeyDrag:!1,themeConfig:{second:{},node:{}},viewConfig:{enableScale:!0,scaleRange:[.1,2]},enableFreeDrag:!0,enableNodeRichText:!1,watermarkConfig:{show:!1},keyboardConfig:{enable:!0},nodeTextEditZIndex:1e3,nodeNoteTooltipZIndex:1e3,enableAutoEnterTextEditWhenKeydown:!0,enableDblclickBackToRootNode:!1,resetScaleOnMoveNodeToCenter:!1,fit:!1,isLimitMindMapInCanvas:!1,layoutConfig:{nodeSpacing:50,rankSpacing:100,secondNodeSpacing:40,secondNodeOffset:20}}),console.log("MindMap实例创建完成:",a.value),console.log("渲染器:",a.value.renderer),console.log("视图:",a.value.view),!a.value){console.error("MindMap实例创建失败");return}if(!a.value.renderer){console.error("思维导图渲染器未正确初始化");return}console.log("思维导图实例创建成功"),Ce(),Y(),Ee(),i.mindmapData&&b(i.mindmapData),setTimeout(()=>{a.value&&Wt()},300),setTimeout(()=>{if(!a.value){console.error("思维导图实例丢失");return}console.log("延迟检查渲染状态");const D=a.value.renderer?.nodeList||[];console.log("延迟检查节点数量:",D.length),D.length===0?console.warn("延迟检查仍然没有节点，等待node_tree_render_end事件"):console.log("延迟检查发现节点已渲染")},500)}catch(y){console.error("初始化思维导图失败:",y),console.error("容器信息:",{exists:!!o.value,rect:o.value?o.value.getBoundingClientRect():null}),y.message&&y.message.includes("宽高不能为0")?setTimeout(()=>{Et()?Gt():$.error("思维导图容器尺寸异常，请刷新页面重试")},1e3):$.error("初始化思维导图失败: "+y.message)}},Ee=()=>{},ge=()=>{m.value&&(clearInterval(m.value),m.value=null)},Ce=()=>{if(!a.value)return;a.value.on("node_click",P=>{const k=Ht(P);n("node-click",k)}),a.value.on("node_active",(P,k)=>{}),a.value.on("node_dblclick",P=>{console.log("双击节点:",P)});let y=null;a.value.on("data_change",P=>{console.log("思维导图数据发生变化");const k=800;y&&clearTimeout(y),y=setTimeout(()=>{Wt()},k)}),a.value.on("node_tree_render_end",()=>{}),a.value.on("node_contextmenu",(P,k)=>{ce(P,"node",k)});let v=0,D=0,I=!1;a.value.on("svg_mousedown",P=>{P.which===3&&(v=P.clientX,D=P.clientY,I=!0)}),a.value.on("mouseup",P=>{I&&(I=!1,!(Math.abs(v-P.clientX)>3||Math.abs(D-P.clientY)>3)&&ce(P,"canvas"))}),a.value.on("node_click",()=>{te()}),a.value.on("draw_click",()=>{te()}),a.value.on("expand_btn_click",te)},Y=()=>{if(a.value)try{a.value.keyCommand.addShortcut("Control+Enter",()=>{a.value.renderer.root&&a.value.renderer.setRootNodeCenter()}),a.value.keyCommand.addShortcut("Control+i",()=>{Lt()}),a.value.keyCommand.addShortcut("Control+l",()=>{ct()}),a.value.keyCommand.addShortcut("Control+Shift+e",()=>{X()}),a.value.keyCommand.addShortcut("Control+Shift+c",()=>{Mt()}),a.value.keyCommand.addShortcut("Shift+Delete",()=>{const y=a.value.renderer.activeNodeList;if(y&&y.length>0){const v=y[0];xt(v)}else $.warning("请先选择一个节点")})}catch(y){console.error("绑定快捷键失败:",y)}},b=y=>{if(!(!a.value||!y))try{const v=q(y);a.value.setData(v),l.value&&a.value.setLayout(l.value)}catch(v){console.error("更新思维导图数据失败:",v)}},j=y=>{if(a.value)try{const v=y?{backgroundColor:"#1a1a1a",lineColor:"#888888",lineWidth:2,lineOpacity:.8,root:{fillColor:"#409eff",color:"#ffffff",borderColor:"#409eff",borderWidth:2,fontWeight:"bold",fontSize:i.fontSize||16,fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Microsoft YaHei", sans-serif',hoverRectColor:"",hoverRectRadius:5},second:{fillColor:"#363636",color:"#ffffff",borderColor:"#888888",borderWidth:1.5,fontWeight:"500",fontSize:i.fontSize||14,fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Microsoft YaHei", sans-serif',hoverRectColor:"",hoverRectRadius:5},node:{fillColor:"#2d2d2d",color:"#ffffff",borderColor:"#666666",borderWidth:1,fontWeight:"400",fontSize:i.fontSize||14,fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Microsoft YaHei", sans-serif',hoverRectColor:"",hoverRectRadius:5}}:{backgroundColor:"#fafafa",lineColor:"#999999",lineWidth:2,lineOpacity:.8,root:{fillColor:"#409eff",color:"#ffffff",borderColor:"#409eff",borderWidth:2,fontWeight:"bold",fontSize:i.fontSize||16,fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Microsoft YaHei", sans-serif',hoverRectColor:"",hoverRectRadius:5},second:{fillColor:"#ffffff",color:"#333333",borderColor:"#cccccc",borderWidth:1,fontWeight:"500",fontSize:i.fontSize||14,fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Microsoft YaHei", sans-serif',hoverRectColor:"",hoverRectRadius:5},node:{fillColor:"#ffffff",color:"#333333",borderColor:"#dddddd",borderWidth:1,fontWeight:"400",fontSize:i.fontSize||14,fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Microsoft YaHei", sans-serif',hoverRectColor:"",hoverRectRadius:5}};a.value.setThemeConfig(v)}catch(v){console.error("更新主题失败:",v)}},vt=y=>{if(a.value)try{a.value.setThemeConfig({root:{fontSize:y},second:{fontSize:y},node:{fontSize:y}})}catch(v){console.error("更新字体大小失败:",v)}},q=y=>{if(!y)return null;const v=D=>{const I={data:{text:D.title||"未命名节点",uid:D.id}};return D.children&&D.children.length>0&&(I.children=D.children.map(P=>v(P))),I};return v(y)},$t=(y,v)=>{if(!y||!v)return null;const D=I=>{if(I.id===v)return I;if(I.children)for(const P of I.children){const k=D(P);if(k)return k}return null};return D(y)},Ht=y=>{if(!y)return null;let v=null;if(y.data)v=y.data;else if(y.nodeData&&y.nodeData.data)v=y.nodeData.data;else return console.warn("convertFromSimpleMindMapNode: 无法找到节点数据",y),null;const D=y.isRoot||!1,I=y.layerIndex||0;return{id:v.uid,title:v.text||"未命名节点",level:I,content:v.note||"",expanded:v.expand!==!1,isRoot:D,children:y.children?y.children.map(P=>Ht(P)):[]}},Yt=()=>{if(!a.value)return null;try{const y=a.value.getData(),v=S(y);return console.log("转换后的数据:",v),console.log("转换后数据检查:"),console.log("- 根节点ID:",v?.id),console.log("- 根节点标题:",v?.title),console.log("- 子节点数量:",v?.children?.length||0),v?.children&&v.children.length>0&&(console.log("- 第一个子节点ID:",v.children[0]?.id),console.log("- 第一个子节点标题:",v.children[0]?.title)),v}catch(y){return console.error("获取思维导图数据失败:",y),null}},S=y=>!y||!y.data?(console.warn("convertSimpleMindMapData: 无效节点",y),null):{id:y.data.uid,title:y.data.text||"未命名节点",content:y.data.note||"",children:y.children?y.children.map(D=>S(D)).filter(Boolean):[]},R=(y,v)=>{if(a.value)try{const D=B(y);D&&a.value.execCommand("INSERT_CHILD_NODE",D,v.title)}catch(D){console.error("添加节点失败:",D)}},it=(y,v)=>{if(!a.value||!v||v.length===0){console.warn("addAIGeneratedNodes: 参数无效",{mindMapInstance:!!a.value,nodeDataList:v,parentNodeId:y});return}try{console.log("开始查找父节点，ID:",y);const D=B(y);if(!D){console.error("找不到父节点:",y);return}console.log("找到父节点:",D),console.log(`为节点"${D.data?.text||"未知节点"}"添加${v.length}个AI生成的子节点`);const I=D;v.forEach((P,k)=>{setTimeout(()=>{try{if(!I||!a.value){console.error(`第${k+1}个AI节点添加失败: 父节点或思维导图实例无效`);return}a.value.renderer.setNodeActive(I,!0),a.value.execCommand("INSERT_CHILD_NODE",!1,[I],{text:P.title||P.text},[]),console.log(`添加AI主节点: ${P.title||P.text}`),P.children&&P.children.length>0&&setTimeout(()=>{const yt=I.children[I.children.length-1];yt&&(console.log(`为节点 "${P.title}" 添加 ${P.children.length} 个子节点`),St(yt,P.children))},200)}catch(yt){console.error(`添加第${k+1}个AI节点失败:`,yt),console.error("父节点状态:",I),console.error("节点数据:",P)}},k*300)})}catch(D){console.error("批量添加AI节点失败:",D)}},St=(y,v)=>{!y||!v||v.length===0||v.forEach((D,I)=>{setTimeout(()=>{try{if(!a.value){console.error("mindMapInstance 不存在，无法添加子节点");return}a.value.renderer.setNodeActive(y,!0),a.value.execCommand("INSERT_CHILD_NODE",!1,[y],{text:D.title||D.text},[]),console.log(`添加子节点: ${D.title||D.text}`),D.children&&D.children.length>0&&setTimeout(()=>{const P=y.children[y.children.length-1];P&&St(P,D.children)},200)}catch(P){console.error("添加子节点失败:",P)}},I*200)})},B=y=>{if(!a.value)return console.error("mindMapInstance 不存在"),null;if(!y)return console.error("nodeId 为空:",y),null;try{if(console.log("正在查找节点，ID:",y),console.log("mindMapInstance.value.renderer:",a.value.renderer),!a.value.renderer.root)return console.error("renderer.root 不存在，思维导图可能未正确初始化"),null;console.log("renderer.root 存在:",a.value.renderer.root);const v=a.value.renderer.findNodeByUid(y);if(v)console.log("找到节点:",v),console.log("找到节点的UID:",v.getData("uid"));else{console.warn(`未找到ID为 ${y} 的节点`);try{const D=a.value.renderer.root;console.log("根节点:",D),D&&(console.log("根节点数据:",D.nodeData?.data),console.log("根节点UID:",D.getData("uid")))}catch(D){console.error("调试信息获取失败:",D)}}return v}catch(v){return console.error("查找节点失败:",v),null}},mt=y=>{if(!y){$.warning("请选择要生成内容的节点");return}console.log("=== handleAIGenerate 调试 ==="),console.log("接收到的simple-mind-map节点:",y),console.log("节点数据结构:",y.nodeData?.data);const v=y.nodeData?.data?.uid||y.data?.uid,D=y.nodeData?.data?.text||y.data?.text;if(console.log("节点UID:",v),console.log("节点标题:",D),!v){console.error("无法获取节点UID"),$.error("无法获取节点信息");return}const I=Yt();console.log("完整思维导图数据:",I);const P=$t(I,v);if(console.log("查找到的真实节点:",P),!P){console.error("无法在思维导图数据中找到对应节点，UID:",v),$.error("无法获取节点信息");return}n("ai-generate-request",{node:P,mindmapData:I})},xt=y=>{if(!y){$.warning("请选择要删除子节点的节点");return}let v=0,D=null;if(y.children&&Array.isArray(y.children)?(D=y.children,v=y.children.length):y._children&&Array.isArray(y._children)?(D=y._children,v=y._children.length):y.nodeData?.children&&Array.isArray(y.nodeData.children)&&(D=y.nodeData.children,v=y.nodeData.children.length),console.log("检测到的子节点数组:",D),console.log("子节点数量:",v),v===0){$.info("该节点没有子节点");return}zs.confirm(`确定要删除"${y.nodeData?.data?.text||"未知节点"}"的所有 ${v} 个子节点吗？

注意：只删除子节点，当前节点会保留。

此操作不可撤销。`,"删除子节点确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning",confirmButtonClass:"el-button--danger"}).then(()=>{try{console.log("用户确认删除，开始执行删除操作");try{re(y,v)}catch(I){console.error("主要删除方法失败，尝试备用方法:",I),zt(y).catch(P=>{console.error("备用删除方法也失败:",P),$.error("所有删除方法都失败了，请刷新页面后重试")})}}catch(I){console.error("删除子节点失败:",I),$.error("删除子节点失败: "+I.message)}}).catch(()=>{console.log("用户取消删除子节点操作")})},re=(y,v)=>{if(!a.value||!y){console.error("mindMapInstance 或 parentNode 不存在"),$.error("删除失败：思维导图实例或节点无效");return}try{if(console.log("开始分批删除子节点，父节点:",y),console.log("预期删除数量:",v),!a.value.renderer||!a.value.renderer.setNodeActive)throw new Error("思维导图渲染器状态异常");const D=3,I=200;let P=0,k=0;const yt=()=>{const Vt=[...y.children||[]];if(Vt.length===0){$.success(`成功删除 ${P} 个子节点`),setTimeout(()=>{try{a.value.renderer.setNodeActive(y,!0),a.value.reRender()}catch(Ct){console.warn("重新渲染失败:",Ct);try{a.value.render()}catch(Rt){console.warn("备用渲染方法也失败:",Rt)}}},100);return}k++,console.log(`开始第${k}批删除，剩余节点数量:`,Vt.length);const Kt=Vt.slice(0,D);let At=0;Kt.forEach((Ct,Rt)=>{setTimeout(()=>{try{if(!y.children||!y.children.includes(Ct)){console.log(`节点已被删除，跳过: ${Ct.nodeData?.data?.text}`),At++,At===Kt.length&&setTimeout(yt,I);return}if(!Ct.nodeData||!Ct.nodeData.data){console.error(`节点数据不完整，跳过: ${Ct}`),At++,At===Kt.length&&setTimeout(yt,I);return}a.value.renderer.setNodeActive(Ct,!0),a.value.execCommand("REMOVE_NODE",[Ct]),P++,At++,console.log(`删除节点: ${Ct.nodeData?.data?.text} (总计: ${P})`),At===Kt.length&&setTimeout(yt,I)}catch(Wi){console.error("删除节点失败:",Wi),At++,At===Kt.length&&setTimeout(yt,I)}},Rt*50)})};yt()}catch(D){console.error("遍历删除子节点失败:",D),$.error("删除子节点失败: "+D.message);try{a.value.reRender()}catch(I){console.warn("错误恢复渲染失败:",I)}}},zt=y=>!y.children||y.children.length===0?($.info("没有子节点需要删除"),!0):new Promise(v=>{console.log("使用超级安全备用删除方法");let D=0;const I=y.children.length,P=()=>{const k=y.children||[];if(k.length===0){$.success(`超级安全删除完成，共删除 ${D} 个子节点`),setTimeout(()=>{try{a.value.renderer.setNodeActive(y,!0),a.value.reRender()}catch(Vt){console.warn("最终渲染失败:",Vt)}v(!0)},100);return}const yt=k[0];try{yt&&yt.nodeData?(console.log(`超级安全删除: ${yt.nodeData?.data?.text} (${D+1}/${I})`),a.value.renderer.setNodeActive(yt,!0),a.value.execCommand("REMOVE_NODE"),D++,setTimeout(P,100)):(console.warn("跳过无效节点"),setTimeout(P,50))}catch(Vt){console.error("删除单个节点失败:",Vt),setTimeout(P,100)}};P()}),Wt=()=>{Wt.timer&&clearTimeout(Wt.timer),Wt.timer=setTimeout(()=>{const y=Yt();if(y){const v=h.convertMindmapToMarkdown(y);f.value=!0,n("markdown-change",v)}else console.warn("无法获取思维导图数据")},150)},ce=(y,v,D=null)=>{p.value={show:!0,x:-9999,y:-9999,type:v,node:D},xe(()=>{const I=document.querySelector(".context-menu");if(!I){console.warn("右键菜单元素未找到");return}const P=I.getBoundingClientRect(),k=P.width||200,yt=P.height||300,Vt=window.innerWidth,Kt=window.innerHeight;let At=y.clientX+10,Ct=y.clientY+10;const Rt=20;At+k>Vt-Rt&&(At=y.clientX-k-10,At<Rt&&(At=Math.max(Rt,(Vt-k)/2))),Ct+yt>Kt-Rt&&(Ct=y.clientY-yt-10,Ct<Rt&&(Ct=Math.max(Rt,(Kt-yt)/2)));const Wi=Vt-k-Rt,Dr=Kt-yt-Rt;At=Math.max(Rt,Math.min(At,Wi)),Ct=Math.max(Rt,Math.min(Ct,Dr)),k>Vt-2*Rt&&(At=Rt),yt>Kt-2*Rt&&(Ct=Rt),p.value.x=At,p.value.y=Ct,console.debug("右键菜单位置:",{x:At,y:Ct,menuWidth:k,menuHeight:yt,viewportWidth:Vt,viewportHeight:Kt,mouseX:y.clientX,mouseY:y.clientY}),setTimeout(()=>{const jn=document.querySelector(".context-menu");if(jn){const Pe=jn.getBoundingClientRect();let Je=!1,yi=p.value.x,xi=p.value.y;Pe.right>Vt-10&&(yi=Vt-Pe.width-10,Je=!0),Pe.bottom>Kt-10&&(xi=Kt-Pe.height-10,Je=!0),Pe.left<10&&(yi=10,Je=!0),Pe.top<10&&(xi=10,Je=!0),Je&&(p.value.x=yi,p.value.y=xi,console.debug("调整后的菜单位置:",{x:yi,y:xi}))}},10)})},te=()=>{p.value={show:!1,x:0,y:0,type:"",node:null}},wt=y=>{const{type:v,node:D}=p.value;if(te(),v==="node"&&D)try{switch(a.value.renderer.setNodeActive(D,!0),y){case"INSERT_NODE":F("INSERT_NODE");break;case"INSERT_CHILD_NODE":F("INSERT_CHILD_NODE");break;case"INSERT_PARENT_NODE":F("INSERT_PARENT_NODE");break;case"EXPAND_ALL_CHILDREN":rt(D);break;case"COLLAPSE_ALL_CHILDREN":kt(D);break;case"UP_NODE":a.value.execCommand("UP_NODE");break;case"DOWN_NODE":a.value.execCommand("DOWN_NODE");break;case"REMOVE_NODE":const I=a.value.view.getTransformData(),P=a.value.view.scale;a.value.execCommand("REMOVE_NODE"),setTimeout(()=>{if(a.value)try{a.value.view.setTransformData({...I,scale:P})}catch(k){console.warn("恢复视图状态失败:",k)}},200);break;case"COPY_NODE":a.value.renderer.copy();break;case"CUT_NODE":a.value.renderer.cut();break;case"PASTE_NODE":a.value.renderer.paste();break;case"AI_GENERATE":mt(D);break;case"REMOVE_ALL_CHILDREN":xt(D);break;default:break}}catch(I){console.error("执行右键菜单操作失败:",I),$.error("操作失败: "+I.message)}else if(v==="canvas")try{switch(y){case"RETURN_CENTER":a.value.renderer.root&&a.value.renderer.setRootNodeCenter();break;case"EXPAND_ALL":a.value.execCommand("EXPAND_ALL");break;case"UNEXPAND_ALL":a.value.execCommand("UNEXPAND_ALL");break;case"PASTE_NODE":a.value.renderer.paste();break;case"SELECT_ALL":a.value.execCommand("SELECT_ALL");break;case"RESET_LAYOUT":ct();break;case"FIT_VIEW":Lt();break;default:break}}catch(I){console.error("执行画布操作失败:",I),$.error("操作失败: "+I.message)}};return t({expandAll:X,collapseAll:Mt,resetLayout:ct,fitView:Lt,zoomIn:bt,zoomOut:Dt,toggleFullscreen:A,isFullscreen:u,getCurrentMindmapData:Yt,addNode:R,addAIGeneratedNodes:it,expandNodeChildren:rt,collapseNodeChildren:kt,syncToMarkdown:Wt,showContextMenu:ce,hideContextMenu:te,cleanupTempNodes:W,initMindmap:Gt,destroyMindmapInstance:se}),(y,v)=>{const D=Bi,I=Fr,P=$r;return Z(),ht("div",{ref_key:"mindmapCanvas",ref:r,class:pe(["mindmap-canvas",{dark:s.isDarkTheme}])},[w("div",lc,[w("div",hc,[N(P,{size:"small"},{default:z(()=>[N(I,{content:"适应画布",placement:"bottom"},{default:z(()=>[N(D,{onClick:Lt,icon:Q(Pr)},null,8,["icon"])]),_:1}),N(I,{content:"放大",placement:"bottom"},{default:z(()=>[N(D,{onClick:bt,icon:Q(si)},null,8,["icon"])]),_:1}),N(I,{content:"缩小",placement:"bottom"},{default:z(()=>[N(D,{onClick:Dt,icon:Q(Br)},null,8,["icon"])]),_:1})]),_:1}),v[23]||(v[23]=w("div",{class:"toolbar-divider"},null,-1)),N(P,{size:"small"},{default:z(()=>[N(I,{content:"展开全部",placement:"bottom"},{default:z(()=>[N(D,{onClick:X,icon:Q(Hr)},null,8,["icon"])]),_:1}),N(I,{content:"折叠全部",placement:"bottom"},{default:z(()=>[N(D,{onClick:Mt,icon:Q(Ur)},null,8,["icon"])]),_:1}),N(I,{content:"重置布局",placement:"bottom"},{default:z(()=>[N(D,{onClick:ct,icon:Q(bi)},null,8,["icon"])]),_:1})]),_:1})]),w("div",dc,[w("div",cc,[v[25]||(v[25]=w("span",{class:"toolbar-label"},"布局:",-1)),ne(w("select",{"onUpdate:modelValue":v[0]||(v[0]=k=>l.value=k),onChange:O,class:"native-select layout-select"},v[24]||(v[24]=[Gr('<option value="mindMap" data-v-a8157cb4>思维导图</option><option value="logicalStructure" data-v-a8157cb4>逻辑结构</option><option value="organizationStructure" data-v-a8157cb4>组织结构</option><option value="timeline" data-v-a8157cb4>时间轴</option><option value="fishbone" data-v-a8157cb4>鱼骨图</option>',5)]),544),[[Kn,l.value]])]),w("div",uc,[v[26]||(v[26]=w("span",{class:"toolbar-label"},"AI模型:",-1)),ne(w("select",{"onUpdate:modelValue":v[1]||(v[1]=k=>x.value=k),class:"native-select model-select"},[(Z(!0),ht(ni,null,Tn(s.availableModels,k=>(Z(),ht("option",{key:k.id,value:k.id},dt(k.name),9,fc))),128))],512),[[Kn,x.value]])]),v[27]||(v[27]=w("div",{class:"toolbar-divider"},null,-1)),N(I,{content:u.value?"退出全屏":"进入全屏",placement:"bottom"},{default:z(()=>[N(D,{onClick:A,icon:u.value?Q(Yr):Q(Wr),size:"small",type:u.value?"primary":"default"},{default:z(()=>[st(dt(u.value?"退出全屏":"全屏"),1)]),_:1},8,["icon","type"])]),_:1},8,["content"])])]),w("div",{ref_key:"mindmapContainer",ref:o,class:"mindmap-container",style:nn({fontSize:`${s.fontSize}px`})},null,4),ne(w("div",{class:"context-menu",style:nn({left:`${p.value.x}px`,top:`${p.value.y}px`}),onClick:v[22]||(v[22]=ue(()=>{},["stop"]))},[p.value.type==="node"?(Z(),ht(ni,{key:0},[w("div",{class:"menu-item",onClick:v[2]||(v[2]=k=>wt("INSERT_NODE"))},v[28]||(v[28]=[w("span",null,"插入同级节点",-1),w("span",{class:"shortcut"},"Enter",-1)])),w("div",{class:"menu-item",onClick:v[3]||(v[3]=k=>wt("INSERT_CHILD_NODE"))},v[29]||(v[29]=[w("span",null,"插入子级节点",-1),w("span",{class:"shortcut"},"Tab",-1)])),w("div",{class:"menu-item",onClick:v[4]||(v[4]=k=>wt("INSERT_PARENT_NODE"))},v[30]||(v[30]=[w("span",null,"插入父节点",-1),w("span",{class:"shortcut"},"Shift + Tab",-1)])),v[41]||(v[41]=w("div",{class:"menu-divider"},null,-1)),w("div",{class:"menu-item",onClick:v[5]||(v[5]=k=>wt("EXPAND_ALL_CHILDREN"))},v[31]||(v[31]=[w("span",null,"展开所有下级节点",-1)])),w("div",{class:"menu-item",onClick:v[6]||(v[6]=k=>wt("COLLAPSE_ALL_CHILDREN"))},v[32]||(v[32]=[w("span",null,"收起所有下级节点",-1)])),v[42]||(v[42]=w("div",{class:"menu-divider"},null,-1)),w("div",{class:"menu-item",onClick:v[7]||(v[7]=k=>wt("UP_NODE"))},v[33]||(v[33]=[w("span",null,"上移节点",-1),w("span",{class:"shortcut"},"Ctrl + ↑",-1)])),w("div",{class:"menu-item",onClick:v[8]||(v[8]=k=>wt("DOWN_NODE"))},v[34]||(v[34]=[w("span",null,"下移节点",-1),w("span",{class:"shortcut"},"Ctrl + ↓",-1)])),v[43]||(v[43]=w("div",{class:"menu-divider"},null,-1)),w("div",{class:"menu-item",onClick:v[9]||(v[9]=k=>wt("COPY_NODE"))},v[35]||(v[35]=[w("span",null,"复制节点",-1),w("span",{class:"shortcut"},"Ctrl + C",-1)])),w("div",{class:"menu-item",onClick:v[10]||(v[10]=k=>wt("CUT_NODE"))},v[36]||(v[36]=[w("span",null,"剪切节点",-1),w("span",{class:"shortcut"},"Ctrl + X",-1)])),w("div",{class:"menu-item",onClick:v[11]||(v[11]=k=>wt("PASTE_NODE"))},v[37]||(v[37]=[w("span",null,"粘贴节点",-1),w("span",{class:"shortcut"},"Ctrl + V",-1)])),v[44]||(v[44]=w("div",{class:"menu-divider"},null,-1)),w("div",{class:"menu-item danger",onClick:v[12]||(v[12]=k=>wt("REMOVE_NODE"))},v[38]||(v[38]=[w("span",null,"删除节点",-1),w("span",{class:"shortcut"},"Delete",-1)])),v[45]||(v[45]=w("div",{class:"menu-divider"},null,-1)),w("div",{class:"menu-item ai-item",onClick:v[13]||(v[13]=k=>wt("AI_GENERATE"))},v[39]||(v[39]=[w("span",null,"🤖 AI生成",-1)])),w("div",{class:"menu-item ai-item",onClick:v[14]||(v[14]=k=>wt("AI_EXPAND_CONTENT"))},v[40]||(v[40]=[w("span",null,"📝 AI扩展内容",-1)]))],64)):p.value.type==="canvas"?(Z(),ht(ni,{key:1},[w("div",{class:"menu-item",onClick:v[15]||(v[15]=k=>wt("RETURN_CENTER"))},v[46]||(v[46]=[w("span",null,"回到中心",-1),w("span",{class:"shortcut"},"Ctrl + Enter",-1)])),w("div",{class:"menu-item",onClick:v[16]||(v[16]=k=>wt("EXPAND_ALL"))},v[47]||(v[47]=[w("span",null,"展开全部",-1)])),w("div",{class:"menu-item",onClick:v[17]||(v[17]=k=>wt("UNEXPAND_ALL"))},v[48]||(v[48]=[w("span",null,"收起全部",-1)])),v[53]||(v[53]=w("div",{class:"menu-divider"},null,-1)),w("div",{class:"menu-item",onClick:v[18]||(v[18]=k=>wt("PASTE_NODE"))},v[49]||(v[49]=[w("span",null,"粘贴节点",-1),w("span",{class:"shortcut"},"Ctrl + V",-1)])),w("div",{class:"menu-item",onClick:v[19]||(v[19]=k=>wt("SELECT_ALL"))},v[50]||(v[50]=[w("span",null,"全选",-1),w("span",{class:"shortcut"},"Ctrl + A",-1)])),v[54]||(v[54]=w("div",{class:"menu-divider"},null,-1)),w("div",{class:"menu-item",onClick:v[20]||(v[20]=k=>wt("RESET_LAYOUT"))},v[51]||(v[51]=[w("span",null,"重置布局",-1),w("span",{class:"shortcut"},"Ctrl + L",-1)])),w("div",{class:"menu-item",onClick:v[21]||(v[21]=k=>wt("FIT_VIEW"))},v[52]||(v[52]=[w("span",null,"适应画布",-1),w("span",{class:"shortcut"},"Ctrl + I",-1)]))],64)):It("",!0)],4),[[fe,p.value.show]])],2)}}},mc=ui(pc,[["__scopeId","data-v-a8157cb4"]]);class gc{constructor(){this.contentExtractors=new Map,this.contentFilters=[],this.setupExtractors(),this.setupFilters()}setupExtractors(){this.contentExtractors.set("claude",t=>typeof t=="string"?t:t.content?t.content:t.text?t.text:t.message?.content?t.message.content:JSON.stringify(t)),this.contentExtractors.set("gpt",t=>!t||t===null||t===void 0?"":typeof t=="string"?t:t.choices?.[0]?.message?.content?t.choices[0].message.content:t.choices?.[0]?.text?t.choices[0].text:t.content?t.content:JSON.stringify(t)),this.contentExtractors.set("qwen",t=>!t||t===null||t===void 0?"":typeof t=="string"?t:t.output?.text?t.output.text:t.text?t.text:t.content?t.content:JSON.stringify(t)),this.contentExtractors.set("ernie",t=>!t||t===null||t===void 0?"":typeof t=="string"?t:t.result?t.result:t.content?t.content:t.text?t.text:JSON.stringify(t)),this.contentExtractors.set("glm",t=>!t||t===null||t===void 0?"":typeof t=="string"?t:t.choices?.[0]?.message?.content?t.choices[0].message.content:t.data?.choices?.[0]?.content?t.data.choices[0].content:t.content?t.content:JSON.stringify(t)),this.contentExtractors.set("default",t=>{if(!t||t===null||t===void 0)return"";if(typeof t=="string")return t;const e=["content","text","result","output","message","choices[0].message.content","choices[0].text","data.content","response.content"];for(const i of e){const n=this.getNestedValue(t,i);if(n&&typeof n=="string")return n}return JSON.stringify(t)})}setupFilters(){this.contentFilters.push(t=>{const e=[/^<thinking>[\s\S]*?<\/thinking>\s*/gmi,/^思考过程：[\s\S]*?(?=##|\n\n|$)/gmi,/^让我思考一下[\s\S]*?(?=##|\n\n|$)/gmi,/^分析：[\s\S]*?(?=##|\n\n|$)/gmi,/^我需要[\s\S]*?(?=##|\n\n|$)/gmi];let i=t;return e.forEach(n=>{i=i.replace(n,"")}),i.trim()}),this.contentFilters.push(t=>{const e=[/^(好的|当然|我来|让我|我将|我会)[\s\S]*?(?=##|\n\n)/gmi,/^(抱歉|对不起|很抱歉)[\s\S]*?(?=##|\n\n)/gmi,/^(根据您的要求|按照您的需求)[\s\S]*?(?=##|\n\n)/gmi,/^以下是[\s\S]*?(?=##|\n\n)/gmi,/^这里是[\s\S]*?(?=##|\n\n)/gmi];let i=t;return e.forEach(n=>{i=i.replace(n,"")}),i.trim()}),this.contentFilters.push(t=>t.replace(/^(#{1,6}\s+.*?)\n+\1/gmi,"$1")),this.contentFilters.push(t=>t.replace(/^#+\s*/gm,e=>"## ").replace(/\n{3,}/g,`

`).trim()),this.contentFilters.push(t=>this.enhancedContentFilter(t))}enhancedContentFilter(t){if(!t||typeof t!="string")return t;let e=t;return[/^(我理解您的需求|根据您的要求|基于上下文信息)[\s\S]*?(?=##|$)/gmi,/^(为了更好地|为了帮助您|为了满足您的需求)[\s\S]*?(?=##|$)/gmi,/^(以下内容|下面的内容|这些内容)[\s\S]*?(?=##|$)/gmi].forEach(r=>{e=e.replace(r,"")}),e=e.replace(/^(##\s*[^#\n]*)\n+\1/gmi,"$1"),e=e.replace(/^##\s*$\n*/gm,""),e=e.replace(/^##\s+\n+(?=##)/gm,""),e=e.replace(/([。！？])\s*([^#\n])/g,`$1

$2`),[/\n*希望以上[内容建议信息].*$/gmi,/\n*如果您需要.*$/gmi,/\n*请根据实际情况.*$/gmi,/\n*以上仅供参考.*$/gmi].forEach(r=>{e=e.replace(r,"")}),e.trim()}processResponse(t,e="default"){try{let n=(this.contentExtractors.get(e)||this.contentExtractors.get("default"))(t);for(const o of this.contentFilters)n=o(n);const r=this.assessContentQuality(n);return{content:n,quality:r,originalLength:typeof t=="string"?t.length:JSON.stringify(t).length,processedLength:n.length,compressionRatio:n.length/(typeof t=="string"?t.length:JSON.stringify(t).length)}}catch(i){return console.error("AI响应处理失败:",i),{content:typeof t=="string"?t:JSON.stringify(t),quality:{score:0,issues:["处理失败"]},error:i.message}}}assessContentQuality(t){const e=[];let i=100;const n={};if(!t||t.trim().length===0)return e.push("内容为空"),i-=50,{score:0,issues:e,metrics:n};const r=t.match(/^#{1,6}\s+.+$/gm)||[],o=r.length;n.titleCount=o,o===0?(e.push("缺少标题结构"),i-=25):o<2&&(e.push("标题数量偏少"),i-=10);const a=t.length;n.wordCount=a,a<50?(e.push("内容过短"),i-=20):a<100&&(e.push("内容较短"),i-=10);const l=this.checkContentQuality(t);e.push(...l.issues),i-=l.penalty;const h=this.assessStructureQuality(t);n.structureScore=h,h<70&&(e.push("内容结构不完整"),i-=(100-h)*.2);const d=this.calculateUniqueness(t);return n.uniquenessRatio=d,d<.7&&(e.push("内容重复度较高"),i-=(.7-d)*50),{score:Math.max(0,Math.round(i)),issues:e,metrics:{...n,avgTitleLength:o>0?r.reduce((c,f)=>c+f.length,0)/o:0,contentDensity:this.calculateContentDensity(t)}}}checkContentQuality(t){const e=[];let i=0;(t.includes("<thinking>")||t.includes("让我思考")||t.includes("思考过程"))&&(e.push("包含思考过程残留"),i+=15),[/抱歉|对不起|很抱歉/g,/无法|不能|无法提供/g,/错误|失败|问题/g].forEach(a=>{a.test(t)&&(e.push("包含错误或道歉信息"),i+=10)});const r=[/详细描述|具体说明|进一步完善/g,/仅供参考|根据实际情况|请注意/g,/希望对您有帮助|如果您需要/g];let o=0;return r.forEach(a=>{const l=t.match(a);l&&(o+=l.length)}),o>2&&(e.push("包含过多空洞表述"),i+=o*3),{issues:e,penalty:i}}assessStructureQuality(t){let e=100;if((t.match(/^#{1,6}\s+.+$/gm)||[]).length===0)return 0;const n=t.split(/^#{1,6}\s+.+$/gm);n.filter(l=>l.trim().length<20).length>n.length*.3&&(e-=30);const o=n.reduce((l,h)=>l+h.length,0)/n.length;return n.reduce((l,h)=>l+Math.pow(h.length-o,2),0)/n.length>o*2&&(e-=20),Math.max(0,e)}calculateUniqueness(t){const e=t.split(/[。！？\n]/).filter(n=>n.trim().length>5);return e.length===0?0:new Set(e.map(n=>n.trim().toLowerCase())).size/e.length}calculateContentDensity(t){const e=t.match(/^#{1,6}\s+.+$/gm)||[],i=t.length,n=e.reduce((r,o)=>r+o.length,0);return n>0?(i-n)/i:0}getNestedValue(t,e){return e.split(".").reduce((i,n)=>{if(n.includes("[")&&n.includes("]")){const[r,o]=n.split(/[\[\]]/);return i?.[r]?.[parseInt(o)]}return i?.[n]},t)}detectModelType(t){const e=t.toLowerCase();return e.includes("claude")?"claude":e.includes("gpt")||e.includes("openai")?"gpt":e.includes("qwen")||e.includes("通义")?"qwen":e.includes("ernie")||e.includes("文心")?"ernie":e.includes("glm")||e.includes("智谱")?"glm":"default"}processStreamChunk(t,e="default"){try{if(!t||t===null||t===void 0)return"";let i;if(typeof t=="string")if(t.startsWith("data: ")){const o=t.substring(6).trim();if(o==="[DONE]"||o==="")return null;try{i=JSON.parse(o)}catch{return console.warn("JSON解析失败，作为纯文本处理:",o.substring(0,50)),this.lightFilter(o)}}else try{i=JSON.parse(t)}catch{return this.lightFilter(t)}else i=t;if(!i||i===null||i===void 0)return"";const r=(this.contentExtractors.get(e)||this.contentExtractors.get("default"))(i);return this.lightFilter(r)}catch(i){return console.error("流式响应处理失败:",i,"原始内容:",t),typeof t=="string"?this.lightFilter(t):""}}lightFilter(t){return!t||typeof t!="string"?t:t.replace(/^<thinking>[\s\S]*?<\/thinking>/gmi,"").replace(/^(好的|当然|让我)\s*/gmi,"")}processBatchResponses(t,e="default"){return t.map(i=>this.processResponse(i,e))}}const Ti=new gc,Qi={claude:{name:"Claude",hasThinking:!0,responseFormat:"content",streamField:"content",maxTokens:8192,temperature:{min:.1,max:1,default:.7},specialHandling:{removeThinking:!0,cleanPreamble:!0,standardizeHeaders:!0},promptOptimization:{useSystemRole:!0,preferStructured:!0,avoidRepetition:!0}},gpt:{name:"GPT",hasThinking:!1,responseFormat:"choices[0].message.content",streamField:"choices[0].delta.content",maxTokens:8192,temperature:{min:0,max:2,default:.7},specialHandling:{removeThinking:!1,cleanPreamble:!0,standardizeHeaders:!0},promptOptimization:{useSystemRole:!0,preferStructured:!0,avoidRepetition:!1}},qwen:{name:"通义千问",hasThinking:!1,responseFormat:"output.text",streamField:"output.text",maxTokens:8192,temperature:{min:.1,max:2,default:.8},specialHandling:{removeThinking:!1,cleanPreamble:!0,standardizeHeaders:!0,removeChinese:!1},promptOptimization:{useSystemRole:!1,preferStructured:!0,avoidRepetition:!0,chineseOptimized:!0}},ernie:{name:"文心一言",hasThinking:!1,responseFormat:"result",streamField:"result",maxTokens:8192,temperature:{min:.1,max:1,default:.7},specialHandling:{removeThinking:!1,cleanPreamble:!0,standardizeHeaders:!0,removeChinese:!1},promptOptimization:{useSystemRole:!1,preferStructured:!0,avoidRepetition:!0,chineseOptimized:!0}},glm:{name:"智谱GLM",hasThinking:!1,responseFormat:"choices[0].message.content",streamField:"choices[0].delta.content",maxTokens:8192,temperature:{min:.1,max:.9,default:.7},specialHandling:{removeThinking:!1,cleanPreamble:!0,standardizeHeaders:!0},promptOptimization:{useSystemRole:!0,preferStructured:!0,avoidRepetition:!0,chineseOptimized:!0}},default:{name:"通用模型",hasThinking:!1,responseFormat:"content",streamField:"content",maxTokens:8192,temperature:{min:.1,max:1,default:.7},specialHandling:{removeThinking:!0,cleanPreamble:!0,standardizeHeaders:!0},promptOptimization:{useSystemRole:!0,preferStructured:!0,avoidRepetition:!0}}};function tn(s){if(!s)return Qi.default;const t=s.toLowerCase();for(const[e,i]of Object.entries(Qi)){if(e==="default")continue;if({claude:["claude","anthropic"],gpt:["gpt","openai","chatgpt"],qwen:["qwen","通义","tongyi"],ernie:["ernie","文心","wenxin","baidu"],glm:["glm","智谱","zhipu","chatglm"]}[e]?.some(r=>t.includes(r)))return{...i,type:e}}return{...Qi.default,type:"default"}}function vc(s,t){const e={temperature:t.creativity||s.temperature.default,max_tokens:Math.min(t.maxTokens||s.maxTokens,s.maxTokens)};return e.temperature=Math.max(s.temperature.min,Math.min(s.temperature.max,e.temperature)),s.type==="claude"&&s.hasThinking&&(e.max_tokens=Math.round(e.max_tokens*1.2)),(s.type==="qwen"||s.type==="ernie")&&(e.temperature=Math.min(e.temperature,.8)),e}function yc(s,t){const e=[];let i=100;if(!s||s.trim().length===0)return{score:0,issues:["内容为空"]};t.hasThinking&&s.includes("<thinking>")&&(e.push("包含thinking标签残留"),i-=15);const n=(s.match(/^#{1,6}\s+.+$/gm)||[]).length;n===0&&(e.push("缺少markdown标题"),i-=20),s.length<100&&(e.push("内容过短"),i-=10);const r=s.split(`
`).filter(a=>a.trim()),o=new Set(r);return r.length>o.size*1.5&&(e.push("内容重复度较高"),i-=15),t.promptOptimization?.chineseOptimized&&(s.includes("抱歉")||s.includes("无法"))&&(e.push("包含拒绝或道歉内容"),i-=10),{score:Math.max(0,i),issues:e,titleCount:n,wordCount:s.length,uniquenessRatio:o.size/r.length}}class xc{constructor(){this.systemPrompts=new Map,this.userPrompts=new Map,this.setupPrompts()}setupPrompts(){this.systemPrompts.set("base",`你是一位专业的思维导图内容生成专家，专门为用户创建结构化的思维导图节点内容。

核心职责：
1. 根据用户指定的模式生成相应的思维导图节点
2. 确保生成的内容逻辑清晰、结构合理
3. 严格按照指定的输出格式进行回复
4. 避免生成与已有节点重复的内容

输出规范：
- 必须使用标准markdown格式
- 每个节点标题使用## 格式
- 标题下方必须提供具体的实际内容，不要使用占位符
- 不要添加任何解释性文字或额外说明
- 禁止使用"详细描述xxx"、"具体说明xxx"等占位符文本

请严格按照用户的要求执行，直接生成真实的内容。`),this.userPrompts.set("subtopics",`请为主题"{{title}}"生成下级的子主题分类。

【完整上下文】
{{contextInfo}}

【生成要求】
- 为当前主题生成下一级的子主题分类
- 每个子主题应该是当前主题的一个细分领域或方面
- 子主题之间应该相互独立，共同构成完整的主题体系
- 充分考虑上级主题的整体框架和已有同级内容
- 每个子主题使用## 标题格式，标题下方只提供简要说明
- 生成准确的子主题分类
- 避免与已有内容重复

【重要约束】
- 只生成一层子主题，不要在子主题下面再创建子层级
- 不要使用###、####等更深层级的标题
- 每个子主题的说明应该简洁明了，不要过于详细
- 不要在说明中包含列表、要点等会被解析为子层级的内容

【输出格式】
## 子主题标题
简要说明该子主题的范围和内容。

请直接输出实际的子主题分类，不要使用占位符或示例文本。`),this.userPrompts.set("children",`请深入分析主题"{{title}}"，提取其核心要点和细节。

【完整上下文】
{{contextInfo}}

【生成要求】
- 深入分析当前主题的内在结构和核心要点
- 提取主题的关键组成部分、重要细节和实施要点
- 每个要点都要有详细的描述、说明和具体内容
- 充分考虑整体框架和上下文层级关系
- 确保要点之间逻辑清晰、相互补充
- 内容要有深度和实用性，能够细化当前主题
- 避免与已有内容重复

【输出格式】
每个要点使用## 标题格式，标题下方直接提供具体的详细分析和说明。

请直接输出实际的要点分析内容，不要使用占位符或示例文本。`),this.userPrompts.set("analysis",`请从多个角度深入分析主题"{{title}}"。

【完整上下文】
{{contextInfo}}

【生成要求】
- 从3-5个不同维度分析主题
- 每个分析角度都要有独特的见解
- 提供深入的分析和观点
- 充分考虑整体框架和上下文关系
- 确保分析全面而专业
- 避免与已有内容重复

【输出格式】
每个分析角度使用## 标题格式，标题下方直接提供具体的分析内容。

请直接输出实际的分析内容，不要使用占位符或示例文本。`),this.userPrompts.set("creative",`请为主题"{{title}}"进行创意发散思考。

【完整上下文】
{{contextInfo}}

【生成要求】
- 生成准确的创新性的思路或观点
- 鼓励跳出常规思维，但要符合整体框架
- 每个创意点都要有具体的说明
- 确保创意的可行性和价值
- 避免与已有内容重复

【输出格式】
每个创意点使用## 标题格式，标题下方直接提供具体的创意内容和价值说明。

请直接输出实际的创意内容，不要使用占位符或示例文本。`)}generateSystemPrompt(t="general"){let e=this.systemPrompts.get("base");const i={novel:`

专业领域：小说创作
特别注意情节逻辑、人物塑造和世界观构建的一致性。`,worldbuilding:`

专业领域：世界观构建
重点关注世界观的一致性、逻辑性和丰富性。`,character:`

专业领域：角色设定
专注于角色的立体性、成长弧线和人物关系。`,technical:`

专业领域：技术内容
确保技术内容的准确性、实用性和可操作性。`,business:`

专业领域：商业分析
关注商业逻辑、市场分析和实施可行性。`,general:""};return e+(i[t]||i.general)}generateUserPrompt(t,e){const i=this.userPrompts.get(t);if(!i)throw new Error(`未找到模式 ${t} 的提示词模板`);const n=this.buildContextInfo(e);return this.replaceVariables(i,{title:e.title||"未命名主题",contextInfo:n})}buildContextInfo(t){console.log("=== buildContextInfo 调试信息 ==="),console.log("输入的context:",JSON.stringify(t,null,2));const e=[];if(e.push(`当前主题：${t.title||"未命名主题"}`),e.push(`层级深度：第${t.depth||0}层`),t.hierarchy&&t.hierarchy.fullPath?(e.push(`完整路径：${t.hierarchy.fullPath}`),console.log("✅ 添加了完整路径:",t.hierarchy.fullPath)):console.log("❌ 缺少完整路径信息"),t.hierarchy&&t.hierarchy.ancestors&&t.hierarchy.ancestors.length>0){const n=t.hierarchy.ancestors.map(r=>r.title).join(" → ");e.push(`上级主题：${n}`),console.log("✅ 添加了上级主题:",n)}else console.log("❌ 缺少上级主题信息");if(t.relationships&&t.relationships.siblings&&t.relationships.siblings.length>0){const n=t.relationships.siblings.map(o=>o.title).join("、"),r=t.relationships.hasMoreSiblings?"等":"";e.push(`同级主题：${n}${r}`)}else e.push("同级主题：无");if(t.relationships&&t.relationships.children&&t.relationships.children.length>0){const n=t.relationships.children.map(o=>o.title).join("、"),r=t.relationships.hasMoreChildren?"等":"";e.push(`已有子主题：${n}${r}`)}else e.push("已有子主题：无");t.content&&t.content.hasContent?e.push(`当前内容：${t.content.currentContent.substring(0,100)}${t.content.currentContent.length>100?"...":""}`):e.push("当前内容：无");const i=e.join(`
`);return console.log("=== 最终构建的上下文信息 ==="),console.log(i),console.log("=== 上下文信息构建完成 ==="),i}replaceVariables(t,e){let i=t;for(const[n,r]of Object.entries(e)){const o=new RegExp(`{{${n}}}`,"g");i=i.replace(o,String(r||""))}return i}validateMode(t){return["subtopics","children","analysis","creative"].includes(t)}getAvailableModes(){return[{id:"subtopics",name:"子主题",description:"生成下级子主题分类"},{id:"children",name:"要点分解",description:"深入分析当前主题的核心要点和细节"},{id:"analysis",name:"多角度分析",description:"从不同维度深入分析"},{id:"creative",name:"创意发散",description:"生成创新性思路和观点"}]}}const en=new xc;function Os(s,t){if(!en.validateMode(s))throw new Error(`不支持的生成模式: ${s}`);const e=en.generateSystemPrompt(t.domain),i=en.generateUserPrompt(s,t);return{system:e,user:i}}class wc{constructor(){this.maxAncestorLevels=3,this.maxSiblingCount=5,this.maxChildrenPreview=3}buildFullContext(t,e,i="general"){if(console.log("=== 上下文构建详细调试 ==="),console.log("当前节点:",t),console.log("思维导图数据根节点:",{id:e?.id,title:e?.title}),console.log("节点ID类型:",typeof t?.id,"值:",t?.id),!t||!e)return console.log("❌ 缺少必要参数，返回空上下文"),this.getEmptyContext();const n=this.findNodeById(t.id,e);console.log("能否在思维导图中找到节点:",!!n,n?.title);const r=this.getNodeInfo(t,e);console.log("节点信息:",r);const o=this.getHierarchyInfo(t,e);console.log("层级信息:",o);const a=this.getRelationshipInfo(t,e),l=this.getContentInfo(t,e);return console.log("上下文构建完成 - 路径:",o.fullPath),{title:t.title||"未命名主题",depth:r.depth,domain:i,hierarchy:o,relationships:a,content:l,stats:{totalDepth:r.totalDepth,siblingCount:a.siblings.length,childrenCount:a.children.length,ancestorCount:o.ancestors.length}}}getNodeInfo(t,e){const i=this.findNodePath(t.id,e),n=Math.max(0,i.length-1),r=this.getMaxDepth(e);return{id:t.id,title:t.title,depth:n,totalDepth:r,path:i,isRoot:n===0,isLeaf:!t.children||t.children.length===0}}getHierarchyInfo(t,e){const i=this.findNodePath(t.id,e),n=[];for(let r=0;r<i.length-1&&r<this.maxAncestorLevels;r++){const o=this.findNodeById(i[r].id,e);o&&n.push({id:o.id,title:o.title,level:r,content:o.content||""})}return{ancestors:n,rootTitle:i.length>0?i[0].title:"",parentTitle:i.length>1?i[i.length-2].title:"",fullPath:i.map(r=>r.title).join(" → ")}}getRelationshipInfo(t,e){const i=this.getSiblingNodes(t,e),n=t.children||[];return{siblings:i.slice(0,this.maxSiblingCount).map(r=>({id:r.id,title:r.title,hasChildren:!!(r.children&&r.children.length>0),content:r.content||""})),children:n.slice(0,this.maxChildrenPreview).map(r=>({id:r.id,title:r.title,hasChildren:!!(r.children&&r.children.length>0),content:r.content||""})),hasMoreSiblings:i.length>this.maxSiblingCount,hasMoreChildren:n.length>this.maxChildrenPreview}}getContentInfo(t,e){return{currentContent:t.content||"",hasContent:!!(t.content&&t.content.trim()),contentLength:t.content?t.content.length:0,isEmpty:!t.content||t.content.trim()===""}}findNodePath(t,e){let i=[];const n=(r,o,a)=>{const l=[...a,{id:r.id,title:r.title}];if(r.id===o)return i=l,!0;if(r.children&&r.children.length>0){for(const h of r.children)if(n(h,o,l))return!0}return!1};return n(e,t,[]),console.log(`查找节点路径 ${t}:`,i.map(r=>r.title).join(" → ")),i}findNodeById(t,e){if(console.log(`🔍 查找节点ID: ${t}, 当前检查节点: ${e.id} (${e.title})`),e.id===t)return console.log(`✅ 找到匹配节点: ${e.title}`),e;if(e.children){console.log(`📁 检查 ${e.children.length} 个子节点`);for(const i of e.children){const n=this.findNodeById(t,i);if(n)return n}}return null}getSiblingNodes(t,e){const i=this.findParentNode(t.id,e);return!i||!i.children?[]:i.children.filter(n=>n.id!==t.id)}findParentNode(t,e){if(e.children)for(const i of e.children){if(i.id===t)return e;const n=this.findParentNode(t,i);if(n)return n}return null}getMaxDepth(t){const e=(i,n=0)=>{if(!i.children||i.children.length===0)return n;let r=n;for(const o of i.children){const a=e(o,n+1);r=Math.max(r,a)}return r};return e(t)}getEmptyContext(){return{title:"未命名主题",depth:0,domain:"general",hierarchy:{ancestors:[],rootTitle:"",parentTitle:"",fullPath:""},relationships:{siblings:[],children:[],hasMoreSiblings:!1,hasMoreChildren:!1},content:{currentContent:"",hasContent:!1,contentLength:0,isEmpty:!0},stats:{totalDepth:0,siblingCount:0,childrenCount:0,ancestorCount:0}}}formatContextForPrompt(t){const e=[];if(e.push(`当前主题：${t.title}`),e.push(`层级深度：第${t.depth}层`),t.hierarchy.fullPath&&e.push(`完整路径：${t.hierarchy.fullPath}`),t.hierarchy.ancestors.length>0){const i=t.hierarchy.ancestors.map(n=>n.title).join(" → ");e.push(`上级主题：${i}`)}if(t.relationships.siblings.length>0){const i=t.relationships.siblings.map(n=>n.title).join("、");e.push(`同级主题：${i}${t.relationships.hasMoreSiblings?"等":""}`)}if(t.relationships.children.length>0){const i=t.relationships.children.map(n=>n.title).join("、");e.push(`已有子主题：${i}${t.relationships.hasMoreChildren?"等":""}`)}return e.join(`
`)}}const ks=new wc,_c={class:"ai-dialog-content"},Mc={class:"dialog-header"},Ec={class:"current-node"},Cc={class:"node-title"},Nc={class:"dialog-body"},Tc={class:"generation-types"},Sc={class:"type-buttons"},Lc={class:"generation-params"},bc={class:"param-controls"},Dc={class:"param-item"},Ac={class:"param-item"},Rc={class:"param-item"},Ic={key:0,class:"prompt-section"},Oc={class:"section-title"},kc={class:"title-actions"},zc={class:"prompt-item"},Fc={class:"prompt-label"},Pc={class:"prompt-item"},Bc={class:"prompt-label"},$c={class:"prompt-stats"},Hc={key:1,class:"custom-prompt"},Uc={class:"section-title"},Gc={key:2,class:"generation-status"},Yc={class:"status-header"},Wc={class:"status-time"},Vc={key:0,class:"live-preview"},Xc={class:"preview-text"},jc={key:3,class:"result-preview"},qc={class:"result-header"},Kc={class:"result-summary"},Zc={class:"summary-text"},Jc={class:"dialog-footer"},Qc={class:"footer-left"},tu={class:"footer-right"},eu=12e4,iu=3e4,nu={__name:"AIDialog",props:{visible:{type:Boolean,default:!1},currentNode:{type:Object,default:null},selectedModel:{type:String,default:""},bookTitle:{type:String,default:"未命名文档"},mindmapData:{type:Object,default:null}},emits:["update:visible","content-generated"],setup(s,{emit:t}){const e=s,i=t,n=Hs(),r=U(!1),o=U(0),a=U(""),l=U(!1),h=U(!1),d=U(!1),c=U(null),f=U(""),g=U(0),u=U(null),m=U(0),p=U(0),x=Y=>{try{const b=n.allAvailableModels.find(j=>j.uniqueId===Y||j.id===Y);return b&&b.config?(console.log("AIDialog获取到AI提供商模型配置:",b.config),b.config):(console.log("AIDialog未找到AI提供商模型配置，使用默认配置"),{temperature:.8,max_tokens:8192,top_p:.8,frequency_penalty:0,presence_penalty:0,stream:!0})}catch(b){return console.error("AIDialog获取AI提供商模型配置失败:",b),{temperature:.8,max_tokens:8192,top_p:.8,frequency_penalty:0,presence_penalty:0,stream:!0}}},E=U({system:"",user:""}),_=U({mode:"children",domain:"general",detail:"moderate",creativity:.7,count:1,customPrompt:""}),T=gt({get:()=>e.visible,set:Y=>i("update:visible",Y)}),C=gt(()=>e.currentNode?`AI生成 - ${e.currentNode.title}`:"AI内容生成"),L=gt(()=>!e.currentNode||!e.mindmapData?[]:A(e.currentNode,e.mindmapData)),F=gt(()=>L.value.length-1),W=gt(()=>!e.currentNode||!e.mindmapData?[]:O(e.currentNode,e.mindmapData)),X=gt(()=>[{id:"children",title:"要点分解",icon:si,mode:"children"},{id:"subtopics",title:"子主题",icon:Zn,mode:"subtopics"},{id:"analysis",title:"多角度分析",icon:Jn,mode:"analysis"},{id:"creative",title:"创意发散",icon:Qn,mode:"creative"}]),Mt=gt({get:()=>Math.round(_.value.creativity*100),set:Y=>{_.value.creativity=Y/100}}),ct=gt(()=>e.selectedModel&&e.currentNode&&c.value),Lt=Y=>{switch(c.value=Y,_.value.mode=Y.mode,Y.id){case"subtopics":_.value.detail="brief",_.value.creativity=.5;break;case"children":_.value.detail="detailed",_.value.creativity=.6;break;case"analysis":_.value.detail="detailed",_.value.creativity=.6;break;case"creative":_.value.detail="moderate",_.value.creativity=.9;break}},bt=()=>a.value?`${a.value.split(`
`).filter(j=>j.trim()).filter(j=>j.match(/^#+\s+/)||j.match(/^[-*+]\s+/)||j.match(/^\d+\.\s+/)).length} 个节点，约 ${a.value.length} 字符`:"",Dt=()=>{if(!r.value||g.value===0)return"";const Y=Math.floor((Date.now()-g.value)/1e3),b=Math.floor(Y/60),j=Y%60;return b>0?`${b}:${j.toString().padStart(2,"0")}`:`${j}s`},A=(Y,b)=>{let j=b;const vt=(q,$t,Ht=[])=>{if(q.id===$t)return[...Ht,q.title];if(q.children)for(const Yt of q.children){const S=vt(Yt,$t,[...Ht,q.title]);if(S)return S}return null};return vt(j,Y.id)||[Y.title]},O=(Y,b)=>{const j=(q,$t)=>{if(q.children)for(const Ht of q.children){if(Ht.id===$t)return q;const Yt=j(Ht,$t);if(Yt)return Yt}return null},vt=j(b,Y.id);return vt&&vt.children?vt.children.filter(q=>q.id!==Y.id):[]};Nt(()=>e.currentNode,Y=>{if(Y){const b=F.value;let j="children",vt="general";b===0?j="subtopics":b>=3&&(j="children");const q=Y.title?.toLowerCase()||"";q.includes("角色")||q.includes("人物")?vt="character":q.includes("世界")||q.includes("设定")?vt="worldbuilding":(q.includes("情节")||q.includes("剧情"))&&(vt="plot"),_.value={mode:j,domain:vt,strategy:"auto",detail:"moderate",creativity:.7,count:1,customPrompt:""},a.value="",o.value=0,h.value&&kt()}}),Nt([()=>_.value.mode,()=>_.value.domain,()=>_.value.detail],()=>{h.value&&kt()}),Nt(h,Y=>{Y&&kt()});const H=()=>{T.value=!1,r.value=!1,o.value=0,a.value="",c.value=null,l.value=!1,m.value=0,p.value=0,u.value&&(clearTimeout(u.value),u.value=null)},rt=()=>{a.value="",o.value=0,ge()},kt=()=>{try{console.log("=== 提示词预览调试信息 ==="),console.log("当前节点:",e.currentNode),console.log("思维导图数据:",e.mindmapData),console.log("领域:",_.value.domain||"general"),console.log("AI对话 - 当前节点:",e.currentNode?.title),console.log("AI对话 - 思维导图根节点:",e.mindmapData?.title);const Y=ks.buildFullContext(e.currentNode,e.mindmapData,_.value.domain||"general"),b=Os(_.value.mode,Y);E.value.system=b.system,E.value.user=b.user,console.log("提示词预览已更新")}catch(Y){console.error("生成提示词预览失败:",Y),$.error("生成提示词预览失败: "+Y.message)}},Et=Y=>Y?Y.length:0,se=()=>Et(E.value.system)+Et(E.value.user),Gt=()=>{console.log("用户手动停止生成"),u.value&&(clearTimeout(u.value),u.value=null),r.value=!1,o.value=100,m.value=0,p.value=0,window.originalReceiveChunk&&(window.receiveChunk=window.originalReceiveChunk),window.originalOnMessageComplete&&(window.onMessageComplete=window.originalOnMessageComplete),a.value.trim()?(console.log("处理部分生成的内容"),Ee()):$.warning("生成已停止，未获得有效内容")},Ee=()=>{try{const Y=tn(e.selectedModel),b=Ti.processResponse(a.value,Y.type);a.value=b.content,b.content.trim()?$.success("已处理部分生成的内容"):$.warning("生成的内容无法解析")}catch(Y){console.error("处理部分内容失败:",Y),$.error("处理部分内容失败")}},ge=async()=>{if(!e.selectedModel){$.warning("请选择AI模型");return}if(!e.currentNode){$.warning("请选择要生成内容的节点");return}r.value=!0,o.value=0,a.value="";try{const Y=tn(e.selectedModel);console.log("使用模型配置:",Y.name,Y.type);let b,j;if(d.value&&E.value.user)b=E.value.system,j=E.value.user,console.log("使用用户编辑的提示词");else{const B=ks.buildFullContext(e.currentNode,e.mindmapData,_.value.domain||"general");console.log("=== AIDialog 上下文调试 ==="),console.log("构建的完整上下文:",B),console.log("上下文是否有层级信息:",!!B.hierarchy),console.log("上下文是否有完整路径:",!!B.hierarchy?.fullPath),console.log("上下文是否有祖先节点:",!!B.hierarchy?.ancestors?.length);const mt=Os(_.value.mode,B);b=mt.system,j=mt.user,console.log("=== 生成的提示词调试 ==="),console.log("系统提示词长度:",mt.system.length),console.log("用户提示词长度:",mt.user.length),console.log("用户提示词预览:",mt.user.substring(0,500)+"..."),console.log("生成新的提示词")}console.log("最终使用的提示词:",{system:b.substring(0,100)+"...",user:j.substring(0,100)+"..."});const vt=[{role:"system",content:b},{role:"user",content:j}],q=rn();f.value=q,g.value=Date.now(),m.value=0,p.value=0;const $t=Y.type;console.log("检测到模型类型:",$t),u.value=setTimeout(()=>{console.warn("生成超时，自动停止"),$.warning("生成超时，已自动停止"),Gt()},eu);const Ht=window.receiveChunk;window.originalReceiveChunk=Ht,window.receiveChunk=B=>{try{const mt=atob(B),xt=new TextDecoder("utf-8").decode(new Uint8Array([...mt].map(zt=>zt.charCodeAt(0))));let re;try{re=JSON.parse(xt)}catch{if(console.warn("流式数据JSON解析失败，尝试直接处理:",xt.substring(0,50)),xt.trim()){const Wt=Ti.processStreamChunk(xt,$t);Wt&&(a.value+=Wt,o.value=Math.min(o.value+2,90))}return}if(re.chat_id===q&&re.content){const zt=Ti.processStreamChunk(re.content,$t);if(zt){const Wt=a.value.length;a.value+=zt;const ce=a.value.length;if(ce>Wt)m.value=ce,p.value=0,o.value=Math.min(o.value+3,90);else{const te=Date.now();if(p.value===0)p.value=te;else if(te-p.value>iu){console.warn("内容生成停滞，自动停止"),$.warning("检测到生成停滞，已自动停止"),Gt();return}}}}}catch(mt){console.error("处理AI响应失败:",mt,"原始chunk长度:",B.length),typeof B=="string"&&B.trim()&&(a.value+=B)}};const Yt=window.onMessageComplete;window.originalOnMessageComplete=Yt,window.onMessageComplete=B=>{if(B===q){u.value&&(clearTimeout(u.value),u.value=null),o.value=100,r.value=!1;const mt=Ti.processResponse(a.value,$t);a.value=mt.content;const xt=yc(mt.content,Y);xt.score<70?(console.warn("内容质量较低:",xt.issues),$.warning(`内容质量提醒: ${xt.issues.join(", ")}`)):xt.score>=90&&console.log("内容质量优秀!"),console.log("内容处理完成:",{模型:Y.name,质量评分:xt.score,标题数量:xt.titleCount,唯一性:Math.round(xt.uniquenessRatio*100)+"%",压缩比:Math.round(mt.compressionRatio*100)+"%"}),window.receiveChunk=window.originalReceiveChunk,window.onMessageComplete=window.originalOnMessageComplete}};const S=tn(e.selectedModel),R=x(e.selectedModel),it=vc(S,_.value),St={stream:!0,temperature:it.temperature,top_p:.9,max_tokens:it.max_tokens,...R};console.log("使用API参数:",St),await window.pywebview.api.model_controller.chat(q,e.selectedModel,vt,St)}catch(Y){console.error("AI生成失败:",Y),$.error("AI生成失败: "+Y.message),r.value=!1}},Ce=()=>{if(!a.value||!e.currentNode){$.warning("没有可应用的内容");return}i("content-generated",{node:e.currentNode,mode:_.value.mode,domain:_.value.domain,content:a.value.trim(),count:_.value.count,detail:_.value.detail,context:{nodePath:L.value,nodeDepth:F.value,siblings:W.value}}),$.success("内容已应用到思维导图"),H()};return(Y,b)=>{const j=Nn,vt=Vr,q=Bi,$t=Fs,Ht=Bs,Yt=Ps,S=Xr,R=Cn,it=qr,St=$s;return Z(),oe(St,{modelValue:T.value,"onUpdate:modelValue":b[11]||(b[11]=B=>T.value=B),title:C.value,width:"600px",height:"500px","close-on-click-modal":!1,onClose:H,class:"ai-dialog native-style",modal:!0,"append-to-body":!0,"destroy-on-close":!0},{default:z(()=>[w("div",_c,[w("div",Mc,[w("div",Ec,[N(j,{class:"node-icon"},{default:z(()=>[N(Q(Di))]),_:1}),w("span",Cc,dt(s.currentNode?.title||"未选择节点"),1),F.value>0?(Z(),oe(vt,{key:0,size:"small",type:"info"},{default:z(()=>[st(dt(F.value)+"层",1)]),_:1})):It("",!0)])]),w("div",Nc,[w("div",Tc,[b[12]||(b[12]=w("div",{class:"section-title"},"生成类型",-1)),w("div",Sc,[(Z(!0),ht(ni,null,Tn(X.value,B=>(Z(),oe(q,{key:B.id,type:c.value?.id===B.id?"primary":"default",icon:B.icon,onClick:mt=>Lt(B),class:"type-btn",size:"default"},{default:z(()=>[st(dt(B.title),1)]),_:2},1032,["type","icon","onClick"]))),128))])]),w("div",Lc,[b[16]||(b[16]=w("div",{class:"section-title"},"生成参数",-1)),w("div",bc,[w("div",Dc,[b[13]||(b[13]=w("label",null,"数量",-1)),N($t,{modelValue:_.value.count,"onUpdate:modelValue":b[0]||(b[0]=B=>_.value.count=B),min:1,max:8,size:"small","controls-position":"right"},null,8,["modelValue"])]),w("div",Ac,[b[14]||(b[14]=w("label",null,"详细程度",-1)),N(Yt,{modelValue:_.value.detail,"onUpdate:modelValue":b[1]||(b[1]=B=>_.value.detail=B),size:"small"},{default:z(()=>[N(Ht,{label:"简洁",value:"brief"}),N(Ht,{label:"适中",value:"moderate"}),N(Ht,{label:"详细",value:"detailed"})]),_:1},8,["modelValue"])]),w("div",Rc,[b[15]||(b[15]=w("label",null,"创意度",-1)),N(S,{modelValue:Mt.value,"onUpdate:modelValue":b[2]||(b[2]=B=>Mt.value=B),min:0,max:100,step:10,size:"small","show-stops":""},null,8,["modelValue"])])])])]),h.value?(Z(),ht("div",Ic,[w("div",Oc,[b[19]||(b[19]=st(" 提示词预览 ")),w("div",kc,[N(q,{onClick:kt,type:"text",size:"small"},{default:z(()=>[N(j,null,{default:z(()=>[N(Q(bi))]),_:1}),b[17]||(b[17]=st(" 刷新 "))]),_:1}),N(q,{onClick:b[3]||(b[3]=B=>h.value=!1),type:"text",size:"small"},{default:z(()=>b[18]||(b[18]=[st("隐藏")])),_:1})])]),w("div",zc,[w("div",Fc,[N(j,null,{default:z(()=>[N(Q(sn))]),_:1}),b[20]||(b[20]=st(" 系统提示词 "))]),N(R,{modelValue:E.value.system,"onUpdate:modelValue":b[4]||(b[4]=B=>E.value.system=B),type:"textarea",rows:3,readonly:"",class:"prompt-textarea system-prompt"},null,8,["modelValue"])]),w("div",Pc,[w("div",Bc,[N(j,null,{default:z(()=>[N(Q(Zn))]),_:1}),b[21]||(b[21]=st(" 用户提示词 ")),N(q,{onClick:b[5]||(b[5]=B=>d.value=!d.value),type:"text",size:"small"},{default:z(()=>[st(dt(d.value?"锁定":"编辑"),1)]),_:1})]),N(R,{modelValue:E.value.user,"onUpdate:modelValue":b[6]||(b[6]=B=>E.value.user=B),type:"textarea",rows:6,readonly:!d.value,class:pe(["prompt-textarea","user-prompt",{editable:d.value}]),placeholder:"用户提示词将在这里显示..."},null,8,["modelValue","readonly","class"])]),w("div",$c,[N(vt,{size:"small",type:"info"},{default:z(()=>[st(" 系统: "+dt(Et(E.value.system))+" 字符 ",1)]),_:1}),N(vt,{size:"small",type:"primary"},{default:z(()=>[st(" 用户: "+dt(Et(E.value.user))+" 字符 ",1)]),_:1}),N(vt,{size:"small",type:"success"},{default:z(()=>[st(" 总计: "+dt(se())+" 字符 ",1)]),_:1})])])):It("",!0),l.value?(Z(),ht("div",Hc,[w("div",Uc,[b[23]||(b[23]=st(" 自定义提示词 ")),N(q,{onClick:b[7]||(b[7]=B=>l.value=!1),type:"text",size:"small"},{default:z(()=>b[22]||(b[22]=[st("隐藏")])),_:1})]),N(R,{modelValue:_.value.customPrompt,"onUpdate:modelValue":b[8]||(b[8]=B=>_.value.customPrompt=B),type:"textarea",rows:3,placeholder:"输入自定义提示词（可选）",resize:"none"},null,8,["modelValue"])])):It("",!0),r.value?(Z(),ht("div",Gc,[w("div",Yc,[N(j,{class:"rotating"},{default:z(()=>[N(Q(jr))]),_:1}),b[24]||(b[24]=w("span",{class:"status-text"},"AI正在生成内容...",-1)),w("span",Wc,dt(Dt()),1)]),N(it,{percentage:o.value,"stroke-width":6},null,8,["percentage"]),a.value?(Z(),ht("div",Vc,[b[25]||(b[25]=w("div",{class:"preview-label"},"生成预览：",-1)),w("div",Xc,dt(a.value.substring(0,200))+"...",1)])):It("",!0)])):It("",!0),!r.value&&a.value?(Z(),ht("div",jc,[w("div",qc,[N(j,{class:"success-icon"},{default:z(()=>[N(Q(ts))]),_:1}),b[27]||(b[27]=w("span",null,"生成完成",-1)),N(q,{onClick:rt,type:"text",size:"small"},{default:z(()=>[N(j,null,{default:z(()=>[N(Q(bi))]),_:1}),b[26]||(b[26]=st(" 重新生成 "))]),_:1})]),w("div",Kc,[w("span",Zc,"已生成 "+dt(bt()),1)])])):It("",!0),w("div",Jc,[w("div",Qc,[N(q,{onClick:b[9]||(b[9]=B=>h.value=!h.value),type:"text",size:"small"},{default:z(()=>[N(j,null,{default:z(()=>[N(Q(Jn))]),_:1}),st(" "+dt(h.value?"隐藏":"预览")+"提示词 ",1)]),_:1}),N(q,{onClick:b[10]||(b[10]=B=>l.value=!l.value),type:"text",size:"small"},{default:z(()=>[N(j,null,{default:z(()=>[N(Q(sn))]),_:1}),st(" "+dt(l.value?"隐藏":"自定义")+"提示词 ",1)]),_:1})]),w("div",tu,[N(q,{onClick:H,size:"default"},{default:z(()=>b[28]||(b[28]=[st("取消")])),_:1}),!r.value&&!a.value?(Z(),oe(q,{key:0,type:"primary",onClick:ge,disabled:!ct.value,size:"default"},{default:z(()=>[N(j,null,{default:z(()=>[N(Q(Qn))]),_:1}),b[29]||(b[29]=st(" 开始生成 "))]),_:1},8,["disabled"])):It("",!0),r.value?(Z(),oe(q,{key:1,type:"danger",onClick:Gt,size:"default"},{default:z(()=>b[30]||(b[30]=[st(" 停止生成 ")])),_:1})):It("",!0),!r.value&&a.value?(Z(),oe(q,{key:2,type:"success",onClick:Ce,size:"default"},{default:z(()=>[N(j,null,{default:z(()=>[N(Q(ts))]),_:1}),b[31]||(b[31]=st(" 应用到思维导图 "))]),_:1})):It("",!0)])])])]),_:1},8,["modelValue","title"])}}},su=ui(nu,[["__scopeId","data-v-98cdc347"]]);class ru{constructor(){this.markdownParser=new ou}applyGeneratedContent(t,e){const{node:i,mode:n,content:r,context:o}=t;try{switch(n){case"subtopics":case"children":case"analysis":case"creative":return this.addChildrenNodes(i,r,e,n);default:throw new Error(`未知的生成模式: ${n}`)}}catch(a){throw console.error("应用AI内容失败:",a),a}}addChildrenNodes(t,e,i,n="children"){const r=this.deepClone(i),o=this.findNodeById(r,t.id);if(o){const a=this.parseContentToNodes(e,n);o.children||(o.children=[]),o.children.push(...a),o.updatedAt=new Date().toISOString(),console.log(`为节点"${o.title}"添加了${a.length}个${n}类型的子节点`)}return r}parseContentToNodes(t,e="children"){console.log("=== AI内容解析开始 ==="),console.log(`模式: ${e}`),console.log(`原始内容长度: ${t.length}`),console.log(`原始内容预览: ${t.substring(0,300)}...`);const i=this.cleanAIResponse(t);switch(console.log(`清理后内容长度: ${i.length}`),e){case"subtopics":return this.parseSubtopicsContent(i,e);case"children":return this.parseDetailedContent(i,e);case"analysis":case"creative":return this.parseSmartContent(i,e);default:return console.warn(`未知模式: ${e}，使用默认处理`),this.parseSmartContent(i,e)}}cleanAIResponse(t){if(!t||typeof t!="string")return"";let e=t;return e=e.replace(/<think>[\s\S]*?<\/think>/gi,""),e=e.replace(/<reasoning>[\s\S]*?<\/reasoning>/gi,""),e=e.replace(/<analysis>[\s\S]*?<\/analysis>/gi,""),e=e.replace(/<reflection>[\s\S]*?<\/reflection>/gi,""),e=e.replace(/\n\s*\n\s*\n/g,`

`),e=e.trim(),["好的，我来为您","我来为您","让我来","我将为您","根据您的要求","基于您的需求","以下是","这里是"].forEach(n=>{const r=new RegExp(`^${n}[^。
]*[。
]?`,"i");e=e.replace(r,"")}),e=e.trim(),console.log("AI响应清理完成"),e}parseDetailedContent(t,e){console.log("=== 详细内容处理（保持层级结构）===");const i=[],n=this.getNodeStyle(e),r=this.markdownParser.parseToSections(t);return console.log(`解析到 ${r.length} 个章节`),r.length>0?r.forEach((o,a)=>{if(o.title&&o.title.trim()){console.log(`处理要点 ${a+1}: ${o.title} (层级: ${o.level})`);const l={id:tt(),title:o.title.trim(),content:o.content?o.content.trim():"",children:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),aiGenerated:!0,aiMode:e,style:n,level:o.level};o.children&&o.children.length>0?(console.log(`节点 "${o.title}" 有 ${o.children.length} 个子章节`),o.children.forEach(h=>{const d={id:tt(),title:h.title.trim(),content:h.content?h.content.trim():"",children:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),aiGenerated:!0,aiMode:e,style:{...n,opacity:.8},level:h.level};h.children&&h.children.length>0&&h.children.forEach(c=>{const f={id:tt(),title:c.title.trim(),content:c.content?c.content.trim():"",children:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),aiGenerated:!0,aiMode:e,style:{...n,opacity:.6},level:c.level};d.children.push(f)}),l.children.push(d)})):o.content&&o.content.trim().length>20&&(console.log(`节点 "${o.title}" 包含详细内容，长度: ${o.content.length}`),this.splitDetailedContent(o.content).forEach((d,c)=>{if(d.trim()){const f={id:tt(),title:d.trim(),content:"",children:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),aiGenerated:!0,aiMode:e,style:{...n,opacity:.8}};l.children.push(f)}})),i.push(l)}}):(console.log("没有找到标题结构，按段落分割"),this.splitByParagraphs(t).forEach((a,l)=>{if(a.trim().length>10){const h=this.extractTitleFromText(a),d={id:tt(),title:h,content:"",children:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),aiGenerated:!0,aiMode:e,style:n},c=a.replace(h,"").trim();c.length>10&&this.splitDetailedContent(c).forEach(g=>{if(g.trim()){const u={id:tt(),title:g.trim(),content:"",children:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),aiGenerated:!0,aiMode:e,style:{...n,opacity:.8}};d.children.push(u)}}),i.push(d)}})),console.log(`详细内容处理完成，生成 ${i.length} 个主节点`),i}splitDetailedContent(t){const e=t.split(/[。\n]/).map(i=>i.trim()).filter(i=>i.length>5);if(e.length<2&&t.length>50){const i=Math.ceil(t.length/3),n=[];for(let r=0;r<t.length;r+=i)n.push(t.substring(r,r+i));return n}return e}parseSubtopicsContent(t,e){console.log("=== 子主题内容处理（平级结构）===");const i=this.preprocessContent(t),n=this.markdownParser.parseToSections(i);if(console.log(`解析到 ${n.length} 个子主题`),n.length===0)return console.log("没有解析到标题结构，尝试智能提取"),this.intelligentTopicExtraction(i,e);const r=[],o=this.getNodeStyle(e);return n.forEach((a,l)=>{if(a.title&&a.title.trim()){console.log(`处理子主题 ${l+1}: ${a.title}`);const h=this.cleanTitle(a.title),d=this.cleanContent(a.content||""),c={id:tt(),title:h,content:d,children:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),aiGenerated:!0,aiMode:e,style:o,quality:this.assessNodeQuality(h,d)};c.quality.score>=60?r.push(c):console.warn("跳过低质量节点:",h,c.quality.issues)}}),console.log(`生成了 ${r.length} 个平级子主题节点`),this.deduplicateNodes(r)}parseSubtopicsFromParagraphs(t,e){console.log("从段落中提取子主题");const i=this.splitByParagraphs(t),n=[],r=this.getNodeStyle(e);return i.forEach((o,a)=>{if(o.trim().length>10){const l=this.extractTitleFromText(o),h={id:tt(),title:l,content:o.replace(l,"").trim(),children:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),aiGenerated:!0,aiMode:e,style:r};n.push(h)}}),console.log(`从段落生成了 ${n.length} 个平级子主题节点`),n}parseSmartContent(t,e){console.log("=== 智能内容处理 ===");const i=this.markdownParser.parseToSections(t);return console.log(`解析到 ${i.length} 个章节`),i.length===0?this.parseDetailedContent(t,e):i.some(r=>r.content&&r.content.length>50)?(console.log("检测到丰富内容，使用详细提取"),this.extractDetailedNodes(i,e)):(console.log("内容较简洁，使用大纲提取"),this.extractOutlineNodes(i,e))}parseOutlineContent(t,e){console.log("=== 大纲内容处理 ===");const i=[],n=this.getNodeStyle(e);return this.extractTitles(t).forEach((o,a)=>{if(o.trim()){const l={id:tt(),title:o.trim(),content:"",children:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),aiGenerated:!0,aiMode:e,style:n};i.push(l)}}),console.log(`大纲处理完成，生成 ${i.length} 个主题节点`),i}determineExtractionStrategy(t,e){const i=t.length,n=t.filter(a=>a.content&&a.content.trim().length>20).length,r=t.filter(a=>a.children&&a.children.length>0).length,o=t.reduce((a,l)=>a+(l.content?.length||0),0)/i;return console.log("内容分析:",{总章节数:i,有实质内容的章节:n,有子标题的章节:r,平均内容长度:Math.round(o)}),e==="outline"?"outline":e==="expand"||e==="analysis"||o>50&&n/i>.6?"detailed":i>5&&o<30?"outline":"mixed"}extractDetailedNodes(t,e){const i=[],n=this.getNodeStyle(e);return t.forEach((r,o)=>{if(!r.title)return;const a={id:tt(),title:r.title,content:"",children:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),aiGenerated:!0,aiMode:e,style:n};if(r.content&&r.content.trim()){const l=this.parseContentDirectly(r.content,e);if(l.length>0)l.forEach(h=>{const d={id:tt(),title:h.title,content:h.content||"",children:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),aiGenerated:!0,aiMode:e,style:{...n,opacity:.8}};a.children.push(d)}),console.log(`从内容中解析出 ${l.length} 个直接子节点`);else{const h={id:tt(),title:"详细内容",content:r.content.trim(),children:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),aiGenerated:!0,aiMode:e,style:{...n,opacity:.8}};a.children.push(h),console.log("内容无明确结构，创建详细内容子节点")}}if(r.children&&r.children.length>0&&r.children.forEach(l=>{a.children.push({id:tt(),title:l.title,content:l.content||"",children:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),aiGenerated:!0,aiMode:e,style:{...n,opacity:.8}})}),a.children.length===0&&r.content&&r.content.trim()){const l={id:tt(),title:"详细内容",content:r.content.trim(),children:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),aiGenerated:!0,aiMode:e,style:{...n,opacity:.8}};a.children.push(l),console.log(`添加默认详细内容子节点，内容长度: ${r.content.trim().length}`)}i.push(a)}),console.log(`详细提取完成，创建${i.length}个父节点，${i.reduce((r,o)=>r+o.children.length,0)}个子节点`),i}extractOutlineNodes(t,e){const i=[],n=this.getNodeStyle(e);return t.forEach(r=>{r.title&&(i.push({id:tt(),title:r.title,content:r.content?r.content.substring(0,100)+"...":"",children:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),aiGenerated:!0,aiMode:e,style:n}),r.children&&r.children.length>0&&r.children.forEach(o=>{i.push({id:tt(),title:o.title,content:o.content?o.content.substring(0,100)+"...":"",children:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),aiGenerated:!0,aiMode:e,style:{...n,opacity:.9}})}))}),console.log(`大纲提取完成，创建${i.length}个平级节点`),i}extractMixedNodes(t,e){const i=[];return this.getNodeStyle(e),t.forEach(n=>{if(!n.title)return;if(n.content&&n.content.trim().length>50){const o=this.extractDetailedNodes([n],e);i.push(...o)}else{const o=this.extractOutlineNodes([n],e);i.push(...o)}}),console.log(`混合提取完成，创建${i.length}个节点`),i}getNodeStyle(t){const e={expand:{backgroundColor:"#f0f9ff",borderColor:"#0ea5e9"},children:{backgroundColor:"#f0fdf4",borderColor:"#22c55e"},analysis:{backgroundColor:"#fef3c7",borderColor:"#f59e0b"},outline:{backgroundColor:"#f3e8ff",borderColor:"#a855f7"},creative:{backgroundColor:"#fce7f3",borderColor:"#ec4899"},related:{backgroundColor:"#f1f5f9",borderColor:"#64748b"}};return e[t]||e.children}parseContentDirectly(t,e){if(!t||!t.trim())return[];const i=[],n=t.split(`
`).map(a=>a.trim()).filter(a=>a);let r=null,o=[];if(n.forEach(a=>{const l=a.match(/^(#+)\s+(.+)$/),h=a.match(/^[-*+]\s+(.+)$/),d=a.match(/^\d+\.\s+(.+)$/),c=a.match(/^([^：:]+)[：:]\s*(.*)$/);if(l)r&&(r.content=o.join(`
`).trim(),r.title&&i.push(r)),r={title:l[2],content:""},o=[];else if(h||d)r&&(r.content=o.join(`
`).trim(),r.title&&i.push(r)),r={title:h?h[1]:d[1],content:""},o=[];else if(c&&!r){const f=c[1].trim(),g=c[2].trim();i.push({title:f,content:g})}else o.push(a)}),r&&(r.content=o.join(`
`).trim(),r.title&&i.push(r)),i.length===0&&t.trim()){const a=t.split(/\n\s*\n/).filter(l=>l.trim());if(a.length>1)return a.map((l,h)=>({title:this.extractTitleFromParagraph(l),content:l.trim()}))}return console.log(`直接解析内容，提取出 ${i.length} 个节点`),i}splitByHeaders(t){const e=[],i=t.split(`
`);let n=null;return i.forEach(r=>{const o=r.trim(),a=o.match(/^(#{1,6})\s+(.+)$/),l=o.match(/^[-*+]\s+(.+)$/),h=o.match(/^\d+\.\s+(.+)$/),d=o.match(/^([^：:]+)[：:]\s*(.*)$/);if(a||l||h||d){n&&e.push(n);let c="",f="";a?c=a[2]:l?c=l[1]:h?c=h[1]:d&&(c=d[1],f=d[2]||""),n={title:c,content:f}}else n&&o&&(n.content+=(n.content?`
`:"")+o)}),n&&e.push(n),e}splitByParagraphs(t){return t.split(/\n\s*\n/).map(e=>e.trim()).filter(e=>e.length>0)}extractTitleFromText(t){const i=t.split(`
`)[0].trim().replace(/^#+\s*/,"").replace(/^[-*+]\s*/,"").replace(/^\d+\.\s*/,"");return i.length<=30&&!i.includes("。")&&!i.includes(".")?i:i.substring(0,15)+(i.length>15?"...":"")}extractTitles(t){const e=[];return t.split(`
`).forEach(n=>{const r=n.trim(),o=r.match(/^(#{1,6})\s+(.+)$/),a=r.match(/^[-*+]\s+(.+)$/),l=r.match(/^\d+\.\s+(.+)$/),h=r.match(/^([^：:]+)[：:]\s*(.*)$/);o?e.push(o[2]):a?e.push(a[1]):l?e.push(l[1]):h&&e.push(h[1])}),e.length===0&&this.splitByParagraphs(t).forEach(r=>{const o=this.extractTitleFromText(r);o&&e.push(o)}),e}extractTitleFromParagraph(t){return this.extractTitleFromText(t)}splitContentIntoParts(t){if(!t||t.trim().length<50)return console.log("内容较短，不分割:",t.substring(0,30)),[];console.log("开始分割内容，原始长度:",t.length);const e=t.split(/\n\s*\n/).map(i=>i.trim()).filter(i=>i.length>0);if(console.log("分割后段落数:",e.length),e.length<=1){const i=t.split(/[。！？.!?]\s*/).filter(n=>n.trim().length>10);return i.length>1?(console.log("按句子分割，句子数:",i.length),i.map((n,r)=>({title:`要点 ${r+1}`,content:n.trim()+(n.match(/[。！？.!?]$/)?"":"。")}))):[]}if(e.length>4){const i=[],n=Math.ceil(e.length/3);for(let r=0;r<e.length;r+=n){const a=e.slice(r,r+n).join(`

`);i.push({title:`要点 ${Math.floor(r/n)+1}`,content:a}),console.log(`创建分组 ${Math.floor(r/n)+1}，内容长度: ${a.length}`)}return i}else{const i=e.map((n,r)=>({title:`要点 ${r+1}`,content:n.trim()}));return console.log("每段落一个要点，共",i.length,"个要点"),i}}parseOutlineContent(t){return this.markdownParser.parseToHierarchy(t)}findNodeById(t,e){if(t.id===e)return t;if(t.children)for(const i of t.children){const n=this.findNodeById(i,e);if(n)return n}return null}findParentNode(t,e){if(t.children)for(const i of t.children){if(i.id===e)return t;const n=this.findParentNode(i,e);if(n)return n}return null}deepClone(t){return JSON.parse(JSON.stringify(t))}preprocessContent(t){return t&&t.replace(/\n{3,}/g,`

`).replace(/^\d+[\.\)]\s*/gm,"").replace(/[【】「」]/g,"").replace(/^(好的|当然|让我)[^。\n]*[。\n]?/gm,"").trim()}intelligentTopicExtraction(t,e){console.log("使用智能提取算法");const i=t.split(/[。！？\n]/).filter(a=>a.trim().length>5),n=[],r=this.getNodeStyle(e);return this.extractTopicsFromSentences(i).forEach(a=>{const l={id:tt(),title:a.title,content:a.content,children:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),aiGenerated:!0,aiMode:e,style:r,confidence:a.confidence};a.confidence>=.6&&n.push(l)}),n}cleanTitle(t){return t.replace(/^[#\s]*/,"").replace(/[：:]\s*$/,"").replace(/^\d+[\.\)]\s*/,"").trim()}cleanContent(t){return t.replace(/^[#\s]*/,"").replace(/详细描述|具体说明|进一步完善/g,"").trim()}assessNodeQuality(t,e){const i=[];let n=100;return(!t||t.length<2)&&(i.push("标题过短"),n-=30),t.length>50&&(i.push("标题过长"),n-=20),e&&e.length>200&&(i.push("内容过长"),n-=10),[/详细描述|具体说明|进一步完善/,/仅供参考|根据实际情况/,/请注意|需要注意/].forEach(o=>{(o.test(t)||o.test(e))&&(i.push("包含无意义内容"),n-=15)}),{score:Math.max(0,n),issues:i}}deduplicateNodes(t){const e=new Set;return t.filter(i=>{const n=i.title.toLowerCase().trim();return e.has(n)?!1:(e.add(n),!0)})}extractTopicsFromSentences(t){const e=[],i=new Map;return t.forEach(r=>{(r.match(/[\u4e00-\u9fa5]{2,}/g)||[]).forEach(a=>{i.set(a,(i.get(a)||0)+1)})}),this.clusterSentencesByKeywords(t,i).forEach((r,o)=>{if(r.sentences.length>0){const a=this.generateTopicTitle(r.keywords),l=r.sentences.join("。");e.push({title:a,content:l,confidence:r.confidence})}}),e}clusterSentencesByKeywords(t,e){const i=[];return Array.from(e.entries()).sort((r,o)=>o[1]-r[1]).slice(0,5).map(([r])=>r).forEach(r=>{const o=t.filter(a=>a.includes(r));o.length>0&&i.push({keywords:[r],sentences:o,confidence:Math.min(.9,o.length/t.length*2)})}),i}generateTopicTitle(t){if(t.length===0)return"未命名主题";const e=t[0],i=["关于","基于","针对",""],n=["方案","策略","方法","要点",""],r=Math.random()>.7?i[Math.floor(Math.random()*i.length)]:"",o=Math.random()>.5?n[Math.floor(Math.random()*n.length)]:"";return`${r}${e}${o}`.trim()}}class ou{parseToSections(t){const e=t.split(`
`),i=[];let n=null,r=null;for(let o=0;o<e.length;o++){const l=e[o].trim();if(!l)continue;const h=this.getHeaderLevel(l);if(h===2){n&&i.push(n),n={title:this.extractTitle(l,2),content:"",children:[],rawContent:""},r=null;const d=this.collectSectionContent(e,o+1,2);n.content=d.content,n.rawContent=d.raw}else if(h===3&&n){r={title:this.extractTitle(l,3),content:"",rawContent:""};const d=this.collectSectionContent(e,o+1,3);r.content=d.content,r.rawContent=d.raw,n.children.push(r)}else if(h===1)continue}return n&&i.push(n),i.filter(o=>o.title&&(o.content.trim()||o.children.length>0))}collectSectionContent(t,e,i){const n=[],r=[];for(let o=e;o<t.length;o++){const a=t[o],l=a.trim(),h=this.getHeaderLevel(l);if(h>0&&h<=i)break;r.push(a),!(!l||h>i&&h>0)&&(l.startsWith("- ")||l.startsWith("* ")||l.match(/^\d+\.\s/)||l)&&n.push(l)}return{content:n.join(`
`).trim(),raw:r.join(`
`).trim()}}extractTitle(t,e){const i="#".repeat(e)+" ";return t.startsWith(i)?t.substring(i.length).trim():t.trim()}getHeaderLevel(t){const e=t.match(/^(#+)\s/);return e?e[1].length:0}cleanContent(t){return t.replace(/^\s*[-*]\s*/gm,"").replace(/\n{3,}/g,`

`).replace(/^\s+|\s+$/g,"").trim()}parseToHierarchy(t){const e=t.split(`
`),i=[],n=[];for(const r of e){const o=r.trim();if(!o)continue;const a=this.getHeaderLevel(o);if(a>0){const l=o.substring(a+1).trim(),h={id:tt(),title:l,content:"",children:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),aiGenerated:!0};for(;n.length>=a;)n.pop();if(n.length===0)i.push(h);else{const d=n[n.length-1];d.children||(d.children=[]),d.children.push(h)}n.push(h)}else if(n.length>0){const l=n[n.length-1];l.content+=(l.content?`
`:"")+o}}return i}getHeaderLevel(t){const e=t.match(/^(#+)\s/);return e?e[1].length:0}}const au={class:"split-mode"},lu={__name:"AdvancedMarkdownEditor",props:{modelValue:{type:String,default:""},title:{type:String,default:"未命名文档"},mode:{type:String,default:"edit",validator:s=>["edit","mindmap"].includes(s)},editFontSize:{type:Number,default:14},mindmapFontSize:{type:Number,default:12}},emits:["update:modelValue","save","export","content-change"],setup(s,{expose:t,emit:e}){const i=new br,n=new ru,r=s,o=e,a=En(),l=Hs(),h=U(r.mode),d=U(!1),c=U(null),f=gt({get:()=>a.selectedModel,set:A=>a.setSelectedModel(A)}),g=gt(()=>l.modelOptions.map(O=>({id:O.value,name:O.label,providerId:O.providerId,providerName:O.providerName}))),u=U(r.modelValue),m=U(null),p=U(!1),x=U(null),E=gt(()=>a.theme==="dark"),_=gt(()=>{const A={fontSize:`${r.editFontSize}px`};return console.log("编辑器字体样式:",A),A});gt(()=>{const A={fontSize:`${r.mindmapFontSize}px`};return console.log("思维导图字体样式:",A),A}),gt(()=>({bold:!0,italic:!0,header:!0,underline:!0,strikethrough:!0,mark:!0,superscript:!0,subscript:!0,quote:!0,ol:!0,ul:!0,link:!0,imagelink:!0,code:!0,table:!0,fullscreen:!0,readmodel:!0,htmlcode:!0,help:!0,undo:!0,redo:!0,trash:!0,save:!0,navigation:!0})),Nt(()=>r.modelValue,A=>{u.value=A,h.value==="mindmap"&&L()}),Nt(()=>r.title,(A,O)=>{O&&A!==O&&(console.log("检测到文件切换，从",O,"到",A),T())}),Nt(()=>r.mode,(A,O)=>{if(console.log("模式切换:",O,"->",A),O==="mindmap"&&m.value){console.log("从思维导图模式切换出去，同步数据到markdown"),p.value=!0;const H=i.convertMindmapToMarkdown(m.value);u.value=H,o("update:modelValue",H),xe(()=>{p.value=!1})}h.value=A,A==="mindmap"?(console.log("切换到思维导图模式，从markdown更新数据"),(u.value||r.title)&&L()):O==="mindmap"&&(console.log("清理思维导图相关状态"),c.value=null,d.value=!1)}),Nt(u,A=>{o("update:modelValue",A),h.value==="mindmap"&&!p.value&&L()}),Sn(async()=>{l.initialized||await l.loadProviders()}),Ln(()=>{const A=document.getElementById("markdown-theme-override-dynamic");A&&A.remove()});const T=()=>{if(console.log("清理编辑器数据..."),m.value=null,c.value=null,d.value=!1,p.value=!1,x.value)try{x.value.destroyMindmapInstance()}catch(A){console.warn("销毁思维导图实例失败:",A)}console.log("编辑器数据清理完成")},C=A=>{u.value=A,o("update:modelValue",A),o("content-change")},L=()=>{try{if(!u.value||!r.title){m.value=i.createEmptyMindmap(r.title||"未命名文档");return}const A=i.parseMarkdownToMindmap(u.value,r.title,{showContentAsNodes:!0});A&&A.id&&A.title?m.value=A:(console.warn("生成的思维导图数据无效，使用空数据"),m.value=i.createEmptyMindmap(r.title||"未命名文档"))}catch(A){console.error("更新思维导图数据失败:",A),m.value=i.createEmptyMindmap(r.title||"未命名文档")}},F=A=>{console.log("节点点击:",A)},W=A=>{c.value=A,d.value=!0},X=A=>{console.log("=== AI生成请求调试信息 ==="),console.log("接收到的参数:",A),console.log("节点信息:",A.node),console.log("当前mindmapData根节点:",m.value?.title),A.mindmapData?(console.log("使用传递的最新mindmapData"),m.value=A.mindmapData):console.warn("参数中没有mindmapData，使用本地数据"),console.log("更新后的mindmapData根节点:",m.value?.title),console.log("mindmapData是否包含目标节点:",Mt(m.value,A.node.id)),c.value=A.node,d.value=!0},Mt=(A,O)=>{if(!A||!O)return!1;console.log(`🔍 在数据中查找节点ID: ${O} (类型: ${typeof O})`);const H=(kt,Et=[])=>{const se=[...Et,kt.title];return console.log(`  检查节点: ${kt.id} (类型: ${typeof kt.id}) - ${kt.title}`),kt.id===O?(console.log(`  ✅ 找到匹配节点! 路径: ${se.join(" → ")}`),!0):kt.children?kt.children.some(Gt=>H(Gt,se)):!1},rt=H(A);return console.log(`查找结果: ${rt?"找到":"未找到"}`),rt},ct=A=>{console.log("收到思维导图Markdown变化，长度:",A.length),p.value=!0,u.value=A,o("update:modelValue",A),o("content-change"),xe(()=>{p.value=!1})},Lt=A=>{try{if(console.log("处理AI生成内容:",A),console.log("当前节点ID:",A.node?.id),h.value==="mindmap"&&x.value){const H=n.parseContentToNodes(A.content,A.mode);if(H&&H.length>0){console.log("准备添加节点到父节点ID:",A.node.id),x.value.addAIGeneratedNodes(A.node.id,H),console.log(`AI生成了${H.length}个节点，已直接添加到思维导图`),console.log("=== 数据同步调试 ==="),console.log("生成前的mindmapData:",JSON.stringify(m.value,null,2));const rt=x.value.getCurrentMindmapData();console.log("生成后的updatedMindmapData:",JSON.stringify(rt,null,2)),rt?(console.log("数据结构对比:"),console.log("- 原始根节点ID:",m.value?.id,"类型:",typeof m.value?.id),console.log("- 更新根节点ID:",rt?.id,"类型:",typeof rt?.id),console.log("- 原始子节点数量:",m.value?.children?.length||0),console.log("- 更新子节点数量:",rt?.children?.length||0),m.value=rt,console.log("✅ 已同步更新mindmapData")):console.error("❌ 无法获取更新后的思维导图数据")}else console.warn("AI生成的内容无法解析为有效节点")}else{const H=n.applyGeneratedContent(A,m.value);m.value=H,p.value=!0;const rt=i.convertMindmapToMarkdown(H);u.value=rt,o("update:modelValue",rt),xe(()=>{p.value=!1})}const O={subtopics:"生成子主题",children:"要点分解",analysis:"多角度分析",creative:"创意发散"};$.success(`${O[A.mode]||"AI生成"}完成`)}catch(O){console.error("应用AI生成内容失败:",O),$.error("应用AI生成内容失败: "+O.message)}};return t({saveDocument:()=>{o("save",{title:r.title,content:u.value})},exportDocument:()=>{o("export",{title:r.title,content:u.value})},getCurrentContent:()=>u.value,setContent:A=>{u.value=A},clearEditorData:T}),(A,O)=>{const H=Si("v-md-editor");return Z(),ht("div",{class:pe(["advanced-markdown-editor",{dark:E.value,"theme-dark":E.value}])},[w("div",{class:pe(["editor-content",`mode-${h.value}`])},[ne(w("div",{class:"edit-mode",style:nn({..._.value,"--preview-font-size":`${r.editFontSize}px`})},[N(H,{modelValue:u.value,"onUpdate:modelValue":O[0]||(O[0]=rt=>u.value=rt),height:"100%",onChange:C},null,8,["modelValue"])],4),[[fe,h.value==="edit"]]),ne(w("div",au,[N(H,{modelValue:u.value,"onUpdate:modelValue":O[1]||(O[1]=rt=>u.value=rt),height:"100%",onChange:C},null,8,["modelValue"])],512),[[fe,h.value==="split"]]),ne(N(mc,{ref_key:"mindmapCanvasRef",ref:x,"mindmap-data":m.value,"font-size":r.mindmapFontSize,"is-dark-theme":E.value,"available-models":g.value,"selected-model":f.value,"onUpdate:selectedModel":O[2]||(O[2]=rt=>f.value=rt),onNodeClick:F,onAiGenerate:W,onAiGenerateRequest:X,onMarkdownChange:ct},null,8,["mindmap-data","font-size","is-dark-theme","available-models","selected-model"]),[[fe,h.value==="mindmap"]])],2),N(su,{visible:d.value,"onUpdate:visible":O[3]||(O[3]=rt=>d.value=rt),"current-node":c.value,"selected-model":f.value,"book-title":r.title,"mindmap-data":m.value,onContentGenerated:Lt},null,8,["visible","current-node","selected-model","book-title","mindmap-data"])],2)}}},hu=ui(lu,[["__scopeId","data-v-e457d1bf"]]),du={class:"workspace-header"},cu={class:"header-left"},uu={key:0,class:"document-info"},fu={class:"document-title"},pu={key:0,class:"document-status"},mu={key:1,class:"no-document"},gu={class:"header-center"},vu={class:"header-right"},yu={class:"workspace-body"},xu={class:"sidebar-header"},wu={class:"sidebar-title-area"},_u={class:"sidebar-title"},Mu={class:"sidebar-actions"},Eu={class:"sidebar-content"},Cu={class:"sidebar-collapsed-actions"},Nu={class:"collapsed-btn-group"},Tu={key:1,class:"welcome-screen"},Su={class:"welcome-content"},Lu={class:"welcome-icon"},bu={class:"welcome-actions"},Du={class:"welcome-features"},Au={class:"feature-item"},Ru={class:"feature-item"},Iu={class:"feature-item"},Ou={key:0,class:"workspace-footer"},ku={class:"footer-left"},zu={class:"status-item"},Fu={class:"status-item"},Pu={class:"status-item"},Bu={class:"footer-right"},$u={class:"status-item"},Hu={key:0,class:"status-item"},Uu={__name:"AdvancedMarkdownEditor",setup(s){const t=En(),e=U([]),i=U(""),n=U(!1),r=U(!0),o=U(!1),a=U(null),l=U("edit"),h=U(!1),d=U(null),c=U(!0),f=U(3e4),g=U(null),u=U(null),m=U(14),p=U(12),x=U(""),E=U({title:"",template:"blank"}),_=[{label:"编辑",value:"edit"},{label:"思维导图",value:"mindmap"}],T=gt(()=>t.theme==="dark"),C=gt(()=>e.value.find(S=>S.id===i.value)),L=gt(()=>{if(!C.value?.content)return{characters:0,words:0};const S=C.value.content,R=S.length,it=S.trim()?S.trim().split(/\s+/).length:0;return{characters:R,words:it}}),F={blank:"",tech:`# 技术文档

## 概述

简要描述项目或技术的目标和用途。

## 架构设计

### 系统架构

描述整体系统架构。

### 技术栈

- 前端：
- 后端：
- 数据库：

## 实现细节

### 核心功能

#### 功能1

详细描述功能实现。

#### 功能2

详细描述功能实现。

## API文档

### 接口列表

| 接口 | 方法 | 描述 |
|------|------|------|
| /api/example | GET | 示例接口 |

## 部署说明

### 环境要求

- Node.js >= 16
- Python >= 3.8

### 部署步骤

1. 克隆代码
2. 安装依赖
3. 配置环境
4. 启动服务

## 常见问题

### Q: 如何解决问题A？

A: 解决方案描述。
`,project:`# 项目计划

## 项目概述

### 项目目标

明确项目要达成的目标。

### 项目范围

定义项目的边界和范围。

## 项目时间线

### 阶段1：需求分析（Week 1-2）

- [ ] 收集用户需求
- [ ] 分析技术可行性
- [ ] 制定技术方案

### 阶段2：设计阶段（Week 3-4）

- [ ] UI/UX设计
- [ ] 系统架构设计
- [ ] 数据库设计

### 阶段3：开发阶段（Week 5-8）

- [ ] 前端开发
- [ ] 后端开发
- [ ] 接口联调

### 阶段4：测试阶段（Week 9-10）

- [ ] 单元测试
- [ ] 集成测试
- [ ] 用户验收测试

### 阶段5：部署上线（Week 11）

- [ ] 生产环境部署
- [ ] 性能优化
- [ ] 监控配置

## 资源分配

### 团队成员

| 姓名 | 角色 | 职责 |
|------|------|------|
| 张三 | 项目经理 | 项目管理 |
| 李四 | 前端开发 | 前端实现 |

### 预算规划

- 人力成本：
- 硬件成本：
- 其他费用：

## 风险管理

### 技术风险

- 风险1：描述及应对措施
- 风险2：描述及应对措施

### 进度风险

- 风险1：描述及应对措施
- 风险2：描述及应对措施
`,notes:`# 学习笔记

## 学习目标

明确本次学习要达成的目标。

## 核心概念

### 概念1

详细解释概念1的定义和重要性。

### 概念2

详细解释概念2的定义和重要性。

## 重点知识

### 知识点1

#### 定义

给出准确的定义。

#### 示例

提供具体的示例说明。

#### 应用场景

说明在什么情况下使用。

### 知识点2

#### 定义

给出准确的定义。

#### 示例

提供具体的示例说明。

## 实践练习

### 练习1

描述练习内容和要求。

### 练习2

描述练习内容和要求。

## 总结

### 关键收获

- 收获1
- 收获2
- 收获3

### 待深入学习

- 需要进一步学习的内容1
- 需要进一步学习的内容2

## 参考资料

- [资料1](链接)
- [资料2](链接)
`};Nt(m,S=>{ct("edit",S)}),Nt(p,S=>{ct("mindmap",S)}),Nt(r,S=>{bt({showSidebar:S})}),Nt(o,S=>{bt({sidebarCollapsed:S})}),Nt(l,S=>{bt({defaultMode:S}),S==="mindmap"&&b()}),Nt(i,(S,R)=>{if(Ce(),R&&S!==R&&(console.log("检测到文档切换，从",R,"到",S,"，重置为编辑模式"),l.value="edit",h.value=!1,u.value))try{u.value.clearEditorData()}catch(it){console.warn("清理编辑器数据失败:",it)}S&&c.value&&Ee()}),Nt(c,S=>{S&&C.value?Ee():Ce()}),Nt(f,()=>{c.value&&C.value&&ge()});const W=S=>{S.ctrlKey&&S.key==="s"&&(S.preventDefault(),C.value&&Et())};Sn(async()=>{await X(),await Dt(),x.value&&console.log("自动加载目录:",x.value),document.addEventListener("keydown",W)}),Ln(()=>{Ce(),document.removeEventListener("keydown",W)});const X=async()=>{try{await t.loadConfig();const S=t.markdownEditor;if(S){m.value=S.fontSize?.edit||14,p.value=S.fontSize?.mindmap||12,r.value=S.preferences?.showSidebar!==!1,o.value=S.preferences?.sidebarCollapsed||!1;const R=S.preferences?.defaultMode||"edit";l.value=R==="preview"||R==="mindmap"?"edit":R,c.value=S.preferences?.autoSave!==!1,f.value=S.preferences?.autoSaveInterval||3e4,x.value=S.lastSelectedDirectory||""}}catch(S){console.error("加载编辑器配置失败:",S)}},Mt=async S=>{try{await t.updateConfigItem("markdownEditor",{...t.markdownEditor,...S})}catch(R){console.error("保存编辑器配置失败:",R)}},ct=async(S,R)=>{const it={fontSize:{...t.markdownEditor?.fontSize,[S]:R}};await Mt(it)},Lt=async S=>{await Mt({lastSelectedDirectory:S})},bt=async S=>{const R={preferences:{...t.markdownEditor?.preferences,...S}};await Mt(R)},Dt=async()=>{try{const S=localStorage.getItem("advanced-markdown-docs");S?(e.value=JSON.parse(S),e.value.length>0&&(i.value=e.value[0].id)):(e.value=[],i.value=null)}catch(S){console.error("加载文档失败:",S),$.error("加载文档失败"),e.value=[],i.value=null}},A=()=>{try{localStorage.setItem("advanced-markdown-docs",JSON.stringify(e.value))}catch(S){console.error("保存文档失败:",S),$.error("保存文档失败")}},O=()=>{E.value={title:"",template:"blank"},n.value=!0},H=async()=>{if(!E.value.title.trim()){$.warning("请输入文档标题");return}try{if(!x.value){$.warning("请先选择一个目录来保存文档");return}let S=E.value.title.trim();S.toLowerCase().endsWith(".md")||(S+=".md");const R=x.value.includes("\\")?"\\":"/";let it=`${x.value}${R}${S}`,St=1,B=S;for(;;)try{const zt=await window.pywebview.api.get_file_info(it);if((typeof zt=="string"?JSON.parse(zt):zt).status==="error")break;S=`${B.replace(".md","")}_${St}.md`,it=`${x.value}${R}${S}`,St++}catch{break}const mt=F[E.value.template]||"",xt=await window.pywebview.api.write_file(it,mt),re=typeof xt=="string"?JSON.parse(xt):xt;if(re.status==="success"){const zt={id:rn(),title:S.replace(".md",""),content:mt,filePath:it,createdAt:new Date,updatedAt:new Date};e.value.unshift(zt),await xe(),i.value=zt.id,l.value="edit",A(),g.value&&await g.value.refreshDirectory(),n.value=!1,$.success("文档创建成功，已切换到编辑模式")}else $.error("创建文档失败: "+re.message)}catch(S){console.error("创建文档失败:",S),$.error("创建文档失败: "+S.message)}},rt=()=>{C.value&&Et()},kt=S=>{Et(S)},Et=async S=>{if(C.value)try{if(C.value.filePath){const it=(S?S.content:C.value.content)||"",St=await window.pywebview.api.write_file(C.value.filePath,it),B=typeof St=="string"?JSON.parse(St):St;B.status==="success"?(S&&(C.value.title=S.title,C.value.content=S.content),C.value.updatedAt=new Date,h.value=!1,A(),$.success("文件保存成功")):$.error("保存文件失败: "+B.message)}else S&&(C.value.title=S.title,C.value.content=S.content),C.value.updatedAt=new Date,h.value=!1,A(),$.success("文档保存成功")}catch(R){console.error("保存文档失败:",R),$.error("保存文档失败: "+R.message)}},se=()=>{r.value=!r.value},Gt=()=>{h.value=!0,c.value&&C.value&&ge()},Ee=()=>{!c.value||!C.value||(d.value=setTimeout(()=>{h.value&&C.value&&Y()},f.value))},ge=()=>{d.value&&clearTimeout(d.value),Ee()},Ce=()=>{d.value&&(clearTimeout(d.value),d.value=null)},Y=async()=>{if(!(!C.value||!h.value))try{await Et(),console.log("自动保存完成")}catch(S){console.error("自动保存失败:",S)}},b=()=>{if(!C.value){if(e.value.length>0){i.value=e.value[0].id,$.info("已自动选择第一个文档用于思维导图显示");return}j();return}(!C.value.content||C.value.content.trim()==="")&&zs.confirm("当前文档内容为空，思维导图需要有内容才能正常显示。是否要添加一些示例内容？","提示",{confirmButtonText:"添加示例内容",cancelButtonText:"保持空白",type:"info"}).then(()=>{C.value.content=vt(),C.value.updatedAt=new Date,A(),$.success("已添加示例内容，可以开始使用思维导图功能")}).catch(()=>{$.info("思维导图模式需要有标题结构的内容才能正常显示")})},j=()=>{const S={id:Date.now().toString(),title:"思维导图示例文档",content:vt(),createdAt:new Date,updatedAt:new Date};e.value.unshift(S),i.value=S.id,A(),$.success("已创建示例文档，可以开始使用思维导图功能")},vt=()=>`# 思维导图示例

这是一个用于演示思维导图功能的示例文档。

## 主要功能

### 自动解析标题
思维导图会自动解析Markdown中的标题层级，将其转换为可视化的节点结构。

### AI辅助生成
每个节点都支持AI辅助内容生成，帮助您扩展思路。

#### 生成类型
- 扩展内容：为当前节点添加更多详细内容
- 生成子节点：基于当前节点生成下级主题
- 相关内容：生成与当前节点相关的并列内容

## 使用方法

### 基本操作
1. 在编辑模式下编写带有标题结构的内容
2. 切换到思维导图模式查看可视化结构
3. 鼠标悬停在节点上显示AI按钮

### 高级功能
- 支持节点拖拽调整位置
- 支持多种布局样式
- 支持缩放和平移操作

## 开始使用

现在您可以：
- 修改这些内容来创建自己的思维导图
- 使用AI功能扩展节点内容
- 尝试不同的布局样式`,q=S=>S?S.split(/[/\\]/).pop():"",$t=S=>{a.value=S,S&&!S.is_directory&&S.name.toLowerCase().endsWith(".md")&&Yt(S)},Ht=async S=>{x.value=S,await Lt(S),console.log("目录已保存到配置:",S)},Yt=async S=>{if(!(!S||!S.path))try{const R=await window.pywebview.api.read_file(S.path),it=typeof R=="string"?JSON.parse(R):R;if(it.status==="success"){console.log("开始创建文档对象...");const St={id:rn(),title:S.name.replace(".md",""),content:it.data||"",filePath:S.path,createdAt:new Date(S.modified_time*1e3),updatedAt:new Date(S.modified_time*1e3)},B=e.value.find(mt=>mt.filePath===S.path);if(B){console.log("文件已存在，切换到现有文档"),i.value=B.id,l.value="edit",h.value=!1,console.log("文件切换完成，已重置为编辑模式");return}console.log("添加新文档到列表..."),e.value.unshift(St),console.log("文档已添加到列表"),console.log("设置当前文档ID..."),await xe(),i.value=St.id,l.value="edit",h.value=!1,console.log("当前文档ID已设置:",i.value),console.log("文件打开完成，已重置为编辑模式"),console.log("文档列表长度:",e.value.length)}else $.error("读取文件失败: "+it.message)}catch(R){console.error("打开文件失败:",R),$.error("打开文件失败: "+R.message)}};return(S,R)=>{const it=Bi,St=Zr,B=Nn,mt=Fs,xt=Qr,re=Jr,zt=to,Wt=Si("Edit"),ce=Si("View"),te=Si("Share"),wt=Cn,y=io,v=Bs,D=Ps,I=eo,P=$s;return Z(),ht("div",{class:pe(["markdown-editor-workspace",{dark:T.value}])},[w("div",du,[w("div",cu,[N(it,{onClick:se,icon:Q(Kr),size:"default",text:"",class:"sidebar-toggle",title:"切换侧边栏"},null,8,["icon"]),C.value?(Z(),ht("div",uu,[w("span",fu,dt(C.value.title),1),h.value?(Z(),ht("span",pu,"•")):It("",!0)])):(Z(),ht("span",mu,"未选择文档"))]),w("div",gu,[N(St,{modelValue:l.value,"onUpdate:modelValue":R[0]||(R[0]=k=>l.value=k),options:_,size:"default"},null,8,["modelValue"])]),w("div",vu,[N(zt,{trigger:"click",placement:"bottom-end"},{dropdown:z(()=>[N(re,null,{default:z(()=>[w("div",{class:"font-control-panel",onClick:R[9]||(R[9]=ue(()=>{},["stop"]))},[R[18]||(R[18]=w("div",{class:"font-control-header"},[w("span",{class:"panel-title"},"字体大小设置")],-1)),N(xt,{divided:"",onClick:R[4]||(R[4]=ue(()=>{},["stop"]))},{default:z(()=>[w("div",{class:"font-control-item",onClick:R[3]||(R[3]=ue(()=>{},["stop"]))},[R[16]||(R[16]=w("span",null,"编辑器:",-1)),N(mt,{modelValue:m.value,"onUpdate:modelValue":R[1]||(R[1]=k=>m.value=k),min:10,max:24,size:"small",style:{width:"80px"},onClick:R[2]||(R[2]=ue(()=>{},["stop"]))},null,8,["modelValue"])])]),_:1}),l.value==="mindmap"?(Z(),oe(xt,{key:0,onClick:R[8]||(R[8]=ue(()=>{},["stop"]))},{default:z(()=>[w("div",{class:"font-control-item",onClick:R[7]||(R[7]=ue(()=>{},["stop"]))},[R[17]||(R[17]=w("span",null,"思维导图:",-1)),N(mt,{modelValue:p.value,"onUpdate:modelValue":R[5]||(R[5]=k=>p.value=k),min:8,max:20,size:"small",style:{width:"80px"},onClick:R[6]||(R[6]=ue(()=>{},["stop"]))},null,8,["modelValue"])])]),_:1})):It("",!0)])]),_:1})]),default:z(()=>[N(it,{size:"small",text:""},{default:z(()=>[N(B,null,{default:z(()=>[N(Q(sn))]),_:1}),R[15]||(R[15]=st(" 字体 "))]),_:1})]),_:1}),N(it,{onClick:rt,icon:Q(Di),disabled:!C.value,size:"default",type:"primary"},{default:z(()=>R[19]||(R[19]=[st(" 保存 ")])),_:1},8,["icon","disabled"])])]),w("div",yu,[ne(w("div",{class:pe(["sidebar",{collapsed:o.value}])},[w("div",xu,[w("div",wu,[N(B,{class:"sidebar-icon"},{default:z(()=>[N(Q(Li))]),_:1}),ne(w("span",_u,"资源管理器",512),[[fe,!o.value]])]),ne(w("div",Mu,[N(it,{onClick:O,icon:Q(si),size:"small",text:"",class:"action-btn"},null,8,["icon"])],512),[[fe,!o.value]])]),ne(w("div",Eu,[N(No,{ref_key:"fileManagerRef",ref:g,"selected-file":a.value,"last-selected-directory":x.value,onFileSelected:$t,onFileOpened:Yt,onDirectorySelected:Ht,onCreateDocument:O},null,8,["selected-file","last-selected-directory"])],512),[[fe,!o.value]]),ne(w("div",Cu,[w("div",Nu,[N(it,{onClick:O,icon:Q(si),size:"small",text:"",class:"expand-btn"},null,8,["icon"])])],512),[[fe,o.value]])],2),[[fe,r.value]]),w("div",{class:pe(["main-editor",{"full-width":!r.value,"sidebar-collapsed":r.value&&o.value}])},[C.value?(Z(),oe(hu,{key:0,ref_key:"advancedMarkdownEditorRef",ref:u,modelValue:C.value.content,"onUpdate:modelValue":R[10]||(R[10]=k=>C.value.content=k),title:C.value.title,mode:l.value,"edit-font-size":m.value,"mindmap-font-size":p.value,onSave:kt,onContentChange:Gt},null,8,["modelValue","title","mode","edit-font-size","mindmap-font-size"])):(Z(),ht("div",Tu,[w("div",Su,[w("div",Lu,[N(B,{size:"80",color:"var(--el-color-primary)"},{default:z(()=>[N(Wt)]),_:1})]),R[25]||(R[25]=w("h2",{class:"welcome-title"},"欢迎使用高级Markdown编辑器",-1)),R[26]||(R[26]=w("p",{class:"welcome-subtitle"},"功能强大的Markdown编辑器，支持编辑模式和AI思维导图模式",-1)),w("div",bu,[N(it,{type:"primary",onClick:O,icon:Q(si),size:"large"},{default:z(()=>R[20]||(R[20]=[st(" 创建新文档 ")])),_:1},8,["icon"]),r.value?It("",!0):(Z(),oe(it,{key:0,onClick:se,icon:Q(Li),size:"large"},{default:z(()=>R[21]||(R[21]=[st(" 打开文件面板 ")])),_:1},8,["icon"]))]),w("div",Du,[w("div",Au,[N(B,null,{default:z(()=>[N(Wt)]),_:1}),R[22]||(R[22]=w("span",null,"强大的编辑功能",-1))]),w("div",Ru,[N(B,null,{default:z(()=>[N(ce)]),_:1}),R[23]||(R[23]=w("span",null,"实时预览",-1))]),w("div",Iu,[N(B,null,{default:z(()=>[N(te)]),_:1}),R[24]||(R[24]=w("span",null,"AI思维导图",-1))])])])]))],2)]),C.value?(Z(),ht("div",Ou,[w("div",ku,[w("span",zu,[N(B,null,{default:z(()=>[N(Q(Di))]),_:1}),st(" "+dt(C.value.title),1)]),w("span",Fu," 字符数: "+dt(L.value.characters),1),w("span",Pu," 字数: "+dt(L.value.words),1)]),w("div",Bu,[w("span",$u,dt(l.value==="edit"?"编辑模式":"思维导图模式"),1),C.value.filePath?(Z(),ht("span",Hu,dt(q(C.value.filePath)),1)):It("",!0)])])):It("",!0),N(P,{modelValue:n.value,"onUpdate:modelValue":R[14]||(R[14]=k=>n.value=k),title:"新建文档",width:"400px"},{footer:z(()=>[N(it,{onClick:R[13]||(R[13]=k=>n.value=!1)},{default:z(()=>R[27]||(R[27]=[st("取消")])),_:1}),N(it,{type:"primary",onClick:H},{default:z(()=>R[28]||(R[28]=[st("创建")])),_:1})]),default:z(()=>[N(I,{model:E.value,"label-width":"80px"},{default:z(()=>[N(y,{label:"文档标题",required:""},{default:z(()=>[N(wt,{modelValue:E.value.title,"onUpdate:modelValue":R[11]||(R[11]=k=>E.value.title=k),placeholder:"输入文档标题",onKeyup:no(H,["enter"])},null,8,["modelValue"])]),_:1}),N(y,{label:"初始内容"},{default:z(()=>[N(D,{modelValue:E.value.template,"onUpdate:modelValue":R[12]||(R[12]=k=>E.value.template=k),placeholder:"选择模板"},{default:z(()=>[N(v,{label:"空白文档",value:"blank"}),N(v,{label:"技术文档模板",value:"tech"}),N(v,{label:"项目计划模板",value:"project"}),N(v,{label:"学习笔记模板",value:"notes"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])],2)}}},l1=ui(Uu,[["__scopeId","data-v-241cda93"]]);export{l1 as default};
