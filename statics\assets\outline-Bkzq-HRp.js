import{_ as it,r as k,bt as dt,c as J,o as rt,w as Fe,E as V,Y,$ as ut,a8 as ve,af as ct,b as c,m as r,e as t,F as m,d as s,g as i,t as pt,v as y,B as mt,C as w,a9 as G,s as vt,V as yt,R as S,S as x,n as O,c1 as ft,J as _t,h as gt,au as se,M as z,aS as ht,bJ as bt,p as f,bk as Oe,bj as $t,bn as wt,b2 as kt,U as ze,aH as Vt,aU as Pe,c2 as St,aM as xt,j as Ut,q as Ct,aD as Tt,aE as Et,bh as Dt,bc as Nt,bb as Ft,ba as Ot,c3 as zt,aO as Pt,c4 as At,c5 as Lt,c6 as Yt,c7 as Bt,c8 as Ae,ap as It,aI as Mt,y as jt,aw as Rt,aG as Jt,ah as ne}from"./entry-DxFfH4M0.js";/* empty css                   *//* empty css                  *//* empty css                    *//* empty css                  *//* empty css                   *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    *//* empty css                *//* empty css                *//* empty css               *//* empty css                   *//* empty css                 */import{u as Gt,i as Le,a as Wt,b as Ht,c as Kt,d as qt,e as Qt,f as Xt,g as Zt,h as el,j as tl,k as ll,l as sl,m as nl}from"./installCanvasRenderer-Cl52Q2Ud.js";const C=[];for(let b=0;b<256;++b)C.push((b+256).toString(16).slice(1));function ol(b,U=0){return(C[b[U+0]]+C[b[U+1]]+C[b[U+2]]+C[b[U+3]]+"-"+C[b[U+4]]+C[b[U+5]]+"-"+C[b[U+6]]+C[b[U+7]]+"-"+C[b[U+8]]+C[b[U+9]]+"-"+C[b[U+10]]+C[b[U+11]]+C[b[U+12]]+C[b[U+13]]+C[b[U+14]]+C[b[U+15]]).toLowerCase()}let ye;const al=new Uint8Array(16);function il(){if(!ye){if(typeof crypto>"u"||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");ye=crypto.getRandomValues.bind(crypto)}return ye(al)}const dl=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),Ye={randomUUID:dl};function W(b,U,fe){if(Ye.randomUUID&&!b)return Ye.randomUUID();b=b||{};const P=b.random??b.rng?.()??il();if(P.length<16)throw new Error("Random bytes length must be >= 16");return P[6]=P[6]&15|64,P[8]=P[8]&63|128,ol(P)}const rl={class:"outline-header"},ul={class:"left-section"},cl={class:"outline-selector-wrapper"},pl={class:"outline-selector"},ml={class:"current-outline-info"},vl={class:"current-outline-title"},yl={class:"outline-history-panel"},fl={class:"panel-header"},_l={class:"history-search"},gl={class:"history-list-container"},hl={key:0,class:"empty-history"},bl={key:1,class:"history-list"},$l=["onClick"],wl={class:"item-content"},kl={class:"item-title"},Vl={class:"item-meta"},Sl={class:"item-timestamp"},xl={class:"item-info"},Ul={class:"item-actions"},Cl={class:"right-section"},Tl={class:"outline-main-content"},El={class:"outline-content-layout"},Dl={class:"outline-nav-sidebar"},Nl={class:"sidebar-section"},Fl={class:"sidebar-menu"},Ol={class:"sidebar-section"},zl={class:"sidebar-menu"},Pl={class:"sidebar-section"},Al={class:"sidebar-menu"},Ll={class:"sidebar-section"},Yl={class:"sidebar-menu"},Bl={class:"outline-content-area"},Il={key:0,class:"expert-section"},Ml={key:1,class:"expert-section structure-section"},jl={class:"section-header"},Rl={class:"dynamic-bridge-system"},Jl={class:"dynamic-segments-container"},Gl={key:0,class:"empty-segments"},Wl={key:1,class:"dynamic-segments-tabs"},Hl={class:"tabs-header-fixed"},Kl={class:"segment-tab-index"},ql={class:"segment-title"},Ql={class:"segment-type-badge"},Xl={class:"segment-contents-container"},Zl={class:"segment-main-info"},es={class:"segment-row"},ts={class:"segment-row"},ls={class:"segment-row"},ss={class:"segment-row"},ns={class:"segment-row"},os={class:"segment-row"},as={class:"segment-row"},is={class:"segment-row"},ds={class:"segment-row"},rs={class:"segment-row"},us={class:"segment-row"},cs={class:"segment-compact-row"},ps={class:"sub-segments-section"},ms={class:"segments-header"},vs={class:"segment-card-header"},ys={class:"segment-index"},fs={class:"segment-actions"},_s={class:"segment-compact-row"},gs={class:"segment-row"},hs={key:0,class:"segment-row"},bs={class:"related-foreshadows"},$s={key:0,class:"foreshadow-group"},ws={class:"foreshadow-group-title"},ks={key:1,class:"foreshadow-group"},Vs={class:"foreshadow-group-title"},Ss={key:0,class:"empty-segments"},xs={class:"insert-segment-actions"},Us={key:2,class:"expert-section"},Cs={class:"section-header"},Ts={class:"foreshadow-scrollable-container"},Es={class:"foreshadow-list expert-foreshadow-list"},Ds={key:0,class:"empty-foreshadows"},Ns=["id","data-type","data-importance"],Fs={class:"foreshadow-card-header"},Os={class:"foreshadow-title"},zs={class:"delete-button-wrapper"},Ps={class:"foreshadow-details-grid"},As={class:"foreshadow-detail-item"},Ls={class:"foreshadow-detail-item"},Ys={class:"foreshadow-detail-item"},Bs={class:"segment-option-index"},Is={class:"segment-option-title"},Ms={class:"segment-option-badge"},js={class:"foreshadow-segment-option sub-segment"},Rs={class:"segment-option-index"},Js={class:"segment-option-title"},Gs={class:"foreshadow-detail-item"},Ws={class:"segment-option-index"},Hs={class:"segment-option-title"},Ks={class:"segment-option-badge"},qs={class:"foreshadow-segment-option sub-segment"},Qs={class:"segment-option-index"},Xs={class:"segment-option-title"},Zs={class:"foreshadow-description"},en={class:"foreshadow-description"},tn={key:0,class:"foreshadow-segment-links"},ln={class:"foreshadow-links-header"},sn={class:"foreshadow-links-content"},nn={key:0,class:"foreshadow-link-item"},on={class:"link-content"},an={key:1,class:"foreshadow-link-item"},dn={class:"link-content"},rn={key:2,class:"foreshadow-link-item"},un={class:"link-content"},cn={key:3,class:"expert-section"},pn={class:"section-header"},mn={class:"emotion-controls"},vn={class:"emotion-card-header"},yn={class:"emotion-curve-container"},fn={class:"emotion-chart-wrapper"},_n={class:"emotion-legend"},gn={key:0,class:"legend-item"},hn={key:4,class:"expert-section"},bn={class:"section-header user-select-none"},$n={class:"preview-actions"},wn={class:"preview-content"},kn={key:0,class:"professional-preview"},Vn={class:"preview-volume-info"},Sn={class:"preview-title"},xn={class:"preview-meta"},Un={class:"meta-item"},Cn={class:"meta-item"},Tn={class:"meta-item"},En={class:"preview-segments"},Dn={key:0,class:"empty-preview"},Nn={class:"segment-header user-select-none"},Fn={class:"segment-header-top"},On={class:"segment-index"},zn={class:"segment-title"},Pn={class:"segment-body"},An={key:0,class:"segment-property"},Ln={class:"property-value"},Yn={key:1,class:"segment-property"},Bn={class:"property-value"},In={key:2,class:"segment-property"},Mn={class:"property-value"},jn={key:3,class:"segment-property"},Rn={class:"property-value"},Jn={key:4,class:"segment-property"},Gn={class:"property-value"},Wn={key:5,class:"segment-property"},Hn={class:"property-value"},Kn={key:6,class:"segment-property"},qn={class:"property-value"},Qn={key:7,class:"segment-property"},Xn={class:"property-value"},Zn={key:8,class:"segment-subsegments"},eo={class:"subsegment-header user-select-none"},to={class:"subsegment-index"},lo={class:"subsegment-title"},so={class:"subsegment-body"},no={key:0,class:"subsegment-property"},oo={key:1,class:"subsegment-property"},ao={key:2,class:"subsegment-property"},io={key:3,class:"subsegment-property"},ro={key:4,class:"subsegment-property"},uo={key:5,class:"subsegment-property"},co={class:"preview-foreshadows"},po={key:0,class:"empty-preview"},mo={key:1,class:"foreshadows-list"},vo={class:"foreshadow-header user-select-none"},yo={class:"foreshadow-index"},fo={class:"foreshadow-title"},_o={class:"foreshadow-type"},go={class:"foreshadow-importance"},ho={class:"foreshadow-body"},bo={key:0,class:"foreshadow-property"},$o={class:"foreshadow-property-value"},wo={key:1,class:"foreshadow-property"},ko={class:"foreshadow-property-value"},Vo={key:2,class:"foreshadow-flow user-select-none"},So={class:"preview-end-indicator"},xo={key:1,class:"simple-preview"},Uo={class:"preview-title"},Co={key:0,class:"preview-description"},To={class:"preview-segments-simple"},Eo={key:0,class:"empty-preview"},Do={class:"segment-title-simple"},No={key:0,class:"segment-content-simple"},Fo={key:1,class:"segment-content-simple"},Oo={key:2,class:"segment-content-simple"},zo={key:3,class:"segment-content-simple"},Po={key:4,class:"segment-content-simple"},Ao={key:5,class:"segment-content-simple"},Lo={key:6,class:"segment-content-simple"},Yo={key:7,class:"segment-content-simple"},Bo={key:8,class:"subsegments-simple"},Io={class:"user-select-none"},Mo={key:0,class:"subsegment-simple-property"},jo={key:1,class:"subsegment-simple-property"},Ro={key:2,class:"subsegment-simple-property"},Jo={key:3,class:"subsegment-simple-property"},Go={key:4,class:"subsegment-simple-property"},Wo={key:5,class:"subsegment-simple-property"},Ho={class:"foreshadows-simple"},Ko={key:0,class:"empty-preview"},qo={key:1,class:"foreshadows-list-simple"},Qo={key:0,class:"foreshadow-simple-property"},Xo={key:1,class:"foreshadow-simple-property"},Zo={key:2,class:"foreshadow-simple-property"},ea={class:"preview-end-indicator"},ta={__name:"outline",setup(b){Gt([Wt,Ht,Kt,qt,Qt,Xt,Zt,el,tl,ll,sl,nl]);const U=k(!1),fe=k("卷设计"),P=k(!1),q=k(!0);let A=null;const H=k(!1),D=k("basic");k("start"),k([]),k(!1),k(!1),k(""),k(""),k(null),k(null);const Q=k(!1),X=k(null);k(null);const o=dt({id:"",timestamp:0,volumeTitle:"",volumeTheme:"",coreConflict:"",volumeFunction:"escalation",foreshadows:[],storyFlow:[],dynamicSegments:[]});k(!0);const Be=[{value:"normal",label:"通用桥段"},{value:"pivot",label:"转折桥段"},{value:"climax",label:"高潮桥段"}],oe=(l=null)=>{const e={id:W(),type:"normal",title:"",description:"",characterStatus:"",emotionValue:50,lockLevel:1,goal:"",conflict:"",result:"",input:"",output:"",foreshadowing:"",rhythm:"steady",subSegments:[],position:l};if(l===null)o.dynamicSegments.push(e);else{o.dynamicSegments.splice(l,0,e);for(let d=l+1;d<o.dynamicSegments.length;d++)o.dynamicSegments[d].position=d}return e},Ie=l=>{const e=o.dynamicSegments.findIndex(d=>d.id===l);if(e!==-1){V({message:`已删除桥段"${o.dynamicSegments[e].title||"未命名桥段"}"`,type:"success"}),o.dynamicSegments.splice(e,1);for(let d=e;d<o.dynamicSegments.length;d++)o.dynamicSegments[d].position=d;o.dynamicSegments.length>0&&Y(()=>{F.value=o.dynamicSegments[Math.min(e,o.dynamicSegments.length-1)].id})}},_e=l=>{const e=o.dynamicSegments.findIndex(a=>a.id===l);if(e===-1)return;const d={id:W(),title:"",emotionValue:50,lockLevel:1,goal:"",conflict:"",result:"",input:"",output:"",foreshadowing:"",rhythm:"steady"};return o.dynamicSegments[e].subSegments.push(d),d},Me=(l,e)=>{const d=o.dynamicSegments.findIndex(p=>p.id===l);if(d===-1)return;const a=o.dynamicSegments[d].subSegments.findIndex(p=>p.id===e);a!==-1&&o.dynamicSegments[d].subSegments.splice(a,1)},j=J(()=>!o.foreshadows||!Array.isArray(o.foreshadows)?[]:o.foreshadows.filter(l=>typeof l=="object"&&l!==null&&!Array.isArray(l)&&l.id));J(()=>{const l=[],e=o.dynamicSegments.map((d,a)=>({value:`DYN${a}`,label:`${a+1}: ${d.title||"未命名"} (${N(d.type)})`}));return l.push({label:"动态桥段",options:e}),o.dynamicSegments.forEach((d,a)=>{if(d.subSegments&&d.subSegments.length>0){const p=d.subSegments.map((g,T)=>({value:`DYN${a}-${T}`,label:`${a+1}.${T+1}: ${g.title||"未命名子桥段"}`}));l.push({label:`${a+1}: ${d.title||"未命名"} 的子桥段`,options:p})}}),l.push({value:"next_volume",label:"后续卷回收"}),l}),J(()=>{const l=[],e=o.dynamicSegments.map((d,a)=>({value:`DYN${a}`,label:`${a+1}: ${d.title||"未命名"} (${N(d.type)})`}));return l.push({label:"动态桥段",options:e}),o.dynamicSegments.forEach((d,a)=>{if(d.subSegments&&d.subSegments.length>0){const p=d.subSegments.map((g,T)=>({value:`DYN${a}-${T}`,label:`${a+1}.${T+1}: ${g.title||"未命名子桥段"}`}));l.push({label:`${a+1}: ${d.title||"未命名"} 的子桥段`,options:p})}}),l.push({value:"next_volume",label:"后续卷回收"}),l});const ge=()=>{const l={id:W(),title:"",type:"physical",importance:2,plantSegment:"",payoffSegment:"",description:"",alternativePlan:"",connectionTypes:[]};o.foreshadows.push(l)},je=l=>{const e=o.foreshadows.findIndex(d=>d.id===l);e!==-1&&o.foreshadows.splice(e,1)},he=l=>({physical:"物品类",information:"情报/信息类",ability:"能力缺陷类",relationship:"人际关系类",environment:"环境规则类"})[l]||l,ae=()=>{X.value&&(A&&A.dispose(),A=Le(X.value),ie())},ie=()=>{if(!A)ae();else{let l=[];l=o.dynamicSegments.map((d,a)=>({name:`${a+1}`,value:d.emotionValue,segmentType:d.type}));const e={title:{text:"动态桥段情绪曲线",left:"center"},tooltip:{trigger:"axis",formatter:function(d){const a=d[0],p=a.name,g=a.value,T=a.data.segmentType;let te=`${p}: ${g}`;return T&&(te+=` (${N(T)})`),te}},legend:{data:["动态桥段情绪"],bottom:"5%"},xAxis:{type:"category",data:l.map(d=>d.name)},yAxis:{type:"value",name:"情绪强度",min:0,max:100,axisLabel:{formatter:"{value}"},axisLine:{show:!0}},series:[{name:"动态桥段情绪",type:"line",data:l.map(d=>({value:d.value,segmentType:d.segmentType})),smooth:!0,lineStyle:{width:4},symbolSize:10,markPoint:{data:[{type:"max",name:"最高点"},{type:"min",name:"最低点"}]}}]};q.value&&o.dynamicSegments.forEach((d,a)=>{if(d.subSegments&&d.subSegments.length>0){const p=d.subSegments.map((g,T)=>({name:`${a+1}.${T+1}`,value:g.emotionValue}));p.length>0&&e.series.push({name:`桥段${a+1}的子桥段`,type:"line",data:[{value:d.emotionValue},...p.map(g=>g.value),{value:d.emotionValue}],smooth:!0,lineStyle:{width:2,type:"dashed"},symbolSize:6,itemStyle:{color:Re(a)}})}}),A.setOption(e)}},Re=l=>{const e=["#67C23A","#E6A23C","#409EFF","#F56C6C","#909399","#9B59B6","#1ABC9C"];return e[l%e.length]},K=l=>{D.value=l,Y(()=>{l==="emotion"&&X.value&&ae()})},be=async()=>{P.value=!0;const l=F.value;try{o.timestamp=Date.now();const e=await window.pywebview.api.book_controller.save_outline(o),d=JSON.parse(e);d.status==="success"?(V({message:"大纲保存成功！",type:"success"}),d.data&&d.data.id&&(o.id=d.data.id),await Z(o.id),Y(()=>{l&&o.dynamicSegments.some(a=>a.id===l)&&(F.value=l)})):V.error("保存失败: "+(d.message||"未知错误"))}catch(e){console.error("保存大纲失败:",e),V.error("保存失败: "+e.message)}finally{P.value=!1}},Je=async()=>{try{const l=JSON.stringify(o,null,2);await window.pywebview.api.copy_to_clipboard(l),V({message:"大纲已复制到剪贴板",type:"success"})}catch(l){console.error("导出大纲失败:",l),V.error("复制到剪贴板失败，请检查浏览器权限")}},Ge=()=>{ne.prompt("请粘贴大纲JSON数据:","导入大纲",{confirmButtonText:"导入",cancelButtonText:"取消",inputType:"textarea",inputPlaceholder:"粘贴JSON格式的大纲数据..."}).then(({value:l})=>{if(!l||l.trim()===""){V.warning("导入数据不能为空");return}try{const e=JSON.parse(l);ne.confirm("确定要导入此大纲数据吗？当前大纲数据将被覆盖。","确认导入",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Object.assign(o,e),o.dynamicSegments&&o.dynamicSegments.length>0&&(F.value=o.dynamicSegments[0].id),D.value==="emotion"&&Y(()=>{ie()}),V.success("大纲数据导入成功")}).catch(()=>{})}catch(e){console.error("解析导入数据失败:",e),V.error("导入失败：JSON数据格式不正确")}}).catch(()=>{})},$e=l=>{(l.ctrlKey||l.metaKey)&&l.key==="s"&&(l.preventDefault(),be())};rt(()=>{Z(),window.addEventListener("resize",Ve),document.addEventListener("keydown",$e),Fe(He,l=>{l==="overview"&&Y(()=>{Ke()})}),Fe(()=>o.dynamicSegments.length,l=>{l>0&&!o.dynamicSegments.find(e=>e.id===F.value)&&Y(()=>{F.value=o.dynamicSegments[0].id})},{immediate:!0})});const Z=async(l=null)=>{try{H.value=!0;const e=await window.pywebview.api.book_controller.get_outlines(),d=JSON.parse(e);if(d.status==="success"&&d.data)if(I.value=d.data||[],I.value.length>0)if(l){const a=I.value.find(p=>p.id===l);a?(Object.assign(o,a),o.foreshadows?o.foreshadows=o.foreshadows.filter(p=>typeof p=="object"&&p!==null&&!Array.isArray(p)).map(p=>({id:p.id||W(),title:p.title||"",type:p.type||"physical",importance:typeof p.importance=="number"?p.importance:parseInt(p.importance)||2,plantSegment:p.plantSegment||"",payoffSegment:p.payoffSegment||"",description:p.description||"",alternativePlan:p.alternativePlan||"",connectionTypes:Array.isArray(p.connectionTypes)?p.connectionTypes:[]})):o.foreshadows=[],V.success("已切换到所选大纲")):(Object.assign(o,I.value[0]),o.foreshadows?o.foreshadows=o.foreshadows.filter(p=>typeof p=="object"&&p!==null&&!Array.isArray(p)).map(p=>({id:p.id||W(),title:p.title||"",type:p.type||"physical",importance:typeof p.importance=="number"?p.importance:parseInt(p.importance)||2,plantSegment:p.plantSegment||"",payoffSegment:p.payoffSegment||"",description:p.description||"",alternativePlan:p.alternativePlan||"",connectionTypes:Array.isArray(p.connectionTypes)?p.connectionTypes:[]})):o.foreshadows=[],V.warning("未找到指定大纲，已加载最新大纲"))}else Object.assign(o,I.value[0]),o.foreshadows?o.foreshadows=o.foreshadows.filter(a=>typeof a=="object"&&a!==null&&!Array.isArray(a)).map(a=>({id:a.id||W(),title:a.title||"",type:a.type||"physical",importance:typeof a.importance=="number"?a.importance:parseInt(a.importance)||2,plantSegment:a.plantSegment||"",payoffSegment:a.payoffSegment||"",description:a.description||"",alternativePlan:a.alternativePlan||"",connectionTypes:Array.isArray(a.connectionTypes)?a.connectionTypes:[]})):o.foreshadows=[],V.success("大纲数据加载成功");else V.info("未找到现有大纲，使用新大纲");else V.warning("加载大纲数据失败，使用空白大纲");o.dynamicSegments.length>0&&(!F.value||!o.dynamicSegments.some(a=>a.id===F.value))&&(F.value=o.dynamicSegments[0].id),D.value==="emotion"&&Y(()=>{ae()})}catch(e){console.error("加载大纲数据失败:",e),V.error("加载大纲数据失败: "+e.message)}finally{H.value=!1}},we=()=>{ne.confirm("确定要创建新大纲吗？未保存的更改将丢失。","创建新大纲",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Object.assign(o,{id:"",timestamp:Date.now(),volumeTitle:"",volumeTheme:"",coreConflict:"",volumeFunction:"escalation",foreshadows:[],storyFlow:[],dynamicSegments:[]}),V.success("已创建新大纲")}).catch(()=>{})},ke=(l=null)=>{const e=l||o.id;if(!e){V.warning("没有选择大纲或大纲尚未保存");return}ne.confirm("确定要删除当前大纲吗？此操作不可恢复。","删除大纲",{confirmButtonText:"删除",cancelButtonText:"取消",type:"danger"}).then(async()=>{try{H.value=!0;const d=await window.pywebview.api.book_controller.delete_outline(e),a=JSON.parse(d);a.status==="success"?(V.success("大纲已删除"),await Z()):V.error("删除失败: "+(a.message||"未知错误"))}catch(d){console.error("删除大纲失败:",d),V.error("删除大纲失败: "+d.message)}finally{H.value=!1}}).catch(()=>{})},We=l=>{if(!l)return"";const e=new Date(l);return`${e.getFullYear()}/${(e.getMonth()+1).toString().padStart(2,"0")}/${e.getDate().toString().padStart(2,"0")} ${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}`},Ve=()=>{A&&A.resize()};ut(()=>{window.removeEventListener("resize",Ve),document.removeEventListener("keydown",$e),A&&(A.dispose(),A=null),B&&(B.dispose(),B=null)}),J(()=>o.storyFlow.filter(l=>l.fromNode==="A")),J(()=>o.storyFlow.filter(l=>l.fromNode==="C"));const He=k("frontBridge"),N=l=>({normal:"通用桥段",pivot:"转折桥段",climax:"高潮桥段"})[l]||"未知类型",Se=k(null);let B=null;const Ke=()=>{if(!Se.value)return;B&&B.dispose(),B=Le(Se.value);const l=o.dynamicSegments.map((d,a)=>({name:d.title||`桥段 ${a+1}`,value:d.emotionValue})),e={grid:{top:20,right:20,bottom:20,left:40,containLabel:!0},xAxis:{type:"category",data:l.map(d=>d.name)},yAxis:{type:"value",name:"情绪值",min:0,max:100},series:[{name:"情绪曲线",type:"line",data:l.map(d=>d.value),smooth:!0,lineStyle:{width:3},itemStyle:{color:"#409EFF"},symbolSize:8}]};B.setOption(e)},F=k(""),L=l=>{if(!l)return"";if(l==="next_volume")return"后续卷回收";if(l.startsWith("DYN"))if(l.includes("-")){const[e,d]=l.replace("DYN","").split("-").map(Number);if(o.dynamicSegments[e]&&o.dynamicSegments[e].subSegments&&o.dynamicSegments[e].subSegments[d]){const p=o.dynamicSegments[e].subSegments[d];return`${e+1}.${d+1}: ${p.title||"未命名子桥段"}`}}else{const e=Number(l.replace("DYN",""));if(o.dynamicSegments[e]){const d=o.dynamicSegments[e];return`${e+1}: ${d.title||"未命名"} (${N(d.type)})`}}return"未知位置"},xe=l=>{if(!l||l==="next_volume")return"";if(l.startsWith("DYN")){if(l.includes("-"))return"success";{const e=Number(l.replace("DYN",""));if(o.dynamicSegments[e]){const d=o.dynamicSegments[e].type;return d==="pivot"?"warning":d==="climax"?"danger":"info"}}}return""},qe=(l,e)=>{if(!l||!e||l==="next_volume"||e==="next_volume"||!l.startsWith("DYN")||!e.startsWith("DYN"))return"未知";let d=0,a=0;return l.includes("-")?d=Number(l.replace("DYN","").split("-")[0]):d=Number(l.replace("DYN","")),e.includes("-")?a=Number(e.replace("DYN","").split("-")[0]):a=Number(e.replace("DYN","")),Math.abs(a-d)},Qe=l=>[...de(l),...re(l)].filter((e,d,a)=>d===a.findIndex(p=>p.id===e.id)),de=l=>o.foreshadows.filter(e=>e.plantSegment===l),re=l=>o.foreshadows.filter(e=>e.payoffSegment===l),Ue=l=>{D.value="foreshadow",Y(()=>{const e=document.getElementById(`foreshadow-${l}`);e&&(e.scrollIntoView({behavior:"smooth",block:"center"}),e.classList.add("foreshadow-highlight"),setTimeout(()=>{e.classList.remove("foreshadow-highlight")},2e3))})},I=k([]),ee=k(""),Ce=J(()=>{if(!ee.value)return I.value;const l=ee.value.toLowerCase();return I.value.filter(e=>(e.volumeTitle||"未命名大纲").toLowerCase().includes(l))}),ue=k(!1),Xe=async()=>{try{ue.value=!0;let l="";const e=o.volumeTitle||"未命名卷";Q.value?l=Ze():l=et();try{await window.pywebview.api.copy_to_clipboard(l),V.success("已复制到剪贴板")}catch(g){console.error("复制到剪贴板失败:",g),V.warning("无法复制到剪贴板，将尝试下载文件")}const d=new Blob([l],{type:"text/plain;charset=utf-8"}),a=URL.createObjectURL(d),p=document.createElement("a");p.href=a,p.download=`${e}_大纲.txt`,document.body.appendChild(p),p.click(),document.body.removeChild(p),V.success("大纲文本导出成功")}catch(l){console.error("导出预览失败:",l),V.error("导出失败: "+l.message)}finally{ue.value=!1}},Ze=()=>{let l="";return l+=`【${o.volumeTitle||"未命名卷"}】
`,l+=`====================
`,l+=`卷主题：${o.volumeTheme||"无"}
`,l+=`核心冲突：${o.coreConflict||"无"}
`,l+=`功能定位：${Te(o.volumeFunction)}

`,l+=`【桥段结构】
`,l+=`====================
`,o.dynamicSegments.length===0?l+=`尚未创建桥段结构。

`:o.dynamicSegments.forEach((e,d)=>{l+=`${d+1}. ${e.title||`未命名桥段${d+1}`} (${N(e.type)})
`,l+=`-----------------
`,e.description&&(l+=`描述：${e.description}
`),e.goal&&(l+=`目标：${e.goal}
`),e.conflict&&(l+=`冲突：${e.conflict}
`),e.result&&(l+=`结果：${e.result}
`),e.input&&(l+=`输入状态：${e.input}
`),e.output&&(l+=`输出状态：${e.output}
`),e.foreshadowing&&(l+=`伏笔/铺垫：${e.foreshadowing}
`),e.characterStatus&&(l+=`角色状态：${e.characterStatus}
`),e.rhythm&&(l+=`节奏设计：${e.rhythm}
`),e.subSegments&&e.subSegments.length>0&&(l+=`
子桥段：
`,e.subSegments.forEach((a,p)=>{l+=`  ${d+1}.${p+1} ${a.title||`未命名子桥段${p+1}`}
`,a.goal&&(l+=`    目标：${a.goal}
`),a.conflict&&(l+=`    冲突：${a.conflict}
`),a.result&&(l+=`    结果：${a.result}
`),a.input&&(l+=`    输入状态：${a.input}
`),a.output&&(l+=`    输出状态：${a.output}
`),a.foreshadowing&&(l+=`    伏笔/铺垫：${a.foreshadowing}
`),a.rhythm&&(l+=`    节奏设计：${a.rhythm}
`)})),l+=`
`}),o.foreshadows.length>0&&(l+=`【伏笔系统】
`,l+=`====================
`,o.foreshadows.forEach((e,d)=>{l+=`${d+1}. ${e.title||"未命名伏笔"} (${he(e.type)})
`,l+=`-----------------
`,e.description&&(l+=`描述：${e.description}
`),e.alternativePlan&&(l+=`备选方案：${e.alternativePlan}
`);const a=e.plantSegment?L(e.plantSegment):"未指定",p=e.payoffSegment?L(e.payoffSegment):"未指定";l+=`埋设：${a} → 回收：${p}

`})),l},et=()=>{let l="";return l+=`《${o.volumeTitle||"未命名卷"}》

`,o.volumeTheme&&(l+=`【主题】${o.volumeTheme}
`),o.coreConflict&&(l+=`【核心】${o.coreConflict}

`),o.dynamicSegments.length===0?l+=`尚未创建桥段结构。

`:o.dynamicSegments.forEach((e,d)=>{l+=`${d+1}. ${e.title||`未命名桥段${d+1}`} (${N(e.type)})
`,e.description&&(l+=`   ${e.description}
`),e.goal&&(l+=`   目标：${e.goal}
`),e.conflict&&(l+=`   冲突：${e.conflict}
`),e.result&&(l+=`   结果：${e.result}
`),e.input&&(l+=`   输入状态：${e.input}
`),e.output&&(l+=`   输出状态：${e.output}
`),e.foreshadowing&&(l+=`   伏笔/铺垫：${e.foreshadowing}
`),e.characterStatus&&(l+=`   角色状态：${e.characterStatus}
`),e.subSegments&&e.subSegments.length>0&&e.subSegments.forEach((a,p)=>{l+=`   ${d+1}.${p+1} ${a.title||`子桥段${p+1}`}:
`,a.goal&&(l+=`     目标：${a.goal}
`),a.conflict&&(l+=`     冲突：${a.conflict}
`),a.result&&(l+=`     结果：${a.result}
`),a.input&&(l+=`     输入状态：${a.input}
`),a.output&&(l+=`     输出状态：${a.output}
`),a.foreshadowing&&(l+=`     伏笔/铺垫：${a.foreshadowing}
`)}),l+=`
`}),o.foreshadows.length>0&&(l+=`【伏笔列表】
`,o.foreshadows.forEach((e,d)=>{l+=`${d+1}. ${e.title||"未命名伏笔"}: ${e.description||"无描述"}
`,e.alternativePlan&&(l+=`   备选方案：${e.alternativePlan}
`);const a=e.plantSegment?L(e.plantSegment):"未指定",p=e.payoffSegment?L(e.payoffSegment):"未指定";l+=`   埋设位置：${a} → 回收位置：${p}

`})),l},Te=l=>({introduction:"开篇引入",escalation:"冲突升级",climax:"高潮转折",resolution:"结局收束",transition:"过渡铺垫"})[l]||l;return(l,e)=>{const d=_t,a=mt,p=pt,g=vt,T=yt,te=bt,tt=kt,h=Ct,$=Et,M=Tt,ce=Ut,pe=Dt,lt=Nt,st=Ft,Ee=Ot,le=zt,me=Pt,De=Lt,R=Mt,nt=jt,ot=Jt,Ne=ct;return ve((r(),c("div",{class:O(["outline-creator",{fullscreen:U.value}]),"element-loading-text":"加载中...","element-loading-background":"rgba(0, 0, 0, 0.7)"},[t("div",rl,[t("div",ul,[t("h1",null,m(fe.value),1),t("div",cl,[s(te,{placement:"bottom-start",width:"360",trigger:"click","popper-class":"outline-history-popover"},{reference:i(()=>[t("div",pl,[t("div",ml,[t("span",vl,m(o.volumeTitle||"未命名大纲"),1),o.id?(r(),z(d,{key:0,size:"small",effect:"plain",class:"current-tag"},{default:i(()=>e[15]||(e[15]=[y(" 已保存 ")])),_:1})):(r(),z(d,{key:1,size:"small",type:"danger",effect:"plain",class:"current-tag"},{default:i(()=>e[16]||(e[16]=[y(" 未保存 ")])),_:1}))]),s(a,{class:"selector-icon"},{default:i(()=>[s(w(ht))]),_:1})])]),default:i(()=>[t("div",yl,[t("div",fl,[e[18]||(e[18]=t("span",{class:"panel-title"},"大纲历史",-1)),s(p,{type:"primary",size:"small",plain:"",onClick:we},{default:i(()=>[s(a,null,{default:i(()=>[s(w(G))]),_:1}),e[17]||(e[17]=y(" 新建大纲 "))]),_:1})]),t("div",_l,[s(g,{modelValue:ee.value,"onUpdate:modelValue":e[0]||(e[0]=n=>ee.value=n),placeholder:"搜索大纲...","prefix-icon":"Search",clearable:""},null,8,["modelValue"])]),t("div",gl,[Ce.value.length===0?(r(),c("div",hl,[s(T,{description:"暂无大纲历史","image-size":60})])):(r(),c("div",bl,[(r(!0),c(S,null,x(Ce.value,n=>(r(),c("div",{key:n.id,class:O(["history-item",{active:o.id===n.id}]),onClick:v=>Z(n.id)},[t("div",wl,[t("div",kl,m(n.volumeTitle||"未命名大纲"),1),t("div",Vl,[t("div",Sl,[s(a,null,{default:i(()=>[s(w(ft))]),_:1}),t("span",null,m(We(n.timestamp)),1)]),t("div",xl,[s(d,{size:"small",effect:"plain"},{default:i(()=>[y(m(n.dynamicSegments?.length||0)+" 段 ",1)]),_:2},1024)])])]),t("div",Ul,[s(p,{type:"danger",size:"small",circle:"",plain:"",onClick:gt(v=>ke(n.id),["stop"])},{default:i(()=>[s(a,null,{default:i(()=>[s(w(se))]),_:1})]),_:2},1032,["onClick"])])],10,$l))),128))]))])])]),_:1})])]),t("div",Cl,[s(tt,{class:"action-buttons"},{default:i(()=>[s(p,{type:"success",onClick:be,loading:P.value,icon:w(Oe)},{default:i(()=>e[19]||(e[19]=[y(" 保存大纲 ")])),_:1},8,["loading","icon"]),s(p,{type:"primary",onClick:we,icon:w(G)},{default:i(()=>e[20]||(e[20]=[y(" 新建大纲 ")])),_:1},8,["icon"]),s(p,{onClick:Je,icon:w($t)},{default:i(()=>e[21]||(e[21]=[y(" 导出 ")])),_:1},8,["icon"]),s(p,{onClick:Ge,icon:w(wt)},{default:i(()=>e[22]||(e[22]=[y(" 导入 ")])),_:1},8,["icon"]),o.id?(r(),z(p,{key:0,type:"danger",onClick:ke,icon:w(se)},{default:i(()=>e[23]||(e[23]=[y(" 删除 ")])),_:1},8,["icon"])):f("",!0)]),_:1})])]),t("div",Tl,[t("div",El,[t("div",Dl,[t("div",Nl,[e[26]||(e[26]=t("h3",{class:"sidebar-title"},"卷级设置",-1)),t("ul",Fl,[t("li",{class:O({active:D.value==="basic"}),onClick:e[1]||(e[1]=n=>K("basic"))},[s(a,null,{default:i(()=>[s(w(ze))]),_:1}),e[24]||(e[24]=y(" 基本信息 "))],2),t("li",{class:O({active:D.value==="structure"}),onClick:e[2]||(e[2]=n=>K("structure"))},[s(a,null,{default:i(()=>[s(w(Vt))]),_:1}),e[25]||(e[25]=y(" 结构设计 "))],2)])]),t("div",Ol,[e[28]||(e[28]=t("h3",{class:"sidebar-title"},"伏笔系统",-1)),t("ul",zl,[t("li",{class:O({active:D.value==="foreshadow"}),onClick:e[3]||(e[3]=n=>K("foreshadow"))},[s(a,null,{default:i(()=>[s(w(Pe))]),_:1}),e[27]||(e[27]=y(" 伏笔管理 "))],2)])]),t("div",Pl,[e[30]||(e[30]=t("h3",{class:"sidebar-title"},"情绪曲线",-1)),t("ul",Al,[t("li",{class:O({active:D.value==="emotion"}),onClick:e[4]||(e[4]=n=>K("emotion"))},[s(a,null,{default:i(()=>[s(w(St))]),_:1}),e[29]||(e[29]=y(" 情绪分析 "))],2)])]),t("div",Ll,[e[32]||(e[32]=t("h3",{class:"sidebar-title"},"输出",-1)),t("ul",Yl,[t("li",{class:O({active:D.value==="preview"}),onClick:e[5]||(e[5]=n=>K("preview"))},[s(a,null,{default:i(()=>[s(w(xt))]),_:1}),e[31]||(e[31]=y(" 预览大纲 "))],2)])])]),t("div",Bl,[D.value==="basic"?(r(),c("div",Il,[e[33]||(e[33]=t("div",{class:"section-header"},[t("h3",null,"基本信息")],-1)),s(pe,{class:"box-card basic-info-card",shadow:"never"},{default:i(()=>[s(ce,{"label-position":"top"},{default:i(()=>[s(h,{label:"卷标题"},{default:i(()=>[s(g,{modelValue:o.volumeTitle,"onUpdate:modelValue":e[6]||(e[6]=n=>o.volumeTitle=n),placeholder:"例：帝都的阴谋"},null,8,["modelValue"])]),_:1}),s(h,{label:"卷主题"},{default:i(()=>[s(g,{modelValue:o.volumeTheme,"onUpdate:modelValue":e[7]||(e[7]=n=>o.volumeTheme=n),placeholder:"例：信任与背叛"},null,8,["modelValue"])]),_:1}),s(h,{label:"核心冲突"},{default:i(()=>[s(g,{type:"textarea",modelValue:o.coreConflict,"onUpdate:modelValue":e[8]||(e[8]=n=>o.coreConflict=n),rows:3,placeholder:"描述本卷的核心矛盾和挑战"},null,8,["modelValue"])]),_:1}),s(h,{label:"卷在整体故事中的作用"},{default:i(()=>[s(M,{modelValue:o.volumeFunction,"onUpdate:modelValue":e[9]||(e[9]=n=>o.volumeFunction=n),placeholder:"选择卷的功能定位",style:{width:"100%"}},{default:i(()=>[s($,{label:"开篇引入",value:"introduction"}),s($,{label:"冲突升级",value:"escalation"}),s($,{label:"高潮转折",value:"climax"}),s($,{label:"结局收束",value:"resolution"}),s($,{label:"过渡铺垫",value:"transition"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})])):D.value==="structure"?(r(),c("div",Ml,[t("div",jl,[e[35]||(e[35]=t("h3",null,"结构设计",-1)),s(p,{type:"primary",onClick:e[10]||(e[10]=n=>oe())},{default:i(()=>[s(a,null,{default:i(()=>[s(w(G))]),_:1}),e[34]||(e[34]=y(" 添加桥段 "))]),_:1})]),t("div",Rl,[t("div",Jl,[o.dynamicSegments.length===0?(r(),c("div",Gl,[s(T,{description:"暂无桥段，请点击'添加桥段'开始创建"},{default:i(()=>[s(p,{type:"primary",onClick:e[11]||(e[11]=n=>oe())},{default:i(()=>e[36]||(e[36]=[y("添加第一个桥段")])),_:1})]),_:1})])):(r(),c("div",Wl,[t("div",Hl,[s(st,{modelValue:F.value,"onUpdate:modelValue":e[12]||(e[12]=n=>F.value=n),class:"segment-tabs",closable:"",onTabRemove:Ie},{default:i(()=>[(r(!0),c(S,null,x(o.dynamicSegments,(n,v)=>(r(),z(lt,{key:n.id,label:n.title||"未命名桥段",name:n.id},{label:i(()=>[t("div",{class:O(["segment-tab-label",{"segment-type-normal":n.type==="normal","segment-type-pivot":n.type==="pivot","segment-type-climax":n.type==="climax"}])},[t("span",Kl,m(v+1),1),t("span",ql,m(n.title||"未命名桥段"),1),t("span",Ql,m(N(n.type)),1)],2)]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])]),t("div",Xl,[(r(!0),c(S,null,x(o.dynamicSegments,(n,v)=>ve((r(),c("div",{key:n.id,class:"segment-content"},[t("div",Zl,[s(ce,null,{default:i(()=>[t("div",es,[s(h,{label:"桥段类型"},{default:i(()=>[s(M,{modelValue:n.type,"onUpdate:modelValue":u=>n.type=u,placeholder:"选择桥段类型"},{default:i(()=>[(r(),c(S,null,x(Be,u=>s($,{key:u.value,label:u.label,value:u.value},null,8,["label","value"])),64))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),t("div",ts,[s(h,{label:"桥段标题"},{default:i(()=>[s(g,{modelValue:n.title,"onUpdate:modelValue":u=>n.title=u,placeholder:"桥段标题"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),t("div",ls,[s(h,{label:"桥段描述"},{default:i(()=>[s(g,{type:"textarea",modelValue:n.description,"onUpdate:modelValue":u=>n.description=u,rows:2,placeholder:"描述这个桥段的主要内容和作用"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),t("div",ss,[s(h,{label:"角色状态"},{default:i(()=>[s(g,{type:"textarea",modelValue:n.characterStatus,"onUpdate:modelValue":u=>n.characterStatus=u,rows:2,placeholder:"描述角色在这个阶段的状态和变化"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),t("div",ns,[s(h,{label:"剧情目标"},{default:i(()=>[s(g,{type:"textarea",modelValue:n.goal,"onUpdate:modelValue":u=>n.goal=u,rows:2,placeholder:"本桥段想要达成的目标"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),t("div",os,[s(h,{label:"剧情冲突"},{default:i(()=>[s(g,{type:"textarea",modelValue:n.conflict,"onUpdate:modelValue":u=>n.conflict=u,rows:2,placeholder:"主要矛盾点是什么？"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),t("div",as,[s(h,{label:"剧情结果"},{default:i(()=>[s(g,{type:"textarea",modelValue:n.result,"onUpdate:modelValue":u=>n.result=u,rows:2,placeholder:"本桥段最终达成的结果"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),t("div",is,[s(h,{label:"输入状态"},{default:i(()=>[s(g,{type:"textarea",modelValue:n.input,"onUpdate:modelValue":u=>n.input=u,rows:2,placeholder:"本桥段的起始状态和前置条件"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),t("div",ds,[s(h,{label:"输出状态"},{default:i(()=>[s(g,{type:"textarea",modelValue:n.output,"onUpdate:modelValue":u=>n.output=u,rows:2,placeholder:"本桥段结束后的状态和成果"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),t("div",rs,[s(h,{label:"伏笔/铺垫"},{default:i(()=>[s(g,{type:"textarea",modelValue:n.foreshadowing,"onUpdate:modelValue":u=>n.foreshadowing=u,rows:2,placeholder:"为后续埋下的伏笔"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),t("div",us,[s(h,{label:"节奏设计"},{default:i(()=>[s(M,{modelValue:n.rhythm,"onUpdate:modelValue":u=>n.rhythm=u,placeholder:"选择节奏类型"},{default:i(()=>[s($,{label:"舒缓",value:"slow"}),s($,{label:"平稳",value:"steady"}),s($,{label:"紧凑",value:"tight"}),s($,{label:"急促",value:"fast"}),s($,{label:"高潮",value:"climax"})]),_:2},1032,["modelValue","onUpdate:modelValue"]),e[37]||(e[37]=t("div",{class:"form-helper-text"},"节奏设计影响读者阅读时的紧张程度",-1))]),_:2},1024)]),t("div",cs,[s(h,{label:"情绪基调"},{default:i(()=>[s(Ee,{modelValue:n.emotionValue,"onUpdate:modelValue":u=>n.emotionValue=u,min:0,max:100,marks:{0:"绝望",30:"低落",50:"中性",75:"振奋",100:"狂喜"}},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),s(h,{label:"锁定程度"},{default:i(()=>[s(le,{modelValue:n.lockLevel,"onUpdate:modelValue":u=>n.lockLevel=u,max:3},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)])]),_:2},1024)]),s(me,{"content-position":"center"},{default:i(()=>e[38]||(e[38]=[y("子桥段")])),_:1}),t("div",ps,[t("div",ms,[e[40]||(e[40]=t("h4",null,"子桥段列表",-1)),s(p,{type:"primary",size:"small",onClick:u=>_e(n.id)},{default:i(()=>[s(a,null,{default:i(()=>[s(w(G))]),_:1}),e[39]||(e[39]=y(" 添加子桥段 "))]),_:2},1032,["onClick"])]),s(Ae,{name:"list",tag:"div",class:"sub-segments-list"},{default:i(()=>[(r(!0),c(S,null,x(n.subSegments,(u,E)=>(r(),c("div",{key:u.id,class:"sub-segment-card"},[t("div",vs,[t("span",ys,m(E+1),1),t("div",fs,[s(p,{type:"danger",size:"small",text:"",onClick:_=>Me(n.id,u.id)},{default:i(()=>[s(a,null,{default:i(()=>[s(w(se))]),_:1})]),_:2},1032,["onClick"])])]),s(ce,null,{default:i(()=>[s(h,{label:"子桥段标题"},{default:i(()=>[s(g,{modelValue:u.title,"onUpdate:modelValue":_=>u.title=_,placeholder:"子桥段标题"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),t("div",_s,[s(h,{label:"情绪"},{default:i(()=>[s(Ee,{modelValue:u.emotionValue,"onUpdate:modelValue":_=>u.emotionValue=_,min:0,max:100,marks:{0:"绝望",30:"低落",50:"中性",75:"振奋",100:"狂喜"}},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),s(h,{label:"锁定度"},{default:i(()=>[s(le,{modelValue:u.lockLevel,"onUpdate:modelValue":_=>u.lockLevel=_,max:3},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),s(h,{label:"剧情目标"},{default:i(()=>[s(g,{type:"textarea",modelValue:u.goal,"onUpdate:modelValue":_=>u.goal=_,rows:2,placeholder:"本桥段想要达成的目标"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),s(h,{label:"剧情冲突"},{default:i(()=>[s(g,{type:"textarea",modelValue:u.conflict,"onUpdate:modelValue":_=>u.conflict=_,rows:2,placeholder:"主要矛盾点是什么？"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),s(h,{label:"剧情结果"},{default:i(()=>[s(g,{type:"textarea",modelValue:u.result,"onUpdate:modelValue":_=>u.result=_,rows:2,placeholder:"本桥段最终达成的结果"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),s(h,{label:"输入状态"},{default:i(()=>[s(g,{type:"textarea",modelValue:u.input,"onUpdate:modelValue":_=>u.input=_,rows:2,placeholder:"本桥段的起始状态和前置条件"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),s(h,{label:"输出状态"},{default:i(()=>[s(g,{type:"textarea",modelValue:u.output,"onUpdate:modelValue":_=>u.output=_,rows:2,placeholder:"本桥段结束后的状态和成果"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),t("div",gs,[s(h,{label:"伏笔/铺垫"},{default:i(()=>[s(g,{type:"textarea",modelValue:u.foreshadowing,"onUpdate:modelValue":_=>u.foreshadowing=_,rows:2,placeholder:"为后续埋下的伏笔"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),Qe(`DYN${v}`).length>0?(r(),c("div",hs,[s(h,{label:"关联伏笔"},{default:i(()=>[t("div",bs,[de(`DYN${v}`).length>0?(r(),c("div",$s,[t("div",ws,[s(a,null,{default:i(()=>[s(w(At))]),_:1}),e[41]||(e[41]=y(" 植入的伏笔: "))]),s(De,{wrap:""},{default:i(()=>[(r(!0),c(S,null,x(de(`DYN${v}`),_=>(r(),z(d,{key:`plant-${_.id}`,size:"small",class:"foreshadow-tag plant-tag",onClick:at=>Ue(_.id)},{default:i(()=>[s(a,null,{default:i(()=>[s(w(Yt))]),_:1}),y(" "+m(_.title||"未命名伏笔"),1)]),_:2},1032,["onClick"]))),128))]),_:2},1024)])):f("",!0),re(`DYN${v}`).length>0?(r(),c("div",ks,[t("div",Vs,[s(a,null,{default:i(()=>[s(w(Bt))]),_:1}),e[42]||(e[42]=y(" 回收的伏笔: "))]),s(De,{wrap:""},{default:i(()=>[(r(!0),c(S,null,x(re(`DYN${v}`),_=>(r(),z(d,{key:`payoff-${_.id}`,size:"small",type:"success",class:"foreshadow-tag payoff-tag",onClick:at=>Ue(_.id)},{default:i(()=>[s(a,null,{default:i(()=>[s(w(Oe))]),_:1}),y(" "+m(_.title||"未命名伏笔"),1)]),_:2},1032,["onClick"]))),128))]),_:2},1024)])):f("",!0)])]),_:2},1024)])):f("",!0),s(h,{label:"节奏设计"},{default:i(()=>[s(M,{modelValue:u.rhythm,"onUpdate:modelValue":_=>u.rhythm=_,placeholder:"选择节奏类型"},{default:i(()=>[s($,{label:"舒缓",value:"slow"}),s($,{label:"平稳",value:"steady"}),s($,{label:"紧凑",value:"tight"}),s($,{label:"急促",value:"fast"}),s($,{label:"高潮",value:"climax"})]),_:2},1032,["modelValue","onUpdate:modelValue"]),e[43]||(e[43]=t("div",{class:"form-helper-text"},"节奏设计影响读者阅读时的紧张程度",-1))]),_:2},1024)]),_:2},1024)]))),128))]),_:2},1024),n.subSegments.length===0?(r(),c("div",Ss,[s(T,{description:"暂无子桥段","image-size":60},{default:i(()=>[s(p,{onClick:u=>_e(n.id)},{default:i(()=>e[44]||(e[44]=[y("添加第一个子桥段")])),_:2},1032,["onClick"])]),_:2},1024)])):f("",!0)]),t("div",xs,[s(p,{type:"default",size:"small",onClick:u=>oe(v+1),class:"insert-segment-button",plain:""},{default:i(()=>[s(a,null,{default:i(()=>[s(w(G))]),_:1}),e[45]||(e[45]=y(" 在此后插入新桥段 "))]),_:2},1032,["onClick"])])])),[[It,F.value===n.id]])),128))])]))])])])):D.value==="foreshadow"?(r(),c("div",Us,[t("div",Cs,[e[47]||(e[47]=t("h3",null,"伏笔系统",-1)),s(p,{type:"primary",size:"small",onClick:ge},{default:i(()=>[s(a,null,{default:i(()=>[s(w(G))]),_:1}),e[46]||(e[46]=y(" 添加伏笔 "))]),_:1})]),t("div",Ts,[t("div",Es,[j.value.length===0?(r(),c("div",Ds,[s(T,{description:"暂无伏笔记录","image-size":60},{default:i(()=>[s(p,{onClick:ge},{default:i(()=>e[48]||(e[48]=[y("添加第一个伏笔")])),_:1})]),_:1})])):f("",!0),s(Ae,{name:"list",tag:"div"},{default:i(()=>[(r(!0),c(S,null,x(j.value,n=>(r(),c("div",{key:n.id,class:"foreshadow-card",id:`foreshadow-${n.id}`,"data-type":n.type,"data-importance":n.importance},[t("div",Fs,[t("div",Os,[s(g,{modelValue:n.title,"onUpdate:modelValue":v=>n.title=v,placeholder:"伏笔标题"},null,8,["modelValue","onUpdate:modelValue"])]),t("div",zs,[s(p,{type:"danger",size:"default",onClick:v=>je(n.id),class:"delete-foreshadow-btn"},{default:i(()=>[s(a,{size:18},{default:i(()=>[s(w(se))]),_:1})]),_:2},1032,["onClick"])])]),t("div",Ps,[t("div",As,[s(h,{label:"伏笔类型"},{default:i(()=>[s(M,{modelValue:n.type,"onUpdate:modelValue":v=>n.type=v,placeholder:"选择伏笔类型",style:{width:"100%"}},{default:i(()=>[s($,{label:"物品类",value:"physical"}),s($,{label:"情报/信息类",value:"information"}),s($,{label:"能力缺陷类",value:"ability"}),s($,{label:"人际关系类",value:"relationship"}),s($,{label:"环境规则类",value:"environment"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),t("div",Ls,[s(h,{label:"重要程度"},{default:i(()=>[s(le,{modelValue:n.importance,"onUpdate:modelValue":v=>n.importance=v,max:3,onChange:v=>{typeof n=="object"&&n!==null&&(n.importance=Number(v))}},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1024)]),t("div",Ys,[s(h,{label:"埋设位置"},{default:i(()=>[s(M,{modelValue:n.plantSegment,"onUpdate:modelValue":v=>n.plantSegment=v,placeholder:"选择埋设位置",filterable:"",style:{width:"100%"}},{default:i(()=>[s(R,{label:"动态桥段"},{default:i(()=>[(r(!0),c(S,null,x(o.dynamicSegments,(v,u)=>(r(),z($,{key:`plant-main-${v.id}`,value:`DYN${u}`,label:`${u+1}: ${v.title||"未命名"} (${N(v.type)})`},{default:i(()=>[t("div",{class:O(["foreshadow-segment-option",`segment-type-${v.type}`])},[t("div",Bs,m(u+1),1),t("div",Is,m(v.title||"未命名桥段"),1),t("div",Ms,m(N(v.type)),1)],2)]),_:2},1032,["value","label"]))),128))]),_:1}),(r(!0),c(S,null,x(o.dynamicSegments.filter(v=>v.subSegments&&v.subSegments.length>0),(v,u)=>(r(),z(R,{key:`plant-sub-group-${v.id}`,label:`${u+1}: ${v.title||"未命名"} 的子桥段`},{default:i(()=>[(r(!0),c(S,null,x(v.subSegments,(E,_)=>(r(),z($,{key:`plant-sub-${v.id}-${E.id}`,value:`DYN${u}-${_}`,label:`${u+1}.${_+1}: ${E.title||"未命名子桥段"}`},{default:i(()=>[t("div",js,[t("div",Rs,m(u+1)+"."+m(_+1),1),t("div",Js,m(E.title||"未命名子桥段"),1)])]),_:2},1032,["value","label"]))),128))]),_:2},1032,["label"]))),128)),s(R,{label:"其他选项"},{default:i(()=>[s($,{value:"next_volume",label:"后续卷回收"})]),_:1})]),_:2},1032,["modelValue","onUpdate:modelValue"]),e[49]||(e[49]=t("div",{class:"form-helper-text"},"选择在哪个剧情桥段中埋下这个伏笔",-1))]),_:2},1024)]),t("div",Gs,[s(h,{label:"回收位置"},{default:i(()=>[s(M,{modelValue:n.payoffSegment,"onUpdate:modelValue":v=>n.payoffSegment=v,placeholder:"选择回收位置",filterable:"",style:{width:"100%"}},{default:i(()=>[s(R,{label:"动态桥段"},{default:i(()=>[(r(!0),c(S,null,x(o.dynamicSegments,(v,u)=>(r(),z($,{key:`payoff-main-${v.id}`,value:`DYN${u}`,label:`${u+1}: ${v.title||"未命名"} (${N(v.type)})`},{default:i(()=>[t("div",{class:O(["foreshadow-segment-option",`segment-type-${v.type}`])},[t("div",Ws,m(u+1),1),t("div",Hs,m(v.title||"未命名桥段"),1),t("div",Ks,m(N(v.type)),1)],2)]),_:2},1032,["value","label"]))),128))]),_:1}),(r(!0),c(S,null,x(o.dynamicSegments.filter(v=>v.subSegments&&v.subSegments.length>0),(v,u)=>(r(),z(R,{key:`payoff-sub-group-${v.id}`,label:`${u+1}: ${v.title||"未命名"} 的子桥段`},{default:i(()=>[(r(!0),c(S,null,x(v.subSegments,(E,_)=>(r(),z($,{key:`payoff-sub-${v.id}-${E.id}`,value:`DYN${u}-${_}`,label:`${u+1}.${_+1}: ${E.title||"未命名子桥段"}`},{default:i(()=>[t("div",qs,[t("div",Qs,m(u+1)+"."+m(_+1),1),t("div",Xs,m(E.title||"未命名子桥段"),1)])]),_:2},1032,["value","label"]))),128))]),_:2},1032,["label"]))),128)),s(R,{label:"其他选项"},{default:i(()=>[s($,{value:"next_volume",label:"后续卷回收"})]),_:1})]),_:2},1032,["modelValue","onUpdate:modelValue"]),e[50]||(e[50]=t("div",{class:"form-helper-text"},"选择在哪个剧情桥段中回收这个伏笔",-1))]),_:2},1024)]),t("div",Zs,[s(h,{label:"伏笔描述"},{default:i(()=>[s(g,{type:"textarea",modelValue:n.description,"onUpdate:modelValue":v=>n.description=v,rows:3,placeholder:"详细描述这个伏笔的内容、表现形式和作用"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),t("div",en,[s(h,{label:"备选处理方案"},{default:i(()=>[s(g,{type:"textarea",modelValue:n.alternativePlan,"onUpdate:modelValue":v=>n.alternativePlan=v,rows:2,placeholder:"如果无法在计划位置回收，有什么备选方案？"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),n.plantSegment||n.payoffSegment?(r(),c("div",tn,[t("div",ln,[s(a,null,{default:i(()=>[s(w(Pe))]),_:1}),e[51]||(e[51]=y(" 剧情关联 "))]),t("div",sn,[n.plantSegment?(r(),c("div",nn,[e[52]||(e[52]=t("div",{class:"link-title"},"伏笔埋设：",-1)),t("div",on,[s(d,{size:"small",type:xe(n.plantSegment)},{default:i(()=>[y(m(L(n.plantSegment)),1)]),_:2},1032,["type"])])])):f("",!0),n.payoffSegment?(r(),c("div",an,[e[53]||(e[53]=t("div",{class:"link-title"},"伏笔回收：",-1)),t("div",dn,[s(d,{size:"small",type:xe(n.payoffSegment)},{default:i(()=>[y(m(L(n.payoffSegment)),1)]),_:2},1032,["type"])])])):f("",!0),n.plantSegment&&n.payoffSegment&&n.plantSegment!=="next_volume"&&n.payoffSegment!=="next_volume"?(r(),c("div",rn,[e[54]||(e[54]=t("div",{class:"link-title"},"跨度：",-1)),t("div",un,m(qe(n.plantSegment,n.payoffSegment))+" 个桥段 ",1)])):f("",!0)])])):f("",!0)])],8,Ns))),128))]),_:1})])])])):D.value==="emotion"?(r(),c("div",cn,[t("div",pn,[e[57]||(e[57]=t("h3",null,"情绪曲线分析",-1)),t("div",mn,[s(nt,{modelValue:q.value,"onUpdate:modelValue":e[13]||(e[13]=n=>q.value=n)},{default:i(()=>e[55]||(e[55]=[y("显示桥段内部情绪")])),_:1},8,["modelValue"]),s(p,{type:"primary",size:"small",plain:"",onClick:ie},{default:i(()=>[s(a,null,{default:i(()=>[s(w(Rt))]),_:1}),e[56]||(e[56]=y(" 刷新图表 "))]),_:1})])]),s(pe,{class:"box-card emotion-curve-card",shadow:"hover"},{header:i(()=>[t("div",vn,[e[59]||(e[59]=t("span",null,"情绪曲线可视化",-1)),s(d,{size:"small",effect:"plain",type:"info"},{default:i(()=>e[58]||(e[58]=[y("根据桥段情绪值生成")])),_:1})])]),default:i(()=>[t("div",yn,[t("div",fn,[t("div",{ref_key:"emotionChartRef",ref:X,class:"emotion-chart"},null,512)]),t("div",_n,[e[61]||(e[61]=t("div",{class:"legend-item"},[t("div",{class:"legend-color",style:{background:"var(--el-color-primary)"}}),t("span",null,"主桥段情绪")],-1)),q.value?(r(),c("div",gn,e[60]||(e[60]=[t("div",{class:"legend-color legend-color-sub"},null,-1),t("span",null,"子桥段情绪",-1)]))):f("",!0)])])]),_:1})])):D.value==="preview"?(r(),c("div",hn,[t("div",bn,[e[63]||(e[63]=t("h3",null,"大纲预览",-1)),t("div",$n,[s(ot,{modelValue:Q.value,"onUpdate:modelValue":e[14]||(e[14]=n=>Q.value=n),"inline-prompt":"","active-text":"专业格式","inactive-text":"普通格式",class:"preview-switch"},null,8,["modelValue"]),s(p,{type:"primary",size:"small",plain:"",onClick:Xe},{default:i(()=>[s(a,null,{default:i(()=>[s(w(ze))]),_:1}),e[62]||(e[62]=y(" 复制并导出 "))]),_:1})])]),s(pe,{class:"box-card outline-preview-card",shadow:"hover"},{default:i(()=>[ve((r(),c("div",wn,[Q.value?(r(),c("div",kn,[t("div",Vn,[t("h2",Sn,m(o.volumeTitle||"未命名卷"),1),t("div",xn,[t("div",Un,[e[64]||(e[64]=t("span",{class:"meta-label user-select-none"},"卷主题：",-1)),t("span",null,m(o.volumeTheme||"无"),1)]),t("div",Cn,[e[65]||(e[65]=t("span",{class:"meta-label user-select-none"},"核心冲突：",-1)),t("span",null,m(o.coreConflict||"无"),1)]),t("div",Tn,[e[66]||(e[66]=t("span",{class:"meta-label user-select-none"},"功能定位：",-1)),t("span",null,m(Te(o.volumeFunction)),1)])])]),t("div",En,[e[82]||(e[82]=t("h3",{class:"preview-section-title user-select-none"},"桥段结构",-1)),o.dynamicSegments.length===0?(r(),c("div",Dn,' 尚未创建桥段结构，请在"结构设计"中添加桥段。 ')):(r(!0),c(S,{key:1},x(o.dynamicSegments,(n,v)=>(r(),c("div",{key:n.id,class:"preview-segment"},[t("div",Nn,[t("div",Fn,[t("span",On,m(v+1),1),t("span",{class:O(["segment-type-badge",`segment-type-${n.type}`])},m(N(n.type)),3)]),t("h4",zn,m(n.title||`未命名桥段${v+1}`),1)]),t("div",Pn,[n.description?(r(),c("div",An,[e[67]||(e[67]=t("div",{class:"property-label user-select-none"},"描述：",-1)),t("div",Ln,m(n.description),1)])):f("",!0),n.goal?(r(),c("div",Yn,[e[68]||(e[68]=t("div",{class:"property-label user-select-none"},"目标：",-1)),t("div",Bn,m(n.goal),1)])):f("",!0),n.conflict?(r(),c("div",In,[e[69]||(e[69]=t("div",{class:"property-label user-select-none"},"冲突：",-1)),t("div",Mn,m(n.conflict),1)])):f("",!0),n.result?(r(),c("div",jn,[e[70]||(e[70]=t("div",{class:"property-label user-select-none"},"结果：",-1)),t("div",Rn,m(n.result),1)])):f("",!0),n.input?(r(),c("div",Jn,[e[71]||(e[71]=t("div",{class:"property-label user-select-none"},"输入：",-1)),t("div",Gn,m(n.input),1)])):f("",!0),n.output?(r(),c("div",Wn,[e[72]||(e[72]=t("div",{class:"property-label user-select-none"},"输出：",-1)),t("div",Hn,m(n.output),1)])):f("",!0),n.foreshadowing?(r(),c("div",Kn,[e[73]||(e[73]=t("div",{class:"property-label user-select-none"},"伏笔：",-1)),t("div",qn,m(n.foreshadowing),1)])):f("",!0),n.characterStatus?(r(),c("div",Qn,[e[74]||(e[74]=t("div",{class:"property-label user-select-none"},"人物：",-1)),t("div",Xn,m(n.characterStatus),1)])):f("",!0),n.subSegments&&n.subSegments.length>0?(r(),c("div",Zn,[e[81]||(e[81]=t("h5",{class:"subsegments-title user-select-none"},"子桥段：",-1)),(r(!0),c(S,null,x(n.subSegments,(u,E)=>(r(),c("div",{key:u.id,class:"subsegment-item"},[t("div",eo,[t("span",to,m(v+1)+"."+m(E+1),1),t("span",lo,m(u.title||`未命名子桥段${E+1}`),1)]),t("div",so,[u.goal?(r(),c("div",no,[e[75]||(e[75]=t("span",{class:"subsegment-property-label"},"目标：",-1)),y(m(u.goal),1)])):f("",!0),u.conflict?(r(),c("div",oo,[e[76]||(e[76]=t("span",{class:"subsegment-property-label"},"冲突：",-1)),y(m(u.conflict),1)])):f("",!0),u.result?(r(),c("div",ao,[e[77]||(e[77]=t("span",{class:"subsegment-property-label"},"结果：",-1)),y(m(u.result),1)])):f("",!0),u.input?(r(),c("div",io,[e[78]||(e[78]=t("span",{class:"subsegment-property-label"},"输入：",-1)),y(m(u.input),1)])):f("",!0),u.output?(r(),c("div",ro,[e[79]||(e[79]=t("span",{class:"subsegment-property-label"},"输出：",-1)),y(m(u.output),1)])):f("",!0),u.foreshadowing?(r(),c("div",uo,[e[80]||(e[80]=t("span",{class:"subsegment-property-label"},"伏笔：",-1)),y(m(u.foreshadowing),1)])):f("",!0)])]))),128))])):f("",!0)])]))),128))]),t("div",co,[e[87]||(e[87]=t("h3",{class:"preview-section-title user-select-none"},"伏笔系统",-1)),j.value.length===0?(r(),c("div",po,' 暂无伏笔记录，请在"伏笔管理"中添加伏笔。 ')):(r(),c("div",mo,[(r(!0),c(S,null,x(j.value,(n,v)=>(r(),c("div",{key:n.id,class:"preview-foreshadow"},[t("div",vo,[t("span",yo,m(v+1),1),t("span",fo,m(n.title||"未命名伏笔"),1),t("span",_o,m(he(n.type)),1),t("span",go,[s(le,{"model-value":n.importance,disabled:"",max:3},null,8,["model-value"])])]),t("div",ho,[n.description?(r(),c("div",bo,[e[83]||(e[83]=t("div",{class:"foreshadow-property-label user-select-none"},"描述：",-1)),t("div",$o,m(n.description),1)])):f("",!0),n.alternativePlan?(r(),c("div",wo,[e[84]||(e[84]=t("div",{class:"foreshadow-property-label user-select-none"},"备选方案：",-1)),t("div",ko,m(n.alternativePlan),1)])):f("",!0),n.plantSegment||n.payoffSegment?(r(),c("div",Vo,[e[85]||(e[85]=t("span",{class:"flow-label"},"埋设：",-1)),y(m(L(n.plantSegment)||"未指定")+" → ",1),e[86]||(e[86]=t("span",{class:"flow-label"},"回收：",-1)),y(m(L(n.payoffSegment)||"未指定"),1)])):f("",!0)])]))),128))]))]),t("div",So,[s(me,null,{default:i(()=>e[88]||(e[88]=[y("预览结束")])),_:1})])])):(r(),c("div",xo,[t("h2",Uo,m(o.volumeTitle||"未命名卷"),1),o.volumeTheme||o.coreConflict?(r(),c("div",Co,[t("p",null,m(o.volumeTheme?`【主题】${o.volumeTheme}`:""),1),t("p",null,m(o.coreConflict?`【核心】${o.coreConflict}`:""),1)])):f("",!0),t("div",To,[o.dynamicSegments.length===0?(r(),c("div",Eo,' 尚未创建桥段结构，请在"结构设计"中添加桥段。 ')):(r(!0),c(S,{key:1},x(o.dynamicSegments,(n,v)=>(r(),c("div",{key:n.id,class:"preview-segment-simple"},[t("h4",Do,[y(m(v+1)+". "+m(n.title||`未命名桥段${v+1}`)+" ",1),t("span",{class:O(["segment-type-badge-simple",`segment-type-${n.type}`])},m(N(n.type)),3)]),n.description?(r(),c("div",No,[e[89]||(e[89]=t("strong",{class:"simple-property-label"},"描述：",-1)),y(m(n.description),1)])):f("",!0),n.goal?(r(),c("div",Fo,[e[90]||(e[90]=t("strong",{class:"simple-property-label"},"目标：",-1)),y(m(n.goal),1)])):f("",!0),n.conflict?(r(),c("div",Oo,[e[91]||(e[91]=t("strong",{class:"simple-property-label"},"冲突：",-1)),y(m(n.conflict),1)])):f("",!0),n.result?(r(),c("div",zo,[e[92]||(e[92]=t("strong",{class:"simple-property-label"},"结果：",-1)),y(m(n.result),1)])):f("",!0),n.input?(r(),c("div",Po,[e[93]||(e[93]=t("strong",{class:"simple-property-label"},"输入：",-1)),y(m(n.input),1)])):f("",!0),n.output?(r(),c("div",Ao,[e[94]||(e[94]=t("strong",{class:"simple-property-label"},"输出：",-1)),y(m(n.output),1)])):f("",!0),n.foreshadowing?(r(),c("div",Lo,[e[95]||(e[95]=t("strong",{class:"simple-property-label"},"伏笔：",-1)),y(m(n.foreshadowing),1)])):f("",!0),n.characterStatus?(r(),c("div",Yo,[e[96]||(e[96]=t("strong",{class:"simple-property-label"},"人物状态：",-1)),y(m(n.characterStatus),1)])):f("",!0),n.subSegments&&n.subSegments.length>0?(r(),c("div",Bo,[(r(!0),c(S,null,x(n.subSegments,(u,E)=>(r(),c("div",{key:u.id,class:"subsegment-item-simple"},[t("strong",Io,m(v+1)+"."+m(E+1)+" "+m(u.title||`子桥段${E+1}`),1),u.goal?(r(),c("div",Mo,[e[97]||(e[97]=t("strong",null,"目标：",-1)),y(m(u.goal),1)])):f("",!0),u.conflict?(r(),c("div",jo,[e[98]||(e[98]=t("strong",null,"冲突：",-1)),y(m(u.conflict),1)])):f("",!0),u.result?(r(),c("div",Ro,[e[99]||(e[99]=t("strong",null,"结果：",-1)),y(m(u.result),1)])):f("",!0),u.input?(r(),c("div",Jo,[e[100]||(e[100]=t("strong",null,"输入：",-1)),y(m(u.input),1)])):f("",!0),u.output?(r(),c("div",Go,[e[101]||(e[101]=t("strong",null,"输出：",-1)),y(m(u.output),1)])):f("",!0),u.foreshadowing?(r(),c("div",Wo,[e[102]||(e[102]=t("strong",null,"伏笔：",-1)),y(m(u.foreshadowing),1)])):f("",!0)]))),128))])):f("",!0)]))),128))]),t("div",Ho,[e[107]||(e[107]=t("h4",{class:"foreshadows-title-simple user-select-none"},"伏笔列表",-1)),j.value.length===0?(r(),c("div",Ko,' 暂无伏笔记录，请在"伏笔管理"中添加伏笔。 ')):(r(),c("ul",qo,[(r(!0),c(S,null,x(j.value,n=>(r(),c("li",{key:n.id},[t("strong",null,m(n.title||"未命名伏笔"),1),n.description?(r(),c("div",Qo,[e[103]||(e[103]=t("strong",null,"描述：",-1)),y(m(n.description),1)])):f("",!0),n.alternativePlan?(r(),c("div",Xo,[e[104]||(e[104]=t("strong",null,"备选方案：",-1)),y(m(n.alternativePlan),1)])):f("",!0),n.plantSegment||n.payoffSegment?(r(),c("div",Zo,[e[105]||(e[105]=t("strong",null,"埋设：",-1)),y(m(L(n.plantSegment)||"未指定")+" → ",1),e[106]||(e[106]=t("strong",null,"回收：",-1)),y(m(L(n.payoffSegment)||"未指定"),1)])):f("",!0)]))),128))]))]),t("div",ea,[s(me,null,{default:i(()=>e[108]||(e[108]=[y("预览结束")])),_:1})])]))])),[[Ne,ue.value]])]),_:1})])):f("",!0)])])])],2)),[[Ne,H.value]])}}},ga=it(ta,[["__scopeId","data-v-2b199da1"]]);export{ga as default};
